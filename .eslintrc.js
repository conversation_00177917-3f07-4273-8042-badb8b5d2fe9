module.exports = {
	extends: [ 'airbnb', 'prettier', 'plugin:react/recommended' ],
	env: {
		browser: true,
		es6: true,
	},
	parser: 'babel-eslint',
	plugins: [ 'react', 'babel', 'react-hooks', 'unicorn' ],
	rules: {
		semi: [ 2, 'never' ],
		'import/prefer-default-export': 0,
		'react/no-array-index-key': 0,
		'import/no-unresolved': 0,
		'react/jsx-one-expression-per-line': 0,
		'react/prop-types': 0,
		'react/forbid-prop-types': 0,
		'react/jsx-indent': 0,
		'react/jsx-wrap-multilines': [ 'error', { declaration: false, assignment: false } ],
		'react/jsx-filename-extension': 0,
		'react/state-in-constructor': 0,
		'react/jsx-props-no-spreading': 0,
		'react/destructuring-assignment': 0,
		'react/require-default-props': 0,
		'react/sort-comp': 0,
		'react/display-name': 0,
		'react/static-property-placement': 0,
		'react/no-find-dom-node': 0,
		'react/no-unused-prop-types': 0,
		'react/default-props-match-prop-types': 0,
		'react-hooks/rules-of-hooks': 2,
		'import/extensions': 0,
		'import/no-cycle': 0,
		'import/no-extraneous-dependencies': 0,
		'jsx-a11y/no-static-element-interactions': 0,
		'jsx-a11y/anchor-has-content': 0,
		'jsx-a11y/click-events-have-key-events': 0,
		'jsx-a11y/anchor-is-valid': 0,
		'jsx-a11y/no-noninteractive-element-interactions': 0,
		'jsx-a11y/label-has-for': 0,
		'comma-dangle': [ 'error', 'always-multiline' ],
		'consistent-return': 0,
		'no-param-reassign': 0,
		'no-underscore-dangle': 0,
		'no-plusplus': 0,
		'no-continue': 0,
		'no-restricted-globals': 0,
		'max-classes-per-file': 0,
		'unicorn/better-regex': 2,
		'unicorn/prefer-string-trim-start-end': 2,
		'unicorn/expiring-todo-comments': 2,
		'unicorn/no-abusive-eslint-disable': 2,
		'no-use-before-define': 0,
		'no-shadow': 0,
		'no-undef': 0,
		'arrow-body-style': 0,
	},
	globals: {
		gtag: true,
	},
}
