### 修改配置项

- 项目初始化后，请修改 abc.config.js 中的 appName，改成对应的项目名，项目名如果是多个单词，请使用短横杠(-)分隔

### 开发阶段

- 修改 host, 增加如下绑定

```
127.0.0.1 sub.baas.uban360.net
```

- 执行如下命令，安装项目依赖

```
xnpm i

// 如果没有 xnpm 命令，请先安装 xadd
npm i xadd -g
```

- 访问 https://cpgxpt.zhengqiyezhi666.com:13001/login/#/index, 切换到【手机快捷登录】，然后使用账号(13811246858/1)登录，最后点击右上角【切换到后台】，进入管理后台

- 执行 dev 命令，进行本地开发

```
npm run dev

```

### 发布阶段

- 发布到测试环境，执行 build 命令

```
- npm run build
```

- 发布到线上环境，执行 release 命令

```
npm run release
```

### 其它说明

- 子项目是按照版本号的方式打包
- 测试环境：推荐保持版本号为 0.0.1，线上环境：每次发布，手动修改 package.json 版本号