const path = require('path')
const webpack = require('webpack')

const isDev = process.env.NODE_ENV === 'development'
const entry = path.resolve(__dirname, './src/index.js')
const CopyWebpackPlugin = require('copy-webpack-plugin')
const { DefinePlugin } = require('webpack')
const externals = require('./config/externals')

const babelOptions = {
  plugins: [],
}

const output = {
  path: path.resolve(__dirname, './dist'),
  // publicPath: isDev ? '/' : './',
  publicPath: isDev ? 'http://localhost:9002/' : './',
  filename: '[name].js',
}

const appName = 'extra-forms'

const env = process.env.NODE_ENV
const config = {
  development: {
    // api: 'http://***********:31006', // 'http://moa-dev.uban360.net:21007',
    api: 'https://moa-bc.uban360.com/',
  },
  production: {
    api: '', // https://baas.uban360.com/bpmn/form-client/#
  },
}

const plugins = [
  new CopyWebpackPlugin([
    // {
    //   from: 'lib/WpsOAAssist',
    //   to: 'WpsOAAssist/',
    // },
    // {
    //   from: 'lib/wwwroot',
    //   to: 'wwwroot/',
    // },
    {
      from: 'lib/template',
      to: 'template/',
    },
    {
      from: 'node_modules/_@xm_Tree@0.0.27@@xm/Tree/dist/static',
      to: 'static/',
    },
  ]),
  new webpack.DefinePlugin({
    ab: JSON.stringify(config[env]),
  }),
]

const apiProxyDomain = 'https://moa-bc.uban360.com/'
// const apiProxyDomain = 'https://tymh.gx.chinamobile.com:21006/'

if (!isDev) {
  plugins.push(
    new DefinePlugin({
      STATIC_PREFIX: `"//" + window._APP_CONFIG.domain + "/${appName}/"`,
    }),
  )

  babelOptions.plugins.push([
    require.resolve('babel-plugin-const-replace-import'),
    {
      libraries: externals,
    },
  ])
} else {
  plugins.push(
    new DefinePlugin({
      STATIC_PREFIX: `'/'`,
    }),
  )
}

module.exports = {
  https: false,
  htmlTemplateUrl: path.resolve(__dirname, './public/index.html'),
  entry: {
    index: entry,
  },
  staticPath: path.resolve(__dirname, './public'),
  // host: 'localhost',
  // host: 'sub.moa-dev.uban360.net',
  // host: '1.tymh.gx.chinamobile.com',
  host: 'sub.moa-bc.uban360.com',
  port: 9002,
  output,
  alias: {
    ROOT: path.resolve(__dirname, './src'),
  },
  cssModule: {
    enable: true,
    localIdentName: `[local]-[hash:base64:5]`,
  },
  proxy: {
    '/baas-wf-portal': apiProxyDomain,
    '/baas-account': apiProxyDomain,
    '/baas-form': apiProxyDomain,
    '/baas-wf-letter': apiProxyDomain,
    '/security-manage-platform': apiProxyDomain,
    '/baas-admin': apiProxyDomain,
    '/baas-contact': apiProxyDomain,
    // '/baas-wf-letter': apiProxyDomain,
    '/baas-moa-document-split': apiProxyDomain,
    '/webaace': apiProxyDomain,
    '/web': apiProxyDomain,
    '/sfs/webUpload/file': {
      target: apiProxyDomain,
      secure: false,
      changeOrigin: true,
    },
    '/sfs': apiProxyDomain,
    // '/security-manage-platform': {
    // 	target: 'http://baas.uban360.net:21006',
    // 	secure: false,
    // 	changeOrigin: true,
    // },
    '/access': {
      target: apiProxyDomain, // 测试环境
      secure: false,
      changeOrigin: true,
      timeout: 360000,
    },
    '/baas-easylabel': apiProxyDomain,
    '/user-comment': {
      target: apiProxyDomain, // 测试环境
      secure: false,
      changeOrigin: true,
      timeout: 360000,
    },
    '/web-search': apiProxyDomain,
    '/baas-todocenter': apiProxyDomain,
    '/baas-news': {
      target: apiProxyDomain,
      changeOrigin: true,
    },
    '/baas-car': 'https://moa-bc.uban360.com/',
    '/doconline': 'https://moa-bc.uban360.com/',
    '/baas-formdata': 'https://moa-bc.uban360.com/',
    '/baas-wf-api': apiProxyDomain,
  },
  // terserConfig: {
  //   parallel: true,
  //   terserOptions: { compress: { drop_console: false, drop_debugger: false } },
  // },
  htmlWebpackPluginOptions: {
    minify: {
      removeComments: true,
      collapseWhitespace: !isDev,
      useShortDoctype: true,
    },
    __DOMAIN__: isDev ? '//moa-bc.uban360.com' : '..',
    template: path.resolve(__dirname, isDev ? './public/index_dev.html' : './public/index.html'),
  },
  plugins,
  env: {
    production: {
      devtool: 'none',
      externals,
    },
  },
  babelOptions,
  compileDependencies: ['ansi-regex', 'strip-ansi', 'axios', 'array-move'],
  analyzerReport: false,
}
