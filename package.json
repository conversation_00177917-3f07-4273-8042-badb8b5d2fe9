{"name": "extra-forms", "version": "0.0.1", "description": "手写表单集合", "main": "index.js", "scripts": {"dev": "cross-env NODE_ENV=development abc dev", "build": "cross-env NODE_ENV=production abc build", "release": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=4096 abc build", "prettier": "prettier --write '**/*.{js,jsx,less,md,json}'", "lint": "eslint . --ext '.js,.jsx'", "lint--fix": "npm run lint -- --fix", "prepare": "is-ci || husky install", "zip": "zip -r sub.zip dist"}, "license": "ISC", "author": {"name": "yuanlf", "email": "<EMAIL>"}, "dependencies": {"@ant-design/icons": "^4.7.0", "@formily/antd": "^1.3.13", "@formily/antd-components": "^1.3.13", "@rematch/core": "^1.4.0", "@rematch/immer": "^1.2.0", "@rematch/loading": "^1.2.1", "@xm/Button": "^1.0.0", "@xm/Dialog": "^1.0.2", "@xm/Tree": "0.0.38-beta.0.0.3", "@xm/baas-entadmin-main": "0.2.35", "@xm/native": "^1.0.50", "@xm/org-selector": "^0.1.1", "@xm/process": "^0.1.22", "@xm/upload-file-list": "1.0.20", "ahooks": "^3.1.4", "antd": "4.21.7", "antd-mobile": "^5.0.0-rc.13", "array-move": "^4.0.0", "axios": "^0.21.0", "bowser": "^2.11.0", "classnames": "^2.3.1", "driver.js": "^1.3.1", "emotion": "^10.0.27", "immer": "^9.0.21", "js-cookie": "^2.2.0", "lodash": "^4.17.11", "mobx": "^6.3.3", "mobx-react-lite": "^3.2.1", "moment": "^2.29.1", "pdfjs-dist-show-signature": "^2.2.229", "react": "^17.0.1", "react-beautiful-dnd": "^13.1.0", "react-dom": "^17.0.1", "react-helmet": "^6.1.0", "react-redux": "^7.2.2", "react-router-dom": "^5.2.0", "react-sortable-hoc": "^2.0.0", "redux": "^4.0.5", "redux-promise-middleware": "^5.1.1", "rxjs": "^6.5.1", "styled-components": "^5.2.1", "uuid": "^8.3.2", "wangeditor": "^4.7.7"}, "devDependencies": {"@babel/preset-env": "^7.16.4", "@xm/tpl": "^0.0.10", "abc-cli": "^0.2.69", "babel-eslint": "^10.1.0", "babel-plugin-const-replace-import": "^0.0.2", "babel-polyfill": "^6.26.0", "copy-webpack-plugin": "^5.1.2", "core-js": "^2.6.12", "cross-env": "^5.2.0", "eslint": "^7.23.0", "eslint-config-airbnb": "^18.2.1", "eslint-config-prettier": "^8.1.0", "eslint-plugin-babel": "^5.3.1", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-react": "^7.23.1", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unicorn": "^29.0.0", "husky": "^5.2.0", "is-ci": "^3.0.0", "lint-staged": "^10.5.4", "prettier": "^2.2.1"}, "husky": {"hooks": {"pre-commit": "lint"}}, "lint-staged": {"*.{js,jsx,less,md,json}": ["eslint . --ext '.js,.jsx'", "prettier --write"]}}