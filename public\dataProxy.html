<!DOCTYPE html>
<html>
  
  <head>
    <!-- <meta charset="UTF-8"> -->
    <META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=GB2312">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>
      dataProxy
    </title>
  </head>
  
  <body>
    <script>
      function urldecode(str, charset, callback) {
          var script = document.createElement('script');
          script.id = '_urlDecodeFn_';
          window._urlDecodeFn_ = callback;
          var isIE = !!window.ActiveXObject || "ActiveXObject" in window
          if (isIE) {
              //隐藏iframe截获提交的字符串
              if (!window['_urlDecode_iframe_']) {
                  var iframe = document.createElement('iframe');
                  //iframe.name = '_urlDecode_iframe_';
                  iframe.setAttribute('name', '_urlDecode_iframe_');
                  iframe.style.display = 'none';
                  iframe.width = "0";
                  iframe.height = "0";
                  iframe.scrolling = "no";
                  iframe.allowtransparency = "true";
                  iframe.frameborder = "0";
                  iframe.src = 'about:blank';
                  document.body.appendChild(iframe);
              }
              //ie下需要指明charset，然后src=datauri才可以
              iframe.contentWindow.document.write('<html><scrip' + 't charset="gbk" src="data:text/javascript;charset=gbk,parent._decodeStr_=\'' + str + '\'"></s'+'cript></html>');
              setTimeout(function() {
                  callback(_decodeStr_);
                  iframe.parentNode.removeChild(iframe);
              }, 300)
          } else {
              var src = "data:text/javascript;charset=" + charset + ",_urlDecodeFn_('" + str + "');";
              src += 'document.getElementById("_urlDecodeFn_").parentNode.removeChild(document.getElementById("_urlDecodeFn_"));';
              script.src = src;
              document.body.appendChild(script);
          }
      }

      function getQuery(key) {
        var query = window.location.search.substring(1);
        var vars = query.split("&");
        for (var i = 0; i < vars.length; i++) {
          var pair = vars[i].split("=");
          if (pair[0] == key) {
            return pair[1];
          }
        }
        return ''
      }

      const returnStr = getQuery('returnStr')
     
      if (returnStr) {
          if (window.opener) {
            urldecode(returnStr, 'gb2312', function(val) {
              window.opener.postMessage({ type: 'GET_REGULATION_DATA', data: val })
            })
          }
          
          window.close()
      }
    </script>
  </body>

</html>