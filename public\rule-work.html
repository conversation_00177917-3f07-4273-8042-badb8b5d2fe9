
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
	<head>
	<title>选择适用业务岗位</title>
	<link rel="stylesheet" href="jquery/ztree/css/demo.css" type="text/css">
	<link rel="stylesheet" href="jquery/ztree/css/zTreeStyle/zTreeStyle.css" type="text/css">
	<script type="text/javascript" src="jquery/jquery-1.4.4.min.js"></script>
	<script type="text/javascript" src="jquery/ztree/js/jquery.ztree.core-3.5.min.js"></script>
	<script type="text/javascript" src="jquery/ztree/js/jquery.ztree.excheck-3.5.min.js"></script>
 
<script type="text/javascript">
var pWindow=window.dialogArguments; 
if(pWindow == null){
	pWindow = window.opener;
}	
var setting = {
		check: {
			enable: true,
			chkboxType: {"Y":"ps", "N":"ps"}
		},
		view: {
			dblClickExpand: false
		},
		data: {
			simpleData: {
				enable: true
			}
		},
		callback: {
			beforeClick: beforeClick,
			onCheck: onCheck
		}
	};

	var zNodes =[
			            {id:4, pId:0, name:"全体员工"}
			            ,{id:5, pId:0, name:"经理人员"}
						,{id:1,pId:0,name:"区公司", open:false, nocheck:false}
					
						,{id:101,pId:1,name:"公司领导", open:false, nocheck:false}
					
						,{id:102,pId:1,name:"综合部", open:false, nocheck:false}
					
						,{id:10203,pId:102,name:"文秘档案室", open:false, nocheck:false}
					
						,{id:10204,pId:102,name:"公关资讯室", open:false, nocheck:false}
					
						,{id:103,pId:1,name:"发展战略/法律事务部", open:false, nocheck:false}
					
						,{id:10303,pId:103,name:"战略管理室", open:false, nocheck:false}
					
						,{id:10304,pId:103,name:"企业管理室", open:false, nocheck:false}
					
						,{id:10305,pId:103,name:"法律管理室", open:false, nocheck:false}
					
						,{id:104,pId:1,name:"财务部", open:false, nocheck:false}
					
						,{id:10403,pId:104,name:"财务管理室", open:false, nocheck:false}
					
						,{id:10404,pId:104,name:"内控管理室", open:false, nocheck:false}
					
						,{id:10405,pId:104,name:"资产管理室", open:false, nocheck:false}
					
						,{id:105,pId:1,name:"财务部财务核算中心", open:false, nocheck:false}
					
						,{id:10502,pId:105,name:"核算管理室", open:false, nocheck:false}
					
						,{id:10503,pId:105,name:"核算执行室", open:false, nocheck:false}
					
						,{id:106,pId:1,name:"人力资源部", open:false, nocheck:false}
					
						,{id:10603,pId:106,name:"劳动组织室", open:false, nocheck:false}
					
						,{id:10604,pId:106,name:"干部管理室", open:false, nocheck:false}
					
						,{id:10605,pId:106,name:"员工培训室", open:false, nocheck:false}
					
						,{id:10606,pId:106,name:"绩效薪酬室", open:false, nocheck:false}
					
						,{id:10607,pId:106,name:"培训规划室", open:false, nocheck:false}
					
						,{id:107,pId:1,name:"市场经营部", open:false, nocheck:false}
					
						,{id:10703,pId:107,name:"市场分析室", open:false, nocheck:false}
					
						,{id:10704,pId:107,name:"大众市场室", open:false, nocheck:false}
					
						,{id:10705,pId:107,name:"家庭市场室", open:false, nocheck:false}
					
						,{id:10706,pId:107,name:"渠道管理室", open:false, nocheck:false}
					
						,{id:10707,pId:107,name:"业务管理室", open:false, nocheck:false}
					
						,{id:10708,pId:107,name:"终端管理室", open:false, nocheck:false}
					
						,{id:10709,pId:107,name:"数据业务室", open:false, nocheck:false}
					
						,{id:108,pId:1,name:"品质管理部", open:false, nocheck:false}
					
						,{id:109,pId:1,name:"网络部", open:false, nocheck:false}
					
						,{id:10903,pId:109,name:"运维管理室", open:false, nocheck:false}
					
						,{id:10904,pId:109,name:"网络管理室", open:false, nocheck:false}
					
						,{id:10905,pId:109,name:"质量管理室", open:false, nocheck:false}
					
						,{id:10906,pId:109,name:"维护优化室", open:false, nocheck:false}
					
						,{id:1091,pId:1,name:"信息技术管理部", open:false, nocheck:false}
					
						,{id:109101,pId:1091,name:"IT管理室", open:false, nocheck:false}
					
						,{id:1091010,pId:1091,name:"大数据开发室", open:false, nocheck:false}
					
						,{id:109102,pId:1091,name:"需求分析室", open:false, nocheck:false}
					
						,{id:109103,pId:1091,name:"业务交付室", open:false, nocheck:false}
					
						,{id:109104,pId:1091,name:"业务管理室", open:false, nocheck:false}
					
						,{id:109105,pId:1091,name:"基础平台室", open:false, nocheck:false}
					
						,{id:109106,pId:1091,name:"监控维护室", open:false, nocheck:false}
					
						,{id:109107,pId:1091,name:"IT安全室", open:false, nocheck:false}
					
						,{id:109108,pId:1091,name:"IT工程室", open:false, nocheck:false}
					
						,{id:109109,pId:1091,name:"大数据管理室", open:false, nocheck:false}
					
						,{id:110,pId:1,name:"信息系统部", open:false, nocheck:false}
					
						,{id:11003,pId:110,name:"规划建设室", open:false, nocheck:false}
					
						,{id:11004,pId:110,name:"业务信息系统开发室", open:false, nocheck:false}
					
						,{id:11005,pId:110,name:"业务支撑系统开发室", open:false, nocheck:false}
					
						,{id:11006,pId:110,name:"管理信息系统开发室", open:false, nocheck:false}
					
						,{id:11007,pId:110,name:"大数据开发支撑室", open:false, nocheck:false}
					
						,{id:111,pId:1,name:"计划技术部", open:false, nocheck:false}
					
						,{id:11103,pId:111,name:"计划管理室", open:false, nocheck:false}
					
						,{id:11104,pId:111,name:"技术管理室", open:false, nocheck:false}
					
						,{id:11105,pId:111,name:"网络技术室", open:false, nocheck:false}
					
						,{id:11106,pId:111,name:"基础网络室", open:false, nocheck:false}
					
						,{id:112,pId:1,name:"采购供应部", open:false, nocheck:false}
					
						,{id:11203,pId:112,name:"采购管理室", open:false, nocheck:false}
					
						,{id:11204,pId:112,name:"采购运营室", open:false, nocheck:false}
					
						,{id:11205,pId:112,name:"供应分析室", open:false, nocheck:false}
					
						,{id:11206,pId:112,name:"物流运营室", open:false, nocheck:false}
					
						,{id:113,pId:1,name:"党群工作部", open:false, nocheck:false}
					
						,{id:11303,pId:113,name:"党务文化室", open:false, nocheck:false}
					
						,{id:11304,pId:113,name:"安全管理室", open:false, nocheck:false}
					
						,{id:114,pId:1,name:"内审部", open:false, nocheck:false}
					
						,{id:11403,pId:114,name:"风险审计室", open:false, nocheck:false}
					
						,{id:11404,pId:114,name:"综合审计室", open:false, nocheck:false}
					
						,{id:115,pId:1,name:"纪检监察部", open:false, nocheck:false}
					
						,{id:11503,pId:115,name:"综合监察室", open:false, nocheck:false}
					
						,{id:11504,pId:115,name:"案件信访核查室", open:false, nocheck:false}
					
						,{id:116,pId:1,name:"工会", open:false, nocheck:false}
					
						,{id:11602,pId:116,name:"办公室", open:false, nocheck:false}
					
						,{id:11603,pId:116,name:"基层工作部", open:false, nocheck:false}
					
						,{id:117,pId:1,name:"客户服务营销中心/电子销售分公司", open:false, nocheck:false}
					
						,{id:11701,pId:117,name:"经营班子", open:false, nocheck:false}
					
						,{id:11702,pId:117,name:"综合部", open:false, nocheck:false}
					
						,{id:11703,pId:117,name:"人力资源部", open:false, nocheck:false}
					
						,{id:11704,pId:117,name:"业务管理部", open:false, nocheck:false}
					
						,{id:11705,pId:117,name:"电子渠道运营部 ", open:false, nocheck:false}
					
						,{id:11706,pId:117,name:"投诉处理部 ", open:false, nocheck:false}
					
						,{id:11707,pId:117,name:"热线运营一部 ", open:false, nocheck:false}
					
						,{id:11708,pId:117,name:"热线运营二部 ", open:false, nocheck:false}
					
						,{id:11709,pId:117,name:"工会", open:false, nocheck:false}
					
						,{id:118,pId:1,name:"网络运营中心", open:false, nocheck:false}
					
						,{id:11803,pId:118,name:"网络监控室", open:false, nocheck:false}
					
						,{id:11804,pId:118,name:"网络业务维护管理室", open:false, nocheck:false}
					
						,{id:11805,pId:118,name:"网管支撑室", open:false, nocheck:false}
					
						,{id:11806,pId:118,name:"传输维护室", open:false, nocheck:false}
					
						,{id:11807,pId:118,name:"动力设备维护室", open:false, nocheck:false}
					
						,{id:11808,pId:118,name:"综合室", open:false, nocheck:false}
					
						,{id:11809,pId:118,name:"客户响应室", open:false, nocheck:false}
					
						,{id:11810,pId:118,name:"互联网维护管理室", open:false, nocheck:false}
					
						,{id:119,pId:1,name:"无线优化中心", open:false, nocheck:false}
					
						,{id:11903,pId:119,name:"无线规划室", open:false, nocheck:false}
					
						,{id:11904,pId:119,name:"无线优化室", open:false, nocheck:false}
					
						,{id:11905,pId:119,name:"无线维护室", open:false, nocheck:false}
					
						,{id:120,pId:1,name:"运营支撑中心", open:false, nocheck:false}
					
						,{id:12003,pId:120,name:"综合质量室", open:false, nocheck:false}
					
						,{id:12004,pId:120,name:"需求分析室", open:false, nocheck:false}
					
						,{id:12005,pId:120,name:"业务管理室", open:false, nocheck:false}
					
						,{id:12006,pId:120,name:"设备维护室", open:false, nocheck:false}
					
						,{id:12007,pId:120,name:"监控维护室", open:false, nocheck:false}
					
						,{id:12008,pId:120,name:"大数据运营支撑室", open:false, nocheck:false}
					
						,{id:12009,pId:120,name:"电子渠道支撑室", open:false, nocheck:false}
					
						,{id:121,pId:1,name:"工程管理中心", open:false, nocheck:false}
					
						,{id:12103,pId:121,name:"土建工程室", open:false, nocheck:false}
					
						,{id:12104,pId:121,name:"交换工程室", open:false, nocheck:false}
					
						,{id:12105,pId:121,name:"无线工程室", open:false, nocheck:false}
					
						,{id:12106,pId:121,name:"传输工程室", open:false, nocheck:false}
					
						,{id:12107,pId:121,name:"动力配套工程室", open:false, nocheck:false}
					
						,{id:12108,pId:121,name:"综合室", open:false, nocheck:false}
					
						,{id:122,pId:1,name:"培训中心", open:false, nocheck:false}
					
						,{id:12203,pId:122,name:"培训管理室", open:false, nocheck:false}
					
						,{id:12204,pId:122,name:"培训支撑室", open:false, nocheck:false}
					
						,{id:123,pId:1,name:"后勤服务中心", open:false, nocheck:false}
					
						,{id:12303,pId:123,name:"接待室", open:false, nocheck:false}
					
						,{id:12304,pId:123,name:"后勤服务室", open:false, nocheck:false}
					
						,{id:124,pId:1,name:"离退休内退服务管理中心", open:false, nocheck:false}
					
						,{id:125,pId:1,name:"数据业务中心/网络信息公司", open:false, nocheck:false}
					
						,{id:12503,pId:125,name:"综合室/综合部", open:false, nocheck:false}
					
						,{id:12504,pId:125,name:"市场室/市场部", open:false, nocheck:false}
					
						,{id:12505,pId:125,name:"大数据运营室/大数据运营部", open:false, nocheck:false}
					
						,{id:12506,pId:125,name:"业务运营室/业务运营部", open:false, nocheck:false}
					
						,{id:12507,pId:125,name:"互联网运营室/互联网运营部", open:false, nocheck:false}
					
						,{id:12508,pId:125,name:"信息运营室/信息运营部", open:false, nocheck:false}
					
						,{id:12509,pId:125,name:"产品运营室/产品运营部", open:false, nocheck:false}
					
						,{id:126,pId:1,name:"政企客户部/集团客户公司", open:false, nocheck:false}
					
						,{id:12603,pId:126,name:"综合室/综合部", open:false, nocheck:false}
					
						,{id:12604,pId:126,name:"财务室/财务部", open:false, nocheck:false}
					
						,{id:12605,pId:126,name:"市场室/市场部", open:false, nocheck:false}
					
						,{id:12606,pId:126,name:"产品集成室/产品集成部", open:false, nocheck:false}
					
						,{id:12607,pId:126,name:"行业客户一室/行业客户一部", open:false, nocheck:false}
					
						,{id:12608,pId:126,name:"行业客户二室/行业客户二部", open:false, nocheck:false}
					
						,{id:12609,pId:126,name:"行业客户三室/行业客户三部", open:false, nocheck:false}
					
						,{id:127,pId:1,name:"阳光工程办公室", open:false, nocheck:false}
					
						,{id:128,pId:1,name:"信息安全管理中心", open:false, nocheck:false}
					
						,{id:129,pId:1,name:"区公司各部门", open:false, nocheck:false}
					
						,{id:2,pId:0,name:"市公司", open:false, nocheck:false}
					
						,{id:204,pId:2,name:"综合部", open:false, nocheck:false}
					
						,{id:205,pId:2,name:"财务部", open:false, nocheck:false}
					
						,{id:20509,pId:205,name:"内控管理中心", open:false, nocheck:false}
					
						,{id:206,pId:2,name:"人力资源部", open:false, nocheck:false}
					
						,{id:207,pId:2,name:"市场经营部", open:false, nocheck:false}
					
						,{id:208,pId:2,name:"品质管理部", open:false, nocheck:false}
					
						,{id:20802,pId:208,name:"品质管理室", open:false, nocheck:false}
					
						,{id:209,pId:2,name:"数据业务/客户运营中心", open:false, nocheck:false}
					
						,{id:210,pId:2,name:"自营渠道运营中心（南宁模式）", open:false, nocheck:false}
					
						,{id:21015,pId:210,name:"A营业厅", open:false, nocheck:false}
					
						,{id:21016,pId:210,name:"B营业厅", open:false, nocheck:false}
					
						,{id:21017,pId:210,name:"C营业厅", open:false, nocheck:false}
					
						,{id:211,pId:2,name:"营销中心（南宁/桂林/钦州模式）", open:false, nocheck:false}
					
						,{id:21118,pId:211,name:"仅南宁、桂林设置", open:false, nocheck:false}
					
						,{id:21119,pId:211,name:"A营业厅", open:false, nocheck:false}
					
						,{id:212,pId:2,name:"采购供应服务中心", open:false, nocheck:false}
					
						,{id:213,pId:2,name:"党群/监督部(不含南宁)", open:false, nocheck:false}
					
						,{id:214,pId:2,name:"党群工作部(南宁)", open:false, nocheck:false}
					
						,{id:215,pId:2,name:"纪检监察部(南宁)", open:false, nocheck:false}
					
						,{id:216,pId:2,name:"工会", open:false, nocheck:false}
					
						,{id:217,pId:2,name:"网络运营中心", open:false, nocheck:false}
					
						,{id:218,pId:2,name:"客户响应支撑中心", open:false, nocheck:false}
					
						,{id:219,pId:2,name:"工程管理中心", open:false, nocheck:false}
					
						,{id:220,pId:2,name:"政企客户部/集团客户分公司", open:false, nocheck:false}
					
						,{id:221,pId:2,name:"营销中心（桂林/钦州模式）", open:false, nocheck:false}
					
						,{id:22101,pId:221,name:"A营业厅", open:false, nocheck:false}
					
						,{id:22102,pId:221,name:"B营业厅", open:false, nocheck:false}
					
						,{id:22103,pId:221,name:"C营业厅", open:false, nocheck:false}
					
						,{id:22104,pId:221,name:"仅桂林设置", open:false, nocheck:false}
					
						,{id:222,pId:2,name:"各地市公司", open:false, nocheck:false}

						,{id:223,pId:2,name:"市公司工程维护部", open:false, nocheck:false}
					
						,{id:3,pId:0,name:"县/区域公司", open:false, nocheck:false}
					
						,{id:305,pId:3,name:"综合部", open:false, nocheck:false}
					
						,{id:306,pId:3,name:"综合部（桂林/钦州模式）", open:false, nocheck:false}
					
						,{id:307,pId:3,name:"市场部（南宁/桂林模式）", open:false, nocheck:false}
					
						,{id:308,pId:3,name:"A营业厅（南宁/桂林模式）", open:false, nocheck:false}
					
						,{id:309,pId:3,name:"B营业厅（南宁/桂林模式）", open:false, nocheck:false}
					
						,{id:310,pId:3,name:"C营业厅（南宁/桂林模式）", open:false, nocheck:false}
					
						,{id:311,pId:3,name:"社会渠道中心（钦州模式）", open:false, nocheck:false}
					
						,{id:312,pId:3,name:"政企客户部", open:false, nocheck:false}
					
						,{id:313,pId:3,name:"工程维护部", open:false, nocheck:false}
					
				
				
					
						,{id:1020302,pId:10203,name:"战略决策支持"}
					
						,{id:1020303,pId:10203,name:"综合秘书"}
					
						,{id:1020304,pId:10203,name:"机要秘书"}
					
						,{id:1020305,pId:10203,name:"档案管理"}
					
						,{id:1020402,pId:10204,name:"公共关系管理"}
					
						,{id:1020403,pId:10204,name:"新闻宣传"}
					
						,{id:1030302,pId:10303,name:"战略决策支持"}
					
						,{id:1030303,pId:10303,name:"发展战略管理"}
					
						,{id:1030402,pId:10304,name:"企业运营管理"}
					
						,{id:1030502,pId:10305,name:"法律事务管理"}
					
						,{id:1040302,pId:10403,name:"综合分析"}
					
						,{id:1040303,pId:10403,name:"综合统计"}
					
						,{id:1040304,pId:10403,name:"财务系统管理"}
					
						,{id:1040305,pId:10403,name:"预算考核管理"}
					
						,{id:1040306,pId:10403,name:"资金管理"}
					
						,{id:1040402,pId:10404,name:"内控管理"}
					
						,{id:1040403,pId:10404,name:"稽核管理"}
					
						,{id:1040502,pId:10405,name:"资产管理"}
					
						,{id:1040503,pId:10405,name:"工程资产管理"}
					
						,{id:1050202,pId:10502,name:"总账及报表会计"}
					
						,{id:1050203,pId:10502,name:"税务管理"}
					
						,{id:1050204,pId:10502,name:"税务风险控制"}
					
						,{id:1050205,pId:10502,name:"税务核算"}
					
						,{id:1050302,pId:10503,name:"核算会计"}
					
						,{id:1050303,pId:10503,name:"报账支撑"}
					
						,{id:1050304,pId:10503,name:"出纳"}
					
						,{id:1060302,pId:10603,name:"招聘管理"}
					
						,{id:1060303,pId:10603,name:"劳动用工管理"}
					
						,{id:1060402,pId:10604,name:"专业人才管理"}
					
						,{id:1060403,pId:10604,name:"干部管理"}
					
						,{id:1060602,pId:10606,name:"绩效管理"}
					
						,{id:1060603,pId:10606,name:"薪酬核算"}
					
						,{id:1060604,pId:10606,name:"薪酬福利管理"}
					
						,{id:1060702,pId:10607,name:"培训发展管理"}
					
						,{id:1070302,pId:10703,name:"战略决策支持"}
					
						,{id:1070303,pId:10703,name:"经营分析"}
					
						,{id:1070304,pId:10703,name:"市场研究"}
					
						,{id:1070402,pId:10704,name:"营销策划"}
					
						,{id:1070403,pId:10704,name:"品牌管理"}
					
						,{id:1070404,pId:10704,name:"资费管理"}
					
						,{id:1070405,pId:10704,name:"传播管理"}
					
						,{id:1070406,pId:10704,name:"业务管理"}
					
						,{id:1070502,pId:10705,name:"资费管理"}
					
						,{id:1070503,pId:10705,name:"营销策划"}
					
						,{id:1070504,pId:10705,name:"业务管理"}
					
						,{id:1070602,pId:10706,name:"实体渠道管理"}
					
						,{id:1070603,pId:10706,name:"电子渠道管理"}
					
						,{id:1070702,pId:10707,name:"业务管理"}
					
						,{id:1070703,pId:10707,name:"卡号管理"}
					
						,{id:1070704,pId:10707,name:"信息安全管理"}
					
						,{id:1070802,pId:10708,name:"终端营销管理"}
					
						,{id:1070803,pId:10708,name:"终端管理"}
					
						,{id:1070902,pId:10709,name:"业务管理"}
					
						,{id:10803,pId:108,name:"客户服务管理"}
					
						,{id:10804,pId:108,name:"服务质量管理"}
					
						,{id:1090302,pId:10903,name:"战略决策支持"}
					
						,{id:1090303,pId:10903,name:"网络经营管理"}
					
						,{id:1090304,pId:10903,name:"网络资源管理"}
					
						,{id:1090305,pId:10903,name:"应急通信保障"}
					
						,{id:1090402,pId:10904,name:"网络运维管理"}
					
						,{id:1090403,pId:10904,name:"网络与信息安全"}
					
						,{id:1090404,pId:10904,name:"互联互通管理"}
					
						,{id:1090405,pId:10904,name:"全业务维护支撑管理"}
					
						,{id:1090502,pId:10905,name:"网络运维管理"}
					
						,{id:1090503,pId:10905,name:"网络质量管理"}
					
						,{id:1090602,pId:10906,name:"网络优化管理"}
					
						,{id:1090603,pId:10906,name:"网络运维管理"}
					
						,{id:1090604,pId:10906,name:"网络代维管理"}
					
						,{id:109101001,pId:1091010,name:"系统集成管理"}
					
						,{id:109101002,pId:1091010,name:"开发测试"}
					
						,{id:109101003,pId:1091010,name:"系统分析及应用优化"}
					
						,{id:109101004,pId:1091010,name:"应用维护与支持"}
					
						,{id:10910101,pId:109101,name:"系统规划管理"}
					
						,{id:10910102,pId:109101,name:"系统支撑方案设计"}
					
						,{id:10910103,pId:109101,name:"运营质量管理"}
					
						,{id:10910104,pId:109101,name:"运维管理"}
					
						,{id:10910201,pId:109102,name:"系统集成管理"}
					
						,{id:10910301,pId:109103,name:"开发测试管理"}
					
						,{id:10910302,pId:109103,name:"应用维护与支持"}
					
						,{id:10910303,pId:109103,name:"系统维护"}
					
						,{id:10910401,pId:109104,name:"系统分析及应用优化"}
					
						,{id:10910402,pId:109104,name:"应用维护与支持"}
					
						,{id:10910501,pId:109105,name:"系统集成管理"}
					
						,{id:10910502,pId:109105,name:"系统项目管理"}
					
						,{id:10910503,pId:109105,name:"系统分析及应用优化"}
					
						,{id:10910504,pId:109105,name:"应用维护与支持"}
					
						,{id:10910505,pId:109105,name:"网络代维管理"}
					
						,{id:10910506,pId:109105,name:"系统维护"}
					
						,{id:10910601,pId:109106,name:"系统监控"}
					
						,{id:10910602,pId:109106,name:"系统监控值机"}
					
						,{id:10910603,pId:109106,name:"系统维护"}
					
						,{id:10910604,pId:109106,name:"网络投诉管理"}
					
						,{id:10910701,pId:109107,name:"信息系统安全管理"}
					
						,{id:10910702,pId:109107,name:"内控风险审计"}
					
						,{id:10910801,pId:109108,name:"系统集成管理"}
					
						,{id:10910802,pId:109108,name:"系统项目管理"}
					
						,{id:10910803,pId:109108,name:"系统支撑方案设计"}
					
						,{id:10910901,pId:109109,name:"系统规划管理"}
					
						,{id:10910902,pId:109109,name:"系统支撑方案设计"}
					
						,{id:10910903,pId:109109,name:"系统项目管理"}
					
						,{id:1100302,pId:11003,name:"系统规划管理"}
					
						,{id:1100303,pId:11003,name:"系统支撑方案设计"}
					
						,{id:1100402,pId:11004,name:"系统项目管理"}
					
						,{id:1100403,pId:11004,name:"系统支撑方案设计"}
					
						,{id:1100502,pId:11005,name:"系统项目管理"}
					
						,{id:1100602,pId:11006,name:"系统项目管理"}
					
						,{id:1100603,pId:11006,name:"信息系统安全管理"}
					
						,{id:1100702,pId:11007,name:"系统规划管理"}
					
						,{id:1100703,pId:11007,name:"系统项目管理"}
					
						,{id:1110302,pId:11103,name:"战略决策支持"}
					
						,{id:1110303,pId:11103,name:"网络规划管理"}
					
						,{id:1110304,pId:11103,name:"投资计划管理"}
					
						,{id:1110305,pId:11103,name:"规划技术综合事务"}
					
						,{id:1110402,pId:11104,name:"技术文档管理"}
					
						,{id:1110403,pId:11104,name:"共建共享管理"}
					
						,{id:1110404,pId:11104,name:"技术管理"}
					
						,{id:1110405,pId:11104,name:"节能与综合利用管理"}
					
						,{id:1110502,pId:11105,name:"项目计划管理"}
					
						,{id:1110602,pId:11106,name:"项目计划管理"}
					
						,{id:1120302,pId:11203,name:"采购管理"}
					
						,{id:1120303,pId:11203,name:"供应商管理"}
					
						,{id:1120402,pId:11204,name:"采购管理"}
					
						,{id:1120403,pId:11204,name:"合同管理"}
					
						,{id:1120404,pId:11204,name:"采供MIS维护与支持"}
					
						,{id:1120502,pId:11205,name:"物流管理"}
					
						,{id:1120503,pId:11205,name:"采购物流信息化管理"}
					
						,{id:1120602,pId:11206,name:"物流管理"}
					
						,{id:1120603,pId:11206,name:"仓储保管"}
					
						,{id:1130302,pId:11303,name:"战略决策支持"}
					
						,{id:1130303,pId:11303,name:"党群工作管理"}
					
						,{id:1130304,pId:11303,name:"企业文化管理"}
					
						,{id:1130402,pId:11304,name:"安全管理"}
					
						,{id:1140302,pId:11403,name:"内控风险审计"}
					
						,{id:1140402,pId:11404,name:"综合审计"}
					
						,{id:1150302,pId:11503,name:"纪检监察"}
					
						,{id:1150303,pId:11503,name:"效能监察"}
					
						,{id:1150402,pId:11504,name:"纪检监察"}
					
						,{id:1160202,pId:11602,name:"工会事务管理"}
					
						,{id:1160302,pId:11603,name:"工会事务管理"}
					
						,{id:1170202,pId:11702,name:"企业运营管理"}
					
						,{id:1170203,pId:11702,name:"综合管理"}
					
						,{id:1170204,pId:11702,name:"法律事务管理"}
					
						,{id:1170205,pId:11702,name:"预算考核管理"}
					
						,{id:1170206,pId:11702,name:"内控管理"}
					
						,{id:1170207,pId:11702,name:"业务稽核"}
					
						,{id:1170208,pId:11702,name:"综合财务"}
					
						,{id:1170209,pId:11702,name:"税务风险控制"}
					
						,{id:1170210,pId:11702,name:"纪检监察"}
					
						,{id:1170211,pId:11702,name:"党群工作管理"}
					
						,{id:1170212,pId:11702,name:"企业文化管理"}
					
						,{id:1170213,pId:11702,name:"安全管理"}
					
						,{id:1170214,pId:11702,name:"驾驶员"}
					
						,{id:1170215,pId:11702,name:"综合事务"}
					
						,{id:1170302,pId:11703,name:"劳动用工管理"}
					
						,{id:1170303,pId:11703,name:"绩效薪酬管理"}
					
						,{id:1170304,pId:11703,name:"人力资源综合管理"}
					
						,{id:1170305,pId:11703,name:"培训发展管理"}
					
						,{id:1170402,pId:11704,name:"运营分析"}
					
						,{id:1170403,pId:11704,name:"客户服务管理"}
					
						,{id:1170404,pId:11704,name:"业务管理"}
					
						,{id:1170405,pId:11704,name:"系统支撑管理"}
					
						,{id:1170406,pId:11704,name:"流程管理"}
					
						,{id:1170407,pId:11704,name:"客服系统维护"}
					
						,{id:1170408,pId:11704,name:"知识管理"}
					
						,{id:1170409,pId:11704,name:"工单稽核"}
					
						,{id:1170503,pId:11705,name:"营销策划"}
					
						,{id:1170504,pId:11705,name:"电子渠道营销"}
					
						,{id:1170505,pId:11705,name:"运营分析"}
					
						,{id:1170506,pId:11705,name:"系统支撑管理"}
					
						,{id:1170507,pId:11705,name:"业务管理"}
					
						,{id:1170508,pId:11705,name:"客服代表班长"}
					
						,{id:1170509,pId:11705,name:"客服系统维护"}
					
						,{id:1170510,pId:11705,name:"客服后台支撑"}
					
						,{id:1170511,pId:11705,name:"知识管理"}
					
						,{id:1170602,pId:11706,name:"投诉管理"}
					
						,{id:1170603,pId:11706,name:"运营分析"}
					
						,{id:1170604,pId:11706,name:"信息安全管理"}
					
						,{id:1170605,pId:11706,name:"排班管理"}
					
						,{id:1170606,pId:11706,name:"客服代表班长"}
					
						,{id:1170607,pId:11706,name:"专席服务"}
					
						,{id:1170608,pId:11706,name:"质检班班长"}
					
						,{id:1170609,pId:11706,name:"质检员"}
					
						,{id:1170610,pId:11706,name:"客服后台支撑"}
					
						,{id:1170703,pId:11707,name:"运营分析"}
					
						,{id:1170704,pId:11707,name:"呼入管理"}
					
						,{id:1170705,pId:11707,name:"外呼管理"}
					
						,{id:1170706,pId:11707,name:"排班管理"}
					
						,{id:1170707,pId:11707,name:"质检班班长"}
					
						,{id:1170708,pId:11707,name:"质检员"}
					
						,{id:1170709,pId:11707,name:"客服代表班长"}
					
						,{id:1170710,pId:11707,name:"客服代表"}
					
						,{id:1170711,pId:11707,name:"电话经理班长"}
					
						,{id:1170712,pId:11707,name:"电话经理"}
					
						,{id:1170713,pId:11707,name:"客服后台支撑"}
					
						,{id:1170803,pId:11708,name:"运营分析"}
					
						,{id:1170804,pId:11708,name:"呼入管理"}
					
						,{id:1170805,pId:11708,name:"外呼管理"}
					
						,{id:1170806,pId:11708,name:"排班管理"}
					
						,{id:1170807,pId:11708,name:"质检班班长"}
					
						,{id:1170808,pId:11708,name:"质检员"}
					
						,{id:1170809,pId:11708,name:"客服代表班长"}
					
						,{id:1170810,pId:11708,name:"客服代表"}
					
						,{id:1170811,pId:11708,name:"客服后台支撑"}
					
						,{id:1170902,pId:11709,name:"工会事务管理"}
					
						,{id:1180302,pId:11803,name:"监控值机"}
					
						,{id:1180303,pId:11803,name:"网络监控"}
					
						,{id:1180304,pId:11803,name:"网络维护"}
					
						,{id:1180305,pId:11803,name:"网络投诉管理"}
					
						,{id:1180306,pId:11803,name:"网络分析"}
					
						,{id:1180402,pId:11804,name:"交换网运行维护"}
					
						,{id:1180403,pId:11804,name:"数据核心网运行维护"}
					
						,{id:1180404,pId:11804,name:"业务系统维护"}
					
						,{id:1180502,pId:11805,name:"网管系统维护"}
					
						,{id:1180503,pId:11805,name:"网络与信息安全"}
					
						,{id:1180602,pId:11806,name:"传输网运行维护"}
					
						,{id:1180603,pId:11806,name:"网络监控"}
					
						,{id:1180604,pId:11806,name:"监控值机"}
					
						,{id:1180702,pId:11807,name:"动力运行维护"}
					
						,{id:1180703,pId:11807,name:"安全生产管理"}
					
						,{id:1180802,pId:11808,name:"网络质量管理"}
					
						,{id:1180803,pId:11808,name:"网络综合事务"}
					
						,{id:1180804,pId:11808,name:"设备资产管理"}
					
						,{id:1180805,pId:11808,name:"网络代维管理"}
					
						,{id:1180902,pId:11809,name:"全业务技术支撑"}
					
						,{id:1181002,pId:11810,name:"数据核心网运行维护"}
					
						,{id:1190302,pId:11903,name:"网络质量管理"}
					
						,{id:1190303,pId:11903,name:"无线网优化"}
					
						,{id:1190402,pId:11904,name:"网络优化管理"}
					
						,{id:1190403,pId:11904,name:"无线网优化"}
					
						,{id:1190502,pId:11905,name:"无线网运行维护"}
					
						,{id:1200302,pId:12003,name:"运维管理"}
					
						,{id:1200303,pId:12003,name:"系统支撑综合事务"}
					
						,{id:1200304,pId:12003,name:"运营质量管理"}
					
						,{id:1200402,pId:12004,name:"开发测试管理"}
					
						,{id:1200403,pId:12004,name:"需求分析与支撑"}
					
						,{id:1200502,pId:12005,name:"系统分析及应用优化"}
					
						,{id:1200503,pId:12005,name:"应用维护与支持"}
					
						,{id:1200602,pId:12006,name:"平台管理优化"}
					
						,{id:1200603,pId:12006,name:"系统维护"}
					
						,{id:1200604,pId:12006,name:"内控风险审计"}
					
						,{id:1200605,pId:12006,name:"应用维护与支持"}
					
						,{id:1200606,pId:12006,name:"信息系统安全管理"}
					
						,{id:1200607,pId:12006,name:"网络代维管理"}
					
						,{id:1200702,pId:12007,name:"系统监控值机"}
					
						,{id:1200703,pId:12007,name:"系统监控"}
					
						,{id:1200704,pId:12007,name:"系统维护"}
					
						,{id:1200705,pId:12007,name:"网络投诉管理"}
					
						,{id:1200802,pId:12008,name:"系统分析及应用优化"}
					
						,{id:1200803,pId:12008,name:"应用维护与支持"}
					
						,{id:1200902,pId:12009,name:"需求分析与支撑"}
					
						,{id:1200903,pId:12009,name:"开发测试管理"}
					
						,{id:1200904,pId:12009,name:"应用维护与支持"}
					
						,{id:1200905,pId:12009,name:"系统维护"}
					
						,{id:1210302,pId:12103,name:"建设项目管理"}
					
						,{id:1210402,pId:12104,name:"建设项目管理"}
					
						,{id:1210502,pId:12105,name:"建设项目管理"}
					
						,{id:1210602,pId:12106,name:"建设项目管理"}
					
						,{id:1210702,pId:12107,name:"建设项目管理"}
					
						,{id:1210802,pId:12108,name:"工程建设管理"}
					
						,{id:1210803,pId:12108,name:"工程建设支撑"}
					
						,{id:1210804,pId:12108,name:"工程建设综合事务"}
					
						,{id:1220302,pId:12203,name:"培训发展管理"}
					
						,{id:1220402,pId:12204,name:"培训发展管理"}
					
						,{id:1230302,pId:12303,name:"接待管理"}
					
						,{id:1230303,pId:12303,name:"接待员"}
					
						,{id:1230304,pId:12303,name:"综合事务"}
					
						,{id:1230402,pId:12304,name:"物业管理"}
					
						,{id:1230403,pId:12304,name:"后勤管理"}
					
						,{id:1230404,pId:12304,name:"车辆管理"}
					
						,{id:1230405,pId:12304,name:"驾驶员"}
					
						,{id:12403,pId:124,name:"离退人员管理"}
					
						,{id:1250302,pId:12503,name:"综合管理"}
					
						,{id:1250303,pId:12503,name:"纪检监察"}
					
						,{id:1250304,pId:12503,name:"法律事务管理"}
					
						,{id:1250305,pId:12503,name:"预算考核管理"}
					
						,{id:1250306,pId:12503,name:"出纳"}
					
						,{id:1250307,pId:12503,name:"业务稽核"}
					
						,{id:1250308,pId:12503,name:"驾驶员"}
					
						,{id:1250309,pId:12503,name:"综合事务"}
					
						,{id:1250402,pId:12504,name:"经营分析"}
					
						,{id:1250403,pId:12504,name:"业务管理"}
					
						,{id:1250404,pId:12504,name:"渠道管理"}
					
						,{id:1250502,pId:12505,name:"系统支持"}
					
						,{id:1250503,pId:12505,name:"业务支持"}
					
						,{id:1250602,pId:12506,name:"营销策划"}
					
						,{id:1250603,pId:12506,name:"业务管理"}
					
						,{id:1250702,pId:12507,name:"业务管理"}
					
						,{id:1250703,pId:12507,name:"营销策划"}
					
						,{id:1250802,pId:12508,name:"营销策划"}
					
						,{id:1250803,pId:12508,name:"业务管理"}
					
						,{id:1250804,pId:12508,name:"知识管理"}
					
						,{id:1250805,pId:12508,name:"质检员"}
					
						,{id:1250902,pId:12509,name:"产品管理"}
					
						,{id:1250903,pId:12509,name:"合作管理"}
					
						,{id:1250904,pId:12509,name:"信息安全管理"}
					
						,{id:1260302,pId:12603,name:"综合管理"}
					
						,{id:1260303,pId:12603,name:"纪检监察"}
					
						,{id:1260304,pId:12603,name:"法律事务管理"}
					
						,{id:1260305,pId:12603,name:"驾驶员"}
					
						,{id:1260402,pId:12604,name:"综合财务"}
					
						,{id:1260403,pId:12604,name:"内控管理"}
					
						,{id:1260404,pId:12604,name:"出纳"}
					
						,{id:1260405,pId:12604,name:"业务稽核"}
					
						,{id:1260503,pId:12605,name:"营销策划"}
					
						,{id:1260504,pId:12605,name:"经营分析"}
					
						,{id:1260505,pId:12605,name:"客户服务管理"}
					
						,{id:1260506,pId:12605,name:"业务管理"}
					
						,{id:1260507,pId:12605,name:"渠道管理"}
					
						,{id:1260603,pId:12606,name:"合作管理"}
					
						,{id:1260604,pId:12606,name:"信息安全管理"}
					
						,{id:1260605,pId:12606,name:"产品经理"}
					
						,{id:1260606,pId:12606,name:"应用解决方案管理"}
					
						,{id:1260703,pId:12607,name:"行业客户经理"}
					
						,{id:1260704,pId:12607,name:"集团客户经理"}
					
						,{id:1260803,pId:12608,name:"行业客户经理"}
					
						,{id:1260804,pId:12608,name:"集团客户经理"}
					
						,{id:1260902,pId:12609,name:"行业客户经理"}
					
						,{id:1260903,pId:12609,name:"集团客户经理"}
					
						,{id:12703,pId:127,name:"建设项目管理"}
					
						,{id:12704,pId:127,name:"工程建设管理"}
					
						,{id:12802,pId:128,name:"网络与信息安全"}
					
						,{id:12901,pId:129,name:"区公司各部门"}
					
						,{id:20403,pId:204,name:"企业运营管理"}
					
						,{id:20404,pId:204,name:"法律事务管理"}
					
						,{id:20405,pId:204,name:"综合秘书"}
					
						,{id:20406,pId:204,name:"公共关系管理"}
					
						,{id:20407,pId:204,name:"综合管理"}
					
						,{id:20408,pId:204,name:"接待员"}
					
						,{id:20409,pId:204,name:"综合事务"}
					
						,{id:20503,pId:205,name:"综合分析"}
					
						,{id:20504,pId:205,name:"预算考核管理"}
					
						,{id:20505,pId:205,name:"资产管理"}
					
						,{id:20506,pId:205,name:"税务风险控制"}
					
						,{id:20507,pId:205,name:"核算会计"}
					
						,{id:20508,pId:205,name:"出纳"}
					
						,{id:2050903,pId:20509,name:"内控管理"}
					
						,{id:2050904,pId:20509,name:"营收资金稽核"}
					
						,{id:2050905,pId:20509,name:"业务稽核"}
					
						,{id:20603,pId:206,name:"劳动用工管理"}
					
						,{id:20604,pId:206,name:"绩效薪酬管理"}
					
						,{id:20605,pId:206,name:"人力资源综合管理"}
					
						,{id:20606,pId:206,name:"培训发展管理"}
					
						,{id:20703,pId:207,name:"营销策划"}
					
						,{id:20704,pId:207,name:"经营分析"}
					
						,{id:20705,pId:207,name:"传播管理"}
					
						,{id:20706,pId:207,name:"终端营销管理"}
					
						,{id:20707,pId:207,name:"业务管理"}
					
						,{id:20708,pId:207,name:"卡号管理"}
					
						,{id:20709,pId:207,name:"渠道管理"}
					
						,{id:20710,pId:207,name:"综合业务管理"}
					
						,{id:2080202,pId:20802,name:"客户服务管理"}
					
						,{id:2080203,pId:20802,name:"服务质量管理"}
					
						,{id:2080205,pId:20802,name:"服务质量管理"}
					
						,{id:2080206,pId:20802,name:"投诉处理"}
					
						,{id:2080207,pId:20802,name:"客服后台支撑"}
					
						,{id:20902,pId:209,name:"营销策划"}
					
						,{id:20903,pId:209,name:"经营分析"}
					
						,{id:20904,pId:209,name:"产品管理"}
					
						,{id:20905,pId:209,name:"综合业务管理"}
					
						,{id:21004,pId:210,name:"综合管理"}
					
						,{id:21005,pId:210,name:"综合财务"}
					
						,{id:21006,pId:210,name:"业务管理"}
					
						,{id:21007,pId:210,name:"清欠员"}
					
						,{id:21008,pId:210,name:"服务质量管理"}
					
						,{id:21009,pId:210,name:"综合事务"}
					
						,{id:21010,pId:210,name:"营销策划"}
					
						,{id:21011,pId:210,name:"综合业务管理"}
					
						,{id:21012,pId:210,name:"区域营销管理（兼Ａ厅厅经理）"}
					
						,{id:21013,pId:210,name:"区域营销经理"}
					
						,{id:21014,pId:210,name:"终端营销管理"}
					
						,{id:2101501,pId:21015,name:"值班经理"}
					
						,{id:2101502,pId:21015,name:"营销员"}
					
						,{id:2101503,pId:21015,name:"营业员"}
					
						,{id:2101601,pId:21016,name:"营业厅经理"}
					
						,{id:2101602,pId:21016,name:"值班经理"}
					
						,{id:2101603,pId:21016,name:"营销员"}
					
						,{id:2101604,pId:21016,name:"营业员"}
					
						,{id:2101701,pId:21017,name:"营业厅经理"}
					
						,{id:2101702,pId:21017,name:"营销员"}
					
						,{id:2101703,pId:21017,name:"营业员"}
					
						,{id:21104,pId:211,name:"综合管理"}
					
						,{id:21105,pId:211,name:"综合财务"}
					
						,{id:21106,pId:211,name:"业务管理"}
					
						,{id:21107,pId:211,name:"清欠员"}
					
						,{id:21108,pId:211,name:"服务质量管理"}
					
						,{id:21109,pId:211,name:"综合事务"}
					
						,{id:21110,pId:211,name:"营销策划"}
					
						,{id:21111,pId:211,name:"综合业务管理"}
					
						,{id:21112,pId:211,name:"区域营销管理（兼Ａ厅厅经理）"}
					
						,{id:21113,pId:211,name:"渠道管理"}
					
						,{id:21114,pId:211,name:"社会渠道经理"}
					
						,{id:21115,pId:211,name:"区域营销经理"}
					
						,{id:21116,pId:211,name:"终端营销管理"}
					
						,{id:21117,pId:211,name:"集团客户经理"}
					
						,{id:2111801,pId:21118,name:"行业客户经理"}
					
						,{id:2111901,pId:21119,name:"营销员"}
					
						,{id:21203,pId:212,name:"采购管理"}
					
						,{id:21204,pId:212,name:"物流管理"}
					
						,{id:21205,pId:212,name:"物流配送"}
					
						,{id:21206,pId:212,name:"后勤管理"}
					
						,{id:21207,pId:212,name:"综合事务"}
					
						,{id:21208,pId:212,name:"车辆管理"}
					
						,{id:21209,pId:212,name:"驾驶员"}
					
						,{id:21303,pId:213,name:"党群工作管理"}
					
						,{id:21304,pId:213,name:"企业文化管理"}
					
						,{id:21305,pId:213,name:"安全管理"}
					
						,{id:21306,pId:213,name:"纪检监察"}
					
						,{id:21307,pId:213,name:"综合审计"}
					
						,{id:21402,pId:214,name:"党群工作管理"}
					
						,{id:21403,pId:214,name:"企业文化管理"}
					
						,{id:21404,pId:214,name:"安全管理"}
					
						,{id:21406,pId:214,name:"综合审计"}
					
						,{id:21502,pId:215,name:"纪检监察"}
					
						,{id:21602,pId:216,name:"工会事务管理"}
					
						,{id:21704,pId:217,name:"传输网运行维护"}
					
						,{id:21705,pId:217,name:"网络维护"}
					
						,{id:21706,pId:217,name:"基站运行维护"}
					
						,{id:21707,pId:217,name:"动力运行维护"}
					
						,{id:21708,pId:217,name:"综合维护"}
					
						,{id:21709,pId:217,name:"无线网优化"}
					
						,{id:21710,pId:217,name:"网络规划管理"}
					
						,{id:21711,pId:217,name:"全业务技术支撑"}
					
						,{id:21712,pId:217,name:"网络综合管理"}
					
						,{id:21713,pId:217,name:"安全生产管理"}
					
						,{id:21714,pId:217,name:"网络代维管理"}
					
						,{id:21715,pId:217,name:"网络与信息安全"}
					
						,{id:21803,pId:218,name:"应用维护与支持"}
					
						,{id:21804,pId:218,name:"系统维护支撑"}
					
						,{id:21805,pId:218,name:"硬件维护管理"}
					
						,{id:21806,pId:218,name:"网络代维管理"}
					
						,{id:21807,pId:218,name:"全业务技术支撑"}
					
						,{id:21808,pId:218,name:"传输网运行维护"}
					
						,{id:21809,pId:218,name:"综合维护"}
					
						,{id:21810,pId:218,name:"建设项目管理"}
					
						,{id:21811,pId:218,name:"工程建设管理"}
					
						,{id:21812,pId:218,name:"工程建设支撑"}
					
						,{id:21813,pId:218,name:"工程建设综合事务"}
					
						,{id:21904,pId:219,name:"全业务技术支撑"}
					
						,{id:21905,pId:219,name:"建设项目管理"}
					
						,{id:21906,pId:219,name:"工程建设管理"}
					
						,{id:21907,pId:219,name:"工程建设支撑"}
					
						,{id:21908,pId:219,name:"工程建设综合事务"}
					
						,{id:22004,pId:220,name:"营销策划"}
					
						,{id:22005,pId:220,name:"经营分析"}
					
						,{id:22006,pId:220,name:"业务管理"}
					
						,{id:22007,pId:220,name:"服务质量管理"}
					
						,{id:22008,pId:220,name:"综合业务管理"}
					
						,{id:22009,pId:220,name:"行业客户经理"}
					
						,{id:22010,pId:220,name:"集团客户经理"}
					
						,{id:22011,pId:220,name:"应用解决方案管理"}
					
						,{id:22012,pId:220,name:"应用解决方案支撑"}
					
						,{id:22013,pId:220,name:"产品经理"}
					
						,{id:2210101,pId:22101,name:"值班经理"}
					
						,{id:2210102,pId:22101,name:"营业员"}
					
						,{id:2210201,pId:22102,name:"营业厅经理"}
					
						,{id:2210202,pId:22102,name:"值班经理"}
					
						,{id:2210203,pId:22102,name:"营销员"}
					
						,{id:2210204,pId:22102,name:"营业员"}
					
						,{id:2210301,pId:22103,name:"营业厅经理"}
					
						,{id:2210302,pId:22103,name:"营销员"}
					
						,{id:2210303,pId:22103,name:"营业员"}
					
						,{id:2210401,pId:22104,name:"应用解决方案管理"}
					
						,{id:22201,pId:222,name:"各地市公司"}
					
						,{id:30502,pId:305,name:"综合管理"}
					
						,{id:30503,pId:305,name:"综合事务"}
					
						,{id:30504,pId:305,name:"后勤管理"}
					
						,{id:30505,pId:305,name:"党群工作管理"}
					
						,{id:30506,pId:305,name:"驾驶员"}
					
						,{id:30507,pId:305,name:"综合财务"}
					
						,{id:30601,pId:306,name:"综合维护管理"}
					
						,{id:30602,pId:306,name:"综合维护"}
					
						,{id:30702,pId:307,name:"营销策划"}
					
						,{id:30703,pId:307,name:"综合业务管理"}
					
						,{id:30704,pId:307,name:"渠道管理"}
					
						,{id:30705,pId:307,name:"业务管理"}
					
						,{id:30706,pId:307,name:"服务质量管理"}
					
						,{id:30707,pId:307,name:"清欠员"}
					
						,{id:30708,pId:307,name:"区域营销管理（兼Ａ厅厅经理）"}
					
						,{id:30709,pId:307,name:"社会渠道经理"}
					
						,{id:30710,pId:307,name:"区域营销经理"}
					
						,{id:30801,pId:308,name:"营销员"}
					
						,{id:30802,pId:308,name:"值班经理"}
					
						,{id:30803,pId:308,name:"营业员"}
					
						,{id:30901,pId:309,name:"营业厅经理"}
					
						,{id:30902,pId:309,name:"值班经理"}
					
						,{id:30903,pId:309,name:"营销员"}
					
						,{id:30904,pId:309,name:"营业员"}
					
						,{id:31001,pId:310,name:"营业厅经理"}
					
						,{id:31002,pId:310,name:"营销员"}
					
						,{id:31003,pId:310,name:"营业员"}
					
						,{id:31102,pId:311,name:"营销策划"}
					
						,{id:31103,pId:311,name:"综合业务管理"}
					
						,{id:31104,pId:311,name:"渠道管理"}
					
						,{id:31105,pId:311,name:"业务管理"}
					
						,{id:31106,pId:311,name:"服务质量管理"}
					
						,{id:31107,pId:311,name:"清欠员"}
					
						,{id:31108,pId:311,name:"社会渠道经理"}
					
						,{id:31109,pId:311,name:"区域营销经理"}
					
						,{id:31110,pId:311,name:"区域营销管理（兼Ａ厅厅经理）"}
					
						,{id:31111,pId:311,name:"营销员"}
					
						,{id:31202,pId:312,name:"业务管理"}
					
						,{id:31203,pId:312,name:"综合业务管理"}
					
						,{id:31204,pId:312,name:"应用解决方案管理"}
					
						,{id:31205,pId:312,name:"行业客户经理"}
					
						,{id:31206,pId:312,name:"集团客户经理"}
					
						,{id:31302,pId:313,name:"综合维护管理"}
					
						,{id:31303,pId:313,name:"综合维护"}
		 ];


function beforeClick(treeId, treeNode) {
	var zTree = jQuery.fn.zTree.getZTreeObj("treeDemo");
	try{}catch(e){}
	var regbusinessId=pWindow.document.getElementById("regbusinessId").value 
	var array=regbusinessId.split(",");
	for(var i=0;i<array.length;i++){
	   //zTree.checkNode(zTree.getNodeByParam("id", array[i], null), true, true);
		var node=zTree.getNodeByParam("id", array[i], null)
		zTree.checkNode(node, true, true);
		var parentNode= node.getParentNode();
		zTree.expandNode(parentNode,true,true,true);
	}			
	return false;
}

function onCheck(e, treeId, treeNode) {
	var zTree = jQuery.fn.zTree.getZTreeObj("treeDemo"),
	nodes = zTree.getCheckedNodes(true),
	v = "";
	vId="";
	for (var i=0, l=nodes.length; i<l; i++) {
	 var nodeChildrens=nodes[i].children;      
	if(nodeChildrens){  
	   }else{
	    v += nodes[i].name + ",";
		vId+= nodes[i].id + ",";
	   }
		
	}
	if (v.length > 0 ) v = v.substring(0, v.length-1);
	if (vId.length > 0 ) vId = vId.substring(0, vId.length-1);
	console.log(v,'aa')
	// pWindow.document.getElementById("TxSygw").value = v;
	// pWindow.document.getElementById("regbusinessId").value = vId;
	pWindow.postMessage({position:v})
}

jQuery(document).ready(function(){
	jQuery.fn.zTree.init(jQuery("#treeDemo"), setting, zNodes);
	beforeClick();
});
</script>        
</head>
	<body>
	<div id="menuContent" class="menuContent" >
		<ul id="treeDemo" class="ztree" style="margin-top:0; width:400px; height: 300px;"></ul>
	</div>
	</body>
</html>