import React, { useEffect } from 'react'
// import { Helmet } from 'react-helmet'
import { Provider } from 'react-redux'
import { ConfigProvider, Empty, message } from 'antd'
import zhCN from 'antd/lib/locale/zh_CN'
import { generateClass } from 'ROOT/utils/resetTheme'

import store from 'ROOT/store'
import Router from 'ROOT/router'
import NoData from 'ROOT/assets/images/no-data.png'
import { loadScript } from '../utils'

// import 'ROOT/assets/iconfont/iconfont.css'

// 控制同时弹出的消息数，避免在页面调用多个接口异常时，出现多个 toast 提示问题
message.config({
  maxCount: 1,
})

// loadScript('https://cdn.bootcdn.net/ajax/libs/js-cookie/latest/js.cookie.min.js')
// loadScript(`${window._APP_CONFIG.apiHost}/wps-oaassist/wwwroot/wpsjsrpcsdk.js`)
loadScript(`${window._APP_CONFIG.apiHost}/wps-oaassist/wwwroot/jsCook.js`)
// loadScript(`${window._APP_CONFIG.apiHost}/wps-oaassist/wwwroot/wps.js`)
// loadScript(`${window._APP_CONFIG.apiHost}/wps-oaassist/wwwroot/js/loaderbuild.js`)

export default () => {
  useEffect(() => {
    generateClass()
    document.body.style.backgroundColor = '#fff'
  }, [])

  return (
    <ConfigProvider
      locale={zhCN}
      renderEmpty={() => <Empty description="暂无相关数据" image={NoData} />}
    >
      {/* <Helmet>
					<script src="https://cdn.bootcdn.net/ajax/libs/js-cookie/latest/js.cookie.min.js"></script>
					<script src={`${window._APP_CONFIG.apiHost}/wps-oaassist/wwwroot/wpsjsrpcsdk.js`} />
					<script src={`${window._APP_CONFIG.apiHost}/wps-oaassist/wwwroot/wps.js`} />
					<script src={`${window._APP_CONFIG.apiHost}/wps-oaassist/wwwroot/js/loaderbuild.js`} />
				</Helmet> */}
      <Provider store={store}>
        <Router />
      </Provider>
    </ConfigProvider>
  )
}
