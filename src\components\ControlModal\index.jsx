import React, { useEffect, useState } from 'react'
import { Form, Button, Modal, Input, Radio, message } from 'antd'
import Service from 'ROOT/service'
import styles from './index.scss'

const { TextArea } = Input

const ControlModal = ({
  visible,
  setShowControlModal,
  actions,
  deptInfo,
  userTaskId,
  procFormDataKey,
}) => {
  const [form] = Form.useForm()
  const [meetingName, setMeetingName] = useState('')
  const [title, setTitle] = useState('')

  useEffect(async () => {
    const meetingName = await actions.getFieldValue('meetingName')
    const title = await actions.getFieldValue('title')
    setMeetingName(meetingName)
    setTitle(title)
  }, [visible])

  const onCancel = () => {
    setShowControlModal(false)
  }

  const onSubmit = async () => {
    const values = await form.validateFields()
    if (!values.desc) {
      message.error('请先填写说明')
      return
    }
    const res = await Service.saveControl({
      deptId: deptInfo.value,
      userTaskKey: userTaskId,
      instructions: values.desc,
      status: values.isControl,
      formTitle: values.title,
      formId: procFormDataKey,
    })
    onCancel()
    if (res.success) {
      message.success('操作成功')
    } else {
      message.error('操作失败')
    }
  }

  return (
    <Modal
      visible={visible}
      width={500}
      bodyStyle={{ padding: '30px 20px 10px' }}
      footer={null}
      onCancel={onCancel}
      maskClosable={false}
      title="管控设置"
    >
      <div className={styles.container}>
        {(meetingName || title) && (
          <Form
            form={form}
            name="form"
            layout="horizontal"
            labelAlign="top"
            labelCol={
              {
                // style: { width: '100%', height: '30px', textAlign: 'left', marginBottom: '4px' },
              }
            }
          >
            <Form.Item
              label="标题"
              name="title"
              initialValue={meetingName || title}
              rules={[{ required: true, message: '请先输入文章标题' }]}
              style={{
                marginBottom: '10px',
              }}
            >
              <Input
                className={styles['text-area']}
                style={{
                  minHeight: '32px',
                  lineHeight: '20px',
                }}
                disabled
                maxLength={200}
                placeholder="请输入"
              />
            </Form.Item>
            <Form.Item
              label="是否纳入管控"
              name="isControl"
              initialValue={0}
              rules={[{ required: true, message: '请先选择是否纳入管控' }]}
              style={{
                marginBottom: '10px',
              }}
            >
              <Radio.Group>
                <Radio value={0}>不纳入公文数量管控</Radio>
                <Radio value={1}>纳入公文数量管控</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item
              label="说明"
              name="desc"
              initialValue=""
              rules={[{ required: true, message: '请先输入说明（最多200字）' }]}
              style={{
                marginBottom: '10px',
              }}
            >
              <TextArea
                className={styles['text-area']}
                style={{
                  minHeight: '32px',
                  lineHeight: '20px',
                  marginBottom: '10px',
                }}
                maxLength={200}
                placeholder="请输入"
                rows={3}
              />
            </Form.Item>
          </Form>
        )}
      </div>
      <div className={styles.footer}>
        <Button onClick={onCancel}>取消</Button>
        <Button type="primary" className={styles.submit} onClick={onSubmit}>
          提交
        </Button>
      </div>
    </Modal>
  )
}

export default ControlModal
