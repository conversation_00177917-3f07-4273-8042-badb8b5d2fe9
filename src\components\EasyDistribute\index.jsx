import React, { useState, useEffect } from 'react'
import { connect } from 'react-redux'
import { Modal, message, Table } from 'antd'
import ClassNames from 'classnames'
import { official } from 'ROOT/service'
import { debounce } from 'lodash'
import { getField } from 'ROOT/Util'

import styles from './index.scss'

const EasyDistribute = (props) => {
  const { visible, onClose, onSuccess, enterType, id, orgId, easyDistributeLeaderList } = props
  const [dataSource, setDataSource] = useState([])
  const initData = () => {
    setDataSource(easyDistributeLeaderList)
  }
  const handleCancel = () => {
    onClose && onClose()
  }
  const handleOk = () => {
    const hostUsers = [...dataSource].map((i) => {
      return { uid: i.userId, name: i.userName }
    })
    if (enterType === 'rule') {
      // 处理规章制度业务
      return onSuccess && onSuccess(hostUsers)
    }
    const params = {
      orgId,
      id,
      hostUsers,
      coUsers: [],
      knowUsers: [],
      opinion: '',
      isSendSms: '',
    }

    official.sendDocument(params).then(() => {
      message.success('一键分发成功')
      onSuccess && onSuccess()
    })
  }
  const renderUserNum = () => {
    return <div>即将分发给以下人员 ( {dataSource.length} )人 ：</div>
  }
  const getColumns = () => {
    return [
      {
        title: '人员姓名',
        dataIndex: 'userName',
        key: 'name',
      },
      {
        title: '部门',
        dataIndex: 'deptName',
        key: 'deptName',
      },
    ]
  }
  useEffect(() => {
    if (visible) {
      initData()
    }
  }, [visible])
  return (
    <Modal
      title="确认要一键分发吗？"
      visible={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      width="600px"
      okText="确认"
      cancelText="取消"
      bodyStyle={{ padding: 0 }}
    >
      <div className={styles['easy-warp']}>
        <div className={styles.header}>{renderUserNum()}</div>
        <Table dataSource={dataSource} columns={getColumns()} />
      </div>
    </Modal>
  )
}
export default connect((state) => ({
  loginUserData: state.loginUserData,
  orgId: state.orgData.currentOrgId,
  orgName: state.orgData.currentOrgName,
}))(EasyDistribute)
