import React, { useState, useEffect } from 'react'
import { Modal, message, Table } from 'antd'
import service from 'ROOT/service'
import styles from './index.scss'

const EasyDistribute = (props) => {
  const { visible, onClose, onSuccess, enterType, id, orgId, easyDistributeLeaderList } = props
  const [dataSource, setDataSource] = useState([easyDistributeLeaderList])
  const initData = () => {
    setDataSource(easyDistributeLeaderList)
  }
  const handleCancel = () => {
    onClose && onClose()
  }
  const handleOk = () => {
    const hostUsers = [...easyDistributeLeaderList].map((i) => {
      return { uid: i.userId, name: i.userName }
    })
    if (enterType === 'rule') {
      // 处理规章制度业务
      return onSuccess && onSuccess(hostUsers)
    }
    const params = {
      orgId,
      id,
      hostUsers,
      coUsers: [],
      knowUsers: [],
      opinion: '',
      isSendSms: '',
    }

    // service.sendDocument(params).then(() => {
    //   message.success('一键分发成功')
    //   onSuccess && onSuccess()
    // })
    onSuccess && onSuccess(hostUsers)
  }
  const renderUserNum = () => {
    return <div>即将分发给以下人员 ( {easyDistributeLeaderList.length} )人 ：</div>
  }
  const getColumns = () => {
    return [
      {
        title: '人员姓名',
        dataIndex: 'userName',
        key: 'name',
      },
      {
        title: '部门',
        dataIndex: 'deptName',
        key: 'deptName',
      },
    ]
  }
  useEffect(() => {
    if (visible) {
      initData()
    }
  }, [visible])
  return (
    <Modal
      title="确认要一键分发吗？"
      visible={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      width="600px"
      okText="确认"
      cancelText="取消"
      bodyStyle={{ padding: 0 }}
    >
      <div className={styles['easy-warp']}>
        <div className={styles.header}>{renderUserNum()}</div>
        <Table dataSource={easyDistributeLeaderList} columns={getColumns()} />
      </div>
    </Modal>
  )
}
export default EasyDistribute
