import React, { useEffect, useState } from 'react'
import { Table, Button, Modal, Input, Radio, message } from 'antd'
import Service from 'ROOT/service'
import { columns, getDataSource } from '../Formily/EvaluationForm/columns';
import styles from './index.scss'

const { TextArea } = Input

const EvaluationModal = ({
  visible,
  setShowEvaluationModal,
  setIsAgree,
  actions,
}) => {
  const [dataSource, setDataSource] = useState([]);
  useEffect(async () => {
    console.log('visible', actions)
  }, [visible])

  const onCancel = () => {
    setShowEvaluationModal(false)
  }

  const onSubmit = async () => {
    setIsAgree('1')
    setShowEvaluationModal(false)
  }
  
  useEffect(() => {
    setDataSource(getDataSource(true))
  }, [])
  return (
    <Modal
      visible={visible}
      width={1200}
      bodyStyle={{ padding: '30px 20px 10px' }}
      footer={null}
      onCancel={onCancel}
      maskClosable={false}
      title="计划外会议为基层减负评估表"
    >
      <div className={styles.evaluationForm}>
        
        <Table
          columns={columns}
          dataSource={dataSource}
          pagination={false}
          size='small'
          sticky
        />
      </div>
      <div className={styles.footer}>
        <Button onClick={() => { setIsAgree('2'); setShowEvaluationModal(false) }}>不同意</Button>
        <Button type="primary" className={styles.submit} onClick={onSubmit}>
          同意
        </Button>
      </div>
    </Modal>
  )
}

export default EvaluationModal
