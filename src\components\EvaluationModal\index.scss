.container {
  :global(.ant-form-item) {
  }

  :global(.ant-input[disabled]) {
    background-color: #f7f8f9;
    border: none;
  }
  :global(.ant-form-item-label) {
    line-height: 32px;
  }
  :global(.ant-form-item) {
    margin: 0 0 5px;
  }
}

.footer {
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #e9ecf0;
  margin-top: 30px;
  padding: 15px 0 0 16px;

  .submit {
    margin-left: 20px;
  }
}
.evaluationForm {
  :global(.ant-table) {
    border-top: 1px solid #d9d9d9;
    border-right: 1px solid #d9d9d9;
    border-bottom: 1px solid #d9d9d9;
    td, th {
      border-left: 1px solid #d9d9d9 !important;
    }
    td:last-child, th:last-child {
      // border-bottom: 1px solid #d9d9d9;
    }
  }
}
