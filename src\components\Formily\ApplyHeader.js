/* eslint-disable jsx-a11y/alt-text */
/* eslint-disable jsx-a11y/label-has-associated-control */
import React, { useEffect, useState } from 'react'
import { sortableContainer, sortableElement, sortableHandle } from 'react-sortable-hoc'
import { Drawer, Button, Checkbox, Table, Modal, Input, message } from 'antd'
import { MenuOutlined } from '@ant-design/icons'
import { arrayMoveImmutable } from 'array-move'

const deleteImg = require('ROOT/assets/images/delete.png')

const SortableItem = sortableElement(props => <tr {...props} />)
const SortableContainer = sortableContainer(props => <tbody {...props} />);
const DragHandle = sortableHandle(() => <MenuOutlined style={{ cDragHandleursor: 'grab', color: '#999' }} />)
// 默认个人信息的字段
const DEFAULT_PERSONAL_FIELD = [{
  name: '姓名',
  type: 'name',
  key: '1',
  age: '',
  index: 0,
  isFixed: true,
  checked: true,
},
{
  name: '手机号',
  type: 'phone',
  key: '2',
  age: '',
  index: 1,
  isFixed: true,
  checked: true,
},
{
  name: '所在单位',
  type: 'unit',
  key: '3',
  age: '',
  index: 2,
  isFixed: true,
  checked: true,
},
{
  name: '职务',
  type: 'position',
  key: '4',
  age: '',
  index: 3,
  isFixed: true,
  checked: true,
},
{
  name: '民族',
  type: 'nation',
  key: '5',
  age: '',
  index: 4,
  isFixed: true,
  checked: false,
},
{
  name: '是否用车',
  type: 'isPickUp',
  key: '6',
  age: '',
  index: 5,
  isFixed: true,
  checked: false,
},
{
  name: '车牌号',
  type: 'licenseNum',
  key: '7',
  age: '',
  index: 6,
  isFixed: true,
  checked: false,
},
{
  name: '备注',
  type: 'remark',
  key: '8',
  age: '',
  index: 7,
  isFixed: true,
  checked: true,
}]
const fixName = ['姓名', '手机号', '所在单位','职务','民族','是否用车','车牌号','备注']
export default (itemProps) => {
  const { value, mutators, props, editable, schema } = itemProps
  const [visible, setVisible] = useState(false)
  const [dataSource, setDataSource] = useState([])
  const [isAddFieldModalVisible, setAddFieldModalVisible] = useState(false)
  const [addField, setAddField] = useState();
  const [fieldType, setFieldType] = useState();
  useEffect(() => {
    if (value && value.length > 0) {
        const defaultData = []
        value.forEach((item, index) => {
          defaultData.push({ key:index, name: item.name, type: item.type, age:'', index, isFixed: fixName.includes(item.name), checked: item.checked})
        })
        setDataSource([...defaultData])
    } else {
      setDataSource([...DEFAULT_PERSONAL_FIELD])
    }
  }, [value])
   
    const onSortEnd = ({ oldIndex, newIndex }) => {
      if (oldIndex !== newIndex) {
          const newData = arrayMoveImmutable([].concat(dataSource), oldIndex, newIndex).filter(el => !!el)
          setDataSource([...newData])
      }
    }

    const DraggableContainer = props => (
        <SortableContainer
            useDragHandle
            disableAutoscroll
            helperClass="row-dragging"
            onSortEnd={onSortEnd}
            {...props}
        />
    )

    const DraggableBodyRow = ({ className, style, ...restProps }) => {
        const index = dataSource.findIndex(x => x.index === restProps['data-row-key'])
        return <SortableItem index={index} {...restProps} />
    }

    const handleChange = (e) => {
      setAddField(e.target.value)
      setFieldType((new Date().getTime()).toString())
    }

    const deleteField = (text, record) => {
      Modal.confirm({
        title: "确认删除吗",
        content: "删除该字段后，报名人员数据将移除该字段信息，请确认",
        onCancel: () => {
          setAddFieldModalVisible(false)
        },
        onOk: () => {
          const _newData = dataSource
          const index = _newData.findIndex((item) => record.key === item.key)
          _newData.splice(index, 1)
          setDataSource([..._newData])
        },
      })
    }
    const handleClickItemCheckbox = (isChecked, key) => {
      dataSource.map(item => {
        if (item.key === key) {
          item.checked = !isChecked
        }
        return item
      })
      setDataSource([...dataSource])
    }
    const columns = [
        {
            dataIndex: 'name',
            width: '70',
            render: (text, record, index) => {
              const isChecked = record.checked
              return (<div className='sort-list'>
                { record.type !== 'name' && record.type !== 'phone' && record.type !== 'unit' ?
                <Checkbox key={record.key} checked={isChecked} onChange={(e) => {
                e.stopPropagation();
                handleClickItemCheckbox(isChecked, record.key)
              }}>{text}</Checkbox> : text }
              {record.isFixed ? <label className='label-info'>默认</label> : <label className='custom-info'>自定义</label>}</div>)
            }
        },
        {
            dataIndex: 'age',
            render: (text, record, index) => <div>{record.isFixed ? null : <img className="delete-img" src={deleteImg} onClick={() => { deleteField(text, record, index) }} /> }</div>,
        },
        {
          dataIndex: 'sort',
          width: 38,
          className: 'drag-visible',
          render: () => <DragHandle />,
        },
    ]

    const modalOk = () => {
        const key = new Date().toString()
        const row = {
            key,
            name: addField,
            type: fieldType,
            age: '',
            index: key,
            isFixed: false,
            checked: true,
        }
        const newData = dataSource
        newData.splice(dataSource.length, 1, row)
        setDataSource([...newData])
        setAddFieldModalVisible(false)
    }
  const showDrawer = () => {
    setVisible(true)
  }
  const onClose = () => {
    setVisible(false)
  }
  const confirm = () => {
    let mapNum = []
    dataSource.forEach((item) => {
      if (item.checked) {
        mapNum.push(item)
      }
    })
    if (mapNum.length === 0) {
      message.error("至少配置一个字段")
      return false
    }
    mutators.change([...dataSource])
    setVisible(false)
  }
  const addCustom = () => {
    if (dataSource.length > 17) {
      message.error("最多添加10个")
      return false
    }
    setAddFieldModalVisible(true)
  }
  return editable ? (
    <div>
      <Button className='edit-header' onClick={showDrawer}>编辑表头字段</Button>
      <Drawer
          title="表头编辑"
          placement="right"
          closable={false}
          onClose={onClose}
          visible={visible}
          className="drawer-body"
        >
          <Button className='custom-btn' onClick={addCustom}>添加自定义字段</Button>
          <Table
              pagination={false}
              dataSource={dataSource}
              columns={columns}
              showHeader={false}
              locale={{emptyText: '请添加字段'}}
              rowKey="index"
              className="header-list-table"
              components={{
                  body: {
                      wrapper: DraggableContainer,
                      row: DraggableBodyRow,
                  },
              }}
          />
          <Modal
              title="自定义字段"
              visible={isAddFieldModalVisible}
              onOk={modalOk}
              onCancel={() => { setAddFieldModalVisible(false) }}
              destroyOnClose
              className='custom-modal'
          >
              <div className='apply-label'>报名字段</div>
              <Input placeholder='请输入' onChange={handleChange} maxLength={10} />
          </Modal>
          <div
            style={{
              position: 'absolute',
              bottom: 0,
              width: '100%',
              borderTop: '1px solid #e8e8e8',
              padding: '10px 16px',
              textAlign: 'right',
              left: 0,
              background: '#fff',
              borderRadius: '0 0 4px 4px',
            }}
          >
            <Button onClick={confirm} type="primary">
              确定
            </Button>
          </div>
        </Drawer>
    </div>
  ) : null
}