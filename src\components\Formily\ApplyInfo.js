/* eslint-disable jsx-a11y/iframe-has-title */
/* eslint-disable no-nested-ternary */
import React, { useState, useEffect} from 'react'
import { Modal, Button, Table, Input, Form, message, Select } from 'antd'
import Cookies from 'js-cookie'
import axios from 'axios'
import Service from 'ROOT/service'

const { Option } = Select
const FormItem = Form.Item
export default (itemProps) => {
  const { value=[], mutators, props, editable, schema} = itemProps
  const { applyList, user, debug } = schema['x-component-props'] || []
  const [columns, setColumns] = useState([])
  const [modalColumns, setModalColumns] = useState([])
  const [visible, setVisible] = useState(false)
  const [dataSource, setDataSource] = useState(value)
  const [applyForm] = Form.useForm()
  const [key, setKey] = useState(0)
  const [configure, setConfigure] = useState({})
  const { meetingName, reportId } = schema['x-component-props'] || []
  const { loginName, loginMobile, loginUid, loginOrgId } = Cookies.getJSON('myself') || {}
  const [ userName, setUserName ] = useState(loginName)
  const [ userMobile, setUserMobile ] = useState(loginMobile)
  const [ allDeptList, setAllDeptList] = useState([])
  const [ defaultUser, setDefaultUser] = useState({})
  const [memberVisible, setMemberVisible] = useState(false)
  const [result, setResult] = useState([])
  const {
    orgId = Cookies.get('orgId'),
    orgName,
    id = Cookies.get('userId'),
    name = Cookies.get('username'),
  } = JSON.parse(
    Cookies.get('uiapp') ||
    Cookies.get('WEBG_STORAGE') ||
    Cookies.get('EntAdminG_STORE') ||
    localStorage.WEBG_STORAGE ||
    localStorage.EntAdminG_STORE ||
    '{}',
  )

  useEffect(async () => {
    setUserMobile(loginMobile)
    setUserName(loginName)
    const res = await Service.getOrgLinkPathForDebug({
      uid: loginUid,
      orgId: loginOrgId,
    })
    const deptInfo = res.data.linkPath && res.data.linkPath.length > 0 && res.data.linkPath.map(item => {
      // const linkPath1 = item.linkPath.replaceAll("\\\\", "\\/")
      const linkPath1 = (item.linkPath && item.linkPath.replace(/\\/g, "/")) || ''
      return {
        id: item.deptId,
        deptPath: linkPath1,
      }
    })
    setAllDeptList(deptInfo)
    setDefaultUser({
      id: loginUid,
      name: loginName,
      mobile: loginMobile,
      orgId: loginOrgId,
      deptId: res.data.linkPath && res.data.linkPath.length > 0 ? res.data.linkPath[0].deptId : '',
    })
  }, [])
  useEffect(() => {
    const defaultList = [{"title":"姓名","dataIndex":"name","key":"name"},{"title":"手机号","dataIndex":"phone","key":"phone"},{"title":"所在单位","dataIndex":"unit","key":"unit"},{"title":"职务","dataIndex":"position","key":"position"},{"title":"备注","dataIndex":"remark","key":"remark"},]
    const arrayInfo = applyList && applyList.length > 0 ? applyList.map(item => {
      return {
        title: Object.values(item)[0],
        dataIndex: Object.keys(item)[0],
        key: Object.keys(item)[0],
      }
    }): defaultList
    const array = arrayInfo && arrayInfo.length > 0 ? [...arrayInfo] : []
    setModalColumns(arrayInfo)
    array.unshift({
      title: '序号',
      key: 'index',
      width: 70,
      render: (text, record, index) => <div>{index + 1}</div>
    })
    if (debug) {
      array.push({
        width: 230,
        title: '操作',
        key: 'operation',
        render: (record, oldRow, index) => {
          return (
            <div>
              <a className='apply-edit' onClick={() => {
                applyForm.setFieldsValue(record)
                setVisible(true)
                setKey(index + 1)
              }}>
                编辑
              </a>
            </div>
          )
        }
      })
    }
    setColumns(array)
  }, [applyList, editable])
  useEffect(() => {
    if (value && value.length > 0) {
      setDataSource(value)
    }
  }, [value])
  const handleSubmit = e => {
    e.preventDefault()
    let dataVal = [...dataSource]
    // eslint-disable-next-line no-unused-expressions
    applyForm && applyForm.validateFields().then(values => {
      values.name = userName
      values.phone = userMobile
      values.defaultUser = defaultUser
      if (!values.unit) {
        values.unit = allDeptList[0].deptPath
      }
      values.key = dataVal.length + 1
      if (key !== 0) {
        dataVal = dataVal.map((item, index) => {
          item.key = index
          if (index + 1 === key) {
            item = values
          }
          return item
        })
      } else {
        dataVal.push(values)
      }
      const isAdd = dataSource && dataSource.length > 0 && dataSource.filter(item => item.name === userName)
      if (dataSource && dataSource.length > 0 && isAdd.length > 0) {
        message.error('您已报名，请勿重复报名')
        return false
      }
      setDataSource(dataVal)
      if (dataVal && dataVal.length > 10){
        setConfigure({
          scroll: {
            x: 1000,
            y: 400,
          },
        })
      }
      mutators.change(dataVal)
      setVisible(false)
    })
  }
  const close = () => {
    setVisible(false)
  }
  const addApply = () => {
    setVisible(true)
    // eslint-disable-next-line no-unused-expressions
    applyForm && applyForm.resetFields()
    setKey(0)
  }
  const downData = () => {
    if (!reportId) {
      return false
    }
    axios.get(`/security-manage-platform/web/meeting/entry/export?reportId=${  reportId}`, // 参数
      {responseType: 'blob'},
    ).then((res) => {
      const reader = new FileReader()
      reader.onload = () => {
        const data = reader.result
        if ( data.indexOf("success") > -1) {
          const resp = data && JSON.parse(data)
          if (!resp.success) {
            message.error(resp.msg)
          } 
        } else if ('download' in document.createElement('a')) {
            const url = window.URL.createObjectURL(res.data) // 创建 url 并指向 blob
            const a = document.createElement('a')
            a.href = url
            a.download = `${meetingName}.xlsx`
            a.click()
            window.URL.revokeObjectURL(url) // 释放该 url
          } else { // IE10+下载
            navigator.msSaveBlob(res.data, `${meetingName}.xlsx`)
          }
      }
      reader.readAsText(res.data)
      
    })
  }
  useEffect(() => {
    if (value && value.length > 10){
      setConfigure({
        scroll: {
          x: 1000,
          y: 400,
        }
      })
    }
  }, [value])
  const defaultChooseSelf = false
  window.addEventListener('message', async (e) => {
    if(!memberVisible) return false //不是当前组件不执行
    const { data } = e
    const { type } = data

    // 点击选人组件中的【取消】按钮
    if (type === 'BAAS_TREE_CANCEL') {
      setMemberVisible(false)
    }

    // 点击选人组件中的【确定】按钮
    if (type === 'BAAS_TREE_CONFIRM') {
      // eslint-disable-next-line no-unused-expressions
      applyForm && applyForm.resetFields()
      const usersInfo = data.data.users
      if (!usersInfo) return false
      const {name, mobile, orgId, id} = usersInfo[0]
      setResult(usersInfo)
      setUserName(name)
      setUserMobile(mobile)
      const res = await Service.getOrgLinkPathForDebug({
        uid: id,
        orgId,
      })
      
      const deptInfo = res.data.linkPath && res.data.linkPath.length > 0 && res.data.linkPath.map(item => {
        // const linkPath1 = item.linkPath.replaceAll("\\\\", "\\/")
        const linkPath1 = (item.linkPath && item.linkPath.replace(/\\/g, "/")) || ''
        return {
          id: item.deptId,
          deptPath: linkPath1,
        }
      })
      setAllDeptList(deptInfo)
      setDefaultUser({
        id,
        name,
        mobile,
        orgId,
        deptId: res.data.linkPath[0].deptId,
      })
      setMemberVisible(false)
    }
  })

  const getDefaultList = () => {
    let defaultList = []
    if (result && result.length > 0) {
      defaultList = result
    } else if (defaultChooseSelf) {
      defaultList = [{
        id,
        name
      }]
    } else {
      defaultList = [{
        id: loginUid,
        name: loginName, 
      }]
    }
    return defaultList
  }
  const params = {
    visible: true,
    needSearchBar: true,
    orgId,
    orgName,
    defaultUserList: getDefaultList(),
    range: 1,
    type: 'multiUser',
    // chooseFromDeptId: '',
    // needLookUp: true,
    // isCustom: true,
    // isChooseFromOtherOrgs: true, // 区公司支持从所有单位选
    // isAssingRootOrg: true,
    // chooseFromDeptId: limitDept && limitDept.length > 0 ? limitDept[0].id : ''
  }
  const notice = () => {
    // setVisible(false)
    setMemberVisible(true)
  }
  return (
    <div style={{ width: '100%', display: 'block'}}>
      <div className='flex-left'>
        { reportId && user === loginName && !editable ?
          <Button className='down-btn' onClick={downData}>下载数据</Button>
        : null}
        {
          editable ? <Button type='primary'  onClick={addApply}>报名</Button> : null
        }
      </div>
      <div className='array-table'>
         <Table
            rowKey='phone'
            bordered
            columns={columns}
            dataSource={dataSource}
            pagination={false}
            {...configure}
            // scroll={{
            //   x: 1000,
            //   y: 400,
            // }}
          />
       </div>
       <Modal
        title="报名"
        visible={visible}
        width={600}
        onOk={handleSubmit}
        onCancel={close}
        className='modal-columns'
        >
        <Form form={applyForm}>
          {modalColumns && modalColumns.length > 0 && modalColumns.map(item => {
            return (
              item.key === 'name' ?
                (
                //!debug ? 
                // <FormItem key={item.key} label={item.title}>
                //   {userName}
                // </FormItem> : 
                <FormItem key={item.key} label={item.title}>
                  <Input placeholder="请选择" type="text" value={userName} onClick={() => notice()} />
                </FormItem>) :
                (item.key === 'phone' ?
                <FormItem key={item.key} label={item.title}>
                  {userMobile}
                </FormItem> : (
                item.key === 'unit' ?
                <FormItem key={item.key} name={item.key} label={item.title} rules={[{ required: true }]}>
                  <Select>
                    {allDeptList.map(item => {
                      return (
                        <Option value={item.deptPath} key={item.id}>{item.deptPath}</Option>
                      )
                    })}
                  </Select>
                </FormItem>
              :
                <FormItem key={item.key} name={item.key} label={item.title} rules={[{ required: true, message: '请输入' }]}>
                  <Input placeholder="请输入" maxLength={200} />
                </FormItem>))
            )
          })}
        </Form> 
       </Modal>
       {memberVisible
        ? <Modal
            visible={memberVisible}
            width={600}
            footer={null}
            closable={false}
            bodyStyle={{
              padding: 0,
              margin: 0,
              overflow: 'hidden',
              height: 590
            }}
          >
            <iframe
              src={`${ab.api}/user-selector?query=${encodeURIComponent(
                JSON.stringify(params),
              )}`}
              frameBorder="0"
              style={{ border: 0 }}
              width="100%"
              height="100%"
            />
      </Modal>
      : null
        }
    </div>
  )
}