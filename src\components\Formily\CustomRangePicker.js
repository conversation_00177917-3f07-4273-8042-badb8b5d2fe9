import React from 'react'

import { DatePicker } from 'antd'

const CustomRangePicker = props => {
  const { editable } = props

  if (!editable) {
    const { value } = props
    return (
      <p className="preview-text" style={{ margin: 0, padding: 0 }}>
        {value ? value.join(' — ') : '-'}
      </p>
    )
  }

  // 可编辑模式下使用原生RangePicker
  return <DatePicker.RangePicker {...props.props} />
}

export default CustomRangePicker
