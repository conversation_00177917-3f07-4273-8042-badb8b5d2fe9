import React from 'react'
import { Select } from 'antd'
import { PreviewText } from '@formily/react-shared-components'

export default (itemProps) => {
  const { value, mutators, props, editable, schema } = itemProps
  const xComponentProps = schema['x-component-props'] || {}
  const { isWidth, disabled } = xComponentProps
  const onChange = (value) => {
    props.enum.map(item => {
      if (item.value == value.value) {
        value = { ...item }
      }
    })
    mutators.change(value)
  }

  return editable
    ? <Select disabled={disabled} labelInValue value={value} options={props.enum} onChange={onChange} style={{ width: isWidth ? '300px' : '' }} />
    : <PreviewText value={value && value.label} />
}
