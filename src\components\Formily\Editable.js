import React from 'react'
import { Input } from 'antd'

export default data => {
  const { name, value, editable, disabled = true, form } = data
  const { setFieldValue } = form

  return (
    <div style={{ width: '100%' }}>
      {editable ? (
        <Input 
          value={name === '_dept' || name === 'draftDepartment' ? value && value.label : value}
          onChange={e => setFieldValue(name, e.target.value)}
          disabled={disabled}
        />
      ) : (
        <div>{(value && value.label) || value || '-'}</div>
      )}
    </div>
  )
}
