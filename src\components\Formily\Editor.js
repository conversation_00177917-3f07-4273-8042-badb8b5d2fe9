import React, { useEffect, useState } from 'react'
import Editor from 'wangeditor'

let first = true

export default (itemProps) => {
  const { value, mutators, props, editable, schema, form } = itemProps
  const isUpdate = props['x-isUpdate']
  const [content, setContent] = useState(value)
  let editor = null
  useEffect(() => {
    if (!editable) return
    editor = new Editor('#editor')
    editor.config.zIndex = 100
    editor.config.excludeMenus = [
      'emoticon',
      'video',
      'image',
    ]
    editor.config.onchange = function (newHtml) {
      mutators.change(newHtml)
    }
    editor.create()
    return () => {
      // 组件销毁时销毁编辑器
      editor.destroy()
    }
  }, [])

  useEffect(() => {
    if (first && value && isUpdate) {
      setContent(value)
      first = false
    }
  }, [value])

  return editable
    ? <div id="editor" style={{ width: '100%' }}>
      <div dangerouslySetInnerHTML={{ __html: content }} />
    </div>
    : <div>
      {/* <p style={{ fontSize: '18px', fontWeight: 'bold', textAlign: 'center' }}>{form.getFieldValue('title')}</p> */}
      <div dangerouslySetInnerHTML={{ __html: value }} />
    </div>
}
