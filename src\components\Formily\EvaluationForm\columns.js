import React from 'react'
import EvaluationResult from './EvaluationResult';
const shareOnCell = (_, index) => {
    if (index % 3 === 0) {
        return {
            rowSpan: 3,
        };
    }
    return {
        rowSpan: 0,
    };
};
export const columns = [
    {
        title: '评估项目',
        dataIndex: 'evaluationProject',
        onCell: shareOnCell,
        width: '20%',
    },
    {
        title: '评估内容',
        dataIndex: 'evaluationCenter',
        width: '60%',
    },
    {
        title: '评估结论(是/否)',
        dataIndex: 'evaluationResult',
        width:'20%',
    }
]

export const getDataSource = (isView=false) => {
    return [{
        evaluationProject: '会议必要性评估',
        evaluationCenter: '1. 是否有紧急且重要的事项需召开会议。',
        evaluationResult: (isView?'是':<EvaluationResult name='resultKey1' />)
    },
    {
        evaluationCenter: '2. 是否可通过其他方式替代会议解决问题。',
        evaluationResult: (isView?'否':<EvaluationResult name='resultKey2' />)
    },
    {
        evaluationCenter: '3. 会议讨论的内容是否符合当前工作重点和战略方向，有助于推动工作目标的实现。',
        evaluationResult: (isView?'是':<EvaluationResult name='resultKey3' />)
    },
    {
        evaluationProject: '会议精简程度评估',
        evaluationCenter: '4. 会议时长安排是否合理。',
        evaluationResult: (isView?'是':<EvaluationResult name='resultKey4' />)
    },
    {
        evaluationCenter: '5. 参会人员范围是否精准，是否只有与会议内容密切相关、对决策有直接作用的人员参加，避免无关人员陪会。',
        evaluationResult: (isView?'是':<EvaluationResult name='resultKey5' />)
    },
    {
        evaluationCenter: '6. 会议议程是否紧凑高效，会议议程是否删除重复、无关紧要的议题，确保会议流程紧凑，不出现拖沓、跑题的情况。',
        evaluationResult: (isView?'是':<EvaluationResult name='resultKey6' />)
    },
    {
        evaluationProject: '对基层工作影响评估',
        evaluationCenter: '7. 是否会增加基层人员额外工作负担，超出基层承受范围。',
        evaluationResult: (isView?'否':<EvaluationResult name='resultKey7' />)
    },
    {
        evaluationCenter: '8. 会议召开时间、地点等是否与基层日常工作产生冲突，干扰基层正常工作秩序，影响工作连续性和效率。',
        evaluationResult: (isView?'否':<EvaluationResult name='resultKey8' />)
    },
    {
        evaluationCenter: '9. 会议内容是否围绕解决基层实际问题、提供政策指导、资源支持等方面展开，为基层提供实际支持和帮助。',
        evaluationResult: (isView?'是':<EvaluationResult name='resultKey9' />)
    },];
}
