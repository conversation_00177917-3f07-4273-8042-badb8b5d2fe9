import React, { useEffect, useState, forwardRef } from 'react';
import { Table, Form } from 'antd';
import { columns, getDataSource } from './columns';
import './index.css';

const EvaluationForm = forwardRef((itemProps, ref) => {
    const { mutators } = itemProps
    const [form] = Form.useForm();
    const [dataSource, setDataSource] = useState([]);
    // 监听表单值变化
    const handleValuesChange = (changedValues, allValues) => {
        const values = Object.values(allValues);
        const allFilled = values.every(value => value !== undefined && value !== null && value !== '');
        if (allFilled) {
            mutators.change(allValues);
        }else{
            mutators.change(null);
        }
    };
    useEffect(() => {
        setDataSource(getDataSource())
    },[])
    return (
        <div style={{ width: '100%' }}>
            <Form
                form={form}
                onValuesChange={handleValuesChange}
                name="evaluationForm"
            >
                <Table
                    columns={columns}
                    dataSource={dataSource}
                    bordered
                    pagination={false}
                    sticky
                />
            </Form>
        </div>
    );
});

export default EvaluationForm;