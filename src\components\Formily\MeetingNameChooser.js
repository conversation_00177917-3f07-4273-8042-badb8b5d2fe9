import React, { useState } from 'react'
import { Select } from 'antd'
import { PreviewText } from '@formily/react-shared-components'
import service from 'ROOT/service'

const { Option } = Select

let timeout
let currentValue

function MeetingNameChooser(itemProps) {
  const [data, setData] = useState([])
  const { value, mutators, props, editable } = itemProps
  const { placeholder, style, uid } = props['x-component-props']
  const fetch = (value, callback) => {
    if (timeout) {
      clearTimeout(timeout)
      timeout = null
    }
    currentValue = value
    const fake = () => {
      service.getListUserChangeMeeting({
        type: 17,
        uid,
        meetingName: value,
      })
      .then(res => {
        if (currentValue === value) {
          const data = res.data.map((item) => ({
            id: item.formId,
            text: item.meetingName
          }))
          callback(data)
        }
      })
    }
    timeout = setTimeout(fake, 300)
  }
  if (!editable) {
    return <PreviewText value={typeof value === 'string' ? value : value && value.label} />
  }
  const handleSearch = (newValue) => {
    if (newValue) {
      fetch(newValue, setData)
    } else {
      setData([])
    }
  }

  const onChange = (key) => {
    data.forEach(item => {
      if (item.id === key) {
        key = { ...item }
      }
    })
    mutators.change(key)
  }

  return (
    <Select
      showSearch
      value={value}
      placeholder={placeholder}
      style={style}
      defaultActiveFirstOption={false}
      showArrow={false}
      filterOption={false}
      onSearch={handleSearch}
      onChange={onChange}
      getPopupContainer={triggerNode => triggerNode.parentNode}
    >
      {
        data.map(item => (
          <Option key={item.id}>{item.text}</Option>
        ))
      }
    </Select>
  ) 
}

export default MeetingNameChooser