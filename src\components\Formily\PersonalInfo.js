import React, { useEffect, useRef } from 'react'
import { Descriptions, Input, message, Select } from 'antd'
import { PreviewText } from '@formily/react-shared-components'
import service from 'ROOT/service'

export default itemProps => {
  const { value = {}, mutators, props, editable, schema } = itemProps
  const xComponentProps = schema['x-component-props'] || {}
  const { field } = xComponentProps
  const deptList = useRef([])
  // const {
  //   uid = Cookies.get('userId'),
  // } = JSON.parse(
  //   Cookies.get('uiapp') ||
  //     Cookies.get('WEBG_STORAGE') ||
  //     Cookies.get('EntAdminG_STORE') ||
  //     localStorage.WEBG_STORAGE ||
  //     localStorage.EntAdminG_STORE ||
  //     '{}',
  // )

  useEffect(() => {
    if (editable && Object.keys(value).length === 0) {
      loadData()
    }
  }, [])

  const loadData = () => {
    if (!window._getRegionRequest_personalinfo) {
      window._getRegionRequest_personalinfo = service.getMyself()
    }
    return window._getRegionRequest_personalinfo
      .then(handleRequest)
      .catch(err => { // eslint-disable-line
        // message.error(err.msg || '网络错误')
      })
  }

  const handleRequest = res => {
    if (+res.code === 200) {
      mutators.change({
        ...value,
        ...combineData(res),
      })
    } else {
      message.error(res.msg || '网络错误')
    }
  }

  const combineData = (res) => {
    let data = {}
    field.forEach(item => {
      let content = {
        value: '',
        label: '',
      }
      if (item.key === 'dept') {
        if (res.data.deptList.length > 1) {
          deptList.current = res.data.deptList
        } else {
          content = {
            value: res.data.deptList.length === 1 ? res.data.deptList[0].id : '',
            label: res.data.deptList.length === 1 ? res.data.deptList[0].name : '',
          }
        }
      }
      data = {
        ...data,
        [item.key]: item.key === 'dept' ? content : res.data[item.key],
      }
    })
    return data
  }

  const handleSelectChange = (key, val) => {
    mutators.change({
      ...value,
      [key]: val,
    })
  }

  const renderTemplate = (key) => {
    return (key === 'dept' && editable && deptList.current.length > 1)
      ? (
        <Select style={{ width: 180 }} labelInValue value={value[key]} onChange={(value) => handleSelectChange(key, value)}>
          {deptList.current.map(item => {
            return (
              <Select.Option key={item.id} value={item.id}>{item.name}</Select.Option>
            )
          })}
        </Select>
      )
      : (
        <PreviewText value={key === 'dept' ? (value[key] && value[key].label) : value[key]} />
      )
  }

  return (
    <Descriptions>
      {field.map((item, index) => {
        return (
          <Descriptions.Item labelStyle={{ display: 'inline-block', width: index === 2 ? 143 : 130, textAlign: 'right' }} key={item.key} label={item.label}>{renderTemplate(item.key)}</Descriptions.Item>
        )
      })}
    </Descriptions>
  )
}
