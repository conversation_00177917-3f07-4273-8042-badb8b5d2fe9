import React, { Component } from 'react'
import { Modal, Input, Tree, message, Breadcrumb } from 'antd'
import classnames from 'classnames'
import { CloseOutlined } from '@ant-design/icons'
import { getRootOrg, getAllList, getSeachResult } from '../Select/SelectOrg/service'
import styles from './index.scss'
import { replaceHtmlString } from 'ROOT/utils'
import fontStyles from './font.scss'
import { get, debounce } from 'lodash'

const { TreeNode } = Tree

export default class SelectOrg extends Component {
  constructor(props) {
    super(props)
    this.state = {
      deptsList: [],
      allList: [],
      result: [],
      searchMode: false,
      treeList: [],
      treeData: [],
      searchResultList: [],
      breadCrumbList: [],
      isSearching: false,
    }
  }

  componentDidMount() {
    this.requestData()
    this.initState()
  }

  initState = () => {
    const { orgList } = this.props
    if (orgList && orgList.length > 0) {
      this.setState({
        result: orgList.map(val => {
          if (val.orgType === 1) {
            return {
              ...val,
              id: `${val.id}-${val.code}-${val.lower}`,
              key: `${val.id}-${val.code}-${val.lower}`,
            }
          }
          return val
        }),
      })
    }
  }

  // 因为id会重复，所以加工下原数据
  processData = data => {
    const result = data.map(val => ({
      ...val,
      id: `${val.id}-${val.code}-${val.lower}`,
    }))
    return result
  }

  // 还原原始数据
  recoverData = data => {
    function recoverId(id) {
      if (typeof id === 'string' && id.indexOf('-') > -1) {
        const arr = id.split('-')
        return arr[0]
      }
      return id
    }
    const result = data.map(val => ({
      ...val,
      id: recoverId(val.id),
      key: recoverId(val.key),
      orgId: recoverId(val.orgId),
    }))
    return result
  }

  requestData = async () => {
    const { API_GET_ROOT_ORG, API_GET_LIST_ALL, type, parentOrgId, workLineType } = this.props

    // '33000000, 87000000'
    // , orgCodes: '33000000'
    const res = await getRootOrg(API_GET_ROOT_ORG, { type, parentOrgId, workLineType })
    const deptsList = this.processData(res.data)
    this.setState({ deptsList })
    // const allRes = await getAllList(API_GET_LIST_ALL, { parentOrgId: this.state.deptsList[0].id, flag: 2, parentDeptId: 0, unlower: 1 });
    // this.setState({ allList: allRes.data.deptList });
    // this.reorganizeTree(this.state.deptsList[0].id);
    const TreeData = deptsList.map(item => ({
      ...item,
      title: item.name,
      key: item.id,
      isLeaf: !item.lower,
      icon: (
        <span
          className={`${fontStyles.iconfontextra} ${fontStyles.icongongsi1} ${styles.icongongsi1}`}
        />
      ),
      children: [],
      disabled: !item.enable,
      // disabled: [1, 2].includes(item.orgCategory), // 单位类型：1：区公司，2：市公司，3：县公司，4：其它，逻辑：【区公司】，【市公司】不能选择
    }))
    this.setState({ treeList: TreeData, treeData: TreeData })
  }

  reorganizeTree = key => {
    const { treeList, allList } = this.state
    const TreeData = treeList.map(item => ({
      ...item,
      title: item.name,
      key: item.id,
      isLeaf: !item.lower,
      children:
        item.id === key
          ? allList.map(items => ({
              ...items,
              parentName: item.name,
              title: items.name,
              key: items.id,
              isLeaf: true,
              icon: (
                <span
                  className={`${fontStyles.iconfontextra} ${fontStyles.icongongsi1} ${styles.icongongsi1}`}
                />
              ),
            }))
          : item.children,
    }))
    this.setState({
      treeList: TreeData,
    })
  }

  renderSearchResult = () => {
    const { searchResultList, isSearching } = this.state
    if (isSearching) {
      return <div className={styles['no-select-data']}>搜索中...</div>
    }
    if (!searchResultList) {
      return <div className={styles['no-select-data']}>暂无数据</div>
    }
    if (searchResultList && searchResultList.length === 0) {
      return <div className={styles['no-select-data']}>暂无数据</div>
    }
    return (
      <div className={styles['search-list']}>
        {searchResultList.map((item, index) => (
          <div className={styles['search-item']} style={{ cursor: 'pointer' }} key={index}>
            <span
              className={`${fontStyles.iconfontextra} ${fontStyles.icongongsi1} ${styles.icongongsi1}`}
            />
            <div
              className={styles['search-text']}
              style={{ marginLeft: 5 }}
              onClick={e => this.selectTree(item.showPath, item)}
              dangerouslySetInnerHTML={{ __html: item.showPath }}
            />
          </div>
        ))}
      </div>
    )
  }

  selectTree = (showPath, item) => {
    const { isRit } = this.props
    const { result, searchMode } = this.state
    const { enable = true } = item
    console.log(item)
    const isHas =
      result &&
      result.some(items => {
        if (isRit) {
          return items.dataRef.id === item.seqId
        }
        if (searchMode) {
          return items.seqId === item.seqId
        }
        return items.id === item.seqId
      })
    if (!isHas && enable) {
      result.push({
        ...item,
        orgId: item.oid,
        deptId: item.seqId,
        dataRef: { id: item.seqId },
        showPath,
      })
      this.setState({
        result,
      })
    }
  }

  renderSearched = item => {
    const { deptsList } = this.state
    const res = deptsList.find(items => items.id == item.oid)
    return res && res.name ? `${res.name}\\${item.name}` : item.name
  }

  renderTreeList = () => {
    const { isRit } = this.props
    const { treeList, treeData } = this.state
    if (isRit) {
      return (
        <Tree
          showIcon
          loadData={this.getData}
          onSelect={this.ritTreeSelect}
          onExpand={this.breadCrumb}
        >
          {treeData.length > 0 &&
            treeData.map(item => {
              return (
                <TreeNode
                  key={item.key}
                  icon={item.icon}
                  title={item.title}
                  isLeaf={!item.lower}
                  dataRef={item}
                />
              )
            })}
        </Tree>
      )
    }
    return (
      <Tree
        autoExpandParent
        showIcon
        defaultExpandedKeys={['0']}
        treeData={treeList}
        onSelect={this.treeSelect}
        loadData={this.onLoadData}
      />
    )
    // return <div>
    //   {
    //     deptsList && deptsList.length > 0 && deptsList.map((item) => (
    //       <div key={item.code}>{item.name}</div>
    //     ))
    //   }s
    // </div>
  }

  breadCrumb = (expandedKeys, info) => {
    const { breadCrumbList } = this.state
    breadCrumbList.push(info.node)
    this.setState({
      breadCrumbList,
    })
  }

  ritTreeSelect = (selectedKeys, info) => {
    const { result } = this.state
    console.log(info.node)
    const isHas = result.some(item => item.dataRef.key == info.node.key)
    if (!isHas) {
      result.push({ ...info.node, showPath: info.node.title })
      this.setState({
        result,
      })
    }
  }

  // 支持下钻的数据
  getData = async ({ key = '' }) => {
    const { API_GET_LIST_ALL } = this.props
    const arr = key.split('-')
    const { data } = await getAllList(API_GET_LIST_ALL, {
      parentOrgId: arr[0],
      flag: 3,
      parentDeptId: 0,
    })
    const { deptList, orgList } = data
    const _deptList = deptList.map(item => ({
      ...item,
      title: item.name,
      icon: (
        <span
          className={`${fontStyles.iconfontextra} ${fontStyles.iconwenjianjia_fill} ${styles.iconwenjianjia_fill}`}
        />
      ),
      key: item.id,
      isLeaf: true,
      children: [],
    }))
    const _orgList = orgList.map(item => ({
      ...item,
      title: item.name,
      key: item.id,
      icon: (
        <span
          className={`${fontStyles.iconfontextra} ${fontStyles.icongongsi1} ${styles.icongongsi1}`}
        />
      ),
      isLeaf: !item.lower,
      children: [],
    }))
    this.setState({
      treeData: [..._deptList, ..._orgList],
    })
  }

  onLoadData = async parentInfo => {
    const { API_GET_LIST_ALL } = this.props
    const { key = '', children, code, id } = parentInfo
    const arr = key.split('-')
    const orgId = get(arr, '0', id)
    const allRes = await getAllList(API_GET_LIST_ALL, {
      parentOrgId: orgId,
      flag: 2,
      parentDeptId: 0,
      unlower: 1,
    })
    const newList = [...allRes.data.deptList]
    if (parentInfo.lower) {
      newList.unshift({
        id: `${code}_allDept`,
        code: `${code}_allDept`,
        orgId,
        name: '公司各部门',
      })
    }
    this.setState({ allList: newList })
    this.reorganizeTree(key)
  }

  treeSelect = (selectedKeys, info) => {
    const { workLineType } = this.props
    const { result } = this.state
    const isHas = result.some(item => item.id == info.node.id)
    const { enable = true } = info.node
    console.log(info)
    const flag = workLineType === '2' && info.node.code === '33000000' // 市公司上行工作协调函的区公司不能选中
    if (!isHas && !flag && enable) {
      result.push({ ...info.node, showPath: this.renderTreePath(info.node) })
      this.setState({
        result,
      })
    }
  }
  // {
  //   name: selectedNodes.name,
  //   id: selectedNodes.id,
  //   code: selectedNodes.code
  // }

  deleteSelected = id => {
    const { isRit } = this.props
    const { result } = this.state
    const newResult = result.filter(
      item => (isRit ? item.dataRef.id : item.id || item.seqId) !== id,
    )
    this.setState({
      result: newResult,
    })
  }

  onClear = () => {
    this.setState({
      result: [],
    })
  }

  handleCancel = () => {
    const { onCancel } = this.props
    onCancel && onCancel()
  }

  handleConfirm = () => {
    const { result } = this.state
    const { onSubmit, isRit } = this.props
    let selectedDept = null
    if (isRit) {
      selectedDept = result.map(item => ({
        ...item,
        deptId: item.orgType === 1 ? null : item.id || item.deptId,
        name: replaceHtmlString(item.title || item.name),
      }))
    } else {
      selectedDept = result.map(item => ({
        ...item,
        orgId: item.orgType === 1 ? item.id || item.orgId : item.orgId,
        deptId: item.orgType === 1 ? null : item.id || item.deptId,
        orgType: item.orgType || 2,
        showPath: replaceHtmlString(item.showPath),
        name: replaceHtmlString(item.name),
      }))
    }

    selectedDept = this.recoverData(selectedDept)
    onSubmit && onSubmit(selectedDept)
    this.handleCancel()
  }

  handleSearchChange = ({ target: { value } }) => {
    value = value.trim()
    this.handleSearch(value)
  }

  handleSearch = async value => {
    const { API_SEARCH_LIST, isRit, type, parentOrgId } = this.props
    this.setState({
      breadCrumbList: [],
      searchMode: true,
      isSearching: true,
    })
    let orgId = ''
    if (!isRit) {
      orgId = type === '2' ? parentOrgId : -1
    }
    const result = await getSeachResult(API_SEARCH_LIST, { keyword: value, option: 2, orgId })
    const { departmentList } = result.data
    console.log(departmentList)
    const newSearchList =
      departmentList &&
      departmentList.length > 0 &&
      departmentList.map(item => ({
        ...item,
        deptId: item.seqId,
        showPath: isRit ? item.name : this.renderSearched(item),
      }))
    this.setState({
      searchMode: !!result.data.departmentList,
      isSearching: false,
      searchResultList: newSearchList,
    })
  }

  renderTreePath = item => {
    const { deptsList } = this.state
    return item.parentName ? `${item.parentName}\\${item.name}` : item.name
  }

  renderBreadCrumb = () => {
    const { breadCrumbList } = this.state
    return (
      <Breadcrumb className={breadCrumbList.length > 0 && styles.breadcrumb}>
        {breadCrumbList &&
          breadCrumbList.length > 0 &&
          breadCrumbList.map(item => (
            <Breadcrumb.Item onClick={() => this.breadCrumbChange(item)} key={item.key}>
              {item.title}
            </Breadcrumb.Item>
          ))}
      </Breadcrumb>
    )
  }

  breadCrumbChange = bread => {
    const { breadCrumbList } = this.state
    const index = breadCrumbList.findIndex(item => item.key === bread.key)
    this.setState({
      breadCrumbList: breadCrumbList.slice(0, index + 1),
    })
    this.getData({ key: bread.key })
  }

  render() {
    const { searchMode, result, treeList } = this.state
    const { visible, isRit } = this.props
    console.log(result)
    return (
      <Modal width={800} visible={visible} onCancel={this.handleCancel} onOk={this.handleConfirm}>
        <div className={styles['extra-forms-select-org']}>
          <div className={styles.title}>选择部门</div>
          <div className={styles.container}>
            <div className={styles['select-org']}>
              {this.renderBreadCrumb()}
              <div>
                <Input
                  placeholder="搜索部门"
                  prefix={
                    <span
                      className={`${fontStyles.iconfontextra} ${fontStyles.iconsousuo} ${styles.iconsousuo}`}
                    />
                  }
                  onChange={debounce(this.handleSearchChange, 500)}
                  style={{ width: 350 }}
                />
              </div>
              <div>
                <div className={styles['tree-node-wrap']}>
                  {searchMode ? this.renderSearchResult() : this.renderTreeList()}
                </div>
              </div>
            </div>
            <div className={styles['select-show']}>
              <div className={styles['select-clear']}>
                <span>已选择（{result.length}）</span>
                {result.length > 0 && (
                  <span className={styles['clear-all']} onClick={this.onClear}>
                    清空
                  </span>
                )}
              </div>
              <div className={styles['select-contant']}>
                {result &&
                  result.length > 0 &&
                  result.map(item => (
                    <div
                      key={isRit ? item.dataRef.id : item.id || item.seqId}
                      className={styles['select-item']}
                    >
                      <div className={styles['select-item-content']}>
                        <span
                          className={`${fontStyles.iconfontextra} ${fontStyles.icongongsi1} ${styles.icongongsi1}`}
                        />
                        <span
                          className={styles['selected-org']}
                          dangerouslySetInnerHTML={{ __html: item.showPath }}
                        />
                      </div>
                      <div
                        className={styles['item-close']}
                        style={{ cursor: 'pointer' }}
                        onClick={() =>
                          this.deleteSelected(isRit ? item.dataRef.id : item.id || item.seqId)
                        }
                      >
                        <CloseOutlined />
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          </div>
        </div>
      </Modal>
    )
  }
}
