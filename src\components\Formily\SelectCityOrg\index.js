import React, { useState } from 'react'
import { Input, Modal } from 'antd'
import { PreviewText } from '@formily/react-shared-components'
import SelectOrg from './SelectCityOrg'

export default itemProps => {
  const [visible, setVisible] = useState(false)
  const { value, mutators, props, editable, schema } = itemProps
  const xComponentProps = schema['x-component-props'] || {}
  const {
    placeholder = '请选择',
    listAllAction,
    myListAction,
    searchAction,
    isWidth,
    isRit,
    type,
    parentOrgId,
    workLineType,
  } = xComponentProps

  const adapterValue = val =>
    Array.isArray(val)
      ? val.map(item => ({
          id: +(item.departmentId || item.id),
          name: item.name,
          type: 'DEPT',
          // _raw: item._data || item,
        }))
      : val

  let names = ''
  if (Array.isArray(value)) {
    const newArray = []
    const allArray = value.filter(i => i.code && i.code.toString().includes('_allDept'))
    value.map(i => {
      if ((i.code && i.code.toString().includes('_allDept')) || i.orgType == 1) {
        newArray.push(i)
      } else {
        const isExistAll = allArray.find(a => a.orgId === i.orgId)
        !isExistAll && newArray.push(i)
      }
    })
    names = newArray.length > 0 ? newArray.map(x => x.showPath).join('，') : ''
  }

  const combineDeptList = list => {
    const deleteName = [
      'active',
      'avatar',
      'checked',
      'children',
      'description',
      'dragOver',
      'dragOverGapBottom',
      'dragOverGapTop',
      'expanded',
      'externalId',
      'halfChecked',
      'icon',
      'isDeleted',
      'isLeaf',
      'judgeExist',
      'loaded',
      'loading',
      'lower',
    ]
    list.forEach(item => {
      deleteName.map(key => {
        item.hasOwnProperty(key) && delete item[key]
      })
    })
    return list
  }

  console.log(myListAction, 5555)

  return editable ? (
    <div style={{ width: isWidth ? '200px' : '100%' }}>
      <Input readOnly value={names} onClick={() => setVisible(true)} placeholder={placeholder} />
      {visible && (
        <SelectOrg
          visible
          API_GET_ROOT_ORG={myListAction}
          API_GET_LIST_ALL={listAllAction}
          API_SEARCH_LIST={searchAction}
          type={type}
          workLineType={workLineType}
          parentOrgId={parentOrgId}
          orgList={value}
          isRit={isRit}
          onCancel={() => setVisible(false)}
          onSubmit={selectedDept => {
            mutators.change(combineDeptList(selectedDept))
          }}
        />
      )}
    </div>
  ) : (
    <PreviewText value={names} />
  )
}
