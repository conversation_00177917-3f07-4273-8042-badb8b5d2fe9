import React ,{useState,useRef,useEffect}from 'react'
import { Select } from 'antd'
import {css,cx} from 'emotion'
import { PreviewText } from '@formily/react-shared-components'
import SelectOrg from '../Select/SelectOrg'



const { Option } = Select
 const SelectDepartment = (itemProps) => {
    const { value=[], mutators, props, editable, schema } = itemProps
    const [selectValue,setSelectValue] =useState(value)
    const [treeValue,setTreeValue] = useState([])
    const [names,setNames] = useState('')
    const xComponentProps = schema['x-component-props'] || {}
    const {
        listAllAction,
        myListAction,
        searchAction,
        type,
        parentOrgId,
        isRit,
      } = xComponentProps
    const [visible,setVisible] = useState(false)
    const children = []
    const sort = (arr) =>{
         arr.sort((a,b)=>{
            if(!a.value.startsWith('_tree')&& b.value.startsWith('_tree')){
                return -1
            }
            return 1
        })
        return arr
    }
    const handleChange = (val) =>{
        setSelectValue(sort(val))
        mutators.change(sort(val))
    }
    const handleClick = () =>{
        setVisible(true)
    }
    useEffect(()=>{
        if(value.length!==0){
            const names =value.length > 0
              ? value.map(x => x.label).join(',')
              : ''
              setNames(names)
            const arr = value.filter(item=>item.value.startsWith('_tree'))
            setTreeValue(arr)
            setSelectValue(value)

        }
    },[value])
    return editable?(
        <div className={css`display:flex`}>
              <Select mode="tags" style={{ width: 300 }} placeholder="请输入" onChange={handleChange}  value={selectValue} labelInValue>
                {children}
            </Select>
            <div className={css`cursor: pointer;font-size:14px;margin-left:10px;color:#1890ff;line-height:30px`} onClick={()=>handleClick()}>+选择部门</div>
            {visible && (<SelectOrg
        visible
        API_GET_ROOT_ORG={myListAction}
        API_GET_LIST_ALL={listAllAction}
        API_SEARCH_LIST={searchAction}
        type={type}
        parentOrgId={parentOrgId}
        orgList={treeValue}
        isRit={isRit}
        onCancel={() => setVisible(false)}
        onSubmit={(selectedDept) => {
            console.log(selectedDept,'selectedDept')
          const selectedDeptArr = selectedDept.map(item=>{
              return {
                  ...item,
                  label:item.showPath,
                  value:`_tree${item.dataRef.id}`,
              }
          })
          const arr = value.filter(item=>!item.value.startsWith('_tree'))
          const val = [...arr,...selectedDeptArr] 
          setSelectValue(val)
          mutators.change(val)
        }}
       />
      )}
        </div>
    ):( <PreviewText value={names} />)
}


export default SelectDepartment