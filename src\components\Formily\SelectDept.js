import React, { useState } from 'react'
import { Input } from 'antd'
import XmTree from '@xm/Tree'
import { PreviewText } from '@formily/react-shared-components'

export default itemProps => {
  const [visible, setVisible] = useState(false) 
  const { value, mutators, props, editable, schema } = itemProps
  const xComponentProps = schema['x-component-props'] || {}
  const {
    placeholder = '请选择',
    listAllAction,
    myListAction,
    searchAction,
    isWidth,
  } = xComponentProps

  const adapterValue = val =>
    Array.isArray(val)
      ? val.map(item => ({
        id: +(item.departmentId || item.id),
        name: item.name,
        type: 'DEPT',
        // _raw: item._data || item,
      }))
      : val

  let names = ''
  if (Array.isArray(value)) {
    names =
      value.length > 0
        ? value.map(x => x.name).join(',')
        : ''
  }

  return editable ? (
    <div  style={{width:isWidth?'200px':'100%'}}>
      <Input
        readOnly
        value={names}
        onClick={() => setVisible(true)}
        placeholder={placeholder}
      />
      {visible && ( 
        <XmTree 
          visible 
          type="multiDept" 
          ableChooseRoot={false} 
          ableChooseOrg={false} 
          ableChooseUser={false} 
          API_GET_ROOT_ORG={myListAction}
          API_GET_LIST_ALL={listAllAction}
          API_SEARCH_LIST={searchAction}
          deptList={value}
          treeCancelCallback={() => setVisible(false)} 
          treeCallback={({deptList}) => {
            mutators.change(adapterValue(deptList))
            setVisible(false) 
          }} 
        /> 
      )}
    </div>
  ) : (
    <PreviewText value={names} />
  )
}
