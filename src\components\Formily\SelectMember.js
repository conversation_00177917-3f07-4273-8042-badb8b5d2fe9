import React, { useState, useEffect } from 'react'
import { Input, Modal, Tree, message, Breadcrumb } from 'antd'
import { getDepartMentList, getUserAllList, getSeachResult } from './Select/SelectOrg/service'
import { CloseOutlined } from '@ant-design/icons';
import { PreviewText } from '@formily/react-shared-components'
import { replaceHtmlString } from 'ROOT/utils'
const { TreeNode } = Tree;
import styles from './SelectMumber.scss'

export default itemProps => {
  const [visible, setVisible] = useState(false) 
  const { value, mutators, props, editable, schema } = itemProps
  const xComponentProps = schema['x-component-props'] || {}
  const {
    placeholder = '请选择',
    listAllAction,
		myListAction,
    rootDeptId,
    searchAction,
    isWidth,
  } = xComponentProps

  let names = ''
  if (Array.isArray(value)) {
    names =
      value.length > 0
        ? value.map(x => x.name).join(',')
        : ''
  }

  return editable ? (
    <div  style={{width:isWidth?'200px':'100%'}}>
      <Input
        readOnly
        value={names}
        onClick={() => setVisible(true)}
        placeholder={placeholder}
      />
      {visible && (<UserTree
        visible
        myListAction={myListAction}
        listAllAction={listAllAction}
        searchAction={searchAction}
        userList={value}
        departmentId={rootDeptId}
        onCancel={() => setVisible(false)}
        onConfirm={(selectedUser) => {
           // name: replaceHtmlString(item.title || item.name),
           let _selectedUser = selectedUser.map(item => {
             return {
               ...item,
               name: replaceHtmlString(item.title || item.name),
             }
           })
          mutators.change(_selectedUser)
          setVisible(false)
        }}
      />)}
    </div>
  ) : (
    <PreviewText value={names} />
  )
}

const UserTree = ({
  visible,
  myListAction,
  listAllAction,
  searchAction,
  departmentId,
  userList,
  onCancel = () => {} ,
  onConfirm = () => {}
}) => {
  const [treeData, setTreeData] = useState([]) 
  const [selectedUser, setSelectedUser] = useState([])
  const [searchList, setSearchList] = useState([])
  const [isSearching, setIsSearching] = useState(false)
  const [searchMode, setSearchMode] = useState(false)
  const [breadCrumbList, setBreadCrumbList] = useState([])
  const [orgId, setOrgId] = useState()
  useEffect(() => {
    requestData();
    initState();
  }, [])

  console.log(searchAction);

  const initState = () => {
    userList && userList.length > 0 && setSelectedUser(userList);
  }

  const requestData = async () => {
    const dept = await getDepartMentList(myListAction, {});
    console.log('dept',dept);
    const rootData = dept.data.map((item) => ({
      ...item,
      title: item.name,
      key: item.id,
      isLeaf: false,
      icon: <span className={`iconfont icongongsi1 ${styles['icongongsi1']}`}></span>,
      children: []
    }))
    setTreeData(rootData);
  }
  const getData = async ({key}) => {
    console.log('departmentId', treeData,departmentId);
    if(treeData && treeData.length > 0) return false
    setOrgId(key)
    const { data } = await getUserAllList(listAllAction, { orgId: key, deptId :departmentId})
    const { deptList = [], orgList = [], userList = [] } = data;
    const _deptList = deptList.map((item) => ({
      ...item, title: item.name, icon: <span className={`iconfont iconwenjianjia_fill ${styles['iconwenjianjia_fill']}`}></span>, key: item.id, isLeaf: false, children: []
    }))
    const _orgList = orgList.map((item) => ({
      ...item, title: item.name, key: item.id, icon: <span className={`iconfont icongongsi1 ${styles['icongongsi1']}`}></span>, isLeaf: false, children: []
    }))
    const _userList = userList.map((item) => ({
      ...item, title: item.name, icon: <span className={`iconfont iconwodeyingyonggerenziliao ${styles['iconwodeyingyonggerenziliao']}`}></span>, key: item.uid, isLeaf: true, children: []
    }))
    const _data = data.deptUsers.map((item) => ({
      ...item, title: item.name, icon: <span className={`iconfont iconwodeyingyonggerenziliao ${styles['iconwodeyingyonggerenziliao']}`}></span>, key: item.uid, isLeaf: true, children: []
    }))
    setTreeData([..._orgList, ..._data]);
  }
  const treeSelect = (selectedKeys, info) => {
    console.log('这是添加的',info,selectedUser);
    const isHas = selectedUser.length > 0 && selectedUser.some(item => item.id == info.node.key);
    if(isHas) {
      message.warn('用户已选择');
    }
    if(!info.node.dataRef.uid) {
      message.warn('只能选择人员')
    }
    // if(info.node.dataRef.departmentId != departmentId) {
    //   message.warn('只能选择本部门的人员');
    // }
    if(!isHas && info.node.dataRef.uid) {
      setSelectedUser(selectedUser.concat([{deptId: info.node.dataRef.departmentId, orgId:orgId, id: info.node.key, mobile: info.node.dataRef.mobile , name: info.node.title }]))
    }
  }
  const onClear = () => {
    setSelectedUser([])
  }
  const deleteSelected = (key) => {
    console.log('删除', key,selectedUser);
    const newResult = selectedUser.filter( item => item.id !== key);
    setSelectedUser(newResult);
  }
  const handleSearchChange = async ({ target: { value } }) => {
	  handleSearch(value)
    setBreadCrumbList([])
  }
  const handleSearch = async (value) => {
    setIsSearching(true);
    let result = await getSeachResult(searchAction, { keyword: value, option: 1 });
 
    let { data: { contactList = [] }} = result;
    contactList = contactList.filter(item =>{
      console.log(item.deptIds,'==');
      return String(item.deptIds).includes(String(departmentId))
    })
    const searchResultList = contactList && contactList.length > 0 && contactList.map((item) =>({
      ...item,
      key: parseInt(item.uid),
      departmentId: parseInt(item.deptIds),
     
    }))
    setIsSearching(false);
    setSearchList(searchResultList);
    setSearchMode(result.data.contactList ? true : false);
  } 

  const renderSearch = () => {
    if (isSearching) {
      return <div className={styles['no-select-data']}>搜索中...</div>
    }
    if(!searchList) {
      return <div className={styles['no-select-data']}>暂无数据</div>
    }
    if(searchList && searchList.length === 0) {
      return <div className={styles['no-select-data']}>暂无数据</div>
    }
    return <div>
      {
        searchList && searchList.map((item) => (
          <div dangerouslySetInnerHTML={{__html: item.name}} className={styles['render-search-item']} onClick={ () => searchOnchange(item)}></div>
        ))
      }
    </div>
  }
  const searchOnchange = (person) => {
   
    const isHas = selectedUser.length > 0 && selectedUser.some(item => item.id == person.key);
    if(isHas) {
      message.warn('用户已选择');
    }
    if(!person.uid) {
      message.warn('只能选择人员')
    }
    // if(person.departmentId != departmentId) {
    //   message.warn('只能选择本部门的人员');
    // }
    if(!isHas && person.uid ) {
      console.log('搜索添加的', person,selectedUser);
      setSelectedUser(selectedUser.concat([{ orgId:orgId, id: person.key, mobile: person.mobile , name: person.title,...person,deptId: person.departmentId, }]))
    }
  }

  const breadCrumb = (expandedKeys, info) => {
    setBreadCrumbList(breadCrumbList.concat([info.node]));
  }

  const renderBreadCrumb = () => {
    return <Breadcrumb className={styles['breadcrumb']}>
      {
        breadCrumbList && breadCrumbList.length > 0 && breadCrumbList.map((item) => (
          <Breadcrumb.Item onClick={() => breadCrumbChange(item)} key={item.key}>{item.title}</Breadcrumb.Item>
        ))
      }
    </Breadcrumb>
  }

  const breadCrumbChange = (bread) => {
    const index = breadCrumbList.findIndex(item => item.key === bread.key);
    setBreadCrumbList(breadCrumbList.slice(0, index + 1));
    getData({ key: bread.key })
  }
  return <Modal
    width={800}
    visible={visible}
    onCancel={onCancel}
    onOk={() => {
      onConfirm(selectedUser);
    }}
    >
      <div className={styles['extra-forms-select-org']}>
        <div className={styles['title']}>选择人员</div>
        <div className={styles['container']}>
          <div className={styles['select-org']}>
            { renderBreadCrumb() }
            <Input placeholder={'搜索人员'} prefix={<span className={"iconfont iconsousuo " + styles['iconsousuo']}></span>} onChange={handleSearchChange} style={{ width: 350 }}></Input>
            <div className={styles['tree-node-wrap']}>
              {
                searchMode ? renderSearch() : <Tree
                  showIcon
                  loadData={getData}
                  onSelect={treeSelect}
                  onExpand={breadCrumb}
                >
                  {
                    treeData.length > 0 && treeData.map(item => {
                      return <TreeNode key={item.key} icon={item.icon} title={item.title} isLeaf={item.isLeaf} dataRef={item}/>
                    })
                  }
                </Tree>
              }
            </div>
          </div>
          <div className={styles['select-show']}>
            <div className={styles['select-clear']}>
              <span>已选择（{ selectedUser && selectedUser.length}）</span>
                {
                  selectedUser && selectedUser.length > 0 && <span className={styles['clear-all']} onClick={onClear}>
                  清空
                </span>
                }
                
              </div>
            <div className={styles['select-contant']}>
              {
                selectedUser && selectedUser.length > 0 && selectedUser.map((item) => (
                  <div key={item.id} className={styles['select-item']}>
                    <div className={styles['select-item-content']} key={item.id}>
                      <span className={`iconfont iconwodeyingyonggerenziliao ${styles['iconwodeyingyonggerenziliao']}`}></span>
                      <span dangerouslySetInnerHTML={{__html:item.name }} className={styles['selected-name']}></span>
                    </div>
                    <div className={styles['item-close']} style={{ cursor: 'pointer' }} onClick={ () => deleteSelected(item.id)}><CloseOutlined/></div>
                  </div>
                ))
              }
            </div>
          </div>
        </div>
      </div>
  </Modal>
}