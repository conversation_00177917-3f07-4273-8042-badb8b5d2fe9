import React, { useState } from 'react'
import { Input } from 'antd'
import XmTree from '@xm/Tree'
import { PreviewText } from '@formily/react-shared-components'

export default itemProps => {
  const [visible, setVisible] = useState(false) 
  const { value, mutators, props, editable, schema } = itemProps
  const xComponentProps = schema['x-component-props'] || {}
  const {
    placeholder = '请选择',
    listAllAction,
		myListAction,
    searchAction,
    isWidth,
  } = xComponentProps
  const adapterValue = val =>
    Array.isArray(val)
      ? val.map(item => ({
        id: item.uid,
        name: item.name,
        deptId: item.departmentId,
        orgId: item.orgId,
        mobile: item.mobile,
        // type: 'DEPT',
        // _raw: item._data || item,
      }))
      : val

  let names = ''
  if (Array.isArray(value)) {
    names =
      value.length > 0
        ? value.map(x => x.name).join(',')
        : ''
  }

  // console.log('listaction',listAllAction,myListAction,searchAction);

  return editable ? (
    <div  style={{width:isWidth?'200px':'100%'}}>
      <Input
        readOnly
        value={names}
        onClick={() => setVisible(true)}
        placeholder={placeholder}
      />
      {visible && ( 
        <XmTree 
				  visible
          type="multiUser" 
          ableChooseRoot={false} 
          ableChooseOrg={false}
          ableChooseUser={false} 
					ableChooseDept={false}
          userList={value} 
					API_GET_ROOT_ORG={myListAction}
          API_GET_LIST_ALL={listAllAction}
          API_SEARCH_LIST={searchAction}
          treeCancelCallback={() => setVisible(false)} 
          treeCallback={({userList}) => {
            mutators.change(adapterValue(userList))
            setVisible(false) 
          }} 
        /> 
      )}
    </div>
  ) : (
    <PreviewText value={names} />
  )
}
// import React, { useState, useEffect, useMemo } from 'react'
// import { Input, Icon, Modal, Tree, message } from 'antd'
// import _ from 'lodash'
// import { useBoolean } from 'ahooks'
// import { css } from 'emotion'
// import service from 'ROOT/service'
// import { connect } from 'react-redux'

// const { TreeNode } = Tree

// const mapState = ({ globalInfo: { baseInfo } }) => ({
//   orgId: baseInfo.orgId,
//   orgName: baseInfo.orgName,
// })

// const SelectMember = formProps => {
//   const { value = [], mutators, props, orgId, editable } = formProps
//   const componentProps = props['x-component-props']
//   const {
//     title,
//     disabled = false,
//     showMaxUserCount = 10,
//     departmentId,
//     noDepartmentTip = '请先选择部门',
//   } = componentProps

//   const [showTree, { setTrue, setFalse }] = useBoolean(false)
//   const [users, setUsers] = useState(value)
//   useEffect(() => {
//     setUsers(value)
//   }, [JSON.stringify(value)])

//   const handleConfirm = (list = []) => {
//     const userList = list.map(item => {
//       return {
//         name: item.name,
//         uid: item.uid,
//       }
//     })
//     mutators.change(userList)
//     setFalse()
//   }

//   const getDefaultValue = () => {
//     const result = users.slice(0, showMaxUserCount).map(item => item.name)
//     return result.join(',') + (users.length > showMaxUserCount ? '...等人' : '')
//   }

//   return (
//     <div style={{ flex: 1 }}>
//       {!editable ? (
//         <span>{getDefaultValue()}</span>
//       ) : (
//         <Input
//           prefix={<Icon type="user" style={{ color: 'rgba(0,0,0,.25)' }} />}
//           disabled={disabled}
//           value={getDefaultValue()}
//           onFocus={e => {
//             if (!departmentId) {
//               e.currentTarget.blur()
//               return message.info(noDepartmentTip)
//             }
//             setTrue()
//           }}
//           placeholder={componentProps.placeholder}
//         />
//       )}
//       {showTree && (
//         <DepartmentUserTree
//           title={title}
//           visible={showTree}
//           onCancel={setFalse}
//           onConfirm={handleConfirm}
//           defaultValue={value}
//           mode="single"
//           orgId={orgId}
//           departmentId={departmentId}
//         />
//       )}
//     </div>
//   )
// }

// SelectMember.isFieldComponent = true

// export default _.flowRight([connect(mapState)])(SelectMember)

// const modeMap = {
//   single: 'single',
//   multiple: 'multiple',
// }
// const DepartmentUserTree = ({
//   title,
//   visible,
//   onCancel = () => {},
//   onConfirm = () => {},
//   mode = modeMap.single,
//   defaultValue = [],
//   orgId,
//   departmentId,
// }) => {
//   const [treeData, setTreeData] = useState([])
//   const [selectedUser, setSelectedUser] = useState(defaultValue)
//   useEffect(() => {
//     getData()
//   }, [])
//   const getData = async pNode => {
//     const _orgId = pNode ? pNode.props.dataRef.orgId : orgId
//     const deptId = pNode ? pNode.props.dataRef.id : departmentId
//     const [departments, users] = await Promise.all([
//       service.getDepartmentList({
//         parentId: deptId,
//         orgId: _orgId,
//       }),
//       service.getDepartmentUsers({
//         deptId,
//         pageIndex: 1,
//         pageSize: 9999999,
//         orgId: _orgId,
//       }),
//     ])
//     if (pNode) {
//       const depts = departments.map(item => {
//         return { ...item, title: item.name, key: item.id + '', isLeaf: false, children: [] }
//       })
//       const _users = users.users.map(item => {
//         return {
//           ...item,
//           title: item.name,
//           key: item.uid + '',
//           isLeaf: true,
//         }
//       })
//       pNode.props.dataRef.children = [...depts, ..._users]
//       setTreeData([...treeData])
//     } else {
//       const depts = departments.map(item => {
//         return { ...item, title: item.name, key: item.id + '', isLeaf: false, children: [] }
//       })
//       const _users = users.users.map(item => {
//         return {
//           ...item,
//           title: item.name,
//           key: item.uid + '',
//           isLeaf: true,
//         }
//       })
//       setTreeData([...depts, ..._users])
//     }
//   }

//   const renderTreeNodes = data =>
//     data.map(item => {
//       if (item.children) {
//         return (
//           <TreeNode
//             selectable={false}
//             title={item.title}
//             isLeaf={false}
//             key={item.key}
//             dataRef={item}
//             icon={<Icon type="gold" />}
//           >
//             {renderTreeNodes(item.children)}
//           </TreeNode>
//         )
//       }
//       return <TreeNode selectable key={item.key} {...item} isLeaf dataRef={item} />
//     })
//   const deleteSelectedUser = idx => {
//     const users = [...selectedUser]
//     users.splice(idx, 1)
//     setSelectedUser(users)
//   }
//   const selectedKeys = useMemo(() => {
//     return selectedUser.map(item => item.uid + '')
//   }, [selectedUser])
  
//   return (
//     <Modal
//       width={600}
//       title={title}
//       visible={visible}
//       onCancel={onCancel}
//       onOk={() => {
//         onConfirm(selectedUser)
//       }}
//       centered
//     >
//       <div
//         className={css`
//           display: flex;
//           height: 400px;
//         `}
//       >
//         <div
//           className={css`
//             flex: 1;
//             border: 1px solid #e9ecf0;
//             border-right-width: 0;
//             overflow-y: auto;
//           `}
//         >
//           <Tree
//             selectedKeys={selectedKeys}
//             showIcon
//             onSelect={(selectedKeys, e) => {
//               setSelectedUser(
//                 e.selectedNodes.map(node => {
//                   return node.props.dataRef
//                 }),
//               )
//             }}
//             multiple={mode === modeMap.multiple}
//             loadData={getData}
//           >
//             {renderTreeNodes(treeData)}
//           </Tree>
//         </div>
//         <div
//           className={css`
//             flex: 1;
//             border: 1px solid #e9ecf0;
//             padding: 20px;
//             overflow-y: auto;
//           `}
//         >
//           {selectedUser.map((user, idx) => {
//             return (
//               <div
//                 className={css`
//                   display: flex;
//                   align-items: center;
//                 `}
//                 key={user.uid}
//               >
//                 <Icon type="user" />
//                 <span
//                   className={css`
//                     flex: 1;
//                     padding-left: 8px;
//                   `}
//                 >
//                   {user.name}
//                 </span>
//                 <Icon
//                   className={css`
//                     cursor: pointer;
//                   `}
//                   onClick={() => {
//                     deleteSelectedUser(idx)
//                   }}
//                   type="delete"
//                 />
//               </div>
//             )
//           })}
//         </div>
//       </div>
//     </Modal>
//   )
// }