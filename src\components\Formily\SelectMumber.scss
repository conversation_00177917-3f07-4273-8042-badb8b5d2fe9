.extra-forms-select-org {
  height: 450px;
}
.tree-node-wrap {
  margin: 10px 0;
  height: 340px;
  // border-right: 1px dashed #666;
  overflow: auto;
}

.title {
  height: 46px;
  font-size: 16px;
  font-weight: bold;
  line-height: 46px;
}
.container {
  display: flex;
  width: 100%;
}
.select-org {
  width: 50%;
  border-right: 1px dashed #666;
}
.select-show {
  width: 50%;
  padding-left: 24px;
}
.no-select-data {
  font-size: 12px;
  margin-top: 50px;
  text-align: center;
  color: #ddd;
}
.tree-node {
  margin-top: 10px;
  width: 100%;
  overflow: hidden;
}
.select-clear {
  height: 30px;
  display: flex;
  justify-content: space-between;
}
.select-contant {
  overflow: hidden;
}
.select-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  height: 32px;
}
.item-close {
  font-family: '微软雅黑';
  font-size: 14px;
  color: #666;
}
.search-list {
  width: 100%;
}
.search-item {
  width: 350px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.select-item-content {
  width: 320px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.clear-all {
  cursor: pointer;
}
.render-search-item {
  cursor: pointer;
  margin-bottom: 5px;
  font-size: 14px;
  color: #333;
}
.render-search-item span {
  color: red;
}
.breadcrumb {
  margin: 10px 0;
  cursor: pointer;
}
.icongongsi1 {
  color: #1890FF;
}
.iconwodeyingyonggerenziliao {
  color: #ff6a00;
}
.selected-name {
  margin-left: 5px;
}
.iconsousuo {
  color: #666;
}
.iconwenjianjia_fill {
  color: #2CAF7C;
}