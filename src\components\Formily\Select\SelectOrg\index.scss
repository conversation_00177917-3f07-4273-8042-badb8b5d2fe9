.extra-forms-select-org {
  height: 450px;

  :global {
    .ant-tree {
      .ant-tree-treenode-disabled {
        .ant-tree-node-content-wrapper {
          color: #000;
        }
      }
    }
  }
}
.tree-node-wrap {
  margin-top: 10px;
  max-height: 350px;
  overflow: auto;
}

.title {
  height: 46px;
  font-size: 16px;
  font-weight: bold;
  line-height: 46px;
}
.container {
  display: flex;
  width: 100%;
}
.select-org {
  width: 50%;
  min-height: 395px;
  border-right: 1px dashed #666;
}
.select-show {
  width: 50%;
  padding-left: 24px;
}
.no-select-data {
  font-size: 12px;
  margin-top: 50px;
  text-align: center;
  color: #ddd;
}
.tree-node {
  margin-top: 10px;
  width: 100%;
  overflow: hidden;
}
.select-clear {
  height: 30px;
  display: flex;
  justify-content: space-between;
}
.select-contant {
  overflow: auto;
  max-height: 365px;
}
.select-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  height: 32px;
}
.item-close {
  font-family: '微软雅黑';
  font-size: 14px;
  color: #666;
}
.search-list {
  width: 100%;
}
.search-item {
  width: 350px;
  display: flex;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.search-text {
  span {
    color: #fb4455;
  }
}
.select-item-content {
  width: 320px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.clear-all {
  cursor: pointer;
}
.icongongsi1 {
  color: #1890ff;
}
.iconwenjianjia_fill {
  color: #2caf7c;
}
.selected-org {
  margin-left: 5px;
}
.breadcrumb {
  margin: 10px 0;
  cursor: pointer;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
