import Cookies from 'js-cookie'
import _ from 'lodash'
import request from '../../../../service/request'


const API = {
  GET_DEPARTMENT_LIST: '/baas-admin/web/org/myList?flag=1', // 获取公司部门
  GET_DEPARTMENT_USER: '/baas-admin/web/department/users', // 获取部门人员
  // http://test.moa-dev.uban360.net:9000/baas-admin/web/listAll?parentOrgId=10456&parentDeptId=0&flag=7&groupCode=
}

const getRootOrg = (url, params) => {
  return request.get(url, params).then((res) => res)
}
const getAllList = (url, params) => {
  return request.get(url, params).then((res) => res)
}
const getSeachResult = (url, params) => {
  return request.get(url, {
    orgId: params.orgId ? params.orgId : Cookies.get('orgId'),
    option: params.option,
    keyword: params.keyword,
  })
}
const getDepartMentList = (url, params) => {
  return request.get(url, params).then((res) => res)  
}
const getUserAllList = (url, params) => {
  return request.get(url, params).then((res) => res)
}

export {
  getRootOrg,
  getAllList,
  getSeachResult,
  getDepartMentList,
  getUserAllList
}