import React from 'react'
import moment from 'moment'
import { Select } from 'antd'
import { PreviewText } from '@formily/react-shared-components'

export default itemProps => {
  const { value, mutators, props, editable } = itemProps

  const onChange = (value) => {
    mutators.change(value)
  }

  return editable
    ? <div className="form-flex-wrap">
      <div className="form-flex-item">
        <Select value={value} options={props.enum} onChange={onChange} />
      </div>
      <div className="form-flex-label">〔{moment().format('YYYY')}〕 号</div>
    </div>
    : <PreviewText value={value} />
}