import React, { useEffect, useState, useMemo, useRef } from 'react';
import { Modal, Radio, Input } from 'antd'
import Cookies from 'js-cookie'
import moment from 'moment'

export default (props) => {
  const { visible, setVisible, watermarkType = 1, setWatermarkType, setInputVal } = props
  const [value, setValue ]  = useState(watermarkType)
  const [watermarkVal, setWatermarkVal ]  = useState('')
  let {
    orgId = Cookies.get('orgId'),
    orgName,
    id = Cookies.get('userId'),
    name = Cookies.get('username')
  } = JSON.parse(
    Cookies.get('uiapp') ||
    Cookies.get('WEBG_STORAGE') ||
    Cookies.get('EntAdminG_STORE') ||
    localStorage.WEBG_STORAGE ||
    localStorage.EntAdminG_STORE ||
    '{}'
  )
  useEffect(() => {
    setValue(watermarkType)
  }, [watermarkType])
  const handleCancel = () => {
    setVisible(false)
  }
  const handleOk = () => {
    setWatermarkType(value)
    if (watermarkVal !== '') {
      setInputVal(watermarkVal)
    }
    setVisible(false)
  }
  const onChange = (e) => {
    setValue(e.target.value)
  }
  const inputChange = e => {
    setWatermarkVal(e.target.value)
  }
  return (
    <div >
        <Modal
          title="设置水印"
          visible={visible}
          onOk={handleOk}
          onCancel={handleCancel}
          width="440px"
          okText="确认"
          cancelText="取消"
          bodyStyle={{ padding: 0 }}
          className="waterMark-modal"
        >
          <div className='waterMark-info'>
            <div className='waterMark-title'><span>水印设置</span></div>
            <Radio.Group onChange={onChange} value={value}>
              <Radio value={1}>无水印</Radio>
              <Radio value={2}>姓名+手机号</Radio>
              <Radio value={3}>自定义显示<Input onChange={inputChange} placeholder='请输入' maxLength={20} /></Radio>
            </Radio.Group>
          </div>
        </Modal>
    </div>
  )
}