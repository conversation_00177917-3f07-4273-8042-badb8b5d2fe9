import React, { useEffect, useState } from 'react'
import { PreviewText } from '@formily/react-shared-components'
import { Modal, Button, Table } from 'antd'
import { message } from 'antd'
import service from 'ROOT/service'
import moment from 'moment'
import axios from 'axios'

const search = require('ROOT/assets/images/search.png')
export default (itemProps) => {
  const { value, mutators, props, editable, schema } = itemProps
  const [visible, setVisible] = useState(false)
  const [imgVisible, setImgVisible] = useState(false)
  const [pageIndex, setPageIndex] = useState(1)
  const [total, setTotal] = useState(0)
  const [dataSource, setDataSource] = useState([])
  const { reportId, meetingName, qrCodeUrl } = schema['x-component-props'] || []
  const pageSize = 10
  const columns = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '单位',
      dataIndex: 'unitName',
      key: 'unitName',
    },
    {
      title: '联系电话',
      dataIndex: 'phoneNumber',
      key: 'phoneNumber',
    },
    {
      title: '签到地点',
      dataIndex: 'address',
      key: 'address',
    },
    {
      title: '签到时间',
      dataIndex: 'gmtCreate',
      key: 'gmtCreate',
      render: (text, record) => {
        return moment(text).format('YYYY-MM-DD HH:mm:ss')
      }
    },
  ]
  const signRecord = () => {
    getTableList()
    setVisible(true)
  }
  const modalOk = () => {
    setVisible(false)
  }
  const getTableList = async () => {
    if (!reportId) {
      return false
    }
    const res = await service.signList({
      reportId,
      pageIndex,
      pageSize,
    })
    if (res.success) {
      setDataSource(res.data.list)
      setTotal(res.data.total)
    }
  }

  useEffect(() => {
    getTableList()
  }, [pageIndex])

  const handleTableChange = (pagination) => {
    setPageIndex(pagination.current)
    setSelectedRowKeys([])
  }
  const downRecord = () => {
    if (!reportId) {
      return false
    }
    axios.get('/security-manage-platform/web/meeting/sign/export?reportId=' + reportId, // 参数
      {responseType: 'blob'},
    ).then((res) => {
      const reader = new FileReader()
      reader.onload = () => {
        const data = reader.result
        if ( data.indexOf("success") > -1) {
          const resp = data && JSON.parse(data)
          if (!resp.success) {
            message.error(resp.msg)
          } 
        } else {
          if ('download' in document.createElement('a')) {
            const url = window.URL.createObjectURL(res.data) // 创建 url 并指向 blob
            const a = document.createElement('a')
            a.href = url
            a.download = `${meetingName}.xlsx`
            a.click()
            window.URL.revokeObjectURL(url) // 释放该 url
          } else { // IE10+下载
            navigator.msSaveBlob(res.data, `${meetingName}.xlsx`)
          }
          setVisible(false)
        }
      }
      reader.readAsText(res.data)
    })
  }
  return (
    <div className='code-info'>
      { qrCodeUrl ?
      <div className='code-img-info'>
        <img src={qrCodeUrl} />
        <div className='hover-info' onClick={() => {setImgVisible(true)}}>
          <div className='width-100'>
            <div className='flex-info'>
              <img src={search} />
            </div>
            <div className='font-12'>点击放大</div>
          </div>
        </div>
      </div>
      : null }
      { qrCodeUrl ?
        <a className='down-code' href={`${qrCodeUrl}&filename=${meetingName}.png`} >下载二维码</a>
      : null }
      <Button className='sign-record' onClick={signRecord}>签到记录</Button>
      <Modal
        title="签到记录"
        width={900}
        visible={visible}
        onOk={modalOk}
        onCancel={() => { setVisible(false) }}
        destroyOnClose
        className='custom-modal'
        footer={[
          <Button key="cancel" onClick={() => { setVisible(false) }}>
            取消
          </Button>,
          <Button key="submit" type="primary" onClick={downRecord}>
            下载
          </Button>,
        ]}
      >
          <Table
            dataSource={dataSource}
            columns={columns}
            pagination={{
              current: pageIndex,
              pageSize,
              total,
            }}
            onChange={handleTableChange}
          />
      </Modal>
      <Modal
        visible={imgVisible}
        onOk={modalOk}
        onCancel={() => { setImgVisible(false) }}
        destroyOnClose
        className='img-modal'
        width={600}
        footer={null}
        bodyStyle={{
          display: 'flex',
          overflow: 'hidden',
          height: 590
        }}
      >
          <img src={qrCodeUrl} />
      </Modal>
    </div>
  )
}
