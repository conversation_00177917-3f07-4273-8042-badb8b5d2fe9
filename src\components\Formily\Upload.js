/* eslint-disable no-nested-ternary */
import React, { useState, useRef, useCallback, useEffect, useMemo } from 'react'
import { message, Button, Modal, Table } from 'antd'
import { css } from 'emotion'
import Service from 'ROOT/service'
import Loading from 'ROOT/components/Loading'
import UploadFileList from '@xm/upload-file-list'

// import SelectMember from '@xm/select-member'
import { onMouseDown } from 'ROOT/utils/index'
import Cookies from 'js-cookie'
import SetWatermark from './SetWatermark'

export default itemProps => {
  const InputImport = useRef()
  const { value = [], mutators, props, schema, form, editable } = itemProps
  const { isShowSetWatermark, up } = schema['x-component-props'] || []
  const { debug, nodeName, limit, operator = {}, taskStatus, enableWrite, needFilePermissionBtn } = props['x-component-props']
  const [loading, setLoading] = useState(false)
  const [visible, setVisible] = useState(false)
  const [tableVisible, setTableVisible] = useState(false)
  const [watermarkVisible, setWatermarkVisible] = useState(false)
  const [treeData, setTreeData] = useState([])
  const [currentFile, setCurrentFile] = useState({})
  const [checkData, setCheckData] = useState({depts: [], orgs: []})
  const [permissionFiles, setPermissionFile] = useState([])
  const { loginName, loginMobile } = Cookies.getJSON('myself') || {}
  const [styleParams, setStyleParams] = useState({
    styleTop: 100,
    styleLeft: 0,
  })
  const [deptStyleParams, setDeptStyleParams] = useState({
    styleTop: 100,
    styleLeft: 0,
  })

  const style = { left: styleParams.styleLeft, top: styleParams.styleTop }
  const deptStyle = { left: deptStyleParams.styleLeft, top: deptStyleParams.styleTop }
  const orgType = {
    1: 'org',
    2: 'dept'
  }
  // 获取拖动值
  const getStyleParams = (styleTop, styleLeft) => {
    setStyleParams({ styleTop, styleLeft })
  }

  const getDeptStyleParams = (styleTop, styleLeft) => {
    setDeptStyleParams({ styleTop, styleLeft })
  }

  const columns = [
    {
      title: '附件名称',
      dataIndex: 'name',
      key: 'name',
      render: (val) => {
        return <p style={{ width: 220, whiteSpace: 'pre-line', wordBreak: 'break-all', wordWrap: 'break-word', textAlign: 'left' }}>{val}</p>
      }
    },
    {
      title: '操作',
      key: "action",
      dataIndex: "action",
      render: (val, record, index) => {
        const { checkData } = record || {}
        const { depts = [], orgs = [] } = checkData && checkData.data || {}
        if (depts.length) {
          depts.forEach(dept => {
            treeData.forEach(tree => {
              if (dept.id === tree.id) {
                dept.pid = tree.pid
              }
              // 部门前要拼接单位名字
              if (dept.pid === tree.id && !dept.name.includes(tree.name)) {
                dept.name = `${tree.name}-${dept.name}`
              }
            })
          })
        }
        if (depts.length === 0 && orgs.length === 0) {
          return <Button type="link" style={{ margin: 0, padding: 0 }} onClick={() => hanldeSetFilePermission(record)}>设置权限</Button>
        } else {
          const showName = depts.concat(orgs).map(item => item.name).join('，')
          return <Button type="link" style={{ margin: 0, padding: 0, whiteSpace: 'pre-line', wordBreak: 'break-all', wordWrap: 'break-word', textAlign: 'left' }} onClick={() => hanldeSetFilePermission(record)}>{showName}</Button>
        }
      }
    }
  ]

  const selectFiles = () => {
    InputImport.current.click()
  }

  const fileChange = (e) => {
    // let files = e.currentTarget.files[0]
    let files = Array.from(e.target.files)
    let arr1 = [], arr2 = [], arr3=[];
    let ableUpload = []
    files.map(item => {
      const { name, size } = item || {}
      const shortName = name.substring(0, name.lastIndexOf('.'))
      // if (!(/^\d+\./.test(shortName))) {
      //   arr1.push(name)
      //   // message.warning('上传文件不符合附件命名要求，附件名称以数字序号+“.”开头')
      //   // InputImport.current.value = null
      //   return
      // }
      if (size <= 0) {
        arr2.push(name)
        // message.error('无法上传0kb或0kb以下大小的文件，请重新选择文件上传')
        // InputImport.current.value = null
        return
      }
      if (size > 200 * 1024 * 1024) {
        arr3.push(name)
        // const str = `文件大小需${maxSize}M以下`
        // message.error(str)
        return
      }
      ableUpload.push(item)
    })
   if (arr2.length > 0) {
      message.warning(`${arr2.join(',')}0kb或0kb以下大小的文件，请重新选择文件上传`)
    } else if (arr3.length > 0) {
      message.warning(`${arr2.join(',')}文件大小需200M以下`)
    }
    if (files.length > 0) {
      ableUpload.forEach(i => uploadFiles(i))
    }
    InputImport.current.value = null
  }

  const uploadFiles = (files) => {
    let formData = new FormData()
    formData.append('file', files)
    setLoading(true)
    Service.uploadFile(formData).then(res => {
      if (res.code === 200) {
        mutators.push({
          name: files.name,
          size: files.size,
          url: res.fileUrl,
          suffix: files.name.substring(files.name.lastIndexOf('.') + 1),
          uid: files.lastModified
        })
        setLoading(false)
      }
    })
  }

  const uploadOptions = {
    enableSort: editable,
    enableDownload: true,
    enableDelete: editable,
    enableRename: editable,
    enablePreview: true
  }

  const combineFileList = (list, val, inputVal) => {
    // limit === 'NONE'：全量查看，debug：全量查看  
    // 其他情况下根据各附件设置的权限是去显示
    let watermarkparams = {}
    let watermarkVal = {}
    if (val) {
      watermarkparams = {
        watermarkType: val,
      }
      if (val === 2) {
        watermarkVal = {
          watermarkVal: loginName + loginMobile,
        }
      }
    }
    if (inputVal) {
      watermarkVal = {
        watermarkVal: inputVal,
      }
    }
    const result = !Array.isArray(list)
    ? []
    : list.map((file, index) => {
      if (!val && file.watermarkType) {
        watermarkparams = {
          watermarkType: file.watermarkType,
        }
        if (file.watermarkType === 2) {
          watermarkVal = {
            watermarkVal: loginName + loginMobile,
          }
        }
      }
      if (!inputVal && file.watermarkVal && file.watermarkType === 3) {
        watermarkVal = {
          watermarkVal: file.watermarkVal,
        }
      }
      // console.log(watermarkVal)
      return ({
        name: `附件：${(file.name || file.fileName).replace(/^附件：/, '')}`,
        size: file.size || file.fileSize,
        url: file.url || file.fileUrl,
        suffix: (file.name || file.fileName).substring((file.name || file.fileName).lastIndexOf('.') + 1),
        uid: file.uid,
        checkData: file.checkData,
        ...watermarkparams,
        ...watermarkVal,
      })
    })
    return result
  }

  const onDelete = (fileList) => {
    mutators.change(combineFileList(fileList))
  }

  const onSortEnd = (fileList) => {
    mutators.change(combineFileList(fileList))
  }

  const onRename = (fileList) => {
    mutators.change(combineFileList(fileList))
  }

  // const beforeRename = (file) => {
  //   if (!(/^\d+\./.test(file.name.replace(/^附件：/, '')))) {
  //     message.warning('上传文件不符合附件命名要求，附件名称以数字序号+“.”开头')
	// 		return false
  //   }
  //   return true
  // }

  const combineFileName = useCallback(() => {
    return combineFileList(value)
  }, [value])

  // 设置权限
  const hanldeSetFilePermission = (file) => {
    setCurrentFile(file)
    if (file.checkData && file.checkData.data) {
      const { data: { depts = [], orgs = [] } } = file.checkData
      setCheckData({depts, orgs})
    } else {
      setCheckData({depts: [], orgs: []})
    }
    setVisible(true)
  }

  // 保存权限设置
  const handleOk = () => {
    setTableVisible(false)
  }

  // 获取选人树数据
  const getTreeData = async () => {
    const { admin_login, orgId } = Cookies.getJSON()
    const { orgId: useOrgId } = admin_login || {}
    try {
      Service.getOrgByResearch({
        orgId: useOrgId || orgId,
        researchType: 1
      }).then(res => {
        const data = []
        res.forEach(res => {
          data.push({ id: res.id, name: res.name, type: orgType[res.orgType], pid: -1 })
          if (res.subList !== null && res.subList.length > 0) {
            res.subList.forEach(list => {
              // data.push({ id: list.id, name: `${res.name}-${list.name}`, /* list.name, */ type: orgType[list.orgType], pid: res.id })
              data.push({ id: list.id, name: list.name, type: orgType[list.orgType], pid: res.id })
            })
          }
        })
        setTreeData(data)
      })
    } catch (err) {
      console.log('getFilePermission err', err)
    }
  }

  useEffect(() => {
    getTreeData()
  }, [])
  const setWatermarkType = (val) => {
    mutators.change(combineFileList(value, val))
  }
  const setInputVal = (val) => {
    mutators.change(combineFileList(value, 3, val))
  }
  const getDefaultList = useMemo(() => {
    const { depts, orgs } = checkData
    const defaultList = [...depts, ...orgs]
    let currentList = []
    if (defaultList && defaultList.length > 0) {
      defaultList.forEach(item => {
        item.name = item.selectName || item.name
      })
      currentList = defaultList
      return currentList
    } else {
      return []
    }
  }, [checkData])

  const params = {
    visible: true,
    orgId: operator.orgId,
    orgName: operator.orgName,
    defaultDeptList: getDefaultList,
    range: 1000,
    type: 'multiOrgAndDept',
    title: '选择部门',
    isChooseFromOtherOrgs: operator.orgId === '10032' || operator.orgId === '10016', // 区公司支持从所有单位选
    needSearchBar: false,
    needHeader: false,
    isDiffOrg: true,
    isAssingRootOrg: true,
    assingDeptLevel: 1,
    onlyChooseDept: true,
    deptIsBefore: true,
    deptNameNeedOrg: true,
    expandTreeLoading: true,
  }

  useEffect(() => {
    function messageEvent(e) {
      const { data = {} } = e
      const { type, data: _data = {} } = data
      const { depts = [], users = [], orgs = [] } = _data
      if (depts.length > 0) {
        depts.forEach(item => {
          item.name = item.selectName || item.name
        })
      }
      // 点击选人组件中的【取消】按钮
      if (type === 'BAAS_TREE_CANCEL') {
        setVisible(false)
      }
      // 点击选人组件中的【确定】按钮
      if (type === 'BAAS_TREE_CONFIRM') {
        // 保存到对应的dataSouce
        const { uid } = currentFile || {}
        if (uid){
          const index = value.findIndex(item => item.uid === uid)
          if (index > -1) {
            value[index].checkData = data
          }
        }

        mutators.change(combineFileList(value))
        // const _dataSource = dataSource.map(item => {
        //   if (item.key === actionIndex) {
        //     return {
        //       ...item,
        //       visibleRange: depts.concat(orgs),
        //     }
        //   } else {
        //     return item
        //   }
        // })
        // setDataSource([..._dataSource])
        setVisible(false)
      }
    }
    if (visible) {
      window.addEventListener('message', messageEvent)
    }
    return () => {
      window.removeEventListener('message', messageEvent)
    }
  }, [visible])


  return (
    <div style={{ width: '100%' }}>
      {editable ? (
        <div style={{ width: '100%', marginBottom: 10 }}>
          <span onClick={selectFiles} className="upload-file-btn">
            上传文件
          </span>
          <input
            type="file"
            ref={InputImport}
            onChange={fileChange}
            className={css`
              position: absolute;
              top: -9999px;
              left: -9999px;
            `}
            multiple="multiple"
          />
          {needFilePermissionBtn && combineFileName().length > 0 && (
            <Button type="link" style={{ marginLeft: 8 }} onClick={() => setTableVisible(true)}>
              附件权限设置
            </Button>
          )}
          { isShowSetWatermark && value.length > 0 ?
              <Button style={{ marginLeft: 20 }} onClick={() => {setWatermarkVisible(true)}}>设置水印</Button>
            : null }
          {loading && <Loading isFullScreen={true} />}
        </div>
      ) : needFilePermissionBtn && combineFileName().length > 0 ? (
        <Button type="link" style={{ marginLeft: 8 }} onClick={() => setTableVisible(true)}>
          附件权限设置
        </Button>
      ) : null}
      { 
       (up && combineFileName().length > 0) && (
        <div style={{ display: 'flex', color: 'red' }}>
          <span>提示：</span> 
          <div>
            <p style={{ margin: 0 }}>1、可点击下载查看上传附件</p>
            <p style={{ margin: 0 }}>2、设置水印功能：目前仅支持PDF格式文件下载时，增加水印</p>
          </div>
        </div>
       )
      }
      <UploadFileList options={uploadOptions} dataSource={combineFileName()} onDelete={onDelete} onSortEnd={onSortEnd} onRename={onRename} />
      <SetWatermark visible={watermarkVisible} setVisible={setWatermarkVisible} watermarkType={value && value.length > 0 ? value[0].watermarkType : 1} setWatermarkType={setWatermarkType} setInputVal={setInputVal} />
      <Modal
        style={{ ...style, overflow: 'hidden'}}
        title={
          <div style={{ cursor: 'move', width: '100%' }} onMouseDown={e => onMouseDown(e, styleParams, getStyleParams)}>
            权限分配
          </div>
        }
        visible={tableVisible}
        onOk={handleOk}
        onCancel={() => {setTableVisible(false)}}
        width="500px"
        okText="确认"
        cancelText="取消"
      >
        <Table dataSource={combineFileName()} columns={columns} pagination={false} />
      </Modal>

      {visible && <Modal
        style={{ ...deptStyle, height: 592, overflow: 'hidden'}}
        title={
          <div
            style={{ cursor: 'move', width: '100%' }}
            onMouseDown={e => onMouseDown(e, deptStyleParams, getDeptStyleParams)}
          >
            选择部门
          </div>
        }
          visible={visible}
          maskClosable={false}
          width={600}
          footer={null}
          onCancel={ () => setVisible(false) }
          bodyStyle={{
            padding: 0,
            margin: 0,
            overflow: 'hidden',
            height: 538
          }}
        >
          <iframe
            src={`${ab.api}/user-selector?query=${encodeURIComponent(
              JSON.stringify({ ...params }),
            )}`}
            id="permissionIframe"
            frameBorder="0"
            style={{ border: 0 }}
            width="100%"
            height="100%"
          />
        </Modal> 
      }
    </div>
  )
}