import React, { useState, useCallback, useRef } from 'react'
import { css } from 'emotion'
import Service from 'ROOT/service'
import Loading from 'ROOT/components/Loading'
import UploadFileList from '@xm/upload-file-list'
import { message } from 'antd'


export default itemProps => {
    const InputImport = useRef()
    const [loading, setLoading] = useState(false)
    const { value, mutators, props, schema, form, editable, initialValue = [] } = itemProps
    const initialValueLength = initialValue? initialValue.length: 0


    const selectFiles = () => {
        InputImport.current.click()
    }

    const fileChange = (e) => {
        console.log('e', e.currentTarget.files);
        if(value && value.length + initialValueLength  > 50) {
            message.error('上传失败，文件数量不能超过50')
            InputImport.current.value = null
            return false
        }
        let files = Object.values(e.currentTarget.files) || []
        console.log('files', files);
        const filesLimit = files.every(a => a.size > 0 && a.size < 204800000)
        if (!filesLimit) {
            message.error('只能上传 0B-200M 范围大小的文件，请重新选择文件上传')
            InputImport.current.value = null
            return false
        }
        files.forEach( data => uploadFiles(data));
        
    }

    const uploadFiles = (files) => {
        let formData = new FormData()
        formData.append('file', files)
        setLoading(true)
        Service.uploadFile(formData).then(res => {
            if (res.code === 200) {
                mutators.push({
                    name: files.name,
                    size: files.size,
                    url: res.fileUrl,
                    suffix: files.name.substring(files.name.lastIndexOf('.') + 1),
                    uid: files.lastModified
                })
                setLoading(false)
            }
        })
    }

    const combineFileList = (list) => {
        return !Array.isArray(list)
            ? []
            : list.map((file, index) => ({
                name: `附件：${(file.name || file.fileName).replace(/^附件：/, '')}`,
                size: file.size || file.fileSize,
                url: file.url || file.fileUrl,
                suffix: (file.name || file.fileName).substring((file.name || file.fileName).lastIndexOf('.') + 1),
                uid: file.uid
            }))
    }

    const onDelete = (fileList) => {
        mutators.change(combineFileList(fileList))
    }

    const onSortEnd = (fileList) => {
        mutators.change(combineFileList(fileList))
    }

    const onRename = (fileList) => {
        mutators.change(combineFileList(fileList))
    }

    const combineFileName = useCallback(() => {
        return combineFileList(value)
    }, [value])

    const uploadOptions = {
        enableSort: editable,
        enableDownload: true,
        enableDelete: editable,
        enableRename: editable,
        enablePreview: !editable
    }

    return (
        <div style={{ width: '100%' }}>
            {editable
                ? <div style={{ width: '100%', marginBottom: 10 }}>
                    <span
                        onClick={selectFiles}
                        className="upload-file-btn"
                    >上传文件</span>
                    <input
                        type="file"
                        ref={InputImport}
                        onChange={fileChange}
                        className={css`
                        position: absolute;
                        top: -9999px;
                        left: -9999px;
                        `}
                        multiple = 'multiple'
                    />
                    {loading && <Loading />}
                </div>
                : null
            }
            <UploadFileList options={uploadOptions} dataSource={combineFileName()} onDelete={onDelete} onSortEnd={onSortEnd} onRename={onRename} />
        </div>
    )
}