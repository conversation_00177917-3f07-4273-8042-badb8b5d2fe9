import React, { useState, useRef, useCallback, useEffect } from 'react'
import { message, Button, Modal, Table } from 'antd'
import { css } from 'emotion'
import Service from 'ROOT/service'
import Loading from 'ROOT/components/Loading'
import UploadFileList from '@xm/upload-file-list'
import SelectMember from '@xm/select-member'
import { onMouseDown } from 'ROOT/utils/index'
import Cookies from 'js-cookie'

export default itemProps => {
  const InputImport = useRef()
  const { value = [], mutators, props, schema, form, editable } = itemProps
  console.log('itemProps', itemProps)
  const { debug, needFilePermissionBtn, nodeName, limit } = props['x-component-props']
  const [loading, setLoading] = useState(false)
  const [visible, setVisible] = useState(false)
  const [tableVisible, setTableVisible] = useState(false)
  const [treeData, setTreeData] = useState([])
  const [currentFile, setCurrentFile] = useState({})
  const [checkData, setCheckData] = useState({depts: [], orgs: []})
  const [styleParams, setStyleParams] = useState({
    styleTop: 100,
    styleLeft: 0,
  })
  
  const orgType = {
    1: 'org',
    2: 'dept'
  }

  // 获取拖动值
  const getStyleParams = (styleTop, styleLeft) => {
    setStyleParams({ styleTop, styleLeft })
  }

  const columns = [
    {
      title: '附件名称',
      dataIndex: 'name',
      key: 'name',
      render: (val) => {
        return <p style={{ width: 220, whiteSpace: 'pre-line', wordBreak: 'break-all', wordWrap: 'break-word', textAlign: 'left' }}>{val}</p>
      }
    },
    {
      title: '操作',
      key: "action",
      dataIndex: "action",
      render: (val, record, index) => {
        const { checkData } = record || {}
        const { depts = [], orgs = [] } = checkData || {}
        if (depts.length) {
          depts.forEach(dept => {
            treeData.forEach(tree => {
              if (dept.id === tree.id) {
                dept.pid = tree.pid
              }
              // 部门前要拼接单位名字
              if (dept.pid === tree.id) {
                dept.name = `${tree.name}-${dept.name}`
              }
            })
          })
        }
        if (depts.length === 0 && orgs.length === 0) {
          return <Button type="link" style={{ margin: 0, padding: 0 }} onClick={() => hanldeSetFilePermission(record)}>设置权限</Button>
        } else {
          const showName = depts.concat(orgs).map(item => item.name).join('，')
          return <Button type="link" style={{ margin: 0, padding: 0, whiteSpace: 'pre-line', wordBreak: 'break-all', wordWrap: 'break-word', textAlign: 'left' }} onClick={() => hanldeSetFilePermission(record)}>{showName}</Button>
        }
      }
    }
  ]

  const selectFiles = () => {
    InputImport.current.click()
  }

  const fileChange = (e) => {
    // let files = e.currentTarget.files[0]
    let files = Array.from(e.target.files)
    // console.log(Array.from(files))
    let arr1 = [], arr2 = [], arr3=[];
    let ableUpload = []
    files.map(item => {
      const { name, size } = item || {}
      const shortName = name.substring(0, name.lastIndexOf('.'))
      // if (!(/^\d+\./.test(shortName))) {
      //   arr1.push(name)
      //   // message.warning('上传文件不符合附件命名要求，附件名称以数字序号+“.”开头')
      //   // InputImport.current.value = null
      //   return
      // }
      if (size <= 0) {
        arr2.push(name)
        // message.error('无法上传0kb或0kb以下大小的文件，请重新选择文件上传')
        // InputImport.current.value = null
        return
      }
      if (size > 200 * 1024 * 1024) {
        arr3.push(name)
        // const str = `文件大小需${maxSize}M以下`
        // message.error(str)
        return
      }
      ableUpload.push(item)
    })
   if (arr2.length > 0) {
      message.warning(`${arr2.join(',')}0kb或0kb以下大小的文件，请重新选择文件上传`)
    } else if (arr3.length > 0) {
      message.warning(`${arr2.join(',')}文件大小需200M以下`)
    }
    if (files.length > 0) {
      ableUpload.forEach(i => uploadFiles(i))
    }
    InputImport.current.value = null
  }

  const uploadFiles = (files) => {
    let formData = new FormData()
    formData.append('file', files)
    setLoading(true)
    Service.uploadFile(formData).then(res => {
      if (res.code === 200) {
        mutators.push({
          name: files.name,
          size: files.size,
          url: res.fileUrl,
          suffix: files.name.substring(files.name.lastIndexOf('.') + 1),
          uid: files.lastModified
        })
        setLoading(false)
      }
    })
  }

  const uploadOptions = {
    enableSort: editable,
    enableDownload: true,
    enableDelete: editable,
    enableRename: editable,
    enablePreview: true
  }

  const combineFileList = (list) => {
    // limit === 'NONE'：全量查看，debug：全量查看  
    // 其他情况下根据各附件设置的权限是去显示

    return !Array.isArray(list)
    ? []
    : list.map((file, index) => ({
      name: `附件：${(file.name || file.fileName).replace(/^附件：/, '')}`,
      size: file.size || file.fileSize,
      url: file.url || file.fileUrl,
      suffix: (file.name || file.fileName).substring((file.name || file.fileName).lastIndexOf('.') + 1),
      uid: file.uid,
      checkData: file.checkData
    }))
  }

  const onDelete = (fileList) => {
    mutators.change(combineFileList(fileList))
  }

  const onSortEnd = (fileList) => {
    mutators.change(combineFileList(fileList))
  }

  const onRename = (fileList) => {
    mutators.change(combineFileList(fileList))
  }

  // const beforeRename = (file) => {
  //   if (!(/^\d+\./.test(file.name.replace(/^附件：/, '')))) {
  //     message.warning('上传文件不符合附件命名要求，附件名称以数字序号+“.”开头')
	// 		return false
  //   }
  //   return true
  // }

  const combineFileName = useCallback(() => {
    return combineFileList(value)
  }, [value])

  // 设置权限
  const hanldeSetFilePermission = (file) => {
    setCurrentFile(file)
    if (file.checkData) {
      const { checkData: { depts = [], orgs = [] } } = file
      setCheckData({depts, orgs})
    }
    setVisible(true)
  }

  // 选人树参数
  const treeProps = {
    title: '设置权限',
    needDrag: true,
    dragDefaultStyle: { top: 150 },
    type: ['dept', 'org'],
    range: 999,
    onOk: () => {
      setVisible(false)
    },
    onCancel: () => {
      setVisible(false)
    },
    onConfirm: (data) => {
      const { uid } = currentFile || {}
      if (uid){
        const index = value.findIndex(item => item.uid === uid)
        if (index > -1) {
          value[index].checkData = data
        }
      }
      mutators.change(combineFileList(value))
      setVisible(false)
    },
  };

  // 保存权限设置
  const handleOk = () => {
    setTableVisible(false)
  }

  // 获取选人树数据
  const getTreeData = async () => {
    const { admin_login, orgId } = Cookies.getJSON()
    const { orgId: useOrgId } = admin_login || {}

    try {
      Service.getOrgByResearch({
        orgId: useOrgId || orgId,
        researchType: 1
      }).then(res => {
        const data = []
        res.forEach(res => {
          data.push({ id: res.id, name: res.name, type: orgType[res.orgType], pid: -1 })
          if (res.subList !== null && res.subList.length > 0) {
            res.subList.forEach(list => {
              // data.push({ id: list.id, name: `${res.name}-${list.name}`, /* list.name, */ type: orgType[list.orgType], pid: res.id })
              data.push({ id: list.id, name: list.name, type: orgType[list.orgType], pid: res.id })
            })
          }
        })
        console.log('get tree data res', data)
        setTreeData(data)
      })
    } catch(err) {
      console.log('getTreeData err', err)
    }
  } 

  useEffect(() => {
    getTreeData()
  }, [])

  return (
    <div style={{ width: '100%' }}>
      {editable
        ? <div style={{ width: '100%', marginBottom: 10 }}> 
            <span
              onClick={selectFiles}
              className="upload-file-btn"
            >上传文件</span>
            <input
              type="file"
              ref={InputImport}
              onChange={fileChange}
              className={css`
                position: absolute;
                top: -9999px;
                left: -9999px;
              `}
              multiple="multiple"
            />
            {
              needFilePermissionBtn && nodeName === '起草人设置查看权限' && combineFileName().length > 0 && (
                <Button type="link" style={{ marginLeft: 8 }} onClick={() =>  setTableVisible(true)}>附件权限设置</Button>
              )
            }
            <Button type="link" style={{ marginLeft: 8 }} onClick={() =>  setTableVisible(true)}>附件权限设置</Button>
          {loading && <Loading isFullScreen={true} />}
        </div>
        : null
      }
      <UploadFileList options={uploadOptions} dataSource={combineFileName()} onDelete={onDelete} onSortEnd={onSortEnd} onRename={onRename}  />

      <Modal
        style={{ left: styleParams.styleLeft, top: styleParams.styleTop, overflow: 'hidden'}}
        title={
          <div style={{ cursor: 'move', width: '100%' }} onMouseDown={e => onMouseDown(e, styleParams, getStyleParams)}>
            权限分配
          </div>
        }
        visible={tableVisible}
        onOk={handleOk}
        onCancel={() => {setTableVisible(false)}}
        width="500px"
        okText="确认"
        cancelText="取消"
      >
        <Table dataSource={combineFileName()} columns={columns} pagination={false} />
      </Modal>

      {
        visible && (
          <SelectMember
            visible={visible}
            value= {checkData}
            data = {[
              treeData,
            ]}
            {...treeProps}
          />
        )
      }
    </div>
  )
}