.item-render {
  display:flex;
  width: 100%;
  height: 52px;
  margin-bottom: 15px;
  background: #F7F8F9;
  border: 1px solid #E9ECF0;
  border-radius: 2px;
  justify-content:space-between;
  padding: 0 16px;
  align-items:center;
}

.item-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.item-left img {
  width: 25px;
  height: 34px;
  flex: 0;
}

.item-content {
  display: flex;
  flex: 1;
  flex-direction: column;
  margin-left: 16px;
  padding-right: 50px;
}

.item-filename {
  width: 100%;
  font-size: 14px;
  color: #262A30;
  letter-spacing: 0;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-right {
  width: 70px;
}

.item-button {
  /* margin-right: 10px; */
  cursor: pointer;
}

.item-button-more {
  width: 20px;
}

.item-button-more img {
  width: 20px;
  height: auto;
}

.wps-lineheight p font {
  line-height: 1.65;
}

