import React, { useEffect, useState } from 'react'
import { Button, Space } from 'antd'
import service from 'ROOT/service'
import { UPLOAD_WPS_URL } from 'ROOT/constants'
import { outWpsEdit } from 'ROOT/utils/wps'
import PreviewComponent from 'ROOT/views/web/MeetNotice/module/previewModal'
import './WpsEditor.css'

let init = true

export default (itemProps) => {
	const { value, mutators, props, editable, schema, form } = itemProps
	const xComponentProps = schema['x-component-props'] || {}
	const { orgId, reportId, type, config, formType, actions, meetTitle } = xComponentProps || {}
	const [fileList, setFileList] = useState([])

	useEffect(() => {
		if (init && value && value.length > 0) {
			setFileList(value)
			init = false
		}
	}, [value])

	const saveNewWpsContent = async (content) => {
		try {
			const param = {
				btype: 5,
				bid: `extra-${reportId}`,
				url: {
					...content,
				},
			}
			await service.saveWpsEditUrl(param)
		} catch (err) {
			console.log('saveWpsEditUrl err', err)
		}
	}

	// 唤起wps在线编辑
	const handleClickWpsEdit = (e) => {
		e.stopPropagation()
		outWpsEdit({
			ruleType: type,
			config,
			fileList,
			index: -1,
			reportId,
			isNew: false,
			formData: form.getFormState().values,
			uploadPath: UPLOAD_WPS_URL,
			isHtml: true,
			// suffix: '.html',
			// needConvert: true,
			callback: (_reportId, fileList) => {
				service.getFormData({
					reportId,
				}).then(async res => {
					const { data: { fileList } } = res.data
					setFileList(fileList)
					mutators.change(fileList)
					saveNewWpsContent(fileList)
				})
			},
			orgId,
		})
	}

	const handleEdit = (e, index) => {
		e.stopPropagation()
		outWpsEdit({
			ruleType: type,
			config,
			fileList,
			index,
			reportId,
			isNew: false,
			formData: form.getFormState().values,
			uploadPath: UPLOAD_WPS_URL,
			isHtml: true,
			// suffix: '.html',
			// needConvert: true,
			callback: (_reportId, fileList) => {
				// if (value && value.length > 0 && value[0].key !== fileList[0].key) {
				// 	return false
				// }
				service.getFormData({ reportId }).then(res => {
					const { data: { fileList } } = res.data
					setFileList(fileList)
					mutators.change(fileList)
					saveNewWpsContent(fileList)
				})
			},
			orgId,
		})
	}

	return (
		<div style={{ width: '100%' }}>
			{editable ?
				<div style={{ marginBottom: '15px' }}>
					{value && value.length > 0 ?
						<Button onClick={(e) => handleEdit(e, 0)}>正文编辑</Button> :
						<Button onClick={handleClickWpsEdit}>正文编辑</Button>
					}
				</div> : null
			}
			{/* {formType === 'meetNotice' &&
				<PreviewComponent actions={actions} meetTitle={meetTitle}/>
			}
			{value && value.length > 0
				? <div className='wps-lineheight' style={{lineHeight: 1.65}} className={`wps-preview ${!editable ? 'wps-preview-disable' : ''}`} dangerouslySetInnerHTML={{ __html: value[0].html }} />
				: null
			} */}
		</div>
	)
}
