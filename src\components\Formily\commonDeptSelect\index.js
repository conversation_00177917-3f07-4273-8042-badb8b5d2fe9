import React, { useState, useEffect } from 'react'
import Cookies from 'js-cookie'
import {css,cx} from 'emotion'
import { Select , Input, message, Modal } from 'antd'
import { PreviewText } from '@formily/react-shared-components'

export default itemProps => {
  const { value=[], mutators, props, editable, schema, width=300 } = itemProps
  const [selectValue,setSelectValue] =useState(value)
  const [treeValue,setTreeValue] = useState([])
  const [names,setNames] = useState('')
  const xComponentProps = schema['x-component-props'] || {}
  const {
    placeholder = '请选择',
    maxCount = 1000,
    defaultChooseSelf = false,
  } = xComponentProps
  const children = []
  const {
    orgId = Cookies.get('orgId'),
    orgName,
    id = Cookies.get('userId'),
    name = Cookies.get('username'),
  } = JSON.parse(
    Cookies.get('uiapp') ||
    Cookies.get('WEBG_STORAGE') ||
    Cookies.get('EntAdminG_STORE') ||
    localStorage.WEBG_STORAGE ||
    localStorage.EntAdminG_STORE ||
    '{}',
  )
  const [visible, setVisible] = useState(false)

  useEffect(() => {
    function messageEvent(e) {
      const { data } = e
      const { type } = data

      // 点击选人组件中的【取消】按钮
      if (type === 'BAAS_TREE_CANCEL') {
        setVisible(false)
      }

      // 点击选人组件中的【确定】按钮
      if (type === 'BAAS_TREE_CONFIRM') {
          const {depts} = data.data || {}
          const selectedDeptArr = depts.map(item=>{
              return {
                  ...item,
                  label:item.name,
                  value:`_tree${item.id}`,
              }
          })
          const arr = value.filter(item=>!item.value.startsWith('_tree'))
          const val = [...arr,...selectedDeptArr] 
          setTreeValue(selectedDeptArr)
          setSelectValue(val)
          mutators.change(val)
          setVisible(false)
      }
    }
    if (visible) {
      window.addEventListener('message', messageEvent)
    }
    return () => {
      window.removeEventListener('message', messageEvent)
    }
  }, [visible])

  const sort = (arr) =>{
         arr.sort((a,b)=>{
            if(!a.value.startsWith('_tree')&& b.value.startsWith('_tree')){
                return -1
            }
            return 1
        })
        return arr
    }
  const handleChange = (val) => {
    const value = val
    for(let i = 0;i<value.length;i++){
      if(treeValue.length){
        const index = treeValue.find(item=>{
          return item.value === value[i].value
        })
        if(index){
          value[i] = index
        }
      }
    }
    setSelectValue(sort(value))
    mutators.change(sort(value))
  }
  const params = {
    visible: true,
    needSearchBar: true,
    orgId,
    orgName,
    defaultDeptList: treeValue,
    range: maxCount,
    type: 'multiDept',
    title: '选择部门',
  }

  const handleClick = () =>{
        setVisible(true)
    }
    useEffect(()=>{
      if(value.length!==0){
            const names =value.length > 0
              ? value.map(x => x.label).join(',')
              : ''
              setNames(names)
            const arr = value.filter(item=>item.value.startsWith('_tree'))
            setTreeValue(arr)
            setSelectValue(value)

        }
    },[value])
  return editable ? (
    <div className={css`display:flex`}>
              <Select mode="tags" style={{ width }} placeholder="请输入" 
              onChange={handleChange}
              value={selectValue} 
              labelInValue>
                {children}
            </Select>
            <div className={css`cursor: pointer;font-size:14px;margin-left:10px;color:#1890ff;line-height:30px`} onClick={()=>handleClick()}>+选择部门</div>
            {visible
        ? <Modal
          visible={visible}
          width={600}
          footer={null}
          closable={false}
          bodyStyle={{
            padding: 0,
            margin: 0,
            overflow: 'hidden',
            height: 590,
          }}
        >
          <iframe
            src={`${ab.api}/user-selector?query=${encodeURIComponent(
              JSON.stringify(params),
            )}`}
            frameBorder="0"
            style={{ border: 0 }}
            width="100%"
            height="100%"
          />
        </Modal> : null
      }
            </div>
  ) : (
    <PreviewText value={names} />
  )
}
