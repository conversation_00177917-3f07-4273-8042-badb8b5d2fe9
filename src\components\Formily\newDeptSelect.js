import React, { useState, useEffect } from 'react'
import Cookies from 'js-cookie'
import { Input, message, Modal } from 'antd'
import { PreviewText } from '@formily/react-shared-components'

export default itemProps => {
  const { value, mutators, props, editable, schema } = itemProps
  const xComponentProps = schema['x-component-props'] || {}
  const {
    placeholder = '请选择',
    maxCount = 1,
    defaultChooseSelf = false,
    // limitDept = []
    rootDeptId
  } = xComponentProps
  console.log('rootDeptId',rootDeptId);
  // let {
  //   orgId = Cookies.get('orgId'),
  //   orgName,
  //   id = Cookies.get('userId'),
  //   name = Cookies.get('username')
  // } = JSON.parse(
  //   Cookies.get('uiapp') ||
  //   Cookies.get('WEBG_STORAGE') ||
  //   Cookies.get('EntAdminG_STORE') ||
  //   localStorage.WEBG_STORAGE ||
  //   localStorage.EntAdminG_STORE ||
  //   '{}'
  // )
  const [visible, setVisible] = useState(false)

  window.addEventListener('message', e => {
    const { data } = e
    const { type } = data
    console.log(data,'data')
    // 点击选人组件中的【取消】按钮
    if (type === 'BAAS_TREE_CANCEL') {
      setVisible(false)
    }

    // 点击选人组件中的【确定】按钮
    if (type === 'BAAS_TREE_CONFIRM') {
      mutators.change(data.data.depts)
      setVisible(false)
    }
  })

  const getDefaultList = () => {
    let defaultList = []
    if (value && value.length > 0) {
      defaultList = value
    } else if (defaultChooseSelf) {
      defaultList = [{
        id,
        name
      }]
    }
    return defaultList
  }

  const params = {
    visible: true,
    needSearchBar: false,
    orgId: xComponentProps.orgId,
    orgName: xComponentProps.orgName,
    defaultUserList: getDefaultList(),
    range: maxCount,
    type:"singleDept",
    chooseFromDeptId: rootDeptId,
    needLookUp: true,
    canChooseOffice: false,
    // chooseFromDeptId: limitDept && limitDept.length > 0 ? limitDept[0].id : ''
  }

  let names = ''
  if (Array.isArray(value)) {
    names = value.map(x => x.name).join('，')
  }
  console.log('xComponentProps',names);
  return editable ? (
    <div style={{width: 100 + '%'}}>
      <Input
        readOnly
        value={names}
        onClick={() => setVisible(true)}
        placeholder={placeholder}
      />
      {visible
        ? <Modal
          visible={visible}
          width={600}
          footer={null}
          closable={false}
          bodyStyle={{
            padding: 0,
            margin: 0,
            overflow: 'hidden',
            height: 590
          }}
        >
          <iframe
            src={`${ab.api}/user-selector?query=${encodeURIComponent(
              JSON.stringify(params)
            )}`}
            frameBorder="0"
            style={{ border: 0 }}
            width="100%"
            height="100%"
          />
        </Modal> : null
      }
    </div>
  ) : (
    <PreviewText value={names} />
  )
}
