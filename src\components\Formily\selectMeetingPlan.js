import React, { useEffect, useState } from 'react'
import { Select, Table } from 'antd'

export default (itemProps) => {
  const { value, mutators, props, editable, schema, form } = itemProps
  const xComponentProps = schema['x-component-props'] || {}
  const { isWidth, disabled } = xComponentProps
  const [tableData, setTableData] = useState([])
  const onChange = (value) => {
    form.getFieldValue('meetingPlanId')
    let chooseData = {}
    props.enum.map(item => {
      if (value && item.value == value.value) {
        setTableData([item])
        chooseData = item
      }
    })
    mutators.change({...chooseData,...value})
  }

  const meetingTypeList = [
    { label: '区公司一类', value: 1 },
    { label: '区公司二类', value: 2 },
    { label: '区公司三类', value: 3 },
    { label: '市公司一类', value: 4 },
    { label: '市公司二类', value: 5 },
    { label: '市公司三类', value: 6 }

  ]
  const columns = [
    {
      title: '会议形式',
      dataIndex: 'meetingForm',
      render: (text) => {
        return text === 1 ? '线下会议' : '线上会议';
      },
    },
    {
      title: '会议年份',
      dataIndex: 'meetingYear',
      key: 'meetingYear',
    },
    {
      title: '会议举办部门',
      dataIndex: 'deptName',
      key: 'deptName',
    },
    {
      title: '会议类型',
      dataIndex: 'meetingType',
      render: (text) => {
        const item = meetingTypeList.find(item => item.value === text);
        return item ? item.label : '';
      },
    },
    {
      title: '计划会议时间',
      dataIndex: 'meetingTime',
      key: 'meetingTime',
    },
    {
      title: '现场参会人数',
      dataIndex: 'meetingUserCount',
      key: 'meetingUserCount',
    },
    {
      title: '会期(天)',
      dataIndex: 'meetingDay',
      key: 'meetingDay',
    },
    {
      title: '备注',
      dataIndex: 'description',
      key: 'description',
    }
  ]
  useEffect(() => {
    if (!value) {
      setTableData([])
      
    }else{
      setTableData([value])
    }
  }, [value])
  return (
    <div style={{ width: '100%' }}>
      {editable ? <Select disabled={!editable} labelInValue value={value} options={props.enum} onChange={onChange} allowClear style={{ width: isWidth ? '100%' : '' }} /> : <div>{value ? value.label : '-'}</div>}
      {value && <Table style={{ marginTop: 10 }} size='small' pagination={false} bordered dataSource={tableData} columns={columns} />}
    </div>
  )
}
