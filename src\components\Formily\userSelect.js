import React, { useState, useEffect, useRef } from 'react'
import Cookies from 'js-cookie'
import { Input, message, Modal } from 'antd'
import { PreviewText } from '@formily/react-shared-components'

export default itemProps => {
  const { value, mutators, props, editable = true, schema } = itemProps
  const xComponentProps = (schema && schema['x-component-props']) || {}
  const {
    placeholder = '请选择',
    maxCount = 1000,
    defaultChooseSelf = false,
    // limitDept = []
    rootDeptId,
    disabled,
    type = 'multiUser', // 支持传递 type 参数，默认为 multiUser
  } = xComponentProps
  const {
    orgId = Cookies.get('orgId'),
    orgName,
    id = Cookies.get('userId'),
    name = Cookies.get('username'),
  } = JSON.parse(
    Cookies.get('uiapp') ||
      Cookies.get('WEBG_STORAGE') ||
      Cookies.get('EntAdminG_STORE') ||
      localStorage.WEBG_STORAGE ||
      localStorage.EntAdminG_STORE ||
      '{}',
  )
  const [visible, setVisible] = useState(false)
  const lastOpenTime = useRef(0)

  useEffect(() => {
    const handleMessage = (e) => {
      const { data } = e
      const { type } = data

      // 只有当前组件的弹窗是可见状态时才处理消息
      if (visible) {
        // 点击选人组件中的【取消】按钮
        if (type === 'BAAS_TREE_CANCEL') {
          setVisible(false)
        }

        // 点击选人组件中的【确定】按钮
        if (type === 'BAAS_TREE_CONFIRM') {
          if (mutators && mutators.change) {
            // 根据不同的 type 处理不同的数据结构
            let selectedData = []
            if (data.data) {
              if (data.data.users) {
                selectedData = data.data.users
              } else if (data.data.depts) {
                // 处理部门数据，转换为与 commonDeptSelect 兼容的格式
                selectedData = data.data.depts.map(item => ({
                  ...item,
                  label: item.name,
                  value: `_tree${item.id}`,
                }))
              }
            }
            mutators.change(selectedData)
          }
          setVisible(false)
        }
      }
    }

    window.addEventListener('message', handleMessage)

    return () => {
      window.removeEventListener('message', handleMessage)
    }
  }, [mutators, visible])

  const getDefaultList = () => {
    let defaultList = []
    if (value && value.length > 0) {
      defaultList = value
    } else if (defaultChooseSelf) {
      defaultList = [
        {
          id,
          name,
        },
      ]
    }
    return defaultList
  }

  // 根据 type 决定使用哪个默认列表参数名
  const defaultListParam = type.includes('Dept') ? 'defaultDeptList' : 'defaultUserList'

  const params = {
    visible: true,
    needSearchBar: false,
    orgId,
    orgName,
    [defaultListParam]: getDefaultList(),
    range: maxCount,
    type: type, // 使用从 props 传递的 type 参数
    chooseFromDeptId: rootDeptId,
    needLookUp: true,
    // chooseFromDeptId: limitDept && limitDept.length > 0 ? limitDept[0].id : ''
  }

  let names = ''
  if (Array.isArray(value)) {
    names = value.map(x => x.label || x.name).join('，')
  }
  return editable ? (
    <div style={{ width: `${100}%` }}>
      <Input
        readOnly
        disabled={disabled}
        value={names}
        onClick={() => {
          lastOpenTime.current = Date.now()
          setVisible(true)
        }}
        placeholder={placeholder}
      />
      {visible ? (
        <Modal
          visible={visible}
          width={600}
          footer={null}
          closable={false}
          bodyStyle={{
            padding: 0,
            margin: 0,
            overflow: 'hidden',
            height: 590,
          }}
        >
          <iframe
            src={`${ab.api}/user-selector?query=${encodeURIComponent(JSON.stringify(params))}`}
            frameBorder="0"
            style={{ border: 0 }}
            width="100%"
            height="100%"
          />
        </Modal>
      ) : null}
    </div>
  ) : (
    <PreviewText value={names} />
  )
}
