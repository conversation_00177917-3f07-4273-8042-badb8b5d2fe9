import React from 'react'
import _ from 'lodash'
import { Spin } from 'antd'
import { css } from 'emotion'

const Loading = ({ type, width = 60, isFullScreen }) => {
	const staticStyle = css`margin: 30px auto;`
	const absStyle = css`
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
`

	return isFullScreen ? (
		<div className={css`
			position: fixed;
			left: 0;
			top: 0;
			width: 100%;
			height: 100%;
			background: rgba(255, 255, 255, 0.3);
			z-index: 11;
		`}>
			<div className={type === 'static' ? staticStyle : absStyle}>
				<Spin />
			</div>
		</div>
	) : (
		<div className={type === 'static' ? staticStyle : absStyle}>
			<Spin />
		</div>
	)
}

export default Loading
