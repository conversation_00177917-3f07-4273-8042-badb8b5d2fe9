import React, { useState, useEffect, useRef } from 'react'
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons'
import PreviewComponent from 'ROOT/views/web/MeetNotice/module/previewModal'

export default ({
  title,
  content,
  actions,
  formType,
  style,
}) => {
  const [backToTop, setBackToTop] = useState(false)

  const contentRef = useRef(null)

  useEffect(() => {
    if (!location.hash.includes('workletter')) return

    const handleScroll = () => {
      const { scrollTop } = document.documentElement
      const offsetTop = contentRef && contentRef.current && contentRef.current.offsetTop

      if (scrollTop + 100 >= offsetTop) {
        setBackToTop(true)
      } else {
        setBackToTop(false)
      }
    }

    window.addEventListener('scroll', handleScroll)

    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [content, location.hash])

  const onScroll = () => {
    const { scrollTop, clientHeight, scrollHeight } = document.documentElement
    const offsetTop = contentRef && contentRef.current && contentRef.current.offsetTop

    if (scrollTop + 100 >= offsetTop) {
      document.documentElement.scrollTop = 0
    }
    if (scrollTop + 100 < offsetTop && Math.ceil(scrollTop + clientHeight) <= scrollHeight) {
      document.documentElement.scrollTop = offsetTop
    }
  }

  return (content) ? (
    <div ref={contentRef} style={style} className="main-text">
      <div className="main-text-label">正文预览</div>
      <div className="main-text-container">
        <div className="main-text-title">{title}</div>
        {formType && formType === 'meetNotice' &&
          <PreviewComponent actions={actions} />
        }
        {content
          ? <div className="main-text-content" dangerouslySetInnerHTML={{ __html: content }} />
          : null
        }
      </div>
      {
        location.hash.includes('workletter') && <div className='main-text-action' onClick={onScroll}>
          {!backToTop ? <ArrowDownOutlined /> : <ArrowUpOutlined />}
          {!backToTop ? '只看正文' : '回到顶部'}
        </div>
      }
    </div>
  ) : null
}