import React, { useState, useRef, useEffect } from 'react'
import { connect } from 'react-redux'
import ClassNames from 'classnames'
import { Button, Form, message } from 'antd'
import SecretSelectUser from '../SecretSelectUser'
import styles from './index.scss'


const FormItem = Form.Item

const RenderAddUserList = (props) => {
  const userRef = useRef()
  const { type, onUserChange, selectTreeNodeList, allSelectList, initUserList } = props
  const [selectUserVisible, setSelectUserVisible] = useState(false)
  const [moreBottonVisible, setMoreBottonVisible] = useState(false)
  const [isShow, setIsShow] = useState(false)
  const [userList, setUserList] = useState([])

  const handleAddUser = () => {
    setSelectUserVisible(true)
  }
  const handleCancelUser = () => {
    setSelectUserVisible(false)
  }
  const handleSelectUser = (users) => {
    setUserList(users)
    onUserChange && onUserChange(type, users)
    setSelectUserVisible(false)
  }
  const handleDeleteUserItem = (users) => {
    const newUserList = userList.filter((u) => u.uid !== users.uid)
    onUserChange && onUserChange(type, newUserList)
    setUserList(newUserList)
  }

  const handleMoreBottonClick = () => {
    setMoreBottonVisible(false)
    setIsShow(true)
  }
  const renderLable = () => {
    switch (type) {
      case 'hostUsers':
        return '主办人'
      case 'coUsers':
        return '协办人'
      case 'knowUsers':
        return '知会人'
      default:
        return '-'
    }
  }
  useEffect(() => {
    const height = userRef.current.scrollHeight
    if (height > 104 && !isShow) {
      setMoreBottonVisible(true)
    } else {
      setMoreBottonVisible(false)
    }
  }, [userList, isShow])
  useEffect(() => {
    if (initUserList.length === 0) {
      setUserList([])
    }
  }, [initUserList])
  return (
    <div className={styles['user-warp']}>
      <FormItem label={renderLable()}>
        <div className={styles.content}>
          <Button
            onClick={() => {
              handleAddUser(type)
            }}
          >
            <i className={ClassNames('iconfont icon-add', styles['add-icon'])} />
            <span>添加人员</span>
          </Button>
          <div ref={userRef} className={ClassNames(styles.users, { [styles['is-show']]: isShow })}>
            {userList.length > 0 &&
              userList.map((user) => {
                return (
                  <div key={user.uid} className={styles['user-item']}>
                    <span className={styles['user-name']}>{user.userName}</span>
                    <i
                      className={ClassNames('iconfont icon-close', styles['user-delete-icon'])}
                      onClick={() => {
                        handleDeleteUserItem(user)
                      }}
                    />
                  </div>
                )
              })}
          </div>
          {moreBottonVisible && (
            <div className={styles.more} onClick={handleMoreBottonClick}>
              查看更多
            </div>
          )}
        </div>
      </FormItem>
      <SecretSelectUser
        visible={selectUserVisible}
        emptyText="请从左侧选择分发人员"
        onCancelUser={handleCancelUser}
        onSelectUser={handleSelectUser}
        selectTreeNodeList={selectTreeNodeList}
        allSelectList={allSelectList}
        userList={userList}
      />
      
    </div>
  )
}
// export default connect((state) => ({
//   loginUserData: state.loginUserData,
//   orgId: state.orgData.currentOrgId,
//   orgName: state.orgData.currentOrgName,
// }))(RenderAddUserList)
export default RenderAddUserList
