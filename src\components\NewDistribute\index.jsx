import React, { useState, useEffect, useReducer } from 'react'
import { connect } from 'react-redux'
import ClassNames from 'classnames'
import { Modal, Switch, Button, Form, message } from 'antd'
import { COMMON_FILE_SETTING, LEVEL_CODE } from 'ROOT/Constant'
import { official } from 'ROOT/service'
import TextareaCommonExpression from '../TextareaCommonExpression'
import RenderUserList from './component/renderUserList'
import styles from './index.scss'

const FormItem = Form.Item
const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 16,
  },
}
const initialState = {
  hostUsers: [],
  coUsers: [],
  knowUsers: [],
  opinion: '',
  isSendSms: false,
}
const creatAction = (type, payload) => {
  return {
    type,
    payload,
  }
}
const reducer = function reducer(state, action) {
  switch (action.type) {
    case 'hostUsers':
      return { ...state, hostUsers: action.payload }
    case 'coUsers':
      return { ...state, coUsers: action.payload }
    case 'knowUsers':
      return { ...state, knowUsers: action.payload }
    case 'opinion':
      return { ...state, opinion: action.payload }
    case 'isSendSms':
      return { ...state, isSendSms: action.payload }
    case 'init':
      return { ...state, hostUsers: [], coUsers: [], knowUsers: [], opinion: '' }
    default:
      return state
  }
}
const renderLevelItem = (name, id, children) => {
  return {
    userName: name,
    level: 1,
    uid: id,
    children,
  }
}
const NewDistribute = (props) => {
  const {
    visible,
    onClose,
    onSuccess,
    id, // 公文id
    orgId,
    fillOpinions, // 意见必填
    isDefSendSms, // 短信通知
    levelCodeNodes, // 所有职级编码
    isBeSender, // 是否第一次
    userLabelCodes, // 当前用户职级编码
    deptList,  // 部门id
    phrases = [],
  } = props
  const [passData, dispatch] = useReducer(reducer, initialState)
  const [selectTreeNodeList, setSelectTreeNodeList] = useState([])
  const [allSelectList, setAllSelectList] = useState([])
  const [isFillopinion, setIsFillopinion] = useState(false)
  const getFilesNodes = (type) => {
    if (!levelCodeNodes.length) return
    let nodeCodeArr = []
    switch (type) {
      case 1:
        nodeCodeArr.push(levelCodeNodes.find((l) => l.lnName == LEVEL_CODE.BOSS).nodeCode)
        break

      case 2:
        nodeCodeArr.push(levelCodeNodes.find((l) => l.lnName == LEVEL_CODE.DEPT_BOSS).nodeCode)
        break
      case 3:
        const manager = levelCodeNodes.filter((l) => {
          if (l.lnName == LEVEL_CODE.DEPT_MANAGER || l.lnName == LEVEL_CODE.OFFICES_MANAGER) {
            return true
          }
          return false
        })
        nodeCodeArr = manager.map((m) => m.nodeCode)
        break
      case 4:
        const staff = levelCodeNodes.filter((l) => {
          return l.lnName == LEVEL_CODE.STAFF
        })
        nodeCodeArr = staff.map((m) => m.nodeCode)
        break

      default:
        break
    }
    return nodeCodeArr
  }
  const getUserNodeCode = () => {
    let index = 0
    for (let i = 0; i < levelCodeNodes.length; i++) {
      const code = levelCodeNodes[i].nodeCode
      if (userLabelCodes.includes(code)) {
        if (i == 2 || i == 3) {
          index = 2
        } else if (i == 4) {
          index = 3
        } else {
          index = i + 1
        }
      }
    }
    return index
  }
  const handleInitSelectUserListData = () => {
    const levelList = []
    const deptIds = isBeSender ? deptList.map((d) => d.id) : []
    const level1 = official.getLevelCodeUserList({ orgId, deptIds, nodeCodes: getFilesNodes(1) })
    const level2 = official.getLevelCodeUserList({
      orgId,
      deptIds,
      nodeCodes: getFilesNodes(2),
    })
    const level3 = official.getLevelCodeUserList({
      orgId,
      deptIds,
      nodeCodes: getFilesNodes(3),
    })
    const level4 = official.getLevelCodeUserList({
      orgId,
      deptIds,
      nodeCodes: getFilesNodes(4),
    })
    const treeNode = [
      {
        name: '公司领导',
      },
      {
        name: '部门总经理',
      },
      {
        name: '部门经理/室经理',
      },
      {
        name: '普通员工',
      },
    ]

    Promise.all([level1, level2, level3, level4]).then((res) => {
      res.forEach((l, i) => {
        // 给用户加上唯一标签
        const symbolItem = l.map((lsItem) => ({
          ...lsItem,
          symbolId: `${lsItem.uid}${lsItem.deptId}`,
        }))

        if (i !== 3) {
          levelList.push(renderLevelItem(treeNode[i].name, i, symbolItem))
        } else {
          const level4User = symbolItem.map((user) => {
            return { ...user, class: i + 1 }
          })
          levelList.push(renderLevelItem(treeNode[i].name, i, level4User))
        }
      })
      // 给人员加上唯一标识
      // 判断是不是第一次分发
      if (!isBeSender) {
        setSelectTreeNodeList(levelList)
        setAllSelectList(
          levelList.reduce((acc, item) => {
            return acc.concat(item.children)
          }, []),
        )
      }
      if (isBeSender) {
        const levelIndex = getUserNodeCode()
        const newLevelList = levelList.slice(levelIndex)
        setSelectTreeNodeList(newLevelList)
        setAllSelectList(
          newLevelList.reduce((acc, item) => {
            return acc.concat(item.children)
          }, []),
        )
      }
    })
  }
  const handleInit = () => {
    dispatch(creatAction('init', ''))
  }
  const handleOk = () => {
    const allUser = [...passData.hostUsers, ...passData.coUsers, ...passData.knowUsers]
    if (!allUser.length) {
      return message.error('请添加人员')
    }
    if (!passData.opinion && isFillopinion) {
      return message.error('请填写意见')
    }
  
    official
      .sendDocument({
        orgId,
        id,
        ...passData,
      })
      .then(() => {
        message.success('分发成功')
      })
    handleInit()
    onSuccess && onSuccess()
  }
  const handleCancel = () => {
    handleInit()
    onClose && onClose()
  }

  const handleOpinionChange = (val) => {
    dispatch(creatAction('opinion', val))
  }
  const handleMsgChange = (val) => {
    dispatch(creatAction('isSendSms', val))
  }
  const handleUserChange = (type, users) => {
    dispatch(creatAction(type, users))
  }
  const renderFooter = () => {
    return [
      <div key="sms" className={styles['send-sms']}>
        <span className={styles['send-sms-label']}> 短信提醒： </span>
        <Switch checked={passData.isSendSms} onChange={handleMsgChange} />
      </div>,
      <Button key="cancel" onClick={handleCancel}>
        取消
      </Button>,
      <Button key="submit" type="primary" onClick={handleOk}>
        确定
      </Button>,
    ]
  }
  useEffect(() => {
    if (visible) {
      handleInitSelectUserListData()
      dispatch(creatAction('isSendSms', isDefSendSms))
      if (fillOpinions.includes(COMMON_FILE_SETTING.SEND_FILE)) {
        setIsFillopinion(true)
      }
    }
  }, [visible])

  return (
    <Modal
      title="分发意见"
      visible={visible}
      width="600px"
      okText="确认"
      centered
      cancelText="取消"
      onCancel={handleCancel}
      footer={renderFooter()}
    >
      <Form className={styles['distribute-body']} {...formItemLayout}>
        <RenderUserList
          type="hostUsers"
          key="hostUsers"
          selectTreeNodeList={selectTreeNodeList}
          allSelectList={allSelectList}
          initUserList={passData.hostUsers}
          onUserChange={handleUserChange}
        />
        <RenderUserList
          type="coUsers"
          key="coUsers"
          selectTreeNodeList={selectTreeNodeList}
          allSelectList={allSelectList}
          initUserList={passData.coUsers}
          onUserChange={handleUserChange}
        />
        <RenderUserList
          type="knowUsers"
          key="knowUsers"
          selectTreeNodeList={selectTreeNodeList}
          allSelectList={allSelectList}
          initUserList={passData.knowUsers}
          onUserChange={handleUserChange}
        />
        <FormItem label="意见" required={isFillopinion}>
          <TextareaCommonExpression
            showPhrase
            phrases={phrases}
            value={passData.opinion}
            onChange={handleOpinionChange}
            placeholder="请输入（最多500字）"
            maxLength={500}
          />
        </FormItem>
      </Form>
    </Modal>
  )
}

export default connect((state) => ({
  loginUserData: state.loginUserData,
  orgId: state.orgData.currentOrgId,
  orgName: state.orgData.currentOrgName,
  deptList: state.orgData.deptList,
}))(NewDistribute)
