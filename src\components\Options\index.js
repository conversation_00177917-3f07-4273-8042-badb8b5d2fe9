import React from 'react'
import ClassNames from 'classnames'
import styles from './index.scss'

export default class Options extends React.Component {
  choose = (item) => {
    const { onChange } = this.props
    // eslint-disable-next-line no-unused-expressions
    onChange && onChange(item)
  }

  render() {
    const { options = [], className } = this.props
    return (
      <div className={ClassNames(styles.options, className)}>
        {options.map((item) => (
          <div key={item.id} className={styles.option} onClick={this.choose.bind(null, item)}>
            {item.name}
          </div>
        ))}
      </div>
    )
  }
}
