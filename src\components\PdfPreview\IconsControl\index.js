import React from 'react'
import ClassNames from 'classnames'
import TipWrapper from 'ROOT/components/TipWrapper'
import styles from './index.scss'

export default class IconsControl extends React.Component {
  constructor(props) {
    super()
    this.state = {}
  }

  enlargeClick = () => {
    const { onResize, size = 1 } = this.props
    const newSize = Math.min(2, size + 0.25)
    onResize && onResize(newSize)
  }

  shrinkClick = () => {
    const { onResize, size = 1 } = this.props
    const newSize = Math.max(0.25, size - 0.25)
    onResize && onResize(newSize)
  }

  rotate = () => {
    const { onRotate } = this.props
    onRotate && onRotate()
  }

  print = () => {
    const { onPrint } = this.props
    onPrint && onPrint()
  }

  render() {
    const { className, size = 1, isControlShow, controlIcons } = this.props

    return (
      <div className={ClassNames(className, styles['resize-control'])}>
        <div
          className={ClassNames(styles['resize-control-icons'], {
            [styles.hide]: !isControlShow,
          })}
        >
          <div className={styles['resize-control-icon-box']} onClick={this.enlargeClick}>
            <TipWrapper className={styles['op-tip']} tipCls={styles.tip} tip='放大' direction="up">
              <i className={ClassNames('iconfont icon-fangda', styles['resize-control-icon'])} />
            </TipWrapper>
          </div>
          <div className={styles['resize-control-icon-box']} onClick={this.shrinkClick}>
            <TipWrapper className={styles['op-tip']} tipCls={styles.tip} tip='缩小' direction="up">
              <i className={ClassNames('iconfont icon-suoxiao', styles['resize-control-icon'])} />
            </TipWrapper>
          </div>
          <div className={styles['resize-control-icon-box']} onClick={this.rotate}>
            <TipWrapper className={styles['op-tip']} tipCls={styles.tip} tip='旋转' direction="up">
              <i className={ClassNames('iconfont icon-xuanzhuan', styles['resize-control-icon'])} />
            </TipWrapper>
          </div>
          <div className={styles['resize-control-icon-box']} onClick={this.print}>
            <TipWrapper className={styles['op-tip']} tipCls={styles.tip} tip='打印' direction="up">
              <i className={ClassNames('iconfont icon-dayin', styles['resize-control-icon'])} />
            </TipWrapper>
          </div>
          {!!controlIcons && <div className={styles.splitor} />}
          {controlIcons}
        </div>
      </div>
    )
  }
}
