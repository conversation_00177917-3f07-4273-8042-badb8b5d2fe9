.resize-control {
  position: absolute;
  bottom: 50px;
  left: 0;
  right: 0;
  box-sizing: border-box;
  // width: 130px;
  margin: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  vertical-align: middle;
  line-height: 0;
  pointer-events: none;

  .resize-control-icons {
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(38, 42, 48, 1);
    border-radius: 4px;
    opacity: 0.5;
    transition: opacity 0.25s;
    pointer-events: initial;
    &.hide {
      opacity: 0;
    }
    &:hover {
      opacity: 1;
    }
  }
  &-icon-box {
    display: inline-block;
    padding: 8px;
    vertical-align: middle;
    line-height: normal;
    cursor: pointer;
    .resize-control-icon {
      font-size: 24px;
      color: #fff;
    }
  }
  .splitor {
    width: 1px;
    align-self: stretch;
    background-color: #fff;
  }
  .op-tip {
    margin-right: 0;
    z-index: 2;
    .tip {
      white-space: nowrap;
    }
  }
}
