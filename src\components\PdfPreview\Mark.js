import React from 'react'
import styles from './Mark.scss'

/**
 *
 */
export default class Mark extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      isNameShow: false,
    }
  }
  mouseEnter = () => {
    if (this.timeout) {
      clearTimeout(this.timeout)
    }
    this.setState({
      isNameShow: true,
    })
  }
  mouseLeave = () => {
    this.timeout = setTimeout(() => {
      this.setState({
        isNameShow: false,
      })
    }, 3000)
  }
  handleDragStart = (e) => {
    e.preventDefault()
  }

  showMark = () => {
    this.setState({isNameShow: true}, () => {
      this.timeout = setTimeout(()=> {
        this.setState({isNameShow: false})
        clearTimeout(this.timeout)
      }, 3000)
    })
  }

  render() {
    const { data = {}, pageHeight, pageWidth, rotateDegree = 0 } = this.props
    const { isNameShow } = this.state
    const pageDistance = 20
    return (
      <div
        style={{
          left: data.x * pageWidth,
          top: data.y * pageHeight + (data.pageNo - 1) * (pageHeight + pageDistance),
          width: data.w * pageWidth,
          height: data.h * pageHeight,
          position: 'absolute',
          transform: `rotate(${rotateDegree}deg)`,
        }}
      >
        <img
          src={data.url}
          className={styles.img}
          onMouseEnter={this.mouseEnter}
          onMouseLeave={this.mouseLeave}
          onDragStart={this.handleDragStart}
        />
        {isNameShow && (
          <div className={styles.nameBox}>
            <div className={styles.name}>{data.uName}</div>
            <div className={styles.name}>{data.time}</div>
          </div>
        )}
      </div>
    )
  }
}
