import React from 'react'
import { Switch } from 'antd'
import PdfPreview from '../index'
import styles from './index.scss'

export default class PdfPreviewModal extends React.Component {
	constructor(props) {
		super(props)
		this.state = {
			isMarksShow: false,
			isShow: false
		}
	}

	switchChange = (checked) => {
		this.setState({
			isMarksShow: checked
		})
	}

	componentDidMount() {
		document.addEventListener(
			'click',
			() => {
				const { isShow } = this.state
				if (isShow) {
					this.setState({
						isShow: false
					})
				}
			},
			true
		)
	}
	showMoreClick = () => {
		this.setState({
			isShow: true
		})
	}

	render() {
		const { isMarksShow } = this.state
		const { extraBtns = null, isMaskShow: needShowMask = false, ...rest } = this.props
		return (
			<div>
				<PdfPreview isMarksShow={isMarksShow} {...rest} />
				{needShowMask && (
					<div className={styles['marks-control']}>
						<div>
							显示签批 <Switch checked={isMarksShow} onChange={this.switchChange} />
						</div>
						{extraBtns}
					</div>
				)}
			</div>
		)
	}
}
