import React from 'react'
import { Modal } from 'antd'
import PdfPreviewMarksControl from '../PdfPreviewMarksControl'

export default class PdfPreviewModal extends React.Component {
  constructor(props) {
    super(props)
  }

  close = () => {
    const { onClose } = this.props
    onClose && onClose()
  }

  render() {
    const { onClose, ...rest } = this.props
    const viewHeight = window.innerHeight - 175
    return (
      <Modal
        visible
        footer={false}
        title="预览"
        width={850}
        onCancel={this.close}
      >
        <PdfPreviewMarksControl {...rest} viewHeight={viewHeight} />
      </Modal>
    )
  }
}
