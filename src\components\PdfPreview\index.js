import React from 'react'
import ClassNames from 'classnames'
// import {Loading} from '@xm/util'
import {message,Spin} from 'antd'
import pdfjsLib from 'pdfjs-dist-show-signature/webpack'
import {getWaterMask, getWaterMarkConfig} from 'ROOT/utils'
import Mark from './Mark'
import IconsControl from './IconsControl'
import styles from './index.scss'


const INITIAL_WIDTH = 793
// eslint-disable-next-line no-unused-vars
const INITIAL_HEIGHT = INITIAL_WIDTH * 1.414

const RENDER_TYPE = {
  CANVAS: 'canvas',
  SVG: 'svg',
}

const ROTATE_DEGREES = [0, 90, 180, 270]

export default class PdfPreview extends React.Component {
 
  constructor(props) {
    super(props)
    this.state = {
      isShow: false,
      scale: 1,
      rotateIndex: 0,
      pageNum: 1,
      pageHeight: 0,
      waterMarkUrl: '',
      isWaterMaskShow: false,
      isControlShow: true,
      isScaleShow: false,
    }
  }

  componentDidMount() {
    // getWaterMarkConfig().then(({ isOpen, text }) => {
    //   this.setState({
    //     isWaterMaskShow: isOpen,
    //     waterMarkUrl: getWaterMask({ text }),
    //   })
    // })
    this.startHide()
    const { url } = this.props
    if (!url) return
    this.renderPdf(url)
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.url !== this.props.url && !!nextProps.url) {
      this.renderPdf(nextProps.url)
    }
    if (nextProps.lookMark && nextProps.lookMark.url && nextProps.lookMark.url !== this.props.lookMark.url) {
      const { pageHeight, scale, rotateIndex } = this.state
      const { marks } = this.props
      const pageIndex = Number(nextProps.lookMark.pageNo)
      const scrollTop = (pageIndex - 1) * pageHeight
      this.pagesContainer.parentNode.parentNode.scrollTop = scrollTop

      // eslint-disable-next-line no-restricted-syntax
      for (const i of [pageIndex]) {
        this.renderPage({
          pdfDocument: this.cachePdfDocument,
          pageIndex,
          scale,
          rotateDegree: ROTATE_DEGREES[rotateIndex],
          pageContainer: this.pagesContainer.children[pageIndex - 1],
        })
      }
      const index = marks.findIndex(x => x.url === nextProps.lookMark.url)
      // eslint-disable-next-line no-unused-expressions
      this[`marks${index}`] && this[`marks${index}`].showMark()
      // eslint-disable-next-line no-unused-expressions
      this.props.resetLookMark && this.props.resetLookMark()
    }
  }

  handleMouseMove = () => {
    if (!this.state.isControlShow) {
      this.setState({
        isControlShow: true,
      })
    }
    this.startHide()
  }

  startHide = () => {
    // eslint-disable-next-line no-unused-expressions
    this.timeout && clearTimeout(this.timeout)
    this.timeout = setTimeout(() => {
      this.setState({
        isControlShow: false,
      })
    }, 3000)
  }

  getPdfDocument = (url) => {
    if (this.cacheUrl === url) {
      return Promise.resolve(this.cachePdfDocument)
    }
    this.cacheUrl = url
    return pdfjsLib.getDocument({
      url,
      // cMapUrl: CMAPS_URL,
      cMapUrl: 'cmaps',
      cMapPacked: true,
    }).promise.then((pdfDocument) => {
      if (this.cacheUrl !== url) {
        throw new Error('url已改变，丢弃getDocument结果')
      } else {
        this.cachePdfDocument = pdfDocument
        return pdfDocument
      }
    })
  }

  renderPageByCanvas = ({ page, container, viewport, pageWidth, pageHeight, rotateDegree }) => {
    const canvas = document.createElement('canvas')
    canvas.width = rotateDegree === 90 || rotateDegree === 270 ? pageHeight : pageWidth
    canvas.height = rotateDegree === 90 || rotateDegree === 270 ? pageWidth : pageHeight
    container.textContent = ''
    container.appendChild(canvas)
    return page.render({
      canvasContext: canvas.getContext('2d'),
      viewport,
    })
  }

  renderPageBySvg = ({ page, container, viewport }) => {
    return page.getOperatorList().then((opList) => {
      const svgGfx = new pdfjsLib.SVGGraphics(page.commonObjs, page.objs)
      return svgGfx.getSVG(opList, viewport).then((svg) => {
        container.textContent = ''
        container.appendChild(svg)
      })
    })
  }

  renderPage = ({ pdfDocument, pageIndex, scale = 1, rotateDegree, type, pageContainer }) => {
    // pageIndex 从1 开始
    return pdfDocument.getPage(pageIndex).then((page) => {
      const { width, height } = page.getViewport({ scale: 1 })
      const viewportScale = (scale * INITIAL_WIDTH) / width
      const pageHeight = height * viewportScale

      const viewport = page.getViewport({
          scale: viewportScale,
          rotation: (rotateDegree + page.rotate) % 360,
        })
        const renderType = type || RENDER_TYPE.CANVAS
      // 部分pdf渲染翻转，无法解决，修改为canvas模式渲染
      // 正常pdf的width应该是594，对于正常的pdf，使用svg方式渲染，清晰度更高，如果宽度不正常，svg渲染可能会导致布局混乱，采用canvas
      // if (width > 600 || width < 590) {
      //   renderType = RENDER_TYPE.CANVAS
      // }

      const params = {
        page,
        viewport,
        container: pageContainer,
        pageWidth: scale * INITIAL_WIDTH,
        pageHeight,
        rotateDegree,
      }

      const renderMethods = {
        [RENDER_TYPE.SVG]: this.renderPageBySvg,
        [RENDER_TYPE.CANVAS]: this.renderPageByCanvas,
      }
      return renderMethods[renderType](params)
    })
  }

  printPdf = () => {
    message.info('下载准备中')
    this.getPdfDocument(this.cacheUrl).then(async (pdfDocument) => {
      const printContainer = document.createElement('printContainer')
      printContainer.id = 'printContainer'
      for (let i = 0, i1 = pdfDocument.numPages; i < i1; i++) {
        const pageContainer = document.createElement('div')
        printContainer.appendChild(pageContainer)
      }
      // 如果显示签批，需要添加签批
      if (this.marksBoxEl) {
        const el = this.marksBoxEl.cloneNode(true)
        el.style.cssText = `position: absolute;
                            top: 0;
                            bottom: 0;
                            left: 0;
                            right: 0;
                            overflow: visible;`
        // 去掉hover后显示的签批信息，只保留签批
        // eslint-disable-next-line no-restricted-syntax
        for(const child of el.children){
            for(let i=1;i<child.children.length;i++){
              child.removeChild(child.children[i])
            }
        }
        printContainer.appendChild(el)
      }
      const { width, height } = await pdfDocument.getPage(1).then((page) => {
        const { width, height } = page.getViewport(1)
        return { width, height }
      })
      const pageStyle = document.createElement('style')
      pageStyle.textContent =
        `${'@supports ((size:A4) and (size:1pt 1pt)) {' +
        '@page { size: '}${ 
        width 
        }pt ${ 
        height 
        }pt;}` +
        `}`
      Promise.all(
        new Array(pdfDocument.numPages).fill(1).map((it, index) => {
          return this.renderPage({
            pdfDocument,
            pageIndex: index + 1,
            pageContainer: printContainer.children[index],
          })
        }),
      ).then(() => {
        document.body.appendChild(printContainer)
        document.head.appendChild(pageStyle)
        const afterPrintHandler = () => {
          document.body.removeChild(printContainer)
          document.head.removeChild(pageStyle)
          window.removeEventListener('afterprint', afterPrintHandler)
        }
        window.addEventListener('afterprint', afterPrintHandler)
        setTimeout(() => {
          window.print()
        })
      })
    })
  }

  renderPdf = async (url) => {
    const isPdfChanged = this.cacheUrl !== url
    this.getPdfDocument(url).then((pdfDocument) => {
      if (isPdfChanged) {
        const {scrollEl} = this
        // eslint-disable-next-line no-unused-expressions
        scrollEl && (scrollEl.scrollTop = 0)
      }
      pdfDocument.getPage(1).then((page) => {
        const { scale, rotateIndex } = this.state
        const { width, height } = page.getViewport(1)
        const viewportScale = (scale * INITIAL_WIDTH) / width
        const pageNums = pdfDocument.numPages

        this.setState(
          {
            pageNum: pageNums,
            isShow: true,
            pageHeight: height * viewportScale,
          },
          () => {
            this.renderdPageIndexs = isPdfChanged ? [1] : this.renderdPageIndexs
            // eslint-disable-next-line no-restricted-syntax
            for (const i of this.renderdPageIndexs) {
              this.renderPage({
                pdfDocument,
                pageIndex: i,
                scale,
                rotateDegree: ROTATE_DEGREES[rotateIndex],
                pageContainer: this.pagesContainer.children[i - 1],
              })
            }
          },
        )
      })
    })
  }

  onResize = (scale) => {
    this.setState(
      {
        scale,
      },
      () => {
        this.showScalePercent()
        this.renderPdf(this.props.url)
      },
    )
  }

  showScalePercent = () => {
    this.setState(
      {
        isScaleShow: true,
      },
      () => {
        if (this.scaleTs) clearTimeout(this.scaleTs)
        this.scaleTs = setTimeout(() => {
          this.setState({
            isScaleShow: false,
          })
        }, 2000)
      },
    )
  }

  handleScroll = (e) => {
    const el = e.target
      const {scrollTop} = el
    const { scale, pageNum, pageHeight, rotateIndex } = this.state
    const pageIndex = Math.ceil(scrollTop / pageHeight) + 1
    const secondPageIndex = Math.ceil(scrollTop / pageHeight + 1 / 2) + 1
    const {renderdPageIndexs} = this
    const pageIndexes = [pageIndex, secondPageIndex]
      .filter((i) => i <= pageNum)
      .filter((i) => !renderdPageIndexs.includes(i))
    if (!pageIndexes.length) return
    this.renderdPageIndexs = [...renderdPageIndexs, ...pageIndexes]
    // eslint-disable-next-line no-restricted-syntax
    for (const i of pageIndexes) {
      this.renderPage({
        pdfDocument: this.cachePdfDocument,
        pageIndex: i,
        scale,
        rotateDegree: ROTATE_DEGREES[rotateIndex],
        pageContainer: this.pagesContainer.children[i - 1],
      })
    }
  }

  rotate = () => {
    const { rotateIndex } = this.state
    const newRotateIndex = (rotateIndex + 1) % ROTATE_DEGREES.length
    this.setState(
      {
        rotateIndex: newRotateIndex,
      },
      () => {
        this.renderPdf(this.props.url)
      },
    )
  }

  render() {
    const {
      isShow,
      scale,
      pageNum,
      pageHeight: originalPageHeight,
      waterMarkUrl,
      isControlShow,
      isWaterMaskShow,
      rotateIndex,
      isScaleShow,
    } = this.state
    const {
      marks = [],
      isMarksShow,
      viewHeight = window.innerHeight - 200,
      style = {},
      className,
      controlIcons,
    } = this.props
    const pages = new Array(pageNum).fill(1)

    const originalPageWidth = INITIAL_WIDTH * scale
    const transformWidthHeight = rotateIndex === 1 || rotateIndex === 3
    const pageWidth = transformWidthHeight ? originalPageHeight : originalPageWidth
    const pageHeight = transformWidthHeight ? originalPageWidth : originalPageHeight

    return (
      <div style={{ ...style }} className={className}>
        <div
          className={ClassNames(styles.preview)}
          style={{
            display: isShow ? 'block' : 'none',
          }}
          onMouseMove={this.handleMouseMove}
        >
          {/* {isWaterMaskShow && (
            <div
              className={styles['water-mark']}
              style={{ backgroundImage: `url(${waterMarkUrl})` }}
             />
          )}
          <div
            className={ClassNames(styles['scale-percent'], {
              [styles['scale-percent_hide']]: !isScaleShow,
            })}
          >
            {scale * 100}%
          </div> */}
          <IconsControl
            isControlShow={isControlShow}
            size={scale}
            controlIcons={controlIcons}
            onResize={this.onResize}
            onRotate={this.rotate}
            onPrint={this.printPdf}
          />
          <div
            // eslint-disable-next-line no-return-assign
            ref={(el) => (this.scrollEl = el)}
            className={styles['preview-content']}
            style={{
              height: viewHeight,
            }}
            onScroll={this.handleScroll}
          >
            <div className={styles['pages-container']}>
              <div ref={(el) => (this.pagesContainer = el)}>
                {pages.map((page, index) => (
                  <div
                    key={index}
                    className={styles.page}
                    style={{
                      width: pageWidth,
                      height: pageHeight,
                    }}
                   />
                ))}
              </div>
              {isMarksShow && (
                <div className={styles.marksBox} ref={(el) => (this.marksBoxEl = el)}>
                  {marks.map((item, i) => (
                    <Mark
                      ref={(instance) => this[`marks${i}`] = instance}
                      key={i}
                      data={item}
                      pageWidth={pageWidth}
                      pageHeight={pageHeight}
                      rotateDegree={ROTATE_DEGREES[rotateIndex]}
                    />
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
        {!isShow && (
          <div style={{ height: viewHeight, width: INITIAL_WIDTH, margin: 'auto' }}>
            <Spin
              // type="dot"
              // isStatic
              style={{
                position: 'relative',
                top: '50%',
                left: '50%',
                margin: '-13px 0 0 -10px',
              }}
             />
          </div>
        )}
      </div>
    )
  }
}