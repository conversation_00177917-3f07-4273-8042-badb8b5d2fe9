:global {
  // 用于打印样式
  #printContainer {
    display: none;
    height: 100%;
  }
  #printContainer > div {
    // 从pdfjs抄过来的样式
    position: relative;
    top: 0;
    left: 0;
    width: 1px;
    height: 1px;
    overflow: visible;
    page-break-after: always;
    page-break-inside: avoid;
  }
  #printContainer canvas {
    display: block;
  }
  @media print {
    @page {
      margin-bottom: 0mm;
      margin-top: 0mm;
    }
    html,
    body {
      height: 100%;
    }
    body {
      background-color: #000;
    }
    body > * {
      display: none !important;
    }
    #printContainer {
      display: block !important;
    }
  }
}
.preview {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  .water-mark {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: repeat;
    z-index: 1;
    pointer-events: none;
  }
  .scale-percent {
    position: absolute;
    left: 0;
    bottom: 0;
    right: 0;
    top: 0;
    bottom: 0;
    box-sizing: border-box;
    width: 74px;
    height: 40px;
    line-height: 22px;
    margin: auto;
    padding: 9px 16px;
    z-index: 1;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    color: #fff;
    pointer-events: none;
    transition: opacity 0.25s;
    text-align: center;
    &_hide {
      opacity: 0;
    }
  }
  &-content {
    overflow: auto;
    display: flex;
    justify-content: center;
    .pages-container {
      position: relative;
      background: #fff;
      .page {
        margin: auto;
        box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
        background-color: #fff;
        transition: all 0.15s;
      }
      canvas {
        margin-bottom: 20px;
        transform-origin: 0 0;
        &:last-child {
          margin-bottom: 0;
        }
      }
      .marksBox {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        // overflow: hidden;
        // pointer-events: none;
      }
    }
  }
}
