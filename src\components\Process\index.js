import React, {useRef, useEffect} from 'react';

const { Buttons:B, LabelLogList:S } = window.process
export function Buttons(props) {
  const container = useRef()
  const btns = useRef()
  useEffect(() => {
    btns.current = new B({
      target: container.current,
      props,
    })
  }, [])
  useEffect(() => {
    btns.current.$set(props)
  }, [props])
  return <div ref={container} />
}
export function Steps(props) {
  const container = useRef()
  useEffect(() => {
    new S({
      target: container.current,
      props,
    })
  }, [])
  return <div ref={container} />
}