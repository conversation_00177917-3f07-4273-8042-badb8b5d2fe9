export default pubsub = {
  _handler: {},
  $on: (type, callback, target) => {
    this._handler[type] = this._handler[type] || []
    this._handler[type].push({
      callback,
      target,
      once: false,
    })
  },
  $off: (type, callback, target) => {
    if (this._handler[type] && this._handler[type].length !== 0) {
      for (let i = this._handler[type].length - 1; i >= 0; i--) {
        if (callback === this._handler[type][i].callback && target === this._handler[type][i].target) {
          this._handler[type].splice(i, 1)
        }
      }
    }
  },
  $emit: (type, params) => {
    if (this._handler[type] && this._handler[type].length !== 0) {
      for (let i = this._handler[type].length - 1; i >= 0; i--) {
        this._handler[type][i].callback.callback(this._handler[type][i].target, params)
        if (this._handler[type][i].once) {
          this._handler[type].splice(i, 1)
        }
      }
    }
  },
}