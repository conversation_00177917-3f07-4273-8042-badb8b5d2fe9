import React from 'react'
import SchemaForm, { FormButtonGroup, Submit, Reset } from '@formily/antd'
import { css } from 'emotion'

import { noop } from 'ROOT/utils'

const Search = ({ schema, onChange = noop, onSubmit = noop, onReset = noop, defaultValue = {}, layout = 'inline', components = {} }) => {
	return (
		<SchemaForm
			layout={layout}
			schema={{
				type: 'object',
				properties: schema
			}}
			onSubmit={onSubmit}
			onReset={onReset}
			onChange={onChange}
			defaultValue={defaultValue}
			onChange={onChange}
			components={components}
		>
			<FormButtonGroup className={css`display: inline-block; vertical-align: middle; line-height: 40px;`}>
				<Submit>搜索</Submit>
				<Reset>重置</Reset>
			</FormButtonGroup>
		</SchemaForm>
	)
}

export default Search
