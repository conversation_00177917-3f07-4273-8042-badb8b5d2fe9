import React, { useState, useEffect, useCallback } from 'react'
import { connect } from 'react-redux'
import { Modal, Input, message, AutoComplete, Tree } from 'antd'
import Icon, { SearchOutlined }  from '@ant-design/icons'
import ClassNames from 'classnames'
import { debounce } from 'lodash'
import styles from './index.scss'

const { TreeNode } = Tree
const { Option } = AutoComplete
const SecretSelectUser = props => {
  const {
    visible,
    onCancelUser,
    onSelectUser,
    selectTreeNodeList,
    allSelectList,
    userList,
    emptyText,
    orgId,
  } = props

  const [allOrgData, setAllOrgData] = useState([])
  const [allOrgSearchData, setAllOrgSearchData] = useState([])
  const [checkedKeys, setCheckedKeys] = useState([])
  const [selectUsers, setSelectUsers] = useState([])
  const [OrgUserList, setOrgUserList] = useState([])
  const [searchValue, setSearchValue] = useState('')
  const [count, setCount] = useState(500)
  const [expandedKeys, setExpandedKeys] = useState([])
  const [autoExpandParent, setAutoExpandParent] = useState(true)
  // const [selectedKeys, setSelectedKeys] = useState([])
  // const [searchList, setSearchList] = useState([])
  const onExpand = expandedKeysValue => {
    console.log(expandedKeysValue)
    setExpandedKeys(expandedKeysValue)
    setAutoExpandParent(false)
  }

  const onCheck = checkedKeysValue => {
    const { checked } = checkedKeysValue

    if (checked.length > count) {
      message.error(`最多可以选中${count}公司和部门，请取消选中或删除后重试`)
      setCheckedKeys(checked.slice(0, -1))
    } else {
      setCheckedKeys(checked)
      const selectKeys = []
      for (let i = 0; i < checked.length; i++) {
        const selectKeysItem = allOrgData.find(item => item.symbolId == checked[i])
        if (selectKeysItem) {
          selectKeys.push(selectKeysItem)
        }
        // selectKeys.push(allOrgData.find((item) => item.symbolId == checked[i]))
      }
      setSelectUsers(selectKeys)
    }
  }
  // const onSelect = (selectedKeysValue) => {
  //   console.log('onSelect', selectedKeysValue)
  //   // setSelectedKeys(selectedKeysValue)
  // }
  const initState = () => {
    setCheckedKeys([])
    setSelectUsers([])
    setExpandedKeys([])
    setSearchValue('')
  }
  const handleCancel = () => {
    onCancelUser && onCancelUser()
  }
  const handleOk = () => {
    onSelectUser && onSelectUser(selectUsers)
  }
  const handleDeleteUser = symbolId => {
    const newSelect = checkedKeys.filter(i => i != symbolId)
    const delSelectusers = selectUsers.filter(d => d.symbolId != symbolId)

    setCheckedKeys(newSelect)
    setSelectUsers(delSelectusers)
  }
  const handleDelAllUser = () => {
    initState()
    setAutoExpandParent(true)
  }
  const initOrgListData = () => {
    console.log(props)
    setOrgUserList(selectTreeNodeList)
    setAllOrgData(allSelectList)
    setAllOrgSearchData(allSelectList)
    setSelectUsers(userList)
    setSearchValue('')
    setCheckedKeys(userList.map(user => user.symbolId))
  }
  const handleRenderOrgTree = list => {
    if (Array.isArray(list) && list.length > 0) {
      return list.map(l => {
        return (
          <TreeNode
            checkable={l.level !== 1}
            key={l.symbolId}
            icon={
              l.level === 2 ? (
                <span
                  className={ClassNames('iconfont icon-CombinedShape1', styles['tree-icon'])}
                  style={{ color: '#ffa400' }}
                />
              ) : (
                <span
                  className={ClassNames('iconfont icon-qiye1', styles['tree-icon'])}
                  style={{ color: l.level < 1 ? '#4f84d2' : '#23c172' }}
                />
              )
            }
            title={
              <span>
                <span>{l.userName}</span>
                {l.level == 1 ? '' : <span>({l.deptName})</span>}
              </span>
            }
          >
            {l.children && l.children.length > 0 && handleRenderOrgTree(l.children)}
          </TreeNode>
        )
      })
    }
    return []
  }

  const handleSearchChange = useCallback(
    debounce(val => {
      const searchListData = allOrgData.filter(v => v.userName.includes(val))
      setAllOrgSearchData(searchListData)
    }, 500),
    [allOrgData],
  )
  const handleValueChange = val => {
    setSearchValue(val)
  }
  const handleSearchSelect = selectKey => {
    const searchListItem = allOrgData.find(v => v.symbolId == selectKey)
    const isHas = selectUsers.find(v => v.symbolId == selectKey)
    setSearchValue(searchListItem.userName)
    if (!isHas) {
      setCheckedKeys(oldData => [...oldData, searchListItem.symbolId])
      setSelectUsers(oldData => [...oldData, searchListItem])
      setExpandedKeys(oldData => [...oldData, searchListItem.symbolId])
      setAutoExpandParent(true)
    }
  }
  const renderOption = item => {
    return (
      <Option key={item.symbolId} text={item.userName}>
        <div className="global-search-item">
          <span className="global-search-item-desc">{item.userName}</span>
        </div>
      </Option>
    )
  }
  useEffect(() => {
    if (visible) {
      initOrgListData()
    }
  }, [visible])
  useEffect(() => {
    if (userList.length === 0) {
      initState()
    }
  }, [userList])
  return (
    <Modal
      title="选择组织"
      visible={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      width="750px"
      okText="确认"
      cancelText="取消"
      bodyStyle={{ padding: 0 }}
    >
      <div className={styles['selelct-warp']}>
        <div className={styles.tree}>
          <div className={styles.search}>
            <AutoComplete
              className={styles['global-search']}
              style={{ width: '100%' }}
              dataSource={allOrgSearchData.map(renderOption)}
              onChange={handleValueChange}
              onSelect={handleSearchSelect}
              onSearch={handleSearchChange}
              placeholder="搜索"
              value={searchValue}
              optionLabelProp="text"
              notFoundContent="没有搜索到相关内容"
              getPopupContainer={triggerNode => triggerNode.parentNode}
            >
              <Input allowClear suffix={<Icon component={SearchOutlined} className="certain-category-icon" />} />
            </AutoComplete>
          </div>
          <Tree
            className={styles['user-tree']}
            checkable
            showIcon
            checkStrictly
            selectable
            onExpand={onExpand}
            expandedKeys={expandedKeys}
            autoExpandParent={autoExpandParent}
            onCheck={onCheck}
            checkedKeys={checkedKeys}
            // onSelect={onSelect}
            // selectedKeys={selectedKeys}
          >
            {OrgUserList.length > 0 ? handleRenderOrgTree(OrgUserList) : null}
          </Tree>
        </div>
        <div className={styles.users}>
          <div className={styles['user-top']}>
            <div className="text">已选择的：</div>
            <div className={styles['all-clear']} onClick={handleDelAllUser}>
              清除
            </div>
          </div>
          <div className={styles['user-list']}>
            {selectUsers.length > 0 &&
              selectUsers.map(item => (
                <div className={styles['user-item']} key={item.symbolId}>
                  {item.level === 2 ? (
                    <span
                      className={ClassNames('iconfont icon-CombinedShape1', styles['user-icon'])}
                      style={{ color: '#ffa400' }}
                    />
                  ) : (
                    <span
                      className={ClassNames('iconfont icon-qiye1', styles['user-icon'])}
                      style={{ color: item.level < 1 ? '#4f84d2' : '#23c172' }}
                    />
                  )}
                  <div className={styles['user-info']}>
                    <div className={styles.name}>{item.userName}</div>
                    <div className={styles.name}>{item.deptName}</div>
                  </div>
                  <span
                    className={ClassNames('iconfont icon-shanchu1', styles.close)}
                    onClick={() => {
                      handleDeleteUser(item.symbolId)
                    }}
                  />
                </div>
              ))}
            {selectUsers.length === 0 && <div className={styles.empty}>{emptyText}</div>}
          </div>
        </div>
      </div>
    </Modal>
  )
}
{
  /* export default connect((state) => ({
  loginUserData: state.loginUserData,
  orgId: state.orgData.currentOrgId,
  orgName: state.orgData.currentOrgName,
}))(SecretSelectUser) */
}

export default SecretSelectUser
