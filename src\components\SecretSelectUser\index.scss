.selelct-warp {
  width: 100%;
  display: flex;

  .users {
    border-left: 1px solid #E9ECF0;
  }

  .user-tree {
    width: 100%;
    height: 625px;
    overflow-y: scroll;
  }

  .tree {
    overflow: hidden;
    flex: 7;
    box-sizing: border-box;
    padding: 0 24px;
  }

  .users {
    flex: 5;
    padding: 0 24px;

    .user-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 40px;
      font-size: 12px;

      .all-clear {
        font-size: 14px;
        cursor: pointer;
        color: #5577CC;
      }
    }

    .user-list {
      width: 100%;
      height: 650px;
      overflow-y: scroll;
    }

    .empty {
      margin-top: 170px;
      font-size: 14px;
      color: #5C626B;
      text-align: center;
    }

    .user-item {
      // height: 50px;
      box-sizing: border-box;
      padding: 2px 0px;
      display: flex;
      align-items: center;

      .user-icon {
        margin-right: 12px;
        font-size: 20px
      }

      .user-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        // height: 28px;
        justify-content: center;

        .name {
          font-size: 14px;
          color: #262A30;
          line-height: 1.2;
        }

        .parent-name {
          font-size: 12px;
          color: #959BA3;
          line-height: 1.2;
        }


      }
    }

    .close {
      font-size: 14px;
      color: #999999;
      margin-right: 20px;
      margin-left: 5px;
      cursor: pointer;
    }


  }

  .search {
    margin: 15px 0px;
  }

  :global {
    .ant-tree-switcher-icon {
      font-size: 16px !important;
    }

  }
}