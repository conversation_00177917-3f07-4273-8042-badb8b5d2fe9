import React from 'react'
import ClassNames from 'classnames'
import { getFileSize } from 'ROOT/utils'
// import { FileIcon } from '@xm/util'
import styles from './index.scss'

export default (props) => {
  const { fileList = [] } = props
  const onDelete = (index) => {
    const { onDelete } = props
    onDelete && onDelete(index)
  }
  return (
    <div>
      {fileList.map((it, index) => {
        return (
          <div key={index} className={styles.file}>
            <div className={styles['file-icon']} type={it.type} name={it.name} size={32} />
            <div className={styles['file-content']}>
              <div className={styles['file-name']}>{it.name}</div>
              <div className={styles['file-size']}>{getFileSize(it.size)}</div>
            </div>
            <i
              className={ClassNames('iconfont icon-close', styles['file-delete'])}
              onClick={onDelete.bind(null, index)}
            />
          </div>
        )
      })}
    </div>
  )
}
