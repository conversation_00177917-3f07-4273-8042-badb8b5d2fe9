@import '~ROOT/styles/theme.scss';

.file {
  display: flex;
  padding: 8px 12px;
  align-items: center;
  overflow: hidden;
  box-shadow: inset 0px -1px 0px 0px rgba(0, 0, 0, 0.1);
  &:last-child{
    box-shadow: none;
  }
  .file-icon {
    margin-right: 8px;
  }
  .file-content {
    line-height: 1;
    flex-grow: 1;
    overflow: hidden;
    .file-name {
      @include ellipsis();
    }
    .file-size {
      margin-top: 6px;
      color: #999999;
    }
  }
  .file-delete {
    margin-left: 20px;
    font-size: 15px;
    cursor: pointer;
  }
}
