import React from 'react'
// import ClassNames from 'classnames'
// import Uploader from '../../../Uploader'
import { Select } from 'antd'
import FileListSubmit from './FileListSubmit'
import styles from './index.scss'

const { Option } = Select

export default class TextaeraCommonExpression extends React.Component {
  constructor() {
    super()
    this.state = {
      isShow: true,
    }
  }

  componentWillUnmount() {
    this.setState({
      isShow: false,
    })
  }

  textClick = (item) => {
    const { onChange } = this.props
    onChange && onChange(item)
  }

  opinionChange = (e) => {
    const { onChange } = this.props
    onChange && onChange(e.target.value)
  }

  onUploadChange = (key, fileList) => {
    const { onFileListChange } = this.props
    onFileListChange && onFileListChange(fileList)
  }

  onFileDelete = (index) => {
    const { fileList, onFileListChange } = this.props
    const newList = [...fileList]
    newList.splice(index, 1)
    onFileListChange && onFileListChange(newList)
  }

  render() {
    const {
      value,
      phrases,
      onChange,
      isFileListShow,
      fileList = [],
      onFileListChange,
      showPhrase = false,
      ...rest
    } = this.props
    const { isShow } = this.state
    return (
      <div className={styles['textarea-common-expression']}>
        <div className={styles['opinion-box']}>
          <textarea
            value={value}
            className={styles.textarea}
            onChange={this.opinionChange}
            {...rest}
          />
          {/* {isFileListShow && isShow && (
            <div
              className={ClassNames(styles['opinion-add-attach'], {
                [styles['opinion-add-attach_bottomline']]: !!fileList.length && fileList.length < 5,
              })}
            >
              <Uploader
                max={10}
                maxFileSize={20}
                list={fileList}
                uploadTip={''}
                crossDomain={false}
                multi={false}
                showFileList={false}
                onChange={this.onUploadChange}
              >
                <span>
                  <i className={ClassNames('iconfont icon-fujian', styles['attach-icon'])} />
                  <span className={styles['attach-text']}>添加附件</span>
                </span>
              </Uploader>
            </div>
          )} */}
          {/* <FileListSubmit fileList={fileList} onDelete={this.onFileDelete} /> */}
        </div>
        {/*  快捷语 */}

        {showPhrase && (
          <div className={styles['phrase-box']} style={{ marginTop: '10px' }}>
            <Select
              placeholder="请选择批语"
              style={{ width: '100%' }}
              value={undefined}
              onChange={this.textClick}
            >
              {phrases.map((item) => {
                return (
                  <Option
                    key={item.id}
                    className={styles.phrase}
                    value={item.comment}
                  >
                    {item.comment}
                  </Option>
                )
              })}
            </Select>
          </div>
        )}
      </div>
    )
  }
}
