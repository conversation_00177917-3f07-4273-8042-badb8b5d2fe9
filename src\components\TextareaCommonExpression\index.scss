.textarea-common-expression {
  display: inline-block;
  width: 100%;

  .opinion-box {
    border-radius: 4px;
    border: 1px solid rgba(233, 236, 240, 1);
  }

  .opinion-add-attach {
    line-height: 29px;
    color: #488ff9;

    &_bottomline {
      border-bottom: 1px solid #e9ecf0;
    }

    .attach-icon {
      margin-left: 9px;
      margin-right: 7px;
      font-size: 18px;
      cursor: pointer;
    }

    .attach-text {
      cursor: pointer;
    }
  }

  .textarea {
    margin-left: 0;
    box-sizing: border-box;
    width: 304px;
    height: 96px;
    padding: 0.5em;
    border-radius: 2px;
    overflow: auto;
    resize: none;
    border: none;
    outline: none;
    line-height: 20px;

    &::-webkit-input-placeholder {
      color: #cbcfd6;
    }
  }

  .phrase-box {
    // margin: 0 -8px;
    // margin-top: 5px;
    margin-bottom: 25px;

    .phrase {
      display: inline-block;
      margin: 0 8px 8px;
      padding: 4px 8px;
      border: 1px solid #cbcfd6;
      color: #5c626b;
      font-size: 12px;
      line-height: 14px;
      border-radius: 2px;
      cursor: pointer;
    }
  }
  .phrase-select {
    width: 100%;
  }
}
