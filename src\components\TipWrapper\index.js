import React from 'react'
import ClassNames from 'classnames'
import styles from './index.scss'

export default function TipWrapper(props) {
  const { className, tipCls, tip = '', children, direction = 'bottom' } = props

  return (
    <span className={ClassNames(styles['tip-box'], className)}>
      {children}
      <div className={ClassNames(styles.tip, styles[`tip_${  direction}`], tipCls)}>
        <div className={styles.arrow} />
        <span>{tip}</span>
      </div>
    </span>
  )
}
