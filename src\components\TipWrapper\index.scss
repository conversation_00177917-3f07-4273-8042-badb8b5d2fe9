@import 'ROOT/styles/theme.scss';

$borderWidth: 5px;

.tip-box {
  margin-right: 16px;
  position: relative;
  display: inline-block;
  line-height: normal;
  &:hover {
    .tip {
      display: initial;
    }
  }
  .tip {
    display: none;
    position: absolute;
    line-height: 17px;
    background: #5c626b;
    color: #fff;
    padding: 5px;
    box-sizing: border-box;
    white-space: pre-line;
    border-radius: 4px;
    z-index: 2;
    &_bottom {
      top: calc(100% + #{2 * $borderWidth});
      left: 50%;
      transform: translate(-50%, 0);
      .arrow {
        left: 50%;
        top: 0;
        transform: translate(-50%, -100%);
        border-color: transparent transparent #5c626b transparent;
      }
    }
    &_right {
      left: calc(100% + #{2 * $borderWidth} + 5px);
      top: 0;
      transform: translate(0, -25%);
      .arrow {
        top: 50%;
        left: 0;
        transform: translate(-100%, -50%);
        border-color: transparent #5c626b transparent transparent;
      }
    }
    &_left {
      right: calc(100% + #{2 * $borderWidth} + 5px);
      top: 0;
      transform: translate(0, -25%);
      .arrow {
        top: 50%;
        right: 0;
        transform: translate(100%, -50%);
        border-color: transparent transparent transparent #5c626b;
      }
    }
    &_up {
      bottom: calc(100% + #{2 * $borderWidth} + 5px);
      left: 50%;
      transform: translate(-50%, 0);
      .arrow {
        left: 50%;
        bottom: 0;
        transform: translate(-50%, 100%);
        border-color: #5c626b transparent transparent transparent;
      }
    }
    .arrow {
      position: absolute;
      border-width: 5px;
      border-style: solid;
    }
  }
}
