import moment from 'moment'

export const maxFilesNumber = 10

export const DEFAULT_PAGING_CONFIG = {
	defaultPageSize: 10,
	showTotal: (total) => {
		return `共 ${total} 条`
	},
	simple: false,
	pageSizeOptions: ['10', '20', '50', '100'],
	showSizeChanger: true,
	showQuickJumper: true,
	total: 0,
};

// 上传通用接口地址
export const UPLOAD_URL = '/sfs/webUpload/file?fileType=1'
// 上传wps
export const UPLOAD_WPS_URL = '/baas-news/web/news/uploadAndConvert'

// 时间戳转换成时间格式 1633596224 => 2021/10/7 下午4:43
export const getLocalTime = (time) => {  
	if(!time) return undefined;
	return moment(time).format('YYYY-MM-DD HH:mm:ss')
}

// 时间戳转换成时间格式 1633596224 => 2021/10/7
export const getLocalTimeDay = (time) => {  
	if(!time) return undefined;
	return moment(time).format('YYYY-MM-DD')
}


export const formBizId = 'custom_form_key'

/**
 * 后台通用配置权限
 */
 export const COMMON_FILE_SETTING = {
  SUBMIT: 6,
  TRANSFER: 7,
  RECEIVE_READ: 15,
  SEND_FILE: 54,
}

/**
 * 根据编码获取密级人群
 */
 export const LEVEL_CODE = {
  BOSS: '公司领导',
  DEPT_BOSS: '部门总经理',
  DEPT_MANAGER: '部门经理',
  OFFICES_MANAGER: '室经理',
  STAFF: '普通员工',
}