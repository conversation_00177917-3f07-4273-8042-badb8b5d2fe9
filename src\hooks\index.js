import React, { useState, useEffect } from 'react'
import service from 'ROOT/service'

export const useGetTips = ({ meetingAategory, procKey, isMeeting, currentDept }) => {
  const [controlTipsInfo, setControlTipsInfo] = useState({})

  useEffect(() => {
    if (isMeeting && !meetingAategory) return
    console.log(currentDept, ' currentDept')
    if (currentDept && currentDept.value) {
      getTips()
    }
  }, [meetingAategory, isMeeting, currentDept])

  const getTips = async () => {
    const res = await service.getTips({
      deptId: currentDept && currentDept.value,
      type: meetingAategory,
      flowTemplateId: procKey,
    })
    if (res.success) {
      setControlTipsInfo(res.data)
    }
  }

  return { controlTipsInfo }
}
