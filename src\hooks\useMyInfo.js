import React, { useState, useEffect } from 'react'
import { message } from 'antd'
import Cookies from 'js-cookie'

import service from 'ROOT/service'

const useMyInfo = (params) => {
  const isSetStorage = params && params.isSetStorage ? params.isSetStorage : false // 兼容水印需要存储到localstorage读取
  const [myInfo, setMyInfo] = useState({})
  const userId = Cookies.get('userId')

  const fetchData = async () => {
    const res = await service.getMyself()
    if (res.success) {
      setMyInfo(res.data)
      if (isSetStorage) {
        localStorage.setItem('userInfo',JSON.stringify(res.data))
      }
    } else {
      message.error(res.msg || '网络错误')
    }
  }

  useEffect( () => {
    fetchData()
  }, [userId])

  return myInfo
}

export default useMyInfo