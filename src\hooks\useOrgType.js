import React, { useState, useEffect } from 'react'

import Cookies from 'js-cookie'
import api from 'ROOT/service'
import { xhrWrapper } from 'ROOT/utils'

const useOrgType = (orgId = Cookies.get('orgId')) => {
  // 单位类型，默认区公司，1:区公司，2:市公司，3:县公司，4:其他
  const [orgType, setOrgType] = useState(1)

  const getOrgType = async () => {
    const [, data] = await xhrWrapper(
      api.getCurrentOrgType({
        orgId,
      }),
    )

    setOrgType(data)
  }

  useEffect(() => {
    if (orgId) {
      getOrgType()
    }
  }, [orgId])

  return orgType
}

export default useOrgType
