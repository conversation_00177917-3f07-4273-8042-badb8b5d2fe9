import React, { useState, useEffect } from 'react'
import service from 'ROOT/service'

const useGetSigner = ({
  userTaskId,
  nodeName,
}) => {
  const [signer, setSigner] = useState('')

  useEffect(async () => {
    if (userTaskId) {
      const res = await service.getSigner({
        userTaskId,
      })
      if (res.success) {
        const log = res.data.procLogResponse
        const nodeList = log.actinsts
        let startNode = {}
        let shNode = {}
        nodeList.forEach(item => {
          // eslint-disable-next-line no-bitwise
          if (item.actinstName === '开始节点') {
            startNode = item.userTasks[item.userTasks.length - 1]
          }
          if (item.actinstName === '部门总经理审核') {
            shNode = item.userTasks[item.userTasks.length - 1]
          }
        })
        console.log('startNode',startNode)
        console.log('shNode', shNode)
        setSigner({
          signer: (shNode && shNode.assigneeName) || (startNode && startNode.assigneeName),
          date: nodeList.length > 0 ? nodeList[0].endTime : '',
          originData: res.data,
        })
      }
    }
  }, [])
  return signer
}

export default useGetSigner
