import React, { useState, useEffect } from 'react'
import service from 'ROOT/service'

const useGetSigner = ({
  userTaskId,
  nodeName,
  permissionNodeName
}) => {
  const [signer, setSigner] = useState('')

  useEffect(async () => {
    if (userTaskId) {
      const res = await service.getSigner({
        userTaskId,
      })
      if (res.success) {
        const log = res.data.procLogResponse
        const nodeArray = nodeName.split(',')
        const nodeList = log.actinsts.filter(item => nodeArray.includes(item.actinstName))
        const permissionNode = log.actinsts[log.actinsts.length - 1]
        let isShowPermissionBtn = false
        if(permissionNode.status !== 1 && permissionNode.actinstName === permissionNodeName) {
          isShowPermissionBtn = true
        }
        let assigneeList = []
        nodeList.forEach(item => {
          // eslint-disable-next-line no-bitwise
          const task = item.userTasks.filter(item => (item.flags & 1024) && item.taskDesc !== '退回任务')
          if (task && task.length > 0) {
            assigneeList = [...assigneeList, ...task]
          }
        })
        setSigner({
          signer: assigneeList.length > 0 ? assigneeList[assigneeList.length-1].assigneeName : '',
          date: nodeList.length > 0 ? nodeList[0].endTime : '',
          originData: res.data,
          isShowPermissionBtn,
        })
      }
    }
  }, [])
  return signer
}

export default useGetSigner
