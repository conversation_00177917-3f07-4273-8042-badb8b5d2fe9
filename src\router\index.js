import React, { Suspense, lazy } from 'react'
import { Route, Switch, Redirect, HashRouter } from 'react-router-dom'
import _ from 'lodash'

import Loading from 'ROOT/components/Loading'
import routerConfig from 'ROOT/router/config'

const Router = () => {
  const defaultRoute = routerConfig[0]

  const getDefaultRoutePath = (defaultRoute = {}, parentPath = '') => {
    const { path, children = [] } = defaultRoute

    const fullPath = [parentPath, path].join('')

    if (children.length === 0) {
      return fullPath
    }

    return getDefaultRoutePath(children[0], fullPath)
  }

  const renderRoute = (routes = [], parentPath = '') => {
    return routes.map(config => {
      const { key, path, children = [] } = config

      const fullPath = parentPath + path
      console.log('key',key,fullPath)
      const Component = lazy(() => import(`ROOT/views/${key}`))
      if (children.length === 0) {
        return <Route extra key={key} path={fullPath} component={Component} />
      }

      // 嵌套路由
      return (
        <Route extra={false} key={key} path={fullPath}>
          <Component>{renderRoute(children, fullPath)}</Component>
        </Route>
      )
    })
  }
  return (
    <Suspense fallback={<Loading />}>
      <HashRouter>
        <Switch>
          <Route
            exact
            path="/"
            component={() => <Redirect to={getDefaultRoutePath(defaultRoute)} />}
          />
          {renderRoute(routerConfig)}
        </Switch>
      </HashRouter>
    </Suspense>
  )
}

export default Router
