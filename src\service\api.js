export default {
  // 头部导航信息：adminType、loginName、root集团信息、子公司列表等
  GET_MYSELF: '/baas-easylabel/business/getOrgLinkPath', // '/baas-account/web/myself',
  QUERY_ORG_LINK_PATH: '/baas-easylabel/business/queryOrgLinkPath', // '/baas-account/web/myself',
  GET_ORGLINKPATH: '/baas-easylabel/business/getOrgLinkPathForDebug',
  // debug模式下可能要取非登录人的部门信息
  GET_ORG_LINK_PATH_FOR_DEBUG: '/baas-easylabel/business/getOrgLinkPathForDebug',
  SAVE_FORM: '/security-manage-platform/web/report/add', // 表单保存接口
  DELETE_FORM: '/security-manage-platform/web/report/delete',
  // SUBMIT_FORM: '',
  GET_SEND_SIGNER: '/baas-wf-portal/task/app/open', // 获取签发人
  GET_PERSONAL_INFO: '/baas-contact/web/process/user/getOne', // todo 接口待定
  GET_FORM_DATA: '/security-manage-platform/web/report/detail', // 获取回填数据
  UPDATE_FORM: '/security-manage-platform/web/report/update', // 表单更新接口
  GET_TEMPLATE: '/access/DocumentMoa/getDocumentBodyFileList', // 选择模块
  // GET_NODE_LIST: '', // 获取审批节点
  ADD_FEEDBACK: '/baas-wf-letter/web/feedback/add', // 增加反馈
  GET_FEEDBACK_LIST: '/baas-wf-letter/web/feedback/list', // 获取反馈列表
  CREATE_FEEDBACK: '/baas-wf-letter/web/feedback/createTask', // 保存定期反馈任务
  CHANGE_TOPDF: '/access/DocumentMoa/doc2pdf', // 转化为pdf
  GETLIST_DATA: '/access/DocumentMoa/getRefNoListByUser', // 获取发文文号部门数据
  AUTO_NUMBER: '/access/DocumentMoa/genRefNo', // 自动编号
  GET_HISTORY: '/access/DocumentMoa/getHisDocumentListByRefNo', // 查看历史
  IS_SHOW_FEEDBACK: '/baas-wf-letter/web/feedback/check', // 是否显示提交反馈按钮
  USER_LABEL: '/baas-wf-letter/web/userLabel/listUserLabels', // 用户标签
  GET_NUMBER_PREFIX: '/security-manage-platform/web/business/get/number', // 获取编号前缀
  GET_DISPATCH_INFO: '/baas-easylabel/business/batchGetPrincipal', // 获取分发弹框数据
  QUERY_DEPT_AUTH_GRANT_INFO: '/baas-wf-portal/agent/queryDeptAuthGrantInfo', // 获取分发人员的代理人信息
  GET_NEXT_NODE: '/baas-wf-portal/actinst/next', // 获取当前节点的下个可选节点列表
  GET_START_NEXT_NODE: '/baas-wf-portal/actinst/startnext', // 获取开始节点的下个可选节点列表
  GET_START_NEXT_COPY_NODE: '/baas-wf-portal/actinst/startnextCopy', // 获取开始节点的下个抄送节点
  COMPLETE_TASK: '/baas-wf-portal/exe/user/task/manual/complete', // 手动提交流程
  START_TASK: '/baas-wf-portal/exe/manual/start', // 启动流程实例
  GET_FLOWID: '/access/DocumentMoa/getDocumentFlowByType', // 获取flowid
  SAVE_DRAFT_URL: '/baas-todocenter/web/todo-task/pushBatch', // 保存草稿
  REVOKE_DRAFT_URL: '/baas-todocenter/web/todo-task/revokeBatch', // 删除草稿
  GET_LEVELCODELIST: '/baas-easylabel/labelmember/batchGetUsers', // 获取部门列表
  SEND_DOCUMENT: '/access/DocumentMoa/sendDocument', // 一键分发
  GETUSERLEVEL_CODE: '/baas-easylabel/business/getLabels', // 获取分发code
  CREATE_BUSSINESSCODE: '/access/DocumentMoa/createDocumentByBusiness', // 创建第一次分发
  GET_BUSSINESSCODE: '/access/DocumentMoa/getDocumentIdByBusiness', // 判断是否第一次分发
  GET_DEPARTMENTLIST: '/baas-easylabel/business/batchGetLeaders', // 获取部们列表
  GET_USEFULEXPRESSION: '/user-comment/baas-user-comment/web/list', // 获取常用语
  GET_DISTRIBUTE: '/baas-easylabel/business/checkUserBelong', // 获取是否有权限
  ORG_TYPE: '/baas-car/car/apply/orgType', // 获取经办人当前公司的类型和市级公司信息
  GRID_LIST: '/baas-car/car/apply/grid/list', // 县公司网格列表
  APPLY_CREATE: '/baas-car/car/apply/create', // 用车申请创建
  APPLY_EDIT: '/baas-car/car/apply/edit', // 用车申请修改
  APPLY_DETAIL: '/baas-car/car/apply/detail', // 用车申请明细查询(包含用车清单)
  APPLY_RECORD_DETAIL: '/baas-car/car/apply/record/edit', // 用车清单更新
  CAR_INFO_ADD: '/baas-car/car/info/add', // 创建车辆信息
  DRIVER_INFO_ADD: '/baas-car/driver/info/add', // 创建司机信息
  CAR_INFO_LIST: '/baas-car/car/info/list', // 车辆信息列表
  DRIVER_INFO_LIST: '/baas-car/driver/info/list', // 司机信息列表
  GET_INFO: '/baas-car/car/info/get', // 获取公司名称
  GET_PREVIEW_URL: '/doconline/tools/get_preview_url',
  GET_ORGTYPE: '/baas-easylabel/business/getOrgGroup', // 获取集团下所有区公司和市公司的orgId
  GET_DEPTS: '/baas-easylabel/orgDepts/get',
  GET_USER_LABELS: '/baas-car/car/apply/userLabels',
  GET_WPS_PARAGRAPH: '/baas-moa-document-split/web/documentSplit/upload', // 获取wps内容分段后的数据
  SAVE_PARAGRAPHDETAIL: '/security-manage-platform/web/paragraph/save', // 保存手动增加分段后的数据
  GET_PARAGRAPHDETAIL: '/security-manage-platform/web/paragraph/detail', // 获取手动增加分段后的数据
  GET_PARAGRAPHCONTENT: '/baas-moa-document-split/web/documentSplit/previewById', // 获取手动增加分段后具体的内容
  UPLOADFILE: '/sfs/webUpload/file?fileType=1',

  // 手写表单保存临时数据
  OA_PERMISSION_SAVE_DRAFT: '/baas-formdata/web/form/data/save',
  // 获取临时数据
  OA_PERMISSION_GET_DRAFT: '/baas-formdata/web/form/data/detail',
  // 协调函获取文件编码
  GET_SERIALNUMBER_URL: '/security-manage-platform/web/report/getSerialNumber',
  // 获取流程节点签发人
  SELECT_PROCINS_BYNAME_AND_PROCKEY: '/baas-wf-portal/task/selectProcinstByNameAndProcKey',
  // 判断流程中表单是否可编辑
  GET_EDITABLE_FORM: '/baas-wf-api/web/user/getUserMobile',
  // 获取orgId列表
  GET_ORGID_LIST: '/baas-easylabel/orgDepts/postOrgDeptExtInfo',
  // 工作协调函
  DOWN_FILE: '/baas-wf-letter/web/file/down',
  // 日志上报
  REPORT_DATA: '/baas-form/h5/log/save',
  SAVE_URL: '/access/WpsAgent/svUrl', // 保存wps保存url
  GET_URL: '/access/WpsAgent/getUrl', // 获取wps保存的url
  PARAGRAH_CLEAR: '/security-manage-platform/web/paragraph/clear', // 清空段落权限
  // 约谈审批
  SAVE_APPOINTMENT: '/security-manage-platform/web/appointment/save', // 约谈表单保存/更新
  GET_APPOINTMENT_INFO: '/security-manage-platform/web/appointment/info', // 约谈表单详情
  UPDATE_SUB_APPOINTMENT: '/security-manage-platform/web/appointment/sub/update', // 子表单更新
  GET_APPOINTMENT_MEMBER_INFO: '/security-manage-platform/web/appointment/member/info', // 一键分发节点人员拉取
  GET_APPOINTMENT_LIST: '/security-manage-platform/web/appointment/list', // 约谈表单列表搜索
  EXPORT_APPOINTMENT: '/security-manage-platform/web/appointment/export', // 约谈表单导出
  GET_ORG_BY_LNCODES: '/baas-easylabel/business/getOrgByLnCodes', // 根据标签查询标签下所有单位
  GET_DEPT_BY_LNCODES_AND_ORG: '/baas-easylabel/business/getDeptByLnCodesAndOrg', // 根据标签查询标签下某单位下的全部部门列表（包括子部门）
  GET_USERS_BY_LNCODES_AND_DEPT: '/baas-easylabel/business/getUsersByLnCodesAndDept', // 根据标签查询标签下在某部门下的所有人员信息
  GET_USER_DEF_LABELS: '/baas-easylabel/business/getUserDefLabels', // 查询某人员在某标签组 下的所有标签信息
  GET_USERS_WITH_LNGROUPS_BY_LNCODES: '/baas-easylabel/business/getUsersWithLnGroupsByLnCodes', // 查询标签下所有人员列表，以及人员在某标签组下的所有标签信息
  GET_CURRENT_ORGTYPE: '/baas-easylabel/business/getCurrentOrgType', // 查询当前公司的公司属性（区公司/市公司/县公司/其他）
  COMPLETE_MANUAL_TASK: '/baas-wf-portal/exe/user/task/manual/complete', // 将前两步获取的数据整理好后，当第三个接口的入参，调用接口
  GET_LABEL_NAME_BY_UIDS: '/baas-easylabel/business/getLabelNameByUids', // 根据人员uid查询人员标签名字信息
  GET_ORG_ALL_USER_BY_LNCODES: '/baas-easylabel/business/getOrgAllUserByLnCodes', // 根据组织和标签查询所有下级的标签人员
  CHECK_ROLE: '/security-manage-platform/web/appointment/checkRole', // 检查用户是否是【党委书记】
  // 报表收集
  GET_REPORT_DETAIL: '/baas-wf-api/report-collect/detail', // 获取主表单内容
  REPORT_EXPORT: '/baas-wf-api/report-collect/record/export', // 下载
  REPORT_QUERY_LIST: '/baas-wf-api/report-collect/record/query-list', // 查询列表
  REPORT_RECORD_SAVE: '/baas-wf-api/report-collect/record/update', // 保存列表数据
  REPORT_SERIAL_MAKE: '/baas-wf-api/report-collect/serial/make', // 补全编号
  GET_REPORT_SERIAL_TEMPLATE: '/baas-wf-api/report-collect/serial/template', // 获取编号模版
  REPORT_SAVE: '/baas-wf-api/report-collect/save', // 保存表单主页面信息
  QUERY_COLLECT_DATE: '/baas-wf-api/report-collect/record/query-collect-date', // 时间下拉
  BATCH_GET_CUSTOMER_FORM_PRINCIPAL: '/baas-easylabel/business/batchGetCustomerFormPrincipal', // 获取一键分发
  GET_UESRTASK: '/baas-wf-portal/task/getUserTask', // 获取一键分发
  LIST_USER_CHANGE_MEETING:
    '/security-manage-platform/web/meetingNotice/manage/listUserChangeMeeting', // 会议变更审批
  DETELE_REPORT: '/baas-wf-api/report-collect/delete', // 注销报表
  // 获取起草提醒
  GET_TIPS: '/baas-wf-api/quota/getTips',
  // 是否纳入管控
  SAVE_CONTROL: '/baas-wf-api/quota/saveControl',
  GET_ORG_BY_RESEARCH: '/baas-easylabel/orgDepts/getOrgByResearch', // 获取选人组件的数据
  SAVE_FILE_PERMISSION: '/security-manage-platform/web/permission/save', // 保存附件配置权限
  GET_FILE_PERMISSION: '/security-manage-platform/web/permission/query', // 查询附件配置的权限
  GET_READ_FILE: '/security-manage-platform/permission/ctrl', // 查询附件权限可见度，判断当前登录人可查看的附件

  GET_ROOM: '/security-manage-platform/web/meeting/appointmen/free/meeting/room', // 获取同步预约会议室列表
  CHECK_NEW_DATA: '/security-manage-platform/web/meeting/appointmen/check/new/data', // 此接口可以校验新旧数据的判别
  CONNECTION: '/security-manage-platform/web/meeting/appointmen/connection', // report和会议室的关联信息
  GET_MEETING: '/security-manage-platform/web/meeting/appointmen/booking/meeting', // 获取已预约会议列表
  CHECK_MEETING: '/security-manage-platform/web/meeting/appointmen/check/meeting', // 检查已预约会议是否已经被关联
  CHECK_ROOM: '/security-manage-platform/web/meeting/appointmen/check/meeting/room', // 校验同步预约会议室时间是否冲突已被预订
  ADD_MEETING: '/security-manage-platform/web/meeting/appointmen/add/reservation/meeting', // 添加或者修改预约会议选项
  EXPORT: '/security-manage-platform/web/meeting/entry/export', // 报名人员下载数据
  SIGN_LIST: '/security-manage-platform/web/meeting/sign/list', // 签到列表
  SIGN_EXPORT: '/security-manage-platform/web/meeting/sign/export', // 签到下载
  LIST_SEND: '/security-manage-platform/web/meetingNotice/manage/listSendMessageRecord', // 会务短信-查询会务短信发送记录
  SEND_MESSAGE: '/security-manage-platform/web/meetingNotice/manage/sendMessage', // 会务短信-会务短信发送接口

  GET_FEEDBACK_DAY: '/security-manage-platform/web/report/getFeedbackDay', // 查询反馈日期
  SAVEFEEDBACK: '/security-manage-platform/web/report/saveFeedback', // 新增/编辑反馈
  GET_FEEDBACK_DETAIL: '/security-manage-platform/web/report/getFeedbackDetail', // 查询反馈详情
  FEEDBACK_LIST: '/security-manage-platform/web/report/getFeedbackList', // 查询反馈内容
  EXPORT_FEEDBACK: '/security-manage-platform/web/report/exportFeedback', // 下载反馈内容

  GET_ONE_BY_ID: '/baas-admin/web/org/getOneById', // 获取上级orgId
  GET_CURRENT_ORG_TYPE: '/baas-easylabel/business/getCurrentOrgType', // 获取公司类型
  GET_ONE_USER: '/baas-contact/web/user/detail/getOneUser', // 获取用户的职位信息
  GET_MULTI_USER: '/baas-contact/web/user/orgUser/deptInfo', // 批量获取用户的职位信息
  GET_MEETING_SIGN_DETAIL: '/security-manage-platform/web/report/meetingSignDetail', // 会议签到消息内容详细查询
  GET_PROC_INS_INFO_OF_FORM: '/baas-wf-portal/procinst/getProcInstInfoOfForm', // 根据表单数据查询流程状态
  COORDINATE_LETTER: '/security-manage-platform/web/report/export/coordinate_letter', // 一键下载
  GET_MEETING_PLAN:'/security-manage-platform/web/meetingPlan/getPlan', //会议通知中获取会议计划
  //会议管理
  GET_MEETING_LIST:'/baas-wf-api/meeting/getMeetingList', // 获取会议列表
  GET_MEETING_DETAIL:'/baas-wf-api/meeting/getMeetingById', // 获取会议详情
  SAVE_MEETING:'/baas-wf-api/meeting/addMeeting', // 保存会议
  UPDATE_TOPICS_STATUS:'/baas-wf-api/meeting/updateTopicsStatus', // 修改议题状态
  DELETE_MEETING:'/baas-wf-api/meeting/deleteMeeting' // 删除会议

}
