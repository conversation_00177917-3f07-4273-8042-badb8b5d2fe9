import { message, Modal } from 'antd'
import axios from 'axios'
import _ from 'lodash'
import qs from 'qs';

const getLoginUrl = () => {
	return `${location.protocol}//${location.host}${_.get(window, '_APP_CONFIG.apiPrefix', '')}/login`
}

const getUrl = (url) => {
	return _.get(window, '_APP_CONFIG.apiPrefix', '') + url
}

const jumpToLogin = () => {
	// iframe 嵌入的场景
	if (top !== window) {
		// top.location.href = getLoginUrl()
	} else {
		// window.location.href = getLoginUrl()
	}
}

const checkSuccess = (url, data) => {
	const { success, retcode, code } = data

	if (/^\/aace/.test(url)) {
		// 注意：不要用全等
		return code === 0
	}

	return success === true || retcode === 0
}

const axiosWindowInstance = axios.create({ withCredentials: true })

// 避免拦截器多次绑定
if (typeof window.myInterceptor !== 'undefined') {
	axiosWindowInstance.interceptors.response.eject(window.myInterceptor)
}

window.myInterceptor = axiosWindowInstance.interceptors.response.use(
	(response) => {
		const { data } = response

		if (checkSuccess(response.config.url, data)) {
			return data
		}
		const msg = _.get(data, 'msg') || _.get(data, 'data.errMsg') || _.get(data, 'errors[0].detail')

		// -701 表示被踢出
		if (data.code === -701) {
			Modal.confirm({
				content: '您的账号已在其他设备上登录',
				cancelText: null,
				okText: '知道了',
				closable: false,
				onOk: () => jumpToLogin(),
			})
		} else if (data.code === -702 || data.retcode === 100) {
			// -702、100未登录
			jumpToLogin()
		} else if (msg && !response.config.hideMessage) {
			if(msg.indexOf('SecurityError')===-1){
				message.error(msg)
			}
		}

		return Promise.reject(data)
	},
	(error) => {
		message.error('网络错误')
		return Promise.reject(error)
	},
)

export default {
	get(url, param, extraParam = {}) {
		return axiosWindowInstance.get(getUrl(url), {
			params: { ...param, t: Date.now() },
		paramsSerializer: (params) => qs.stringify(params, { indices: false }),
			...extraParam,
		})
	},

	post(url, param, extraParam = {}) {
		return axiosWindowInstance.post(getUrl(url), param, extraParam)
	},
	req: axiosWindowInstance.request,
}
