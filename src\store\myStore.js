import _ from 'lodash'

import api from 'ROOT/service'
import { xhrWrapper } from 'ROOT/utils'

const myStore = {
	name: 'myStore',
	state: {
		loading: true,
		myInfo: {},
	},
	reducers: {
		setMyInfo(state, payload) {
			return { ...state, myInfo: payload }
		},
	},
	effects: (dispatch) => ({
		async getMyInfo() {
			const [ err, res ] = await xhrWrapper(api.getMyself())

			if (!err) {
				dispatch.myStore.setMyInfo(_.get(res, 'data', {}))
			}
		},
	}),
}

export default myStore
