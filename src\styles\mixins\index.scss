@mixin prefix($name, $v) {
  -webkit-#{$name}: $v;
  -moz-#{$name}: $v;
  -ms-#{$name}: $v;
  -o-#{$name}: $v;
  #{$name}: $v;
}

@mixin keyframes($animation-name) {
  @-webkit-keyframes #{$animation-name} {
    @content;
  }
  @-moz-keyframes #{$animation-name} {
    @content;
  }
  @-ms-keyframes #{$animation-name} {
    @content;
  }
  @-o-keyframes #{$animation-name} {
    @content;
  }
  @keyframes #{$animation-name} {
    @content;
  }
}

@mixin borderRadius($v) {
  border-radius: $v;
}

@mixin opacity($v) {
  opacity: $v;
  filter: alpha(opacity=$v * 100);
  -ms-filter: alpha(opacity=$v * 100);
}


@mixin posCenter($w, $h) {
  position: absolute;
  left: 50%;
  top: 50%;
  width: $w;
  height: $h;
  margin-left: -($w / 2);
  margin-top: -($h / 2);
}

@mixin ellipsis() {
  overflow: hidden;
  text-overflow: ellipsis;
  -ms-text-overflow: ellipsis;
  white-space: nowrap;
  word-break: keep-all;
}

@mixin clearfix() {
  *zoom: 1;
  &:after {
    content: '';
    display: block;
    width: 0;
    height: 0;
    clear: both;
    visibility: hidden;
  }
}


%link-normal {
  color: #999;
  outline: none;
  text-decoration: none;
  &:link, &:visited, &:hover, &:active {
    color: #999;
    outline: none;
  }
}

%layer-root {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 11;
}
%bg-layer {
  width: 100%;
  height: 100%;
  background-color: #000;
  @include opacity(0.5);
}
%close-layer-icon {
  cursor: pointer;
  font-size: 23px;
  position: absolute;
  top: 10px;
  right: 10px;
  color: #f8f8f8;
}