import moment from 'moment'
import Service from 'ROOT/service'
import bowser from 'bowser'
import axios from 'axios'
import { isUndefined } from 'lodash'
import API from 'ROOT/service/api'

export const xhrWrapper = promise => {
  return promise
    .then(data => {
      return [null, data]
    })
    .catch(err => [err, null])
}

export const noop = () => {}

export const formatDate = (date, format = 'YYYY-MM-DD HH:mm:ss') => {
  if (!date) return date

  return moment(date).format(format)
}

export const genId = length => {
  return Number(Math.random().toString() + Date.now())
    .toString(36)
    .substr(3, length)
}

export const getQueryString = (source = window.location.search) => {
  const str = {}
  source
    .substr(1)
    .split('&')
    .forEach(key => {
      const arr = key.split('=')
      Object.assign(str, {
        [arr[0]]: arr[1],
      })
    })
  return str
}
export const suffix = name => {
  const result = name.substring(name.lastIndexOf('.'), name.length)
  return result
}
// 根据文号数组返回文号字符串
export const getSerialNumStr = serialArr =>
  serialArr[0] || serialArr[1] || serialArr[2]
    ? `${serialArr[0] || ''}〔${serialArr[1] || ''}〕${serialArr[2] || ''}号`
    : '-'
export const getSerialNumStrFromStr = serialStr => {
  // const { value } = getFiled(data.fields, 'refNo')[0]
  // const refNo = JSON.parse(value)
  let refNo = ['', '', '']
  try {
    refNo = JSON.parse(serialStr)
  } catch (e) {
    console.error(`JSON.parse refNo ERROR ${serialStr}`)
  }
  return getSerialNumStr(refNo)
}
/**
 * 文件大小显示
 * @param {Number} size
 * @returns
 */
export const getFileSize = (size, toFixedNum = 2) => {
  if (!size) return '0 B'

  const s = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  let i = Math.floor(Math.log(size) / Math.log(s))

  if (i > sizes.length - 1) {
    i = sizes.length - 1
  }

  // eslint-disable-next-line no-restricted-properties
  size = (size / Math.pow(s, i)).toFixed(toFixedNum)

  if (!sizes[i] || !size) return '0 B'

  return size + sizes[i]
}

export const downloadFile = (url, fileName) => {
  const el = document.createElement('iframe')
  el.style = 'position:fixed;height:0;width:0;'

  if (/\/fcscloud/.test(url)) {
    el.src = url
  } else {
    el.src = `${url}&filename=${encodeURIComponent(fileName)}`
  }
  document.body.appendChild(el)

  setTimeout(() => {
    document.body.removeChild(el)
  }, 2000)
}

/**
 * 获取水印base64
 */
export const getWaterMask = config => {
  const defaultConfig = {
    text: '水印',
    fontSize: 20,
    rotate: -30,
    color: 'rgba(203, 207, 214, .5)',
  }
  const mergedConfig = {
    ...defaultConfig,
    ...config,
  }
  const { text, fontSize, rotate, color } = mergedConfig

  const $canvas = document.createElement('canvas')
  const ctx = $canvas.getContext('2d')

  ctx.font = `normal normal 100 ${fontSize}px MicrosoftYahei`

  const { width } = ctx.measureText(text)
  const height = fontSize
  const rotateAngle = (rotate * Math.PI) / 180
  let drawWidth = Math.abs(width * Math.cos(rotateAngle)) + fontSize
  let drawHeight = Math.abs(width * Math.sin(rotateAngle)) + 2 * fontSize

  drawWidth *= 1.2
  drawHeight *= 1.2

  $canvas.width = drawWidth
  $canvas.height = drawHeight
  ctx.fillStyle = color

  ctx.textBaseline = 'middle'
  ctx.textAlign = 'center'
  ctx.font = `normal normal 100 ${fontSize}px MicrosoftYahei`
  ctx.translate(drawWidth / 2, drawHeight / 2)
  ctx.rotate(rotateAngle)
  ctx.fillText(text, 0, 0)

  return $canvas.toDataURL()
}

let waterMarkPromise = null
export const reGetWaterMarkConfig = () => {
  waterMarkPromise = Service.getDocumentWatermark({
    orgId: 25280,
  })
}
export const getWaterMarkConfig = () => {
  if (!waterMarkPromise) {
    reGetWaterMarkConfig()
  }
  return waterMarkPromise.then(({ data }) => {
    const { openWatermark, type, customize } = data.watermake
    let text = ''
    if (openWatermark) {
      if (type === 1) {
        const userInfo = JSON.parse(localStorage.getItem('userInfo'))
        const { account, loginName } = userInfo
        text = loginName + String(account).slice(-4)
      } else {
        text = customize
      }
    }
    return {
      isOpen: openWatermark,
      text,
    }
  })
}

export const getBase64FromImgUrl = (url, type = 'png') => {
  return new Promise(resolve => {
    const image = new Image()
    image.crossOrigin = 'anonymous'
    image.onload = () => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      canvas.width = image.width
      canvas.height = image.height
      ctx.drawImage(image, 0, 0)
      resolve(canvas.toDataURL(`image/${type}`, 1))
    }
    image.src = url
  })
}

export const downloadUrl = (url, name) => {
  return /\/fcscloud/.test(url) ? url : `${url}&filename=${encodeURIComponent(name)}`
}

export const closeTheWindow = (search, props) => {
  const newPage = search ? getQueryString(search).newPage : ''
  if (newPage) {
    closeWindow()
    return
  }
  const backurl = search ? getQueryString(search).backurl : ''
  if (backurl) {
    window.top.location.href = decodeURIComponent(backurl)
  } else {
    // window.opener = null
    // window.open('', '_self')
    // window.close()
    closeWindow()
  }
}

/**
 * 检查操作系统
 *
 * @returns Win10 | Win7 | WinVista | Win2003 | WinXP | Win2000 | Linux | Unix | Mac
 */
export function detectOS() {
  // const sUserAgent = navigator.userAgent;

  const isWin = navigator.platform == 'Win32' || navigator.platform == 'Windows'
  const isMac =
    navigator.platform == 'Mac68K' ||
    navigator.platform == 'MacPPC' ||
    navigator.platform == 'Macintosh' ||
    navigator.platform == 'MacIntel'
  if (isMac) return 'Mac'
  const isUnix = navigator.platform == 'X11' && !isWin && !isMac
  if (isUnix) return 'Unix'
  const isLinux = String(navigator.platform).indexOf('Linux') > -1
  if (isLinux) return 'Linux'
  if (isWin) {
    return 'Windows'
  }
  return 'other'
}

// 是否是 window
export const isWindow = /^win/i.test(detectOS())

export const replaceHtmlString = text => {
  if (text) {
    return text.replace(/<\/?[A-Za-z]+(\s+[A-Za-z]+=".*")*>/g, '')
  }
  return text
}

export const isDeskTop = bowser.parse(window.navigator.userAgent).platform.type === 'desktop'

// 刷新当前页面
export const reloadWindow = () => {
  location.reload()
}
/**
 *  获取 对应字段
 *
 * @export
 * @param {object[]} [fields=[]]
 * @param {string} [fieldName='']
 * @returns
 */
export function getField(fields = [], fieldName = '') {
  let currentField = null
  let currentFieldIndex = -1
  if (fields.length === 0 || !fieldName) {
    return [currentField, currentFieldIndex]
  }

  currentFieldIndex = fields.findIndex(item => {
    return item.fieldName === fieldName
  })

  if (currentFieldIndex !== -1) {
    currentField = fields[currentFieldIndex]
  }

  return [currentField, currentFieldIndex]
}

export const FIELD_TYPE = {
  /**
   * 标题
   */
  TITLE: 'title',

  /**
   * 文号
   */
  REF_NO: 'refNo',

  /**
   * 外部来文文号
   */
  OUTSIDE_REF_NO: 'outsideRefNo',

  /**
   * 签批单
   */
  SIGN_FILE: 'signFile',

  /**
   * 正文
   */
  BODY_FILE: 'bodyFile',

  THEME_WORD: 'themeWord',

  /**
   * 公文类型
   */
  OFFICIAL_TYPE: 'officialType',

  /**
   * 紧急程度
   */
  URGENCY_LEVEL: 'urgencyLevel',

  /**
   * 密级
   */
  SECRET_CLASS: 'secretClass',

  SECRECY_TERM: 'secrecyTerm',

  /**
   * 发送日期
   */
  DISPATCH_DATE: 'dispatchDate',

  /**
   * 接收日期
   */
  RECEIPT_DATE: 'receiptDate',

  /**
   * 接收单位
   */
  DISPATCH_COMPANY: 'dispatchCompany',

  /**
   * 收文单位
   */
  RECEIPT_COMPANY: 'receiptCompany',

  /**
   * 主送
   */
  MAIN_SEND: 'mainSend',

  /**
   * 抄送
   */
  COPY_SEND: 'copySend',

  ISS_UER: 'issUer',
  PROOFREADER: 'proofreader',
  PRINTER: 'printer',
  IMPRESSION: 'impression',

  /**
   * 附件
   */
  ENCLOSURE: 'enclosure',

  /**
   * 收文文号
   */
  RECEIPT_REF_NO: 'receiptRefNo',

  /**
   * 行文类型
   */
  WRITING: 'writing',

  /**
   * 是否用章
   */
  USESEAL: 'useSeal',

  /**
   * 拟稿部门
   */
  FILE_DEPARTMENT: 'fileDepartment',

  /**
   * 起草人
   */
  CREAT_PERSON: 'creatPerson',

  /**
   * 创建时间/拟稿时间
   */
  CREAT_TIME: 'creatTime',

  /**
   * 参考信息
   */
  REFERENCE: 'reference',
}

export const loadScript = src => {
  const script = document.createElement('script')
  script.src = src
  script.async = false
  document.head.appendChild(script)
}

export const getHtmlCode = (url, callback) => {
  if (!url) return
  fetch(url)
    .then(response => response.blob())
    .then(blob => {
      const reader = new FileReader()
      reader.onload = () => {
        let htmlData = reader.result
        htmlData = new window.DOMParser().parseFromString(htmlData, 'text/html')
        // .replace(/<!--.*?-->/gs, '')
        callback(htmlData.body.innerHTML)
      }
      reader.readAsText(blob, 'GBK')
    })
}

export const emergencyLevel = {
  1: 2,
  2: 1,
  3: 0,
}

export function openDownloadDialog(url, saveName, isDirectly = false) {
  if (window.navigator.msSaveBlob) {
    try {
      // 创建XMLHttpRequest对象
      const xhr = new XMLHttpRequest()
      // 配置请求方式、请求地址以及是否同步
      xhr.open('GET', url, true)
      // 设置请求结果类型为blob
      xhr.responseType = 'blob'
      // 请求成功回调函数
      xhr.onload = function () {
        if (this.status === 200) {
          // 请求成功
          // 获取blob对象
          const blob = this.response
          // 获取blob对象地址，并把值赋给容器，注意saveName，不能为空，否则在IE环境下下载乱码
          if (saveName) {
            window.navigator.msSaveBlob(blob, encodeURIComponent(saveName))
          } else {
            window.navigator.msSaveBlob(blob)
          }
        }
      }
      xhr.send()
    } catch (e) {
      console.log(e)
    }
  } else {
    const fileName = saveName
    const el = document.createElement('iframe')
    el.style = 'position:fixed;height:0;width:0;'
    if (/\/fcscloud/.test(url) || isDirectly) {
      el.src = url
    } else {
      el.src = `${url}`
    }
    document.body.appendChild(el)
    setTimeout(() => {
      document.body.removeChild(el)
    }, 10000)
  }
}

export function patchData(values, details) {
  if (!values) return details
  Object.keys(details).forEach(key => {
    if (isUndefined(values[key])) {
      values[key] = details[key]
    }
  })
  return values
}

// 判断是否超出边界
export const inWindow = (left, top, startPosX, startPosY) => {
  const H = document.clientHeight
  const W = document.clientWidth
  if (
    (left < 50 && startPosX > left) ||
    (left > W - 50 && startPosX < left) ||
    (top < 50 && startPosY > top) ||
    (top > H - 50 && startPosY < top)
  ) {
    return false
  }
  return true
}

const throttle = (fn, wait) => {
  let timer = null
  return function () {
    const context = this
    const args = arguments
    if (!timer) {
      timer = setTimeout(() => {
        fn.apply(context, args)
        timer = null
      }, wait)
    }
  }
}

export const onMouseDown = (e, styleParams, callback) => {
  e.preventDefault()
  // 记录初始移动的鼠标位置
  const startPosX = e.clientX
  const startPosY = e.clientY
  const { styleLeft, styleTop } = styleParams

  const move = e => {
    const left = e.clientX - startPosX + styleLeft
    const top = e.clientY - startPosY + styleTop
    if (inWindow(e.clientX, e.clientY, startPosX, startPosY)) {
      callback(top, left)
    }
  }

  const leave = () => {
    document.onmousemove = null
  }

  // 添加鼠标移动事件
  document.onmousemove = throttle(move, 30)

  // 鼠标放开时去掉移动事件
  document.onmouseup = throttle(leave, 30)
}

export const safeParse = (str, initValue) => {
  let result = {}
  try {
    result = JSON.parse(str) || {}
  } catch (e) {
    result = initValue || {}
  }
  return result
}

export const closeWindow = () => {
  if (
    navigator.userAgent.indexOf('Firefox') !== -1 ||
    navigator.userAgent.indexOf('Chrome') !== -1
  ) {
    if (window.top) {
      window.top.close()
    } else {
      window.close()
    }
  } else {
    window.top.opener = null
    window.top.open('', '_self')
    window.top.close()
  }
}

export const downloadAsBlob = async (url, param = {}, method) => {
  const search = {
    method: method || 'post',
    url,
    responseType: 'blob',
  }

  if (method === 'get') {
    search.params = {
      ...param,
    }
  } else {
    search.data = {
      ...param,
    }
  }
  const axiosIns = axios.create({ withCredentials: true })
  const res = await axiosIns(search)
  return res
}

export const downloadBlob = res => {
  const { data } = res
  if (data) {
    // 处理文件名
    let fileName = ''
    if (!res.headers['content-disposition']) {
      // message.error('导出失败')
      return false
    }
    const attrs = res.headers['content-disposition'].split(';')
    for (let i = 0, l = attrs.length; i < l; i++) {
      const temp = attrs[i].split('=')
      if (temp.length > 1 && temp[0] === 'filename*') {
        fileName = temp[1].slice("utf-8''".length)
        break
      }
    }
    fileName = decodeURIComponent(fileName)
    // 获取数据类型
    const type = res.headers['content-type'].split(';')[0]
    const blob = new Blob([res.data], { type })
    if (navigator.msSaveBlob) {
      window.navigator.msSaveOrOpenBlob(blob, fileName)
    } else {
      const a = document.createElement('a')
      const blobUrl = window.URL.createObjectURL(blob)
      a.download = fileName
      a.href = blobUrl
      document.body.appendChild(a)
      a.click()
      URL.revokeObjectURL(blobUrl)
      document.body.removeChild(a)
    }
    return true
  }
}

export const oneKeyDownload = async reportId => {
  const res = await downloadAsBlob(
    API.COORDINATE_LETTER,
    {
      reportId,
    },
    'get',
  )

  if (res) {
    downloadBlob(res)
  }
}

/**
 * 批量发送请求
 * @param {Array<Function>} requests - 返回 Promise 的请求函数数组
 * @param {Object} [options] - 配置项
 * @param {number} [options.concurrency=5] - 最大并发数
 * @param {number} [options.retry=2] - 失败重试次数
 * @param {number} [options.timeout=10000] - 单个请求超时时间(ms)
 * @param {Function} [options.onProgress] - 进度回调函数
 * @returns {Promise<Array<{status: 'fulfilled' | 'rejected', data: any, retries: number}>>}
 */
export async function batchRequests(
  requests,
  { concurrency = 5, retry = 2, timeout = 10000, onProgress } = {},
) {
  const results = new Array(requests.length).fill(null) // 预填充结果数组
  const indexedRequests = requests.map((req, index) => ({ req, index })) // 带索引的请求队列
  let pending = 0
  let completed = 0

  const withTimeout = request => {
    return Promise.race([
      request(),
      new Promise((_, reject) => setTimeout(() => reject(new Error('Request timeout')), timeout)),
    ])
  }

  const executeWithRetry = async (request, retriesLeft = retry) => {
    try {
      const data = await withTimeout(request)
      return { status: 'fulfilled', data, retries: retry - retriesLeft }
    } catch (error) {
      if (retriesLeft > 0) {
        return executeWithRetry(request, retriesLeft - 1)
      }
      return { status: 'rejected', data: error, retries: retry }
    }
  }

  const updateProgress = () => {
    onProgress &&
      onProgress({
        completed: completed,
        total: requests.length,
        percentage: ((completed / requests.length) * 100).toFixed(2),
      })
  }

  return new Promise(resolve => {
    const run = async () => {
      while (indexedRequests.length > 0 && pending < concurrency) {
        const { req, index } = indexedRequests.shift()
        pending++

        executeWithRetry(req).then(result => {
          results[index] = result // 按原始索引存储结果
          pending--
          completed++
          updateProgress()

          if (indexedRequests.length > 0) {
            run()
          } else if (pending === 0) {
            resolve(results) // 返回按原始顺序排列的结果数组
          }
        })
      }
    }

    for (let i = 0; i < Math.min(concurrency, requests.length); i++) {
      run()
    }
  })
}
