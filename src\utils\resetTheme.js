import { injectGlobal } from 'emotion';

const defaultColor = {
  bar: '#115ED2',
  brand: '#4F84D2',
  brand2: 'rgba(79, 132, 210, 0.1)',
  link: '#4F84D2',
  bg: 'linear-gradient(180deg, #A4C4F4 0%, #D2E4FF 100%)',
}

const localColor = localStorage.getItem('color') && localStorage.getItem('color') !== 'undefined' ? JSON.parse(localStorage.getItem('color')) : defaultColor

const generateClass = (color = localColor) => {
    const { bar, brand, brand2, link, bg } = color;
    localStorage.setItem('color', JSON.stringify(color))
    injectGlobal`
    #root{
        .upload-file-btn:hover {
            color: ${brand};
            border-color: ${brand} !important;
        }
        .ant-tabs.mb10 .ant-tabs-tab{
            background:#1890ff !important;
            padding-top: 10px !important;
        }
        .ant-tabs.mb10 .ant-tabs-tab-active{
            background: transparent !important;
        }
        .ant-tabs .ant-tabs-tab-active{
            color: ${brand};
        }
        .ant-tabs.mb10 .ant-tabs-tab .ant-tabs-tab-btn{
            color: #fff;
            padding: 0 15px;
        }
        .ant-tabs.mb10 .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn{
            color: #1890ff;
        }
        .ant-tabs-ink-bar{
            background: ${brand} !important;
        } 
        .ant-tabs-nav .ant-tabs-tab:hover{
            color: ${brand} !important;
        }
        .ant-input-group-addon a {
          color: ${brand} !important;
        }
        .ant-card-body a {
          color: ${brand} !important;
        }
        // antdesign primary按钮样式覆盖
        .ant-btn:hover,
        .ant-btn:focus,
        .ant-btn:active{
        //   border-color: ${brand} !important;
        //   color: ${brand} !important;
        // border-color: ${brand2} !important;
        // color: #fff; 
        }
        .ant-btn-link{
          color: ${brand} !important;
        }
        .moa-file-item-actions .ant-btn-link{
            border: none !important;
        }
        .ant-btn-primary{
            background: ${brand} !important;
            border-color: ${brand} !important;
        }
        .ant-btn-primary:hover{
            background: ${brand} !important;
            border-color: ${brand} !important;
            color: white !important;
        }
        // antdesign 输入框样式覆盖
        .ant-input:focus,.ant-input:not(.ant-input-disabled):hover{
            border-color: ${brand} !important;
        }
        .ant-input-affix-wrapper:hover .ant-input:not(.ant-input-disabled){
            border-color: ${brand} !important;
        }
        .ant-input:focus{
            box-shadow: 0 0 0 2px ${brand2} !important;
        }
        // antdesign 数字输入框样式覆盖
        .ant-input-number:not(.ant-input-number-disabled):hover{
          border-color: ${brand} !important;
        }
        .ant-input-number:focus, .ant-input-number-focused{
          border-color: ${brand} !important;
          box-shadow: 0 0 0 2px ${brand2} !important;
        }
        .ant-input-number-handler:hover .ant-input-number-handler-down-inner,
        .ant-input-number-handler:hover .ant-input-number-handler-up-inner {
          color: ${brand} !important;
        }
        // antdesign 分页样式覆盖
        .ant-pagination-item-active{
            border-color: ${brand} !important;
            background: ${brand}
        }
        .ant-pagination-item:hover{
            border-color: ${brand} !important;
        }
        .ant-pagination-item:hover a{
            color: ${brand} !important;
        }
        .ant-pagination-item-active a{
            color: #fff
        }
        .ant-pagination-item-active:hover a{
            color: #fff !important;
        }
        .ant-pagination-next:focus .ant-pagination-item-link, .ant-pagination-next:hover .ant-pagination-item-link, .ant-pagination-prev:focus .ant-pagination-item-link, .ant-pagination-prev:hover .ant-pagination-item-link{
            color: ${brand};
            border-color: ${brand};
        }
        .ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-link-icon, .ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-link-icon{
            color: ${brand}
        }
        //antdesign表格hover背景色
        .ant-table-tbody>tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected)>td, .ant-table-tbody>tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected)>td, .ant-table-thead>tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected)>td, .ant-table-thead>tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected)>td{
            background-color: ${brand2}
        }
        //antdesign 开关开启背景颜色
        .ant-switch-checked{
            background-color: ${brand}
        }

        .ant-calendar-picker:hover .ant-calendar-picker-input:not(.ant-input-disabled){
            border-color: ${brand};
            box-shadow: 0 0 0 2px ${brand2};
        }
        .ant-radio-inner::after{
            background-color: ${brand}
        }
        .ant-radio-wrapper:hover .ant-radio, .ant-radio:hover .ant-radio-inner, .ant-radio-input:focus+.ant-radio-inner{
            border-color: ${brand}
        }
        .ant-radio-checked:after {
            border-color: ${brand}
        }
        .ant-radio-input:focus+.ant-radio-inner {
            box-shadow: 0 0 0 2px ${brand2} !important;
        }
        .ant-radio-checked .ant-radio-inner {
            border-color: ${brand} !important;
        }
        .ant-checkbox-checked .ant-checkbox-inner{
            background-color: ${brand};
            border-color: ${brand};
        }
        .ant-checkbox-disabled .ant-checkbox-inner {
            background-color: #f5f5f5 !important;
        }
        .ant-checkbox-input:focus+.ant-checkbox-inner, .ant-checkbox-wrapper:hover .ant-checkbox-inner, .ant-checkbox:hover .ant-checkbox-inner{
            border-color: ${brand};
        }
        .ant-checkbox-checked:after{
            border-color: ${brand};
        }
        .ant-timeline-item .ant-timeline-item-head{
            color:${brand};
            border-color: ${brand};
        }
        .ant-select-selector:hover{
            border-color: ${brand};
        }
        .ant-select-focused .ant-select-selector, .ant-select-selector:active, .ant-select-selector:focus {
            border-color:  ${brand};
            border-right-width: 1px!important;
            outline: 0;
            box-shadow: 0 0 0 2px ${brand2};
        }
        .ant-menu-submenu-selected{
            color: ${brand};
        }
        .ant-menu-submenu-title:hover{
            color: ${brand};
        }
        .ant-menu-submenu-inline>.ant-menu-submenu-title:hover .ant-menu-submenu-arrow:after,.ant-menu-submenu-inline>.ant-menu-submenu-title:hover .ant-menu-submenu-arrow:before{
            background: ${brand};
        }
        .ant-cascader-picker-label:hover+.ant-cascader-input{
            border-color: ${brand};
        }
        .ant-cascader-picker:focus .ant-cascader-input{
            border-color: ${brand} !important;
            box-shadow: 0 0 0 2px ${brand2};
        }
    }

    .ant-calendar-month-panel-header a:hover{
        color: ${brand}
    }
    .ant-calendar-month-panel-selected-cell .ant-calendar-month-panel-month, .ant-calendar-month-panel-selected-cell .ant-calendar-month-panel-month:hover{
        background: ${brand}
    }


    .ant-modal-confirm,.ant-modal-footer,.ant-drawer-content-wrapper{
        .ant-btn-primary{
        background-color: ${brand};
        border-color: ${brand};
        color: #fff;
    } .ant-btn-primary:hover{
        background-color: ${brand};
        border-color: ${brand};
    } .ant-btn-primary:focus{
        background-color: ${brand};
        border-color: ${brand};
    }
    }
    .ant-modal-root .c_text{
        color: ${link} !important;
    }
    .ant-calendar-today-btn {
        color: ${link} !important;
    }

    .ant-calendar-today .ant-calendar-date{
        color: ${brand};
        border-color: ${brand};
        background: ${brand2};
    }
    .ant-calendar-date:hover {
        background: ${brand2};
        cursor: pointer;
    }
    .ant-menu-submenu-popup .ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected{
        background-color: rgba(0,0,0,0.1);
    }
    .ant-menu-submenu-popup .ant-menu-item-selected, .ant-menu-item-selected>a, .ant-menu-item-selected>a:hover{
        color: rgba(0,0,0,0.5)
    }
    .ant-menu-submenu-popup .ant-menu-item>a{
        color: rgba(0,0,0,.65) 
    }
    .ant-menu-submenu-popup .ant-menu-item>a:hover{
        color: ${link}
    }

    .ant-menu-submenu-title:hover{
        color: ${link};
    }
    .ant-menu-submenu-inline>.ant-menu-submenu-title:hover .ant-menu-submenu-arrow:after, .ant-menu-submenu-inline>.ant-menu-submenu-title:hover .ant-menu-submenu-arrow:before, .ant-menu-submenu-vertical-left>.ant-menu-submenu-title:hover .ant-menu-submenu-arrow:after, .ant-menu-submenu-vertical-left>.ant-menu-submenu-title:hover .ant-menu-submenu-arrow:before, .ant-menu-submenu-vertical-right>.ant-menu-submenu-title:hover .ant-menu-submenu-arrow:after, .ant-menu-submenu-vertical-right>.ant-menu-submenu-title:hover .ant-menu-submenu-arrow:before, .ant-menu-submenu-vertical>.ant-menu-submenu-title:hover .ant-menu-submenu-arrow:after, .ant-menu-submenu-vertical>.ant-menu-submenu-title:hover .ant-menu-submenu-arrow:before {
        background: linear-gradient(90deg,${link},${link});
    }
    .ant-menu-vertical .ant-menu-submenu-selected{
        color: ${link};
    }
    .ant-menu-item-active, .ant-menu-item:hover, .ant-menu-submenu-active, .ant-menu-submenu-title:hover, .ant-menu:not(.ant-menu-inline) .ant-menu-submenu-open{
        color: ${link};
    }
    .ant-calendar-range .ant-calendar-selected-end-date .ant-calendar-date, .ant-calendar-range .ant-calendar-selected-start-date .ant-calendar-date{
        background: ${brand}
    }
    .ant-calendar-range .ant-calendar-selected-end-date .ant-calendar-date:hover, .ant-calendar-range .ant-calendar-selected-start-date .ant-calendar-date:hover{
        background: ${brand};
    }
    .ant-calendar-range .ant-calendar-in-range-cell:before{
        background: ${brand2};
    }
    .ant-calendar-picker:focus .ant-calendar-picker-input:not(.ant-input-disabled){
        border-color: ${brand} !important;
        box-shadow: 0 0 0 2px ${brand2};
    }
    .ant-calendar-time .ant-calendar-footer .ant-calendar-today-btn{
        color: ${brand}
    }
    .ant-calendar-time .ant-calendar-footer .ant-calendar-time-picker-btn{
        color: ${brand}
    }
    .ant-calendar-selected-day .ant-calendar-date {
        background: ${brand2};
    }

    .ant-calendar .ant-calendar-ok-btn{
        background-color: ${brand};
        border: 1px solid ${brand};
    }
    .ant-calendar .ant-calendar-ok-btn-disabled{
        background-color: #f5f5f5;
        border-color: #d9d9d9;
    }
    .ant-calendar .ant-calendar-ok-btn:focus, .ant-calendar .ant-calendar-ok-btn:hover{
        background-color: ${brand};
        border-color: ${brand};
    }
    .ant-calendar .ant-calendar-ok-btn-disabled:hover{
        background-color: #f5f5f5;
        border-color: #d9d9d9;
    }
    .ant-cascader-menu-item:hover{
        background: ${brand2}
    }
    .ant-time-picker-input:not(.ant-time-picker-input-disabled):focus,
    .ant-time-picker-input:not(.ant-time-picker-input-disabled):hover{
      border-color: ${brand};
    }
    .ant-time-picker-input:not(.ant-time-picker-input-disabled):focus,
    .ant-time-picker-input:not(.ant-time-picker-input-disabled):hover {
      box-shadow: 0 0 0 2px ${brand2};
    }
    .ant-time-picker-panel .ant-btn-primary{
        background-color: ${brand};
        border-color: ${brand};
    }
    .ant-time-picker-panel-select li:focus{
        color: ${link}
    }
    .ant-time-picker-panel-select li:hover{
        background-color: ${brand2};
    }
    .ant-form-item-control .bc-brand{
        border-color: ${brand};
        color: ${brand};
    }
    .ant-calendar-month-panel-tbody{
        .ant-calendar-month-panel-month:hover {
            background: ${brand2};
            cursor: pointer;
        }
    }
    .ant-calendar-year-panel-selected-cell .ant-calendar-year-panel-year, .ant-calendar-year-panel-selected-cell .ant-calendar-year-panel-year:hover{
            background: ${brand}
        }
        .ant-calendar-header a:hover {
            color: ${brand};
        }
    .ant-modal-body, .ant-drawer-content-wrapper{
        .ant-cascader-picker-label:hover+.ant-cascader-input{
            border-color: ${brand};
        }
        .ant-cascader-picker:focus .ant-cascader-input{
            border-color: ${brand} !important;
            box-shadow: 0 0 0 2px ${brand2};
        }
        .ant-select-selector:hover{
            border-color: ${brand};
        }
        .ant-select-focused .ant-select-selector, .ant-select-selector:active, .ant-select-selector:focus {
            border-color:  ${brand};
            border-right-width: 1px!important;
            outline: 0;
            box-shadow: 0 0 0 2px ${brand2};
        }
        .ant-input-number:not(.ant-input-number-disabled):hover{
            border-color: ${brand} !important;
        }
        .ant-input-number:focus, .ant-input-number-focused{
            border-color: ${brand} !important;
            box-shadow: 0 0 0 2px ${brand2} !important;
        }
        .ant-input-number-handler:hover .ant-input-number-handler-down-inner,
        .ant-input-number-handler:hover .ant-input-number-handler-up-inner {
            color: ${brand} !important;
        }
        .ant-checkbox-checked .ant-checkbox-inner{
            background-color: ${brand};
            border-color: ${brand};
        }
        .ant-checkbox-input:focus+.ant-checkbox-inner, .ant-checkbox-wrapper:hover .ant-checkbox-inner, .ant-checkbox:hover .ant-checkbox-inner{
            border-color: ${brand};
        }
        .ant-checkbox-checked:after{
            border-color: ${brand};
        }
        .ant-radio-inner::after{
            background-color: ${brand};
        }
        .ant-radio-wrapper:hover .ant-radio, .ant-radio:hover .ant-radio-inner, .ant-radio-input:focus+.ant-radio-inner{
            border-color: ${brand};
        }

        .ant-switch-checked{
            background-color: ${brand};
        }
        .ant-switch:focus{
            box-shadow: 0 0 0 2px ${brand2};
        }
        .ant-radio-input:focus+.ant-radio-inner {
            box-shadow: 0 0 0 3px ${brand2};
        }
        .ant-dropdown-menu{
            .ant-dropdown-menu-item:hover, .ant-dropdown-menu-submenu-title:hover{
                color: ${link}
            }
        }

        // antdesign 输入框样式覆盖
        .ant-input:focus,.ant-input:hover{
            border-color: ${brand} !important;
        }
        .ant-input-affix-wrapper:hover .ant-input:not(.ant-input-disabled){
            border-color: ${brand} !important;
        }
        .ant-input:focus{
            box-shadow: 0 0 0 2px ${brand2} !important;
        }
    }
        .sub-menu-pop .ant-menu{
            background-color: ${bar};
        }
        .sub-menu-pop .ant-menu .ant-menu-item:hover{
            background-color: ${brand2};
        }
        .sub-menu-pop .ant-menu .ant-menu-item>a:hover{
            color: #fff;
        }
        .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
            background-color: ${brand2} !important
        }
        // .ant-select-item-option:hover:not(.ant-select-item-option-disabled){
        //     background-color: ${brand2}
        // }
        // .ant-select-item-option-active:not(.ant-select-item-option-disabled){
        //     background-color: ${brand}
        // }
        // .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
        //     background-color: ${brand2}
        // }
        .ant-spin-dot-item{
            background-color: ${brand} !important;
        }
    `
}

export {
  generateClass,
  localColor
}