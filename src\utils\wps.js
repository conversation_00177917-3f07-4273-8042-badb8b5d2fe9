import { message, Modal } from 'antd'
import Cookies from 'js-cookie'
import service from 'ROOT/service'
import { isWindow } from './index'
import '../lib/LAB'
import { v4 as uuidv4 } from 'uuid';

$LAB.setGlobalDefaults({ AllowDuplicates: false, CacheBust: true })

// wps cookie 变量 index -> docid
const WPSFileMap = '_WPSFileMap_'

const mimeTypeMap = {
  doc: 'application/msword',
  docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
}


let wpsInited = false

/**
 * 启动 WPS 前置
 *
 * @param {Function} [callback=() => {}]
 * @param {boolean} [isCheck=false]
 */
 function startOutWps (callback = () => { }, isCheck = false) {
  const openWps = () => {
    // if (!isWindow) {
    //   message.error('当前操作系统不支持在线编辑！')
    //   return
    // }
  
    if (!callback) {
      console.error('缺少执行函数')
      return
    }

    const checkStatus = () => {
      return new Promise((resolve, reject) => {
        if (!isCheck) {
          resolve(true)
          return
        }
        getDocStatus({
          status: (statusRes) => {
            if (statusRes) {
              Modal.warning({
                title: 'WPS提醒',
                content: '当前有文档正在编辑，请结束后再编辑',
                okText: '知道了',
                cancelText: '取消'
              })
              reject(new Error('error'))
              return
            }
            resolve(true)
          }
        })
      })
    }

    // if (wpsInited) {
    //   checkStatus().then(callback)
    //   return
    // }

    let prefixBase = STATIC_PREFIX
    if (/^\/\//.test(prefixBase)) {
      prefixBase = `${location.protocol}${prefixBase}`
    }
    installWpsAddin(prefixBase, () => {
      wpsInited = true
      checkStatus().then(callback)
    })
  }

  // 判断 wps 相关的依赖是否加载，加载过，直接唤起 wps 客户端
  // if (window.isInitWps) {
  //   openWps()

  //   return
  // }

  // 需要优先加载 wps 中的依赖，才能正常唤起 wps 客户端
  $LAB
  .script(`${window._APP_CONFIG.apiHost}/wps-oaassist/wwwroot/wpsjsrpcsdk.js`).wait()
  .script(`${window._APP_CONFIG.apiHost}/wps-oaassist/wwwroot/wps.js`).wait()
  .script(`${window._APP_CONFIG.apiHost}/wps-oaassist/wwwroot/js/loaderbuild.js`).wait(function() {
    // 项目卸载时，需要更改这里的变量状态为 false，避免出现副作用
    // window.isInitWps = true

    openWps()
  })
}



let globalMaskEl = null

export function getWpsEditing() {
  return new Promise((resolve) => {
    startOutWps(() => {
      getDocStatus({
        status: (statusRes) => {
          resolve(!!statusRes)
        },
      })
    })
  })
}

export const wpsMask = {
  async init(isEdit = false) {
    const creatMask = () => {
      if (globalMaskEl) {
        return
      }
      globalMaskEl = createMaskElement()
      globalMaskEl.addEventListener('click', async () => {
        // const hasEdit = await getWpsEditing()
        // if (hasEdit) {
        //   message.warning('当前有文档正在编辑')
        //   return
        // }
        document.body.removeChild(globalMaskEl)
        globalMaskEl = null
      })
      document.body.appendChild(globalMaskEl)
    }
    if (isEdit) {
      creatMask()
      return
    }

    const hasEdit = await getWpsEditing()
    if (!hasEdit) {
      return
    }
    creatMask()
  },

  remove() {
    if (globalMaskEl) {
      document.body.removeChild(globalMaskEl)
      globalMaskEl = null
    }
  },
}

/**
 * 打开外部 WPS 程序在线编辑
 *
 * @export
 * @param {string} type 公文类型
 * @param {object} config 公文类型配置
 * @param {object[]} fileList 文件信息对象数组
 * @param {number} index 文件所在值中的索引
 * @param {string|number} id 公文ID
 * @param {boolean} isNew 是否是新建公文
 * @param {Function} callback 回调函数
 */
export function outWpsEdit({ ruleType, config, fileList = [], index, reportId, isNew, formData, callback, orgId, uploadPath = '/sfs/webUpload/file', isHtml = false, suffix,
needConvert }) {
  // const userInfo =JSON.parse(localStorage.getItem('userInfo'))
  // const {loginName} = userInfo || {}

  const { loginName } = Cookies.getJSON('myself') || {}

  const isCreate = index < 0
  if (!isCreate && fileList.length === 0) {
    return
  }

  const currentFile = fileList[index]
  if (!isCreate && !currentFile) {
    return
  }

//   if (!isCreate && !['doc', 'docx'].includes(currentFile.type)) {
//     message.error('非 word 文档不可在线编辑')
//     return
//   }

  startOutWps(() => {
      console.log(currentFile,'dd')
    const { url:originalUrl, type, name: fileName } = currentFile || {}
    const key = uuidv4() //Date.now()

    openDoc({
      key,
      moduleName: 'extra-forms',
      // docId: file.key,
      isNew,
      orgId,
      docId: '1111111111',
      userName: loginName,
      filePath: isCreate ? undefined : `${originalUrl}&contenttype=${mimeTypeMap[type]}`,
      fileName,
      newFileName: '新建文档.docx',
      uploadPath,
      params: JSON.stringify({
        isHtml,
        type: ruleType,
        config,
        id: reportId,
        orgId,
        file: isCreate ? undefined : currentFile,
        index,
        list: fileList || [],
        isNew,
        data: formData,
        suffix,
        needConvert
      }),
    }, {
     
      save: (res, features = {}) => {
        if (res.moduleName !== 'extra-forms'|| key !== res.key ) {
          return false
        }
        const { id: gId, list } = res
        callback(gId, list)
      },
    })
  }, true)
}



const createMaskElement = () => {
  const el = document.createElement('div')
  el.textContent = 'wps编辑中,点击关闭'
  el.style.cssText = `
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99999;
  text-align: center;
  padding-top: 20%;
  color: #fff;
  font-size: 16px;
  `
  return el
}

/**
 * 打开外部 WPS 程序在线套红
 *
 * @export
 * @param {string} tplUrl 套红模板url
 * @param {object[]} fileList 正文数据列表
 * @param {number} index 操作文件的索引
 * @param {string|number} id 公文ID
 * @param {Function} callback 回调
 */
export function outTaohong({ ruleType, config, tplUrl,fileList = [], index, reportId, isNew, formData, callback, orgId, uploadPath = '/sfs/webUpload/file', isHtml = false }) {
  // const userInfo =JSON.parse(localStorage.getItem('userInfo'))
  // const {loginOrgId,loginName} = userInfo || {}

  const { loginName } = Cookies.getJSON('myself') || {}

  if (fileList.length === 0) {
    return
  }

  const currentFile = fileList[index]
  if (!currentFile || !tplUrl) {
    return
  }

  // if (!['doc', 'docx'].includes(currentFile.type)) {
  //   message.error('非 word 文档不可套红')
  //   return
  // }

  startOutWps(() => {
    const { url:originalUrl, type } = currentFile || {}
    const key = uuidv4() //Date.now()
    const params = {
      key,
      moduleName: 'extra-forms',
      userName: loginName,
      // bookMarksStart: '正文内容B',
      // bookMarksEnd: '正文内容E',
      // filePath: 'http://127.0.0.1:3888/file/样章.docx',
      // filePath: `${originalUrl}&contenttype=application/vnd.openxmlformats-officedocument.wordprocessingml.document`,
      // filePath: `${originalUrl}&contenttype=application/msword&filename=a.doc`,
      // filePath: `${originalUrl}&contenttype=${mimeTypeMap[type]}&filename=${file.name}`,
      filePath: `${originalUrl}&contenttype=${mimeTypeMap[type]}`,
      uploadPath,
      templateURL: tplUrl,
      insertFileUrl: tplUrl,
      params: JSON.stringify({
        isHtml,
        type: ruleType,
        config,
        id: reportId,
        orgId,
        file: currentFile,
        index,
        list: fileList || [],
        data: formData,
      }),
    }

    // insertRedHeader(params, {
    openDoc(params, {
     
      save: (res, features = {}) => {
        if (res.moduleName !== 'extra-forms' || key !== res.key ) {
          return false
        }
        console.log(res,'res')
        callback(res.id)
      },
    })
  }, true)


}

/**
 * 打开外部 WPS 程序在线创建文档
 *
 * @export
 * @param {object[]} fileList 文件信息对象数组
 * @param {string|number} id 公文ID
 * @param {Function} callback 回调函数
 */
export function outWpsCreate(fileList = [], id, callback) {
  outWpsEdit(fileList, -1, id, callback)
}

export const getFileInfoFromSaveMsg = (msg) => {
  let { extdata } = msg.message
  try {
    extdata = JSON.parse(extdata)
  } catch (error) {
    console.log('handleWpsFileSaveMsg JSON.parse', error)
  }

  const wpsFileId = extdata.sourceId
  const { url } = extdata
  return {
    wpsFileId,
    url,
  }
}

/**
 * 通知 WPS 关闭
 *
 * @export
 * @param {*} isForce
 */
export function exitWPSV(isForce) {
  if (isWindow && exitWPS) {
    exitWPS(isForce)
  }
}

// 获取wps最新保存的内容
export async function getWpsUrl(id) {
  let wpsContent = null
  try {
    wpsContent = await service.getWpsEditUrl({ btype: 5, bid: `extra-${id}` })
    if (wpsContent) {
      const { url } = wpsContent || {}
      if (url) {
        wpsContent = JSON.parse(url)['0']
      } else {
        wpsContent = null
      }
    }
  } catch(err) {
    console.log('getWpsEditUrl err', err)
  } finally {
    return wpsContent
  }
}