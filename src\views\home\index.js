import React, { useEffect } from 'react'
import _ from 'lodash'
import { connect } from 'react-redux'

const mapState = ({ myStore }) => ({
  myInfo: myStore.myInfo,
})

const mapActions = ({ myStore }) => ({
  actions: { getMyInfo: myStore.getMyInfo },
})

const Home = ({ myInfo, actions, children }) => {
  return <div>{children}</div>
}

export default _.flowRight([connect(mapState, mapActions)])(Home)
