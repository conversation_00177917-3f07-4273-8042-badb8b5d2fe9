import React, { useState, useEffect, useMemo } from 'react'
import { message, Mo<PERSON>, Button } from 'antd' 
import { SchemaMarkupForm, createFormActions,FormEffectHooks, createAsyncFormActions } from '@formily/antd'
import { CreateBusSchema } from '../../schema'
import Editable from 'ROOT/components/Formily/Editable'
import Cookies from 'js-cookie'
import Service from 'ROOT/service'
import {
    FormMegaLayout,
    Input,
    DatePicker,
    Select,
    Radio,
    NumberPicker,
    ArrayTable,
    FormCard,
} from '@formily/antd-components'

const { onFieldValueChange$ } = FormEffectHooks

export default (props)=> {
    const { userInfo,close ,setData} = props
    const actions = useMemo(() => createAsyncFormActions(), []);
    Editable.isFieldComponent = true
    const orgId = Cookies.get('orgId')
    const [initialValues, setInitialValues] = useState({countyName: ''})
    const [contyList, setCountryList ] = useState([])
    console.log(props)
    useEffect(()=>{
        setInitialValues({
            ...initialValues,
            orgName:userInfo.cityOrgName,
            orgId,
        })
    },[userInfo])

    useEffect(()=>{
        getCountryList()
    },[])

    const getCountryList=()=> {
        Service.getInfo({orgId}).then(res=> {
            console.log(res)
            setCountryList(res.data.county)
        })
    }

    const submit= async (e)=> {
        const data = await actions.submit()
      
        Service.carInfoAdd({
            ...data.values,
            countyName: contyList.filter(item=>item.countyId==data.values.countyId)[0].countyId
        }).then(res=> {
            message.success('车辆创建成功')
            if(e===2){
                setData({
                    ...data.values,
                })
            }
            close()
        })
    }

    const expressionScope= {
        country: contyList.map(item=> {
            return {
                label: item.countyName,
                value: item.countyId
            }
        })
    }

    return <Modal
        visible={true}
        title="创建车辆信息"
        width={600}
        onOk={submit}
        onCancel={close}
        footer={
            <div>
                <Button onClick={close}>取消</Button>
                <Button onClick={()=>submit(1)}>创建</Button>
                <Button onClick={()=>submit(2)} type='primary'>创建并选择</Button>
            </div>
        }
    >
        <SchemaMarkupForm 
            schema={CreateBusSchema}
            actions={actions}
            initialValues={initialValues}
            expressionScope={{ ...expressionScope }}
            components={{
                Editable,
                Input,
                Select,
                DatePicker,
                FormCard,
                NumberPicker
            }}
        >
        </SchemaMarkupForm>
    </Modal>
}