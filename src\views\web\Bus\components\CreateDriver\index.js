import React, { useMemo } from 'react'
import { <PERSON><PERSON>, Button,message } from 'antd' 
import { SchemaMarkupForm, createFormActions,createAsyncFormActions } from '@formily/antd'
import { DriverSchema } from '../../schema'
import Editable from 'ROOT/components/Formily/Editable'
import Service from 'ROOT/service'
import {
    Input,
    Select,
} from '@formily/antd-components'

export default (props)=> {
    const { close, setData } = props
    Editable.isFieldComponent = true
    const actions = useMemo(() => createAsyncFormActions(), []);

    const submit= async(e)=> {
        const data = await actions.submit()
        const { values } = data
        console.log(data)
        Service.driverInfoAdd({...data.values}).then(res=> {
            message.success('驾驶员创建成功')
            if(e===2){
                setData({
                    driver: values.name,
                    driverPhone: values.mobile,
                    driverAttr: values.driverAttr,
                    driverId: res.data.driverId
                })
            }
            close()
        })
    }

    return <Modal
        visible={true}
        title="创建驾驶员信息"
        width={600}
        onOk={submit}
        onCancel={close}
        footer={
            <div>
                <Button onClick={close}>取消</Button>
                <Button onClick={()=>submit(1)}>创建</Button>
                <Button onClick={()=>submit(2)} type='primary'>创建并选择</Button>
            </div>
        }
    >
        <SchemaMarkupForm 
            schema={DriverSchema}
            actions={actions}
            components={{
                Editable,
                Input,
                Select
            }}
        />
    </Modal>
}