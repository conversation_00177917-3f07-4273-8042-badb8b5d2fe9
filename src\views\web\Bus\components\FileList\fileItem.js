import React from 'react'
import PropTypes from 'prop-types'
import ClassNames from 'classnames'
import bowser from 'bowser'
import {download, getFileIcon, getFileExt, getFileSize} from './util'
import styles from './index.scss'

function FileItem (props) {
  const agent = bowser.parse(window.navigator.userAgent)
  const {
    icon,
    cls,
    name,
    size,
    onDelete,
    downloadable,
    downloadUrl,
    beforeDownload,
    onClick,
    renderExtraActions,
  } = props

  const onDownloadHandler = (ev) => {
    ev.stopPropagation()
    if (!beforeDownload) {
      download(downloadUrl, name)
    } else {
      typeof beforeDownload === 'function' && beforeDownload(props, (url) => {
        download(url || downloadUrl, name)
      })
    }
  }

  const onDeleteHandler = (ev) => {
    ev.stopPropagation()
    onDelete(props)
  }

  const onFileClickHandler = () => {
    onClick && onClick(props)
  }

  const hasTwoBtn = () => {
    const hasDownload = downloadable && downloadUrl
    const hasDelete = !!onDelete
    const hasExtra = renderExtraActions instanceof Function
    return [hasDownload, hasDelete, hasExtra].filter(Boolean).length === 2
  }

  const clazz = ClassNames(styles.item, {
    [styles.has_one]: !!onDelete || (downloadable && downloadUrl),
    [styles.has_two]: hasTwoBtn(),
    [styles.has_three]: (
      downloadable
      &&
      downloadUrl
      &&
      !!onDelete
      &&
      renderExtraActions instanceof Function
    ),
  }, cls)

  const renderExtraBtn = () => {
    if (!(renderExtraActions instanceof Function)) {
      return null
    }
    return renderExtraActions(props)
  }

  return (
    <div className={clazz} style={agent.platform.type === 'desktop' ? {width:'32%'}: {width:'100%'}} onClick={onFileClickHandler}>
      <div className={styles.icon}>
        {
          icon ?
            <img src={icon} alt={name} /> :
            <i className={ClassNames(
              styles.fileicon,
              styles.filetype,
              styles[getFileIcon(getFileExt(name))]
            )} />
        }
      </div>
      <div className={styles.content}>
        <div className={styles.name} title={name}>{name}</div>
        {
          size ?
            <div className={styles.size}>{getFileSize(size)}</div> :
            null
        }
      </div>
      <div className={styles.btns}>
        {
          downloadable ?
            <i className={ClassNames(styles.fileicon, styles['icon-download'])} onClick={onDownloadHandler} /> :
            null
        }
        {
          onDelete ?
            <i className={ClassNames(styles.fileicon, styles['icon-close'])} onClick={onDeleteHandler} /> :
            null
        }
        {renderExtraBtn()}
      </div>
    </div>
  )
}

FileItem.propTypes = {
  cls: PropTypes.string,
  icon: PropTypes.string,
  name: PropTypes.string.isRequired,
  size: PropTypes.number,
  onDelete: PropTypes.func,
  onClick: PropTypes.func,
  downloadable: PropTypes.bool,
  downloadUrl: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.object
  ]),
  beforeDownload: PropTypes.oneOfType([
    PropTypes.func,
    PropTypes.bool
  ]),
  renderExtraActions: PropTypes.func,
}

FileItem.defaultProps = {
  cls: '',
  icon: '',
  size: 0,
  onDelete: null,
  onClick: null,
  downloadable: false,
  downloadUrl: '',
  beforeDownload: false,
  renderExtraActions: null,
}

export default FileItem