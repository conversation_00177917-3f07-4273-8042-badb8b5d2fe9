import React from 'react'
import PropTypes from 'prop-types'
import FileItem from './fileItem'

function FileList (props) {
  const {
    cls,
    toEach,
    onDelete,
    downloadable,
    beforeDownload,
    className,
    onClick,
    renderExtraActions,
  } = props

  return (
    <div className={className}>
      {
        toEach.map((item, index) => (
          <FileItem
            key={index}
            {...item}
            curIndex={index}
            cls={cls}
            beforeDownload={beforeDownload}
            downloadable={item.downloadable === false ? item.downloadable : downloadable}
            onDelete={typeof item.onDelete !== 'undefined' ? item.onDelete : onDelete}
            onClick={onClick}
            renderExtraActions={renderExtraActions}
          />
        ))
      }
    </div>
  )
}

FileList.propTypes = {
  toEach: PropTypes.arrayOf(PropTypes.shape({
    name: PropTypes.string.isRequired
  })).isRequired,
  onDelete: PropTypes.func,
  onClick: PropTypes.func,
  beforeDownload: PropTypes.oneOfType([
    PropTypes.func,
    PropTypes.bool
  ]),
  downloadable: PropTypes.bool,
  cls: PropTypes.string,
  className: PropTypes.string,
  renderExtraActions: PropTypes.func,
}

FileList.defaultProps = {
  onDelete: null,
  onClick: null,
  beforeDownload: false,
  downloadable: false,
  cls: '',
  className: '',
  renderExtraActions: null,
}

FileList.Item = FileItem

export default FileList