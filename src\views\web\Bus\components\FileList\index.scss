

@font-face {font-family: "fileicon";
	src: url('//at.alicdn.com/t/font_413588_qvbuojucqlac3di.eot?t=1517996857010'); /* IE9*/
	src: url('//at.alicdn.com/t/font_413588_qvbuojucqlac3di.eot?t=1517996857010#iefix') format('embedded-opentype'), /* IE6-IE8 */
	url('data:application/x-font-woff;charset=utf-8;base64,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') format('woff'),
	url('//at.alicdn.com/t/font_413588_qvbuojucqlac3di.ttf?t=1517996857010') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
	url('//at.alicdn.com/t/font_413588_qvbuojucqlac3di.svg?t=1517996857010#iconfont') format('svg'); /* iOS 4.1- */
}

.fileicon {
	font-family:"fileicon" !important;
	font-size:16px;
	font-style:normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}
.icon-download:before { content: "\e729"; }

.icon-close:before { content: "\e70c"; }

 %icon-word:before { content: "\e65b"; }

 %icon-Excel:before { content: "\e65c"; }

 %icon-Generic:before { content: "\e65d"; }

 %icon-ExperienceDesign:before { content: "\e65e"; }

 %icon-HTML:before { content: "\e65f"; }

 %icon-image:before { content: "\e660"; }

 %icon-keynote:before { content: "\e661"; }

 %icon-CSS:before { content: "\e662"; }

 %icon-music:before { content: "\e663"; }

 %icon-lllustrator:before { content: "\e664"; }

%icon-PDF{ &:before { content: "\e665"; }}

 %icon-Photoshop:before { content: "\e666"; }

 %icon-Text:before { content: "\e667"; }

 %icon-sketch:before { content: "\e668"; }

 %icon-zip:before { content: "\e66a"; }

 %icon-Video:before { content: "\e66b"; }

 %icon-PowerPoint:before { content: "\e66c"; }

 %icon-gonggongwenjianjia:before { content: "\e66d"; }

 %icon-wenjianjia:before { content: "\e66e"; }

.item {
	display: inline-block;
	position: relative;
	background: #fff;
	border-radius: 2px;
	padding: 8px;
	font-size: 14px;
	margin: 0 15px 10px 0;
	box-sizing: border-box;
	border: 1px solid #e9ecf0;
	height: 50px;
	line-height: 18px;
	overflow: hidden;
    cursor: pointer;
	&.has_one {
		.content {
			padding-right: 20px;
		}
	}

	&.has_two {
		.content {
			padding-right: 45px;
		}
	}

	&.has_three {
		.content {
			padding-right: 75px;
		}
	}
}

.icon {
	display: block;
	position: absolute;
	width: 32px;
	height: 32px;
	left: 8px;
	top: 8px;
	line-height: 32px;

	img {
		width: 100%;
		height: 100%;
	}
}

.content {
	display: block;
	box-sizing: border-box;
	width: 100%;
	padding: 0 0 0 40px;
	font-size: 12px;
}

.name {
	color: #262a30;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: keep-all;
        white-space: nowrap;
    
}

.size {
	color: #959ba3;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: keep-all;
    white-space: nowrap;
}

.extra {
	position: absolute;
	right: 8px;
	top: 8px;
}
.btns {
	position: absolute;
	right: 8px;
	top: 50%;
	margin-top: -8px;
	> i {
		margin-left: 10px;
		cursor: pointer;
		font-weight: bold;
		color: #959ba3;

		&:hover {
			color: #5c626b;
		}
	}
}

.filetype {
	font-size: 32px;
}

.zip {
	@extend  %icon-zip;
	color: #c09040;
}

.ppt {
	@extend  %icon-PowerPoint;
	color: #ff8022;
}

.mp4 {
	@extend  %icon-Video;
	color: #ffa400;
}

.psd {
	@extend  %icon-Photoshop;
	color: #488ff9;
}

.pdf {
	@extend %icon-PDF;
	color: #f53939;
}

.ai {
	@extend  %icon-lllustrator;
	color: #ffa400;
}

.mp3 {
	@extend  %icon-music;
	color: #ff8022;
}

.css {
	@extend  %icon-CSS;
	color: #488ff9;
}

.html {
	@extend  %icon-HTML;
	color: #ff8022;
}

.sketch {
	@extend  %icon-sketch;
	color: #ffa400;
}

.note {
	@extend  %icon-keynote;
	color: #488ff9;
}

.id {
	@extend  %icon-ExperienceDesign;
	color: #f95f88;
}

.xls {
	@extend  %icon-Excel;
	color: #2dc888;
}

.txt {
	@extend  %icon-Text;
	color: #488ff9;
}

.img {
	@extend  %icon-image;
	color: #ffa400;
}

.doc {
	@extend  %icon-word;
	color: #488ff9;
}

.none {
	@extend  %icon-Generic;
	color: #cbcfd6;
}