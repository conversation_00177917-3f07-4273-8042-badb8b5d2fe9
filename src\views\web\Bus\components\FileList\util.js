// import img from '../images/img.png'
// import excel from '../images/excel.png'
// import zip from '../images/zip.png'
// import mp3 from '../images/mp3.png'
// import mp4 from '../images/mp4.png'
// import ppt from '../images/ppt.png'
// import pdf from '../images/pdf.png'
// import txt from '../images/txt.png'
// import word from '../images/word.png'
// import none from '../images/none.png'

const isSafari = /^(?=.+Mac OS)(?=.+Safari)(?=.+Version\/)(?!.+Chrome)/i.test(window.navigator.userAgent)

export function download (url, name, target) {
  if (!url) return

  let tmpTag = document.createElement('a')
  const fileName = name || (String(url.split(/.+\//)[1]).match(/[^:?*'"<>&|/\\]+\.\w+/))[0] || 'file'
  const isSpecialName = fileName.indexOf('&') > -1
  if (!isSpecialName) {
    url += `${url.indexOf('?') > -1 ? '&' : '?'}filename=${fileName}&contenttype=application/octet-stream`
  } else {
    url += `${url.indexOf('?') > -1 ? '&' : '?'}filename=${encodeURIComponent(fileName)}&contenttype=application/octet-stream`
  }


  if (!isSpecialName && 'download' in tmpTag && !isSafari) {
    tmpTag.download = fileName
    tmpTag.href = url
    setTimeout(() => {
      tmpTag.click()
      tmpTag.parentNode.removeChild(tmpTag)
      tmpTag = null
    })
  } else {
    tmpTag = document.createElement('iframe')
    // 在https内无法加载http的内容
    // 如果图片地址如果是http的话，尝试将http改成https
    const {protocol} = window.location

    tmpTag.src = protocol === 'https:' ? url.replace(/^http:/, protocol) : url

    tmpTag.onload = () => {
      tmpTag.onload = null
      tmpTag.parentNode.removeChild(tmpTag)
      tmpTag = null
    }
  }
  tmpTag.style.display = 'none';
  (target ? target.parentNode : document.body).appendChild(tmpTag)
}

export function getFileExt (name) {
  if (!name) return ''

  const ns = name.split('.')
  const len = ns.length
  const ext = len > 1 ? ns[ns.length - 1] : ''

  return ext
}

/**
 * 获取可阅读的文件大小
 * @param size
 * @returns {*}
 */
export function getFileSize (size) {
  if (!size) return '0B'

  const s = 1024
  const sizes = ['B', 'KB', 'M', 'G']
  let i = Math.floor(Math.log(size) / Math.log(s))

  if (i > sizes.length - 1) {
    i = sizes.length - 1
  }

  return (size / Math.pow(s, i)).toFixed(2) + sizes[i]
}

const TYPE_ICON_MAP = {
  zip: 'zip',
  rar: 'zip',
  '7z': 'zip',

  ppt: 'ppt',
  pps: 'ppt',
  pptx: 'ppt',
  ppsx: 'ppt',

  rmvb: 'mp4',
  mp4: 'mp4',
  avi: 'mp4',
  rm: 'mp4',
  wmv: 'mp4',
  mkv: 'mp4',

  psd: 'psd',

  pdf: 'pdf',

  ai: 'ai',

  mp3: 'mp3',
  wav: 'mp3',

  css: 'css',

  html: 'html',

  sketch: 'sketch',

  note: 'note',

  id: 'id',

  xls: 'xls',
  xlsx: 'xls',
  xlsm: 'xls',

  txt: 'txt',

  png: 'img',
  jpg: 'img',
  jpeg: 'img',
  gif: 'img',
  bmp: 'img',
  webp: 'img',
  svg: 'img',

  doc: 'doc',
  docx: 'doc',
  docm: 'doc',
  dotm: 'doc',
  none: 'none'
}

export function getFileIcon (type = 'none') {
  type = type.toLowerCase()
  return !TYPE_ICON_MAP[type] ? TYPE_ICON_MAP.none : TYPE_ICON_MAP[type]
}