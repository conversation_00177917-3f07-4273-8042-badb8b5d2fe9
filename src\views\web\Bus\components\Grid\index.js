import React, { useState, useEffect } from 'react'
import { Input, Button, Modal, Table, Radio } from 'antd'
import { DashOutlined } from '@ant-design/icons'
import Service from 'ROOT/service'
import { css } from 'emotion'
import Cookies from 'js-cookie'

export default (itemProps) => {
    const { value, mutators, props, editable, schema } = itemProps
    const orgId = Cookies.get('orgId')
    const [visible, setVisible] = useState(false)
    const [selectValue, setSelectValue] = useState(null)
    const [dataSource, setDataSource] = useState([])
    const columns = [
        {
            title: '',
            dataIndex: 'record',
            key: 'record',
            render: (val, record) => {
                return <Radio checked={record.gridNo===selectValue} onChange={e=>setSelectValue(record.gridNo)} />
            },
        },
        {
            title: '网格名称',
            dataIndex: 'gridName',
            key: 'gridName',
        },
        {
            title: '网格编号',
            dataIndex: 'gridNo',
            key: 'gridNo',
        },
        {
            title: '网格归属',
            dataIndex: 'gridAscription',
            key: 'gridAscription',
        },
    ];

    const getList=()=> {
        Service.getGridList({orgId}).then(res=> {
            const { data } = res
            setDataSource(data)
        })
    }

    const submit=()=> {
        const item = dataSource.filter(item=>item.gridNo===selectValue)[0]
        mutators.change(item)
        setSelectValue(null)
        setVisible(false)
    }
    return <div className={css`
        flex: 1;
        display: flex;
    `}>
        <Button onClick={() => {
            setVisible(true)
            getList()
        }}><DashOutlined /></Button>
        {
            visible && <Modal
                title="选择网格"
                width={800}
                visible={visible}
                onCancel={() => setVisible(false)}
                onOk={() => submit()}
            >
                <div>
                    <Table columns={columns} dataSource={dataSource} rowKey={record=>record.gridNo} pagination={true}/>
                </div>
            </Modal>
        }
    </div>
}