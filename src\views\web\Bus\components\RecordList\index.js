import React, { useState } from "react";
import { css } from 'emotion'
import { RECORDS_LABEL } from '../../constant'
import { LeftOutline, RightOutline } from 'antd-mobile-icons'

export default (props) => {
    const { recordList } = props
    const [page, setPage] = useState(recordList.length ?1:0)

    const left= ()=> {
        if(page<=1){
            return false
        }
        setPage(page-1)
    }

    const right =()=> {
        if(page===recordList.length){
            return false
        }

        setPage(page+1)
    }

    return <div className={css`
        width: 100%;
        overflow: hidden;
    `}>
        <div className={css`
            text-align:right;
        `}>
            <div className={css`
            padding: 8px 10px;
            background: #FFFFFF;
            display: inline-block;
            font-size: 16px;
            box-shadow: 0 1px 2px 0 rgba(0,0,0,0.07);
            border-radius: 20px;
            margin-right: 20px;
        `}>
                <LeftOutline className={css`
                    color: ${page<=1?'#ccc':'#262A30'}
                `} disabled onClick={left} />
                <span className={css`
                margin: 0 16px;
            `}>{page}/{recordList.length}</span>
                <RightOutline className={css`
                    color: ${page===recordList.length?'#ccc':'#262A30'}
                `} onClick={right} />
            </div>
        </div>
        <div className={css`
            width: ${recordList.length * 100}%;
            display: flex;
            transition-duration: 500ms;
            transform: translateX(-${(page-1)*(100/recordList.length)}%);
        `}>
            {
                recordList.map(item => {
                    return <div className={css`
                        width: 100%;
                    `} key={item.id}>
                        {
                            RECORDS_LABEL.map(it => {
                                return <div className={css`
                                padding: 10px 0;
                                border-bottom: 1px solid #E9ECF0;
                            `} key={it.value}>
                                    <div className={css`
                                    font-size: 14px;
                                    color: #959BA3;
                                `}>{it.label}</div>
                                    <div className={css`
                                    margin-top: 10px;
                                    font-size: 16px;
                                    color: #262A30;
                                `}>{item[it.value] || '-'}</div>
                                </div>
                            })
                        }
                    </div>
                })
            }
        </div>
    </div>
}