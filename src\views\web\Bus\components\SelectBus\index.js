import React, { useState, useEffect } from 'react'
import { PlusCircleOutlined } from '@ant-design/icons'
import { css } from 'emotion'
import Wrap from '../wrap'
import { Modal, Select, DatePicker, Space, message } from 'antd'
import { CARATTR } from '../../constant'
import moment from 'moment'
import Service from 'ROOT/service'
import styles from '../../index.scss'
import locale from 'antd/es/date-picker/locale/zh_CN';
import _ from 'lodash'
const { Option } = Select


export default (props) => {
    const { close,createCar,createDriver, initData,finish,initValue,editObj } = props
    const [carList, setCarList] = useState([])
    const [driverList, setDriverList] = useState([])
    const [data,setData] = useState({})
    const editable = initValue.eventType === 'receive_car' //收车
    const [useStart, setUseStart] = useState(initData.useStart || '')
    const [useEnd, setUseEnd] = useState(initData.useEnd || '')

    useEffect(() => {
        setData(initData)
        getBusList('')
        getDriver('')
    }, [])

    useEffect(()=> {
        console.log(props)
    },[props.initData])

    const getBusList = (carNo) => {
        Service.getCarInfoList({ carNo: carNo }).then(res => {
            setCarList(res.data)
        })
    }

    const onSearchCar = (e) => {
        getBusList(e)
    }

    const onSearchDriver=(e)=> {
        getDriver(e)
    }

    const getDriver = (name) => {
        Service.getDriverInfoList({ name: name }).then(res => {
            setDriverList(res.data)
        })
    }

    const selectCar= (e)=> {
        setData({
            ...data,
            ...carList.filter(item=>item.carNo===e)[0]
        })
    }

    const selectDriver=(e)=> {
        setData({
            ...data,
            ...driverList.filter(item=>item.driverId===e)[0]
        })
    }

    const submit=()=>{
        if(!data.carNo){
            message.warning('请选择车辆')
            return false
        }
        if(!data.driverId){
            message.warning('请选择驾驶员')
            return false
        }
        let newData = _.cloneDeep(data)
        if(editObj.editUseEnd || editObj.editUseStart){
            newData = {
                ...newData,
                useEnd,
                useStart
            }
        }
        if(useStart && useEnd && moment(useStart).unix() > moment(useEnd).unix()){
            Modal.info({
                title:'提醒',
                content: '收车时间不得小于出车时间',
            })
            return false
        }
        finish(newData)
    }

    return <div>
        <Modal
            title="车辆使用信息"
            visible={true}
            width={600}
            onCancel={() => close()}
            onOk={submit}
        >
            <div>
                <Wrap name="车辆信息">
                    <div>
                        <div className={styles.content}>
                            <span className={css`
                            font-size: 14px;
                            color: #262A30;
                        `}>选择车辆</span>
                            <a disabled={!editObj.send_car_start} onClick={()=>createCar(data)}><PlusCircleOutlined style={{ marginRight: '8px' }} />创建车辆信息</a>
                        </div>
                        <Select disabled={!editObj.send_car_start} value={data.carNo || undefined} showSearch={true} onSearch={onSearchCar} onChange={selectCar} style={{ width: '100%', marginBottom: '24px' }}>
                            {
                                carList.map((item,index)=> {
                                    return <Option key={index} value={item.carNo}>{item.carNo}</Option>
                                })
                            }
                        </Select>
                        <div className={styles.detail}>
                            <div className={styles.itemWrap}>
                                <div >车牌号：{data &&  data.carNo}</div>
                                <div>车辆属性：{data&& data.carAttr && CARATTR[data.carAttr] }</div>
                            </div>
                            <div className={styles.itemWrap}>
                                <div >车品牌：{data &&  data.carBrand}</div>
                                <div >车型：{data &&  data.carModel}</div>
                            </div>
                        </div>
                    </div>
                </Wrap>
                <Wrap name="驾驶员信息">
                    <div>
                        <div className={styles.content}>
                            <span className={css`
                            font-size: 14px;
                            color: #262A30;
                        `}>选择驾驶员</span>
                            <a disabled={!editObj.send_car_start} onClick={()=>createDriver(data)}><PlusCircleOutlined style={{ marginRight: '8px' }} />创建驾驶员信息</a>
                        </div>
                        <Select
                            disabled={!editObj.send_car_start}
                            value={data.driverId || undefined}
                            onSearch={onSearchDriver}
                            showSearch={true}
                            style={{ width: '100%', marginBottom: '24px' }}
                            onChange={selectDriver}
                        >
                            {
                                driverList.map((item=> {
                                    return <Option key={item.driverId} value={item.driverId}>{item.driver}</Option>
                                }))
                            }
                        </Select>
                        <div className={styles.detail}>
                            <div className={styles.itemWrap}>
                                <div >姓名：{data && data.driver}</div>
                                <div>手机号：{data && data.driverPhone}</div>
                            </div>
                        </div>
                    </div>
                </Wrap>
                {
                    (editObj.editUseEnd || editObj.editUseStart) && <Wrap name="用车信息">
                    <div className={styles.detail}>
                        <div className={styles.info}>
                            <div className={styles.infoitem}>
                                <div className={styles.title}>实际出车时间</div>
                                <DatePicker disabled={!editObj.editUseStart} locale={locale} defaultValue={useStart?moment(useStart, 'YYYY-MM-DD HH:mm:ss'):''} showTime onChange={e=>{
                                    setUseStart(e?moment(e).format('YYYY-MM-DD HH:mm:ss'):'')
                                }} style={{ width: '90%' }} />
                            </div>
                            <div className={styles.infoitem}>
                                <div className={styles.title}>实际收车时间</div>
                                <DatePicker disabled={!editObj.editUseEnd} locale={locale} defaultValue={useEnd?moment(useEnd, 'YYYY-MM-DD HH:mm:ss'):''} showTime onChange={e=>{setUseEnd(e?moment(e).format('YYYY-MM-DD HH:mm:ss'):'')}} style={{ width: '90%' }} />
                            </div>
                        </div>
                    </div>
                </Wrap>
                }
            </div>
        </Modal>
    </div>
}