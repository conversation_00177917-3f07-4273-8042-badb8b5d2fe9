import React, { useState, useEffect, useMemo } from 'react'
import { Table, Button, message } from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import { css } from 'emotion'
import _ from 'lodash'
import NewCreateBus from '../newCreateBus'
import { CARATTR } from '../../constant'

export default (props) => {
    const { initData, setPropsData, initValue, editObj } = props
    const [dataIndex, setDataIndex] = useState(null)
    const [data, setData] = useState({})
    const [dataSource, setDataSource] = useState(initData)
    const [busShow, setBusShow] = useState(false)
   

    const oldColumns = [
        {
            key: "carNo",
            dataIndex: "carNo",
            title: "车牌号",
        },
        {
            key: "carAttr",
            dataIndex: "carAttr",
            title: "车辆属性",
            render: (val)=> {
                return CARATTR[val]
            },
        },
        {
            key: "carBrand",
            dataIndex: "carBrand",
            title: "车品牌",
        },
        {
            key: "carModel",
            dataIndex: "carModel",
            title: "车型",
        },
        {
            key: "driver",
            dataIndex: "driver",
            title: "驾驶员",
        },
        {
            key: "driverPhone",
            dataIndex: "driverPhone",
            title: "驾驶员号码",
        },
        {
            key: "useStart",
            dataIndex: "useStart",
            title: "实际出车时间",
        },
        {
            key: "useEnd",
            dataIndex: "useEnd",
            title: "实际收车时间",
        },
        {
            key: "action",
            dataIndex: "action",
            title: "操作",
            render: (val, record,index) => {
                return (<div>
                    {
                        editObj.send_car_start && <a onClick={()=>del(index)}>删除</a>
                    }
                    {
                        (editObj.editUseEnd || editObj.send_car_start || editObj.editUseStart) &&<a className={css`
                            margin-left: ${editObj.send_car_start?'10':0}px
                        `} onClick={()=>{
                            setBusShow(true)
                            setData(record)
                            setDataIndex(index)
                        }}>编辑</a>
                    }
                </div>)
            },
        },
    ]

    const newColumns =  [
        {
            key: "carNo",
            dataIndex: "carNo",
            title: "车牌号",
        },
        {
            key: "carModel",
            dataIndex: "carModel",
            title: "车型",
        },
        {
            key: "driver",
            dataIndex: "driver",
            title: "司机姓名",
        },
        {
            key: "driverPhone",
            dataIndex: "driverPhone",
            title: "司机手机号",
        },
        {
            key: "action",
            dataIndex: "action",
            title: "操作",
            render: (val, record,index) => {
                return (<div>
                    {
                        editObj.send_car_start && <a onClick={()=>del(index)}>删除</a>
                    }
                    {
                        (editObj.send_car_start) &&<a className={css`
                            margin-left: ${editObj.send_car_start?'10':0}px
                        `} onClick={()=>{
                            setBusShow(true)
                            setData(record)
                            setDataIndex(index)
                        }}>编辑</a>
                    }
                </div>)
            },
        },
    ]

    const columns = useMemo(() => dataSource.findIndex(item => item.carAttr) !== -1 ? oldColumns : newColumns, [dataSource])

    useEffect(() => {
        setDataSource(initData)
    }, [])

    const del=(num)=> {
        // 删除列表数据
        const newArr = dataSource.filter((item,index)=> index !== num)
        setDataSource(newArr)
        setPropsData(newArr)
    }

    const finish = (e) => {
        console.log(e,'e')
        // 选择车辆驾驶员确定按钮
        setBusShow(false)
        if(dataIndex!==null){
            // 代表编辑
            const newArr = dataSource.map((item,index)=> {
                if(index===dataIndex){
                    return e
                }
                return item
            })
            setDataSource(newArr)
            setPropsData(newArr)
        }else {
             // 代表新增
            if(dataSource.some(item=> item.carNo===e.carNo )){
                message.warning('已选择该车辆，请重新选择')
                return false
            }
            setDataSource(dataSource.concat([{ ...e }]))
            setPropsData(dataSource.concat([{ ...e }]))
        }
        setData({})
        setDataIndex(null)
    }

    const handleClose = () => {
        setBusShow(false)
        setData({})
        setDataIndex(null)
    }

    return (<div>
        <Table
            columns={columns}
            dataSource={dataSource}
            rowKey={(record, index) =>{
                console.log(index)
            }}
            pagination={false}
        />
        {
            editObj.send_car_start && <div className={css`
            text-align: center;
            padding: 10px 0;
        `}>
            <Button className={css`
                width: 100%;
                color: #fff !important;
            `} type="primary" ghost onClick={() => setBusShow(true)} icon={<PlusOutlined />}>增加</Button>
            </div>
        }
        {/* {
            busShow && <SelectBus editObj={editObj} initValue={initValue}  finish={finish} initData={data} createCar={createCar} createDriver={createDriver} close={() => setBusShow(false)} />
        } */}
        {
            busShow && <NewCreateBus editObj={editObj} initValue={initValue}  finish={finish} initData={data} close={handleClose} />
        }
    </div>)
}