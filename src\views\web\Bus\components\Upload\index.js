import React, { useState, useEffect, useRef } from 'react'
import { UploadOutlined, PaperClipOutlined, DeleteOutlined } from '@ant-design/icons'
import UploadFileList from "@xm/upload-file-list";
import {
    Upload,
    Button,
    Space,
    message,
} from 'antd'
import FileList from '../FileList'
import { downloadUrl } from 'ROOT/utils'
import { css } from 'emotion'
import { Modal } from 'antd'
import Service from 'ROOT/service'
import { nativeCommon } from 'ROOT/components/util'
import Loading from 'ROOT/components/Loading'
import bowser from 'bowser'

let first = true

export default itemProps => {
    const InputImport = useRef()
    const { value = [], mutators, props, schema, form, editable } = itemProps
    console.log(editable, 'editable')
    const xComponentProps = schema['x-component-props'] || {}
    const [loading, setLoading] = useState(false)

    const selectFiles = () => {
        InputImport.current.click()
    }

    const fileChange = (e) => {
        let files = e.currentTarget.files
        uploadFiles(files)
    }

    // const getFileItem = async (data)=> {
    //     console.log(data,'data')
    //     let formData = new FormData()
    //     formData.append('file', data)
    //     const res = await Service.uploadFile(formData)
    //     console.log(res,'res')
    //     return { 
    //         name: data.name,
    //         fileUrl: res.fileUrl,
    //         type: data.type,
    //         size: data.size
    //     }
    // }

    const uploadFiles = async (files) => {
        const list = Array.from(files)
        if (list.some(item => isNaN(Number(item.name.substr(0, 1))))) {
            message.warning('文件名首字符必须数字')
            InputImport.current.value = null
            return false
        } else if (list.some(item => item.size === 0)) {
            message.error('无法上传0kb或0kb以下大小的文件，请重新选择文件上传')
            InputImport.current.value = null
            return false
        } else if (list.some(item => item.size > 200 * 1024 * 1024)) {
            message.error('无法上传大于200M的文件，请重新选择文件上传')
            InputImport.current.value = null
            return false
        } else if (list.length > (50 - value.length)) {
            message.error('文件上传总数不得大于50，请重新选择文件上传')
            InputImport.current.value = null
            return false
        }
        else {
            setLoading(true)
            let newList = []
            for (let i = 0; i < list.length; i++) {
                let formData = new FormData()
                formData.append('file', list[i])
                const res = await Service.uploadFile(formData)

                newList.push({
                    name: list[i].name,
                    fileUrl: res.fileUrl,
                    type: list[i].type,
                    size: list[i].size
                })
            }
            const submitList = value.concat(newList.map(item => {
                console.log(item)
                return {
                    fileName: item.name,
                    fileUrl: item.fileUrl,
                    fileType: item.name.substring(item.name.lastIndexOf('.') + 1),
                    fileSize: item.size
                }
            }))
            console.log(submitList, 'submitList')
            mutators.change(submitList)
            InputImport.current.value = null
            setLoading(false)
        }
    }

    const getFileList = (data = []) => {

        const newList = data && data instanceof Array && data.length > 0 ? data.map(file => {
            if (file.fileName) {
                return {
                    name: file.fileName,
                    size: file.fileSize,
                    suffix: file.fileName.substring(file.fileName.lastIndexOf('.') + 1),
                    url: file.fileUrl
                }
            } else {
                return null
            }
        }) : []

        return newList.filter(item => item)
    }

    const changeList = (data) => {
        console.log(data)
        mutators.change(data.map(item => ({
            fileName: item.name,
            fileUrl: item.url,
            fileType: item.suffix,
            fileSize: item.size
        })))
    }

    return <div className={css`width:100%`}>
        {
            editable && <span
                onClick={selectFiles}
                className="upload-file-btn"
            >
                上传文件
            </span>
        }
        <input
            type="file"
            multiple="multiple"
            ref={InputImport}
            onChange={fileChange}
            className={css`
                        position: absolute;
                        top: -9999px;
                        left: -9999px;
                        `}
        />
        {
            getFileList(value).length > 0 ? <div>
                <UploadFileList className={css`
                    width: 100%;
                    margin-top:20px;
            `} dataSource={getFileList(value)}
                    options={
                        { enablePreview: true, enableDownload: true, enableRename: editable, enableSort: editable, enableDelete: editable }
                    }
                    onRename={e => changeList(e)}
                    onSortEnd={e => changeList(e)}
                    onDelete={e => changeList(e)}
                />
            </div> : !editable && !getFileList(value).length ? <p>无</p> : null
        }
        {
            loading && <Loading isFullScreen={true} />
        }
    </div>
}