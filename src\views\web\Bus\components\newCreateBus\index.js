import React, { useState, useEffect, useMemo } from 'react'
import { message, Modal } from 'antd' 
import { SchemaMarkupForm, createAsyncFormActions } from '@formily/antd'
import Cookies from 'js-cookie'
import Service from 'ROOT/service'
import {
    Input,
} from '@formily/antd-components'
import { newCreateBus } from '../../schema'

export default (props)=> {
    const { close, initData} = props
    const actions = useMemo(() => createAsyncFormActions(), [])
    const submit= async ()=> {
      const data = await actions.submit()
      props.finish(data.values)
    }

    return (<Modal
        visible
        title="车辆使用信息"
        width={520}
        onOk={submit}
        onCancel={close}
    >
        <SchemaMarkupForm 
            schema={newCreateBus}
            actions={actions}
            initialValues={initData}
            components={{
                Input,
            }}
         />
    </Modal>)
}