import React from 'react'
import { css } from 'emotion'

export default (props) => {
    console.log(props)
    const { children, name } = props
    return <div className={css`margin-bottom: 24px`}>
        <div className={css`
            display: flex;
            align-items: center;
        `}>
            <span className={css`
            width: 4px;
            height: 14px;
            background: #488FF9;
            display: inline-block;
        `}></span>
            <span className={css`
                font-weight: Semibold
                font-size: 14px;
                color: #262A30;
                margin-left:8px;
            `}>{name}</span>
        </div>
        <div>
            {children}
        </div>
    </div>
}