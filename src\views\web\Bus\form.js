import React, { useMemo, useState, useEffect } from 'react'
import { SchemaMarkupForm, FormEffectHooks, createAsyncFormActions } from '@formily/antd'
import Editable from 'ROOT/components/Formily/Editable'
import Upload from './components/Upload'
import moment from 'moment'
import {
    FormMegaLayout,
    Input,
    DatePicker,
    Select,
    Radio,
    NumberPicker,
    ArrayTable,
    FormBlock
} from '@formily/antd-components'
import SelectMember from 'ROOT/components/Formily/userSelect'
import Table from './components/Table'
import { Schema, HideMobileArr } from './schema'
import Grid from './components/Grid'
import { CITYSIDE, USERANAGE } from './constant'
import Service from 'ROOT/service'

export const actions = createAsyncFormActions()

export default (props) => {
    const { userInfo, expressionScope, editable, changeMobile = () => { } } = props
    Editable.isFieldComponent = true
    Upload.isFieldComponent = true
    SelectMember.isFieldComponent = true
    Grid.isFieldComponent = true
    Table.isFieldComponent = true
    const { onFieldValueChange$, onFieldInputChange$ } = FormEffectHooks
    const [initValue, setInitValue] = useState({})

    useEffect(() => {
        console.log(props)
        document.title = "车辆使用审批"
        setInitValue(props.initValue)
    }, [props])

    const uesEffects = () => {
        onFieldValueChange$('gridNameWrap.grid').subscribe(({ value }) => {
            console.log(value)
            if (value) {
                actions.setFieldState('gridNameWrap.gridName', state => {
                    state.value = value.gridName
                })
                actions.setFieldState('gridNoWrap.gridNo', state => {
                    state.value = value.gridNo
                })
            }
        })
        onFieldValueChange$('gridNoWrap.grid').subscribe(({ value }) => {
            if (value) {
                actions.setFieldState('gridNameWrap.gridName', state => {
                    state.value = value.gridName
                })
                actions.setFieldState('gridNoWrap.gridNo', state => {
                    state.value = value.gridNo
                })
            }
        })
        onFieldValueChange$('carUser').subscribe(async ({ value }) => {
            console.log(value, 'carUser')
            if (value && value.length) {
                if (value[0].mobile) {
                    let { mobile = '' } = value[0]
                    changeMobile(mobile)
                    // if (!value[0].deptId) {
                    if (HideMobileArr.includes(mobile)) {
                        mobile = mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
                    }
                    actions.setFieldState('carUserPhone', state => {
                        state.value = mobile
                    })
                }
            }
        })
        onFieldInputChange$('time').subscribe(({ value }) => {
            const checkDate = moment(value[0]).unix()
            const endDate = moment(value[1]).unix()
            actions.setFieldState('days', state => {
                const day = (endDate - checkDate) / 3600 / 24
                let num = null
                if (day > Math.floor(day)) {
                    num = Math.floor(day) + 1
                } else {
                    num = day
                }
                state.value = num
            })
        })
        onFieldInputChange$('citySide').subscribe(({ value }) => {
            const arr = props.initValue.applyNo ? props.initValue.applyNo.split(']') : []
            actions.setFieldState('applyNo', state => {
                state.value = `${userInfo.cityOrgName}${CITYSIDE[value]}用车[${new Date().getFullYear()}]${arr.length > 1 ? arr[1] : ''}`
            })

        })
    }

    return (<div>
        {
            userInfo.cityOrgName && <SchemaMarkupForm
                schema={Schema}
                actions={actions}
                editable={editable}
                initialValues={initValue}
                effects={() => {
                    uesEffects()
                }}
                components={{
                    DatePicker,
                    RangePicker: DatePicker.RangePicker,
                    Input,
                    FormMegaLayout,
                    Select,
                    TextArea: Input.TextArea,
                    Radio,
                    Editable,
                    Upload,
                    NumberPicker,
                    ArrayTable,
                    RadioGroup: Radio.Group,
                    SelectMember,
                    Grid,
                    FormBlock,
                }}
                expressionScope={{ ...expressionScope, editable: editable }}
            >
            </SchemaMarkupForm>
        }
    </div>)
}