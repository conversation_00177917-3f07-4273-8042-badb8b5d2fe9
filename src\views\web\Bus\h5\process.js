import React, { useState, useMemo, useEffect } from 'react'
import { css } from 'emotion'
import { withRouter } from 'react-router-dom'
import { Buttons, Steps } from 'ROOT/components/Process'
import { Space, Button, Tabs, Toast } from 'antd-mobile'
import { getQueryString, closeTheWindow, reloadWindow } from 'ROOT/utils'
import { patchData } from 'ROOT/utils/index'
import Form, { actions } from '../form'
import { CITYSIDE, USERANAGE } from '../constant'
import RecordList from '../components/RecordList'
import Service from 'ROOT/service'
import style from '../index.scss'
import Cookies from 'js-cookie'
import { isEmpty } from 'lodash'

export default withRouter((props) => {
    const { userTaskId, procFormDataKey } = useMemo(() => getQueryString(props.location.search), [props.location.search])
    const orgId = Cookies.get('orgId')
    const [userInfo, setUserInfo] = useState({})
    const [initValue, setInitValue] = useState({})
    const [tab, setTab] = useState('content')
    const [editObj, setEditObj] = useState({
        receive_car: false,
        send_car_start: false,
        editBase: false,
        editUseEnd: false,
        editUseStart: false
    })

    useEffect(() => {
        document.title = `${userInfo.cityOrgName}车辆使用审批单`
    }, [])

    const getDetail = () => {
        Service.getOrgType({ orgId }).then(info => {
            setUserInfo(info.data)
            Service.getApplyDetail({ id: procFormDataKey }).then(res => {
                const { data } = res
                setInitValue({
                    ...initValue,
                    ...data,
                    time: [data.useStart, data.useEnd],
                    isGrid: data.isGrid.toString(),
                    gridNameWrap: {
                        gridName: data.gridName
                    },
                    gridNoWrap: {
                        gridNo: data.gridNo
                    },
                    carUser: [{
                        id: data.carUserId,
                        name: data.carUserName,
                    }],

                })
            })
        })
    }

    const expressionScope = {
        useRange: USERANAGE[userInfo.orgType],
        uploadTitle: '附件',
        orgType: userInfo.orgType,
        labelAlign: 'left',
        editable: false
    }

    const setFormAccess = ({ access, editMode, draft }) => {
        console.log(access)
        if (access && Object.keys(access).length > 0) {
            let obj = {}
            Object.keys(access).map(item => {
                obj[item] = access[item] === 'WRITE' ? true : false
            })
            setEditObj({
                ...editObj,
                ...obj
            })
        }
        if (draft) {
            setInitValue({
                ...draft
            })
        } else {
            getDetail()
        }
    }


    const submitForm = async () => {
        const res = await submit()
        if (res.success) {
            return Promise.resolve()
        } else {
            return Promise.reject()
        }
    }

    const submit = async () => {
        const res = await Service.applyRecordEdit({
            applyId: procFormDataKey,
            recordList: initValue.recordList
        })
        return res
    }

    const finishSubmit = async () => {
        reloadWindow()
    }

    return (
        <div className={css`
        padding: 15px;
        padding-bottom: 60px;
        .sc-gsDKAQ {
            border-bottom: 1px solid #E9ECF0;
        }
    `}>
            <Tabs activeKey={tab} onChange={setTab}>
                <Tabs.Tab title="申请内容" key="content" >
                    {
                        userInfo.orgType && <Form
                            editable={false}
                            initValue={initValue}
                            userInfo={userInfo}
                            expressionScope={expressionScope}
                        />
                    }
                </Tabs.Tab>
                <Tabs.Tab title="审批流程" key="process">
                    <div className="h5-main-body" style={{ padding: '10px 15px 60px' }}>
                        <Steps
                            userTaskId={userTaskId}
                        />
                    </div>
                </Tabs.Tab>
                <Tabs.Tab title="用车清单" key="useCar">
                    <RecordList recordList={initValue.recordList} />
                </Tabs.Tab>
            </Tabs>
            <div className={style.buttongroup}>
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    {
                        initValue.eventType !== 'send_car_start' && initValue.eventType !== 'receive_car' ? (
                            !isEmpty(initValue) && (
                                <Buttons
                                    userTaskId={userTaskId}
                                    onFinish={finishSubmit}
                                    onSubmit={submitForm}
                                    onMount={setFormAccess}
                                    onSubmitOpen={async () => {
                                        const data = await actions.submit()
                                        const result = patchData(data.values, initValue)
                                        return {
                                            ...result,
                                            carApplyUser: {
                                                id: values.carUserId,
                                                name: values.carUserName,
                                                orgId: orgId
                                            }
                                        }
                                    }}
                                />
                            )
                        ) : '请前往web进行操作'
                    }
                </div>
            </div>
        </div>
    )
})