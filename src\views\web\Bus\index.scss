.buttongroup {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 60px;
    padding-top: 14px;
    text-align: center;
    background-color: white;
    box-shadow: 0 0 5px rgba(0, 0, 0, .2);
    z-index: 103;
}
.detail {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.45);
    .itemWrap {
        display: flex;
        margin-bottom: 24px;
        div {
            width: 50%;
        }
    }
}
.content {
    display: flex;
    justify-content: space-between;
    padding-top: 20px;
    padding-bottom: 8px;
}

.info {
    display: flex;
    margin-bottom: 24px;
    margin-top: 24px;
    .infoitem {
        width: 50%;
        .title {
            font-size: 14px;
            color: #262a30;
            margin-bottom: 8px;
        }
    }
}

.grid-gaps-child
    > .iCAyUt
    > .ant-form-item-control-wrapper
    > .ant-form-item-control
    > .ant-form-item-children
    > .mega-layout-container-wrapper
    > .mega-layout-container-content.grid,
.jTpGQJ
    > .ant-form-item-control-wrapper
    > .ant-form-item-control
    > .ant-form-item-children
    > .mega-layout-item-content
    > .mega-layout-container-content.grid,
.jTpGQJ
    > .ant-form-item-control
    > .ant-form-item-control-input
    > .ant-form-item-control-input-content
    > .mega-layout-container-wrapper
    > .mega-layout-container-content.grid,
.jTpGQJ
    > .ant-form-item-control
    > .ant-form-item-control-input
    > .ant-form-item-control-input-content
    > .mega-layout-item-content
    > .mega-layout-container-content.grid {
    grid-column-gap: 0px !important;
}
