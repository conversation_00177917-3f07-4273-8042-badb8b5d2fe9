import React, { useMemo, useEffect, useState, useRef } from 'react'
import Form, { actions } from '../form'
import { withRouter } from 'react-router-dom'
import { Buttons } from 'ROOT/components/Process'
import { message, Modal } from 'antd'
import { getQueryString, closeTheWindow } from 'ROOT/utils'
import { CITYSIDE, USERANAGE } from '../constant'
import Cookies from 'js-cookie'
import Service from 'ROOT/service'
import useMyInfo from 'ROOT/hooks/useMyInfo'
import moment from 'moment'
import _, { isEmpty } from 'lodash'
import { patchData } from 'ROOT/utils/index'
import { getWpsUrl } from 'ROOT/utils/wps'
import { HideMobileArr } from '../schema'

const { confirm } = Modal

let timer = null
export default withRouter((props) => {
    const orgId = Cookies.get('orgId')
    const myInfo = useMyInfo({ isSetStorage: true })
    const [userInfo, setUserInfo] = useState({})
    const { userTaskId, procKey, appId, procFormDataKey, procFormKey } = useMemo(() => getQueryString(props.location.search), []);
    const [formId, setFormId] = useState(procFormDataKey)
    const [initValue, setInitValue] = useState({ citySide: 1 })
    const [isSave, setSave] = useState(false)
    const [mobile, changeMobile] = useState() // 暂存用车人手机，用于提交时放回去

    const expressionScope = {
        useRange: USERANAGE[userInfo.orgType],
        uploadTitle: '附件',
        orgType: userInfo.orgType,
        labelAlign: 'top',
        editable: true
    }

    useEffect(() => {
        console.log(myInfo, 'myInfo')
        if (myInfo.loginOrgId) {
            getUserInfo()
        }
    }, [myInfo])

    useEffect(() => {
        window.addEventListener('process', e => changeStatus(e), false)
        return () => {
            clearTimeout(timer)
        }
    }, [])

    useEffect(() => {
        if (procFormDataKey) {
            getDetail()
        }
    }, [])

    const getDetail = async () => {
        const wpsContent = await getWpsUrl(procFormDataKey)
        Service.getApplyDetail({ id: procFormDataKey }).then(res => {
            if (wpsContent) {
                res.data.data.fileList[0] = wpsContent
            }
            let { data } = res
            let { carUserPhone = '' } = data
            changeMobile(carUserPhone)
            if (HideMobileArr.includes(carUserPhone)) {
                carUserPhone = carUserPhone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
            }
            setInitValue({
                ...initValue,
                ...data,
                carUserPhone,
                time: [data.useStart, data.useEnd],
                isGrid: data.isGrid.toString(),
                gridNameWrap: {
                    gridName: data.gridName
                },
                gridNoWrap: {
                    gridNo: data.gridNo
                },
                carUser: [{
                    id: data.carUserId,
                    name: data.carUserName,
                }],
                remark: data.remark || '无'
            })
        })
    }

    const saveDraft = async (type) => {
        const data = await submitData()
        setFormId(data.data || formId)
        const res = await Service.saveDraft({
            appId,
            appTasks: [{
                appTaskId: data.data || formId,
                businessType: 2,
                emergencyLevel: undefined, //123
                handleEntry: [{
                    handleType: 0, // 草稿
                    handlerId: myInfo.loginUid,
                }],
                processType: `${userInfo.cityOrgName}车辆使用审批单`, //未知
                sponsorId: myInfo.loginUid,
                jumpToDetail: 1,
                title: `${userInfo.cityOrgName}车辆使用审批单`, //
                detailUrl: decodeURIComponent(procFormDataKey ? `${location.href.replace('/extra-forms/', '/extra-forms-h5/').replace(/backurl=.*?&|backurl=.*?$/, '').replace('pc', 'h5')}` : `${location.href.replace('/extra-forms/', '/extra-forms-h5/').replace(/backurl=.*?&|backurl=.*?$/, '').replace('pc', 'h5')}&procFormDataKey=${data.data || formId}`),
                webDetailUrl: decodeURIComponent(procFormDataKey ? `${location.href.replace(/backurl=.*?&|backurl=.*?$/, '')}` : `${location.href.replace(/backurl=.*?&|backurl=.*?$/, '')}&procFormDataKey=${data.data || formId}`),
            }],
        })
        if (res.success) {
            if (type === 1) {
                message.success('保存', () => {
                    closeTheWindow(props.location.search)
                })
            } else {
                message.success('保存', () => {
                    setSave(true)
                })
            }
        }
    }

    const deleteDraft = (id) => {
        confirm({
            content: '你选择了注销当前文档，系统将删除该文档，是否继续？',
            onOk: async () => {
                const res = await Service.deleteDraft({
                    appId,
                    appTasks: [{
                        appTaskId: formId || procFormDataKey,
                        handlerIds: [myInfo.loginUid],
                    }],
                })
                if (res.success) {
                    message.success('注销成功')
                    closeTheWindow(props.location.search)
                }
            },
        })
    }

    const deleteDraftNoConfirm = () => {
        Service.deleteDraft({
            appId,
            appTasks: [{
                appTaskId: formId || procFormDataKey,
                handlerIds: [myInfo.loginUid],
            }],
        }).then(res => {
            console.log(res)
        })
    }

    const buttonGroup = () => {
        const array = [{
            name: '保存退出',
            async: false,
            onClick: () => {
                saveDraft(1)
            }
        }, {
            name: '保存',
            async: false,
            onClick: () => {
                saveDraft(2)
            }
        }]
        const delDraft = [{
            name: '注销',
            async: false,
            onClick: () => {
                deleteDraft(1)
            }
        }]
        return array.concat(formId ? delDraft : [])
    }

    const changeStatus = (e) => {
        if (e.detail.type === 'started') {
            timer = setTimeout(() => {
                closeTheWindow(props.location.search)
            }, 1000)
        }
    }

    const getUserInfo = async () => {
        // const deptData = await Service.getDepts({
        //     orgId: myInfo.loginOrgId,
        //     uid: myInfo.loginUid || ''
        // })
        // const deptList = deptData.data
        const { linkPath } = myInfo
        Service.getOrgType({ orgId: myInfo.loginOrgId }).then(res => {
            const { data } = res
            setUserInfo(data)
            setInitValue({
                ...initValue,
                applyNo: `${data.cityOrgName}${CITYSIDE[initValue.citySide]}用车〔${new Date().getFullYear()}〕`,
                phone: myInfo.loginMobile || '',
                userName: myInfo.loginName || '',
                deptName: linkPath[0].linkPath.split('\\').join('/'),
                deptId: linkPath[0].deptId,
                uid: myInfo.loginUid || '',
                _dept: {
                    value: linkPath[0].deptId,
                    label: linkPath[0].linkPath.split('\\').join('/'),
                },
                applyDate: moment(new Date()).format('YYYY-MM-DD')
            })
        })
    }

    const submitForm = async () => {
        const data = await actions.submit()
        let { values } = data
        const res = await submitData()
        if (procFormDataKey) {
            deleteDraftNoConfirm()
        }
        if (isSave) {
            deleteDraftNoConfirm()
        }
        if (res.success) {
            return res.data || formId
        }
        throw new Error('保存出错')
    }

    const submitData = async () => {
        const data = await actions.submit()
        const { values } = data
        let newData = _.cloneDeep(values)
        newData.useStart = newData.time[0]
        newData.useEnd = newData.time[1]
        newData.carUserName = newData.carUser[0].name
        newData.carUserId = newData.carUser[0].id || newData.carUser[0].uid
        newData.carUserPhone = mobile
        delete newData.time
        delete newData.carUser
        delete newData._dept
        if (newData.isGrid == '1') {
            newData.gridName = newData.gridNameWrap.gridName
            newData.gridNo = newData.gridNoWrap.gridNo

            delete newData.gridNameWrap
            delete newData.gridNoWrap
        }
        newData = patchData(newData, initValue)

        const res = (procFormDataKey || isSave || formId) ? await Service.applyEdit({ ...newData, id: formId, orgId }) : await Service.applyCreate({ ...newData, orgId })
        return res
    }

    return (
        <div>
            <h1 className='form-title'>{userInfo.cityOrgName}车辆使用审批单</h1>
            <Form
                initValue={initValue}
                userInfo={userInfo}
                expressionScope={expressionScope}
                editable={true}
                changeMobile={changeMobile}
            />
            {
                (!isEmpty(initValue) || !procFormDataKey) && (
                    <Buttons
                        procKey={procKey}
                        appId={appId}
                        onSubmit={submitForm}
                        onSubmitOpen={async () => {
                            const data = await actions.submit()
                            const { values } = data
                            values.carUserPhone = mobile
                            if (values.isGrid == '1') {
                                values.gridName = values.gridNameWrap.gridName
                                values.gridNo = values.gridNoWrap.gridNo
                            }
                            if (values.days <= 0) {
                                message.error('开始时间不得大于等于结束时间')
                                throw new Error('开始时间不得大于等于结束时间')
                            } else if (values.days > 7) {
                                return new Promise(function (resolve, reject) {
                                    Modal.warning({
                                        title: '超长使用车辆提醒',
                                        content: '当前用车时长已经超过7天了，请注意用车',
                                        onOk: () => {
                                            resolve({
                                                ...values,
                                                carApplyUser: [{
                                                    id: values.carUser[0].id,
                                                    name: values.carUser[0].name,
                                                    orgId: myInfo.loginOrgId
                                                }],
                                                useStart: values.time[0],
                                                useEnd: values.time[1],
                                                carUserName: values.carUser[0].name,
                                                carUserId: values.carUser[0].id
                                            })
                                        }
                                    })
                                })
                            }
                            let result = {
                                ...values,
                                carApplyUser: [{
                                    id: values.carUser[0].id,
                                    name: values.carUser[0].name,
                                    orgId: myInfo.loginOrgId
                                }],
                                useStart: values.time[0],
                                useEnd: values.time[1],
                                carUserName: values.carUser[0].name,
                                carUserId: values.carUser[0].id
                            }
                            result = patchData(result, initValue)
                            return result
                        }}
                        extraButtons={buttonGroup()}
                    />
                )
            }
        </div>)
})