import React, { useMemo, useEffect, useState } from 'react'
import Form, { actions } from '../form'
import { withRouter } from 'react-router-dom'
import { Buttons, Steps } from 'ROOT/components/Process'
import { CITYSIDE, USERANAGE } from '../constant'
import { getQueryString, closeTheWindow, reloadWindow } from 'ROOT/utils'
import Cookies from 'js-cookie'
import Service from 'ROOT/service'
import _, { isEmpty } from 'lodash'
import Table from '../components/Table'
import useMyInfo from 'ROOT/hooks/useMyInfo'
import { Space, Button, message, Tabs, Modal } from 'antd'
import { HideMobileArr } from '../schema'
import { patchData } from 'ROOT/utils/index'

const { TabPane } = Tabs

let timer = null

export default withRouter(props => {
    const orgId = Cookies.get('orgId')
    const myInfo = useMyInfo({ isSetStorage: true })
    const [userInfo, setUserInfo] = useState({})
    const { userTaskId, userTaskStutus, appId, procFormDataKey } = useMemo(() => getQueryString(props.location.search), []);
    const [editable, setEditable] = useState(false)
    const [initValue, setInitValue] = useState({ remark: '无' })
    const [mobile, changeMobile] = useState('') // 暂存用车人手机，用于提交时放回去
    const [nodeNames, setNodeName] = useState('')
    const [editObj, setEditObj] = useState({
        receive_car: false,
        send_car_start: false,
        editBase: false,
        editUseEnd: false,
        editUseStart: false
    })



    const getDetail = () => {
        Service.getOrgType({ orgId }).then(info => {
            setUserInfo(info.data)
            Service.getApplyDetail({ id: procFormDataKey }).then(res => {
                let { data } = res
                let { carUserPhone = '' } = data
                changeMobile(carUserPhone)

                if (HideMobileArr.includes(carUserPhone)) {
                    carUserPhone = carUserPhone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
                }
                setInitValue({
                    ...initValue,
                    ...data,
                    carUserPhone,
                    time: [data.useStart, data.useEnd],
                    isGrid: data.isGrid.toString(),
                    gridNameWrap: {
                        gridName: data.gridName
                    },
                    gridNoWrap: {
                        gridNo: data.gridNo
                    },
                    carUser: [{
                        id: data.carUserId,
                        name: data.carUserName,
                    }],
                    remark: data.remark || '无'
                })
            })
        })
    }

    const submit = async () => {
        if (editObj.editBase) {
            const data = await actions.submit()
            const { values } = data
            values.carUserPhone = mobile
            console.log(values)
            let newData = _.cloneDeep(values)
            if (newData.isGrid == '1') {
                newData.gridName = newData.gridNameWrap.gridName
                newData.gridNo = newData.gridNoWrap.gridNo
                delete newData.gridNameWrap
                delete newData.gridNoWrap
            }
            newData.useStart = newData.time[0]
            newData.useEnd = newData.time[1]
            newData.carUserName = newData.carUser[0].name
            newData.carUserId = newData.carUser[0].id
            delete newData.time
            delete newData.carUser
            delete newData._dept
            newData = patchData(newData, initValue)
            const res = await Service.applyEdit({ ...newData, orgId, id: procFormDataKey })
            return res
        } else {
            const data = await actions.submit()
            const { values } = data
            const res = await Service.applyRecordEdit({
                applyId: procFormDataKey,
                recordList: initValue.recordList
            })
            return res
        }
    }

    useEffect(() => {
        window.addEventListener('process', e => changeStatus(e), false)
        return () => {
            clearTimeout(timer)
        }
    }, [])

    const changeStatus = (e) => {
        switch (e.detail.type) {
            case 'save-and-quit': closeTheWindow(props.location.search);
                break;
            default: timer = setTimeout(() => {
                closeTheWindow(props.location.search)
            }, 1000);
                break
        }

    }

    const setPropsData = (e) => {
        console.log(e, 'e')
        setInitValue({
            ...initValue,
            recordList: e
        })
    }

    const setFormAccess = ({ access, editMode, draft, process }) => {
        setNodeName(process.nodeName)
        if (access && Object.keys(access).length > 0) {
            let obj = {}
            Object.keys(access).map(item => {
                obj[item] = access[item] === 'WRITE' ? true : false
            })
            console.log(obj)
            setEditObj({
                ...editObj,
                ...obj
            })
            setEditable(obj.editBase)
        }
        if (draft) {
            Service.getOrgType({ orgId }).then(info => {
                setUserInfo(info.data)
                let { carUserPhone = '' } = draft
                changeMobile(carUserPhone)

                if (HideMobileArr.includes(carUserPhone)) {
                    carUserPhone = carUserPhone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
                }
                setInitValue({
                    ...initValue,
                    ...draft,
                    carUserPhone,
                    remark: info.remark || '无',
                })
            })
        } else {
            getDetail()
        }
    }

    const expressionScope = {
        useRange: USERANAGE[userInfo.orgType],
        uploadTitle: '附件',
        orgType: userInfo.orgType,
        labelAlign: 'left',
        editable: editable
    }

    const buttonGroup = () => {
        const array = [{
            name: '保存退出',
            async: false,
            onClick: () => {
                saveDraft(1)
            }
        }, {
            name: '保存',
            async: false,
            onClick: () => {
                saveDraft(2)
            }
        }]
        return nodeNames === '开始节点' && editObj.editBase ? array : []
    }

    const saveDraft = async (type) => {
        const data = await submit()
        // const res = await Service.saveDraft({
        //     appId:userTaskId,
        //     appTasks: [{
        //         appTaskId:Number(procFormDataKey),
        //         businessType: 2,
        //         emergencyLevel: undefined, //123
        //         handleEntry: [{
        //             handleType: 0, // 草稿
        //             handlerId: myInfo.loginUid,
        //         }],
        //         processType: `${userInfo.cityOrgName}车辆使用审批单`, //未知
        //         sponsorId: myInfo.loginUid,
        //         jumpToDetail: 1,
        //         title: `${userInfo.cityOrgName}车辆使用审批单`, //
        //         detailUrl: decodeURIComponent(procFormDataKey ? `${location.href.replace('/extra-forms/', '/extra-forms-h5/').replace(/backurl=.*?&|backurl=.*?$/, '').replace('pc','h5')}` : `${location.href.replace('/extra-forms/', '/extra-forms-h5/').replace(/backurl=.*?&|backurl=.*?$/, '').replace('pc','h5')}&procFormDataKey=${procFormDataKey}`),
        //         webDetailUrl: decodeURIComponent(procFormDataKey ? `${location.href.replace(/backurl=.*?&|backurl=.*?$/, '')}` : `${location.href.replace(/backurl=.*?&|backurl=.*?$/, '')}&procFormDataKey=${procFormDataKey}`),
        //     }],
        // })
        if (data.success) {
            if (type === 1) {
                message.success('保存', () => {
                    closeTheWindow(props.location.search)
                })
            } else {
                message.success('保存', () => {
                    setSave(true)
                })
            }
        }
    }

    return (
        <div>
            <h1 className='form-title'>{userInfo.cityOrgName}车辆使用审批单</h1>
            {
                userInfo.orgType && initValue.id && <Form
                    initValue={initValue}
                    userInfo={userInfo}
                    expressionScope={expressionScope}
                    editable={editable}
                />
            }
            {
                initValue.id && <Table
                    initValue={initValue}
                    userInfo={userInfo}
                    initData={initValue.recordList || []}
                    setPropsData={setPropsData}
                    editObj={editObj}
                />
            }
            <Tabs defaultActiveKey="1">
                <TabPane tab="审批流程" key="1">
                    <Steps
                        userTaskId={userTaskId}
                    />
                </TabPane>
            </Tabs>
            {
                !isEmpty(initValue) && (
                    <Buttons
                        userTaskId={userTaskId}
                        onMount={setFormAccess}
                        onSave={async () => {
                            const data = await actions.submit()
                            let { values } = data
                            values = patchData(values, initValue)
                            values.carUserPhone = mobile
                            return {
                                ...initValue,
                                ...values,
                                carUserName: values.carUser[0].name,
                                carUserId: values.carUser[0].id,
                                recordList: initValue.recordList
                            }
                        }}
                        onSubmitOpen={async (action) => {
                            const data = await actions.submit()
                            let { values } = data
                            values.carUserPhone = mobile
                            console.log(editObj, 'editObj')
                            console.log(action, 'action')
                            values = patchData(values, initValue)
                            if (action) {
                                const res = await submit()
                                return {
                                    ...values,
                                    carApplyUser: [{
                                        id: values.carUser[0].id,
                                        name: values.carUser[0].name,
                                        orgId: orgId
                                    }],
                                    useStart: values.time[0],
                                    useEnd: values.time[1],
                                    carUserName: values.carUser[0].name,
                                    carUserId: values.carUser[0].id,
                                    recordList: initValue.recordList
                                }
                            }
                            if (editObj.send_car_start && !initValue.recordList.length) {
                                message.error('请选择车辆')
                                throw new Error('请选择车辆')
                            }
                            if (editObj.send_car_start) {
                                const res = await submit()
                                if (res.success) {
                                    return {
                                        ...values,
                                        carApplyUser: [{
                                            id: values.carUser[0].id,
                                            name: values.carUser[0].name,
                                            orgId: orgId
                                        }],
                                        useStart: values.time[0],
                                        useEnd: values.time[1],
                                        carUserName: values.carUser[0].name,
                                        carUserId: values.carUser[0].id,
                                        recordList: initValue.recordList
                                    }
                                } else {
                                    throw new Error('提交报错')
                                }
                            }
                            else {
                                const res = await submit()
                                if (res.success) {
                                    return {
                                        ...values,
                                        carApplyUser: [{
                                            id: values.carUser[0].id,
                                            name: values.carUser[0].name,
                                            orgId: orgId
                                        }],
                                        useStart: values.time[0],
                                        useEnd: values.time[1],
                                        carUserName: values.carUser[0].name,
                                        carUserId: values.carUser[0].id,
                                        recordList: initValue.recordList
                                    }
                                } else {
                                    throw new Error('提交报错')
                                }
                            }
                        }}
                        extraButtons={buttonGroup()}
                    />
                )
            }
        </div>
    )
})