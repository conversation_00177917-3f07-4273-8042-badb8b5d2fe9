import { UPLOAD_URL } from 'ROOT/constants'
import locale from 'antd/lib/date-picker/locale/zh_CN'

export const HideMobileArr = ['18878899977', '18877116999', '13707818840', '15877100000', '13607888860', '15977759696', '13977182539', '13807710209']
export const Schema = {
    type: 'object',
    properties: {
        layout: {
            'x-component': 'mega-layout',
            'x-component-props': {
                grid: true,
                autoRow: true,
                labelAlign: '{{labelAlign}}',
                className: 'grid-gaps',
                responsive: {
                    lg: 3,
                    m: 2,
                    s: 1,
                },
            },
            properties: {
                deptName: {
                    key: 'deptName',
                    name: 'deptName',
                    title: '起草部门',
                    'x-component': 'Editable',
                },
                applyDate: {
                    key: 'applyDate',
                    name: 'applyDate',
                    title: '起草时间',
                    'x-component': 'Editable',
                },
                userName: {
                    key: 'userName',
                    name: 'userName',
                    title: '起草人',
                    'x-component': 'Editable',
                },
                applyNo: {
                    key: 'applyNo',
                    name: 'applyNo',
                    title: '文件编号',
                    'x-component': 'Editable',
                },
                citySide: {
                    key: 'citySide',
                    name: 'citySide',
                    title: '用车类型',
                    required: true,
                    'x-component': 'Select',
                    enum: [
                        { label: '市内用车', value: 1 },
                        { label: '市外用车', value: 2 },
                    ],
                    required: true,
                },
                phone: {
                    key: 'phone',
                    name: 'phone',
                    title: '经办人电话',
                    required: true,
                    maxLength: '11',
                    'x-component': 'Input',
                    "x-rules": [{ format: "phone", message: "手机格式不正确" }],
                },
                reason: {
                    key: 'reason',
                    name: 'reason',
                    title: '事由',
                    required: true,
                    maxLength: '200',
                    'x-component': 'TextArea',
                    'x-mega-props': {
                        span: 3,
                    },
                },
                carUser: {
                    key: 'carUser',
                    name: 'carUser',
                    title: '用车人',
                    required: true,
                    'x-component': 'SelectMember',
                    'x-component-props': {
                        maxCount: 1
                    }
                },
                carUserPhone: {
                    key: 'carUserPhone',
                    name: 'carUserPhone',
                    title: '联系电话',
                    required: true,
                    'x-component': 'Editable',
                    // "x-rules": [{ format: "phone", message: "手机格式不正确" }],
                },
                useRange: {
                    key: 'useRange',
                    name: 'useRange',
                    title: '用车范围',
                    required: true,
                    'x-component': 'Select',
                    enum: '{{useRange}}'
                },
                time: {
                    key: 'time',
                    name: 'time',
                    required: true,
                    title: '用车时间',
                    'x-component': 'RangePicker',
                    'x-component-props': {
                        format: 'YYYY-MM-DD HH:mm:ss',
                        locale,
                        showTime: true,
                        style: {
                            width: '100%',
                        },
                    }
                },
                days: {
                    key: 'days',
                    name: 'days',
                    title: '用车时长（天）',
                    'x-component': 'Editable',
                },
                passengerNum: {
                    key: 'passengerNum',
                    name: 'passengerNum',
                    title: '乘坐人数',
                    maxLength: '3',
                    required: true,
                    'x-component': 'NumberPicker',
                    'x-component-props': {
                        min: 2,
                        max: 999,
                        setp: 1
                    },

                },
                fromAddress: {
                    key: 'fromAddress',
                    name: 'fromAddress',
                    title: '出发地',
                    required: true,
                    maxLength: '50',
                    'x-component': 'Input',
                },
                toAddress: {
                    key: 'toAddress',
                    name: 'toAddress',
                    title: '目的地',
                    required: true,
                    maxLength: '50',
                    'x-component': 'Input',
                },
            },
        },
        gridWrap: {
            'x-component': 'mega-layout',
            'x-component-props': {
                grid: true,
                autoRow: true,
                labelAlign: '{{labelAlign}}',
                className: 'grid-gaps-child',
                responsive: {
                    lg: 3,
                    m: 2,
                    s: 1,
                },
            },
            properties: {
                isGrid: {
                    key: 'isGrid',
                    name: 'isGrid',
                    title: '是否网格用车',
                    required: true,
                    disabled: true,
                    'x-component': 'RadioGroup',
                    enum: [
                        { label: '是', value: '1', disabled: "{{orgType!==3}}" },
                        { label: '否', value: '0', },
                    ],
                    'x-linkages': [
                        {
                            type: 'value:visible',
                            target: '*(gridNameWrap,gridNoWrap)',
                            condition: '{{ $self.value === "1" }}',
                        },
                    ],
                },
                gridNameWrap: {
                    type: 'object',
                    title: '网格名称',
                    visible: false,
                    required: true,
                    properties: {
                        gridName: {
                            key: 'gridName',
                            name: 'gridName',
                            'x-component': 'Editable',
                            'x-mega-props': {
                                span: 2,
                            },
                        },
                        grid: {
                            type: 'void',
                            visible: "{{editable}}",
                            key: 'selectGrid',
                            name: 'selectGrid',
                            'x-component': 'Grid',
                        }
                    }
                },
                gridNoWrap: {
                    type: 'object',
                    title: '网格编号',
                    visible: false,
                    required: true,
                    properties: {
                        gridNo: {
                            key: 'gridNo',
                            name: 'gridNo',
                            'x-component': 'Editable',
                            'x-mega-props': {
                                span: 2,
                            },
                        },
                        grid: {
                            type: 'void',
                            visible: "{{editable}}",
                            key: 'selectGrid',
                            name: 'selectGrid',
                            'x-component': 'Grid',
                        }
                    }
                },
            }
        },
        layout1: {
            'x-component': 'mega-layout',
            'x-component-props': {
                grid: true,
                autoRow: true,
                labelAlign: '{{labelAlign}}',
                className: 'grid-gaps',
                responsive: {
                    lg: 3,
                    m: 2,
                    s: 1,
                },
            },
            properties: {
                remark: {
                    key: 'remark',
                    name: 'remark',
                    type: 'string',
                    title: '备注',
                    maxLength: '500',
                    'x-component': 'TextArea',
                    'x-mega-props': {
                        span: 3,
                    },
                },
                fileList: {
                    type: 'string',
                    title: '{{uploadTitle}}',
                    key: 'fileList',
                    'x-component': 'Upload',
                    'x-component-props': {
                        action: UPLOAD_URL,
                        listType: 'text',
                    },
                    'x-mega-props': {
                        span: 3,
                    },
                },
            }
        }
    },
}

export const CreateBusSchema = {
    type: 'object',
    properties: {
        layout: {
            'x-component': 'mega-layout',
            'x-component-props': {
                grid: true,
                autoRow: true,
                labelAlign: 'top',
                className: 'grid-gaps',
                responsive: {
                    lg: 2,
                    m: 2,
                    s: 1,
                },
            },
            properties: {
                orgName: {
                    key: 'orgName',
                    name: 'orgName',
                    title: '归属公司',
                    required: true,
                    'x-component': 'Editable',
                },
                countyId: {
                    key: 'countyId',
                    name: 'countyId',
                    required: true,
                    title: '归属县/区分公司',
                    'x-component': 'Select',
                    enum: "{{country}}"
                },
                carNo: {
                    key: 'carNo',
                    name: 'carNo',
                    required: true,
                    title: '车牌号',
                    maxLength: '8',
                    'x-component': 'Input',
                    "x-rules": [
                        {
                            validator: (val) => {
                                const xreg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF])|([DF][A-HJ-NP-Z0-9][0-9]{4}))$/;

                                const creg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$/;

                                console.log(creg.test(val))
                                if ((val.length === 7 && creg.test(val)) || (val.length === 8 && xreg.test(val))) {
                                    return true
                                } else {
                                    return '请输入正确的车牌'
                                }
                            }
                        }
                    ],
                },
                carAttr: {
                    key: 'carAttr',
                    name: 'carAttr',
                    required: true,
                    title: '车辆属性',
                    'x-component': 'Select',
                    enum: [
                        { label: '长期租赁', value: '1' },
                        { label: '短期租赁', value: '2' },
                        { label: '自有车辆', value: '3' },
                    ],
                    // 'x-linkages': [
                    //     {
                    //         type: 'value:visible',
                    //         target: '*(layout1)',
                    //         condition: '{{ $self.value === "3" }}',
                    //     },
                    //     {
                    //         type: 'value:visible',
                    //         target: '*(layout2)',
                    //         condition: '{{ $self.value === "1"||  $self.value === "2" }}',
                    //     }
                    // ]
                },
                carBrand: {
                    key: 'carBrand',
                    name: 'carBrand',
                    required: true,
                    title: '品牌',
                    'x-component': 'Input',
                },
                carModel: {
                    key: 'carModel',
                    name: 'carModel',
                    required: true,
                    title: '车型',
                    maxLength: '20',
                    'x-component': 'Input',
                },
            },
        },
        // layout2: {
        //     'x-component': 'mega-layout',
        //     visible: false,
        //     'x-component-props': {
        //         grid: true,
        //         autoRow: true,
        //         labelAlign: 'top',
        //         className: 'grid-gaps',
        //         responsive: {
        //             lg: 2,
        //             m: 2,
        //             s: 1,
        //         },
        //     },
        //     properties: {
        //         price: {
        //             key: 'price',
        //             name: 'price',
        //             required: true,
        //             title: '租赁金额',
        //             'x-component': 'Select',
        //         },
        //         jiage: {
        //             key: 'jiage',
        //             name: 'jiage',
        //             required: true,
        //             title: '超时租赁单价（元/时）',
        //             'x-component': 'NumberPicker',
        //         },
        //         number: {
        //             key: 'number',
        //             name: 'number',
        //             required: true,
        //             title: '合同编号',
        //             'x-component': 'Input',
        //         },
        //         li: {
        //             key: 'li',
        //             name: 'li',
        //             required: true,
        //             title: '初始公里数',
        //             'x-component': 'Input',
        //         },
        //         time1: {
        //             key: 'time1',
        //             name: 'time1',
        //             title: '租赁开始时间',
        //             'x-component': 'DatePicker',
        //             'x-component-props': {
        //                 format: 'YYYY-MM-DD'
        //             }
        //         },
        //         time: {
        //             key: 'time',
        //             name: 'time',
        //             title: '租赁结束时间',
        //             'x-component': 'DatePicker',
        //             'x-component-props': {
        //                 format: 'YYYY-MM-DD'
        //             }
        //         },
        //     }
        // },
        // layout1: {
        //     'x-component': 'mega-layout',
        //     visible: false,
        //     'x-component-props': {
        //         grid: true,
        //         autoRow: true,
        //         labelAlign: 'top',
        //         className: 'grid-gaps',
        //         responsive: {
        //             lg: 2,
        //             m: 2,
        //             s: 1,
        //         },
        //     },
        //     properties: {
        //         time1: {
        //             key: 'time1',
        //             name: 'time1',
        //             title: '保险费（元）',
        //             'x-component': 'NumberPicker',
        //         },
        //         time: {
        //             key: 'time',
        //             name: 'time',
        //             title: '年审费（元）',
        //             'x-component': 'NumberPicker',
        //         },
        //     }
        // },
    },
}

export const DriverSchema = {
    type: 'object',
    properties: {
        layout: {
            'x-component': 'mega-layout',
            'x-component-props': {
                grid: true,
                autoRow: true,
                labelAlign: 'top',
                className: 'grid-gaps',
                responsive: {
                    lg: 2,
                    m: 2,
                    s: 1,
                },
            },
            properties: {
                name: {
                    key: 'name',
                    name: 'name',
                    require: true,
                    title: '驾驶员姓名',
                    'x-component': 'Input',
                },
                driverAttr: {
                    key: 'driverAttr',
                    name: 'driverAttr',
                    require: true,
                    title: '驾驶员属性',
                    'x-component': 'Select',
                    enum: [
                        { label: '租赁', value: '1' },
                        { label: '自有', value: '2' },
                    ]
                },
                mobile: {
                    key: 'mobile',
                    name: 'mobile',
                    require: true,
                    title: '手机号码',
                    'x-component': 'Input',
                    "x-rules": [{ format: "phone", message: "手机格式不正确" }],
                },
                certNo: {
                    key: 'certNo',
                    name: 'certNo',
                    title: '证件号码',
                    'x-component': 'Input',
                },
            }
        },
    }
}


export const newCreateBus = {
    type: 'object',
    properties: {
        layout: {
            'x-component': 'mega-layout',
            'x-component-props': {
                grid: true,
                autoRow: true,
                labelAlign: 'top',
                className: 'grid-gaps',
                responsive: {
                    lg: 1,
                    m: 1,
                    s: 1,
                },
            },
            properties: {
                carNo: {
                    key: 'carNo',
                    name: 'carNo',
                    required: true,
                    title: '车牌号',
                    'x-component': 'Input',
                },
                carModel: {
                    key: 'carModel',
                    name: 'carModel',
                    required: true,
                    title: '车型',
                    'x-component': 'Input',
                },
                driver: {
                    key: 'driver',
                    name: 'driver',
                    required: true,
                    title: '司机姓名',
                    'x-component': 'Input',
                    
                },
                driverPhone: {
                    key: 'driverPhone',
                    name: 'driverPhone',
                    title: '司机手机号',
                    required: true,
                    'x-component': 'Input',
                    "x-rules": [{ format: "phone", message: "手机格式不正确" }],
                },
            },
        },
    },
}
