import React from 'react'

import BizDeptVerify from '../BizDeptVerify'
import LawDeptOpinion from '../LawDeptOpinion'
import Remark from '../Remark'

import { getRowSpanNos } from '../../utils'

const shareOnCell = (_, index) => {
  if (index === 0) {
    return {
      rowSpan: 5,
    }
  }

  if (getRowSpanNos(0, 5).includes(index)) {
    return {
      rowSpan: 0,
    }
  }

  if (index === 5) {
    return {
      rowSpan: 5,
    }
  }

  if (getRowSpanNos(5, 5).includes(index)) {
    return {
      rowSpan: 0,
    }
  }

  if (index === 10) {
    return {
      rowSpan: 7,
    }
  }

  if (getRowSpanNos(10, 7).includes(index)) {
    return {
      rowSpan: 0,
    }
  }

  if (index === 19) {
    return {
      rowSpan: 18,
    }
  }

  if (getRowSpanNos(19, 18).includes(index)) {
    return {
      rowSpan: 0,
    }
  }

  return {}
}

const shareOnCell1 = (_, index) => {
  if (index === 10) {
    return {
      rowSpan: 5,
    }
  }

  if (getRowSpanNos(10, 5).includes(index)) {
    return {
      rowSpan: 0,
    }
  }

  if (index === 34) {
    return {
      rowSpan: 3,
    }
  }

  if (getRowSpanNos(34, 3).includes(index)) {
    return {
      rowSpan: 0,
    }
  }

  return {}
}

export const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    onCell: shareOnCell,
    width: 70,
  },
  {
    title: '类别',
    dataIndex: 'categoryName',
    onCell: shareOnCell,
    width: 140,
  },
  {
    title: '审核要点',
    dataIndex: 'checkPoint',
  },
  {
    title: '法律风险防控要求',
    dataIndex: 'lawRiskControl',
    onCell: shareOnCell1,
    width: 300,
  },
  {
    title: '业务部门核实情况',
    dataIndex: 'bizDeptVerify',
  },
  {
    title: '法律部门复核意见',
    dataIndex: 'lawDeptOpinion',
  },
  {
    title: '备注',
    dataIndex: 'remark',
  },
]

export const getDataSource = (data = {}, access = {}, onChange, debug = false) => {
  const getParams = fieldName => {
    return {
      fieldName,
      value: data[fieldName],
      access,
      debug,
      onChange,
    }
  }

  return [
    {
      index: 1,
      categoryName: '拟采购商品房基本情况',
      checkPoint: '名称',
      lawRiskControl: '记录开发商公司或出卖人名称全称',
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_0')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_0')} />,
      remark: <Remark {...getParams('remark_0')} />,
    },
    {
      checkPoint: '拟购房屋详细地址',
      lawRiskControl:
        '记录拟购商品房的具体地址，具体到房号：XX市XX县（区）XX路XX号XX楼盘XX组团XX栋XX单元XX号',
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_1')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_1')} />,
      remark: <Remark {...getParams('remark_1')} />,
    },
    {
      checkPoint: '规划用途(住宅/办公/商业)',
      lawRiskControl: (
        <div>
          <p>
            1.《民法典》第二百七十九条规定：业主不得违反法律、法规以及管理规约，将住宅改变为经营性用房。业主将住宅改变为经营性用房的，除遵守法律、法规以及管理规约外，应当经有利害关系的业主一致同意。
          </p>
          <p>
            2.购置房屋作为传输汇聚机房、营业厅，法律性质上属于经营性用房，应优先选择规划用途为“办公/商业”的房屋，否则可能引发其他业主投诉或阻挠。
          </p>
          <div>
            3.在城市小区及在乡镇、城郊场景，如需购置用途为“住宅”的房屋时，建议做好以下风险评估及防控措施：
            <ol>
              <li>（1）优先征得有利害关系的全体业主同意；</li>
              <li>
                （2）若确实无法取得全体利害关系业主同意的，应在购买前充分评估可能发生的业主投诉、诉讼以及因投诉、诉讼引发的管道光缆、引电、设备进场安装等施工受阻、维护进出入受阻、拆除机房、赔偿损失等风险；
              </li>
              <li>
                （3）在购房合同中约定购置后施工过程受阻或被投诉时，卖方应积极出面协调、协助公司化解纠纷；
              </li>
              <li>
                （4）在建设过程中做好机房隔音降噪措施，最大限度减少风险的发生，并做好风险应对预案供决策参考。
              </li>
            </ol>
          </div>
        </div>
      ),
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_2')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_2')} />,
      remark: <Remark {...getParams('remark_2')} />,
    },
    {
      checkPoint: '房屋面积',
      lawRiskControl: '写明拟购置房屋的具体面积，以权属证明上记载的面积为准',
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_3')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_3')} />,
      remark: <Remark {...getParams('remark_3')} />,
    },
    {
      checkPoint: '竣工与否',
      lawRiskControl: '购置一手新房如已竣工，应要求开发商出具《建设工程质量竣工验收备案证明》。',
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_4')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_4')} />,
      remark: <Remark {...getParams('remark_4')} />,
    },
    {
      index: 2,
      categoryName: '出卖人基本情况',
      checkPoint: '营业执照',
      lawRiskControl: (
        <div>
          <p>
            1.营业执照上登记的信息均可在“全国企业信用信息公示系统”网页中，通过“公司名称”、“注册号”等关键信息查询，核实真伪;
          </p>
          <p>2.留存房地产公司营业执照复印件（加盖公章）。</p>
        </div>
      ),
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_5')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_5')} />,
      remark: <Remark {...getParams('remark_5')} />,
    },
    {
      checkPoint: '组织机构代码证（如有）',
      lawRiskControl: (
        <div>
          <p>
            1.机构代码证上登记的信息均可在“全国组织机构代码管理中心”网页中，通过“公司名称”、“机构代码”等关键信息查询，核实真伪；
          </p>
          <p>2.留存对方机构代码证复印件（加盖公章）。</p>
        </div>
      ),
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_6')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_6')} />,
      remark: <Remark {...getParams('remark_6')} />,
    },
    {
      checkPoint: '税务登记证',
      lawRiskControl: (
        <div>
          <p>1.税务登记证上登记的信息可在当地税务系统网站中查询，核实真伪；</p>
          <p>2.留存税务登记证复印件（加盖公章）。</p>
        </div>
      ),
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_7')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_7')} />,
      remark: <Remark {...getParams('remark_7')} />,
    },
    {
      checkPoint: '出卖人经营情况',
      lawRiskControl: (
        <div>
          <p>
            1.通过企业信用信息公示系统、企查查、天眼查等途径查询出卖人公司是否存在违法违规被处罚的情况；
          </p>
          <p>
            2.通过中国执行信息公开网查询是否存在大额诉讼或执行案件等可能导致拟购置房屋被查封拍卖的情况。
          </p>
        </div>
      ),
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_8')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_8')} />,
      remark: <Remark {...getParams('remark_8')} />,
    },
    {
      checkPoint: '如为个人业主',
      lawRiskControl:
        '提供身份证复印件，审核其身份信息和有可能影响交易的其他信息，如是否为夫妻或多人共有财产、有无其他争议等；若存在共有人，需所有共有人一起签订买卖合同或由其他共有人出具授权书。',
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_9')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_9')} />,
      remark: <Remark {...getParams('remark_9')} />,
    },
    {
      index: 3,
      categoryName: '拟购商品房建设工程基本情况',
      checkPoint: '土地使用权证',
      lawRiskControl: (
        <div>
          <p>
            1.公司应当核验开发商是否具备“五证”，即《国有土地使用证》、《建设用地规划许可证》、《建设工程规划许可证》、《建设工程施工许可证》、《商品房销售（预售）许可证》，取得“五证”表示开发商建设的楼盘已经得到土地、规划、建设、房管等部门的批准，具备对外销售的条件；
          </p>
          <p>2.留存“五证”复印件（加盖公章）。</p>
        </div>
      ),
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_10')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_10')} />,
      remark: <Remark {...getParams('remark_10')} />,
    },
    {
      checkPoint: '建设用地规划许可证',
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_11')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_11')} />,
      remark: <Remark {...getParams('remark_11')} />,
    },
    {
      checkPoint: '建设工程规划许可证',
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_12')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_12')} />,
      remark: <Remark {...getParams('remark_12')} />,
    },
    {
      checkPoint: '建设工程施工许可证',
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_13')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_13')} />,
      remark: <Remark {...getParams('remark_13')} />,
    },
    {
      checkPoint: '商品房预售许可证',
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_14')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_14')} />,
      remark: <Remark {...getParams('remark_14')} />,
    },
    {
      checkPoint: '建设工程质量竣工验收备案证明',
      lawRiskControl:
        '《建设工程质量竣工验收备案证明》主要是证明商品房验收合格，并可交付使用，是办理《商品房产权证》的必备条件。',
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_15')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_15')} />,
      remark: <Remark {...getParams('remark_15')} />,
    },
    {
      checkPoint: '房屋产权证',
      lawRiskControl: (
        <div>
          <p>1.购买现房时，核实是否已经取得不动产权证；</p>
          <p>
            2.为进一步防范风险，应到当地房产管理部门或不动产登记中心对房屋产权登记情况进行核实权属登记情况、是否存在抵押、查封等情况；
          </p>
          <p>
            3.购买预售房时，也应至当地房产管理部门或不动产登记中心核实拟购置房产是否存在已被违法办理产权登记等异常情况。
          </p>
        </div>
      ),
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_16')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_16')} />,
      remark: <Remark {...getParams('remark_16')} />,
    },
    {
      index: 4,
      categoryName: '他项权情况',
      checkPoint: '是否存在土地使用权、在建工程、房屋抵押或查封情况',
      lawRiskControl: (
        <div>
          <p>
            1.查看开发商出示的《土地使用权证》时，须注意该证书上：“有关他项权利证明”栏内是否注明有抵押、查封情况，如存在土地使用权或在建工程抵押、查封情况的，存在不能或不能及时办理房产证的风险；
          </p>
          <p>
            2.在实践中，开发商将土地使用权进行抵押再贷款的情况时有发生。如无可替代商铺供选择的，需要特别关注交易过程中的风险，通过分次付款等方式强化风险管控。必要时建议委托律师到土地管理部门、房产管理部门核实是否存在抵押、查封的情况。
          </p>
        </div>
      ),
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_17')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_17')} />,
      remark: <Remark {...getParams('remark_17')} />,
    },
    {
      index: 5,
      categoryName: '定金',
      checkPoint: '是否需支付定金',
      lawRiskControl: (
        <div>
          <p>1.支付定金应符合管理办法规定的特殊场景要求；</p>
          <p>2.若需支付定金，应签订《定金协议》；并留存好《定金协议》和定金收据；</p>
          <p>
            3.签订合同后，应将已支付的定金直接冲抵购房款，并要求对方开具包含定金金额在内的发票；
          </p>
          <p>
            4.支付定金后若因对方原因导致无法签订购房合同，应在确定无法签订购房合同之日通过书面形式要求对方在规定期限内（建议最长不超过30日）内双倍退还已支付的定金，并留存好书面材料和对方签收记录。
          </p>
        </div>
      ),
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_18')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_18')} />,
      remark: <Remark {...getParams('remark_18')} />,
    },
    {
      index: 6,
      categoryName: '主要合同条款',
      checkPoint: '优先使用政府公布的合同范本',
      lawRiskControl:
        '在购买商品房时，应尽量优先选择适用政府部门公开发布的范本或主要条款，如无法协调使用政府范本，也应当在合同中包含以下关键条款内容。',
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_19')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_19')} />,
      remark: <Remark {...getParams('remark_19')} />,
    },
    {
      checkPoint: '计价方式',
      lawRiskControl:
        '目前主要有按套内建筑面积计算、按建筑面积计算2种计价方式，应明确开发商所称商铺销售价格是套内价格，还是建筑面积价格。',
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_20')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_20')} />,
      remark: <Remark {...getParams('remark_20')} />,
    },
    {
      checkPoint: '每平米单价',
      lawRiskControl: '-',
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_21')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_21')} />,
      remark: <Remark {...getParams('remark_21')} />,
    },
    {
      checkPoint: '总价款',
      lawRiskControl: '-',
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_22')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_22')} />,
      remark: <Remark {...getParams('remark_22')} />,
    },
    {
      checkPoint: '付款方式',
      lawRiskControl: (
        <div>
          以分次付款为原则，一次性付款为例外（是否可一次性付款，建议同步咨询财务部门的意见）。
          采取一次性付款时，采购者应尽到以下（包括但不限于）审查义务 ：
          <p>
            1.充分了解开发商注册资金、法人、财务等资信、证照情况，信用记录是否良好，是否大型/上市公司等；
          </p>
          <p>2.充分了解拟购商品房所在楼盘“五证”是否齐全，了解楼盘整体在售情况等；</p>
          <p>3.充分了解拟购商品房是否存在抵押或者产权纠纷的情况等；</p>
          <p>4.后续注意跟进拟购商品房面积差异问题，要求开发商做到多退少补等。</p>
        </div>
      ),
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_23')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_23')} />,
      remark: <Remark {...getParams('remark_23')} />,
    },
    {
      checkPoint: '交付条件',
      lawRiskControl: (
        <div>
          商铺交付时应当符合下列条件：
          <p>1.已取得建设工程竣工验收备案证明文件；</p>
          <p>2.已取得房屋测绘报告；</p>
          <p>3.已通过消防竣工验收；</p>
          <p>
            4.已纳入城市供电网络并正式供电，供水、排水配套设施齐全，宽带网络、有线电视等线路敷设到户。
          </p>
        </div>
      ),
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_24')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_24')} />,
      remark: <Remark {...getParams('remark_24')} />,
    },
    {
      checkPoint: '交付时间',
      lawRiskControl: <div>交付时间具体到年月日，建议交付时间原则上不超过24个月。 </div>,
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_25')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_25')} />,
      remark: <Remark {...getParams('remark_25')} />,
    },
    {
      checkPoint: '停车位购买资格',
      lawRiskControl: (
        <div>
          公司购买机房、营业厅等商铺时，应根据实际需要在合同谈判时考虑停车位需求，并明确要求开发商保证车位购买资格、购买数量，以防止无车位使用。{' '}
        </div>
      ),
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_26')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_26')} />,
      remark: <Remark {...getParams('remark_26')} />,
    },
    {
      checkPoint: '逾期付款的违约责任',
      lawRiskControl: (
        <div>
          商品房买卖合同中逾期付款的违约责任一般有2种，一种是按天计算违约金，但开发商无权解除合同；一种是逾期超过XX天，开发商有权解除合同，并追究买方违约责任。为了公司利益最大化，应选择第一种违约责任方式，违约金计算比例应基本与开发商违约时的计算比例一致。{' '}
        </div>
      ),
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_27')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_27')} />,
      remark: <Remark {...getParams('remark_27')} />,
    },
    {
      checkPoint: '逾期交房的违约责任',
      lawRiskControl: (
        <div>
          在“逾期交房的违约责任条款”中优先约定如下对公司有利的处理方式：
          <p>1.逾期超过X日后，买受人有权解除合同。</p>
          <p>
            2.买受人解除合同的，应当书面通知出卖人。出卖人应当自解除合同通知送达之日起15日内退还买受人已付全部房款（含已付贷款部分），并自买受人付款之日起，按照X%
            （不低于中国人民银行公布的同期贷款基准利率）
            计算给付利息；同时，出卖人按照全部房价款的X%向买受人支付违约金。
          </p>
          <p>
            3.买受人要求继续履行合同的，合同继续履行，出卖人按日计算向买受人支付全部房价款万分之X的违约金。
            逾期交房的违约金计算比例应与逾期付款时违约金的计算比例基本一致，如均为万分之五。
          </p>
        </div>
      ),
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_28')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_28')} />,
      remark: <Remark {...getParams('remark_28')} />,
    },
    {
      checkPoint: '面积差异的处理方式',
      lawRiskControl: (
        <div>
          在“面积确认及面积差异处理条款”中优先约定如下对公司有利的处理方式：
          <p>1.面积误差比绝对值在3%以内（含3%）的，据实结算房价款；</p>
          <p>
            2.面积误差比绝对值超出3%时，买受人有权退房。
            买受人退房的，出卖人在买受人提出退房之日起30天内将买受人已付款退还给买受人，并按同期银行贷款利率给付利息。
            买受人不退房的，产权登记面积大于合同约定面积时，面积误差（明确是建筑面积还是套内面积）比在3%以内（含3%）部分的房价款由买受人补足；超出3%部分的房价款由出卖人承担，产权归买受人。产权登记面积小于合同登记面积时，面积误差比绝对值在3%以内（含3%）部分的房价款由出卖人返还买受人；绝对值超出3%部分的房价款由出卖人双倍返还买受人。
          </p>
          <p>3.因设计变更造成面积差异，双方不解除合同的，应当签订补充协议。</p>
        </div>
      ),
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_29')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_29')} />,
      remark: <Remark {...getParams('remark_29')} />,
    },
    {
      checkPoint: '规划、设计变更',
      lawRiskControl: (
        <div>
          在“规划设计变更条款”中优先约定如下对公司有利的处理方式：
          <p>
            1.双方签订合同后，出卖人按照法定程序变更建筑工程施工图设计文件，涉及商品房规划用途、面积、结构形式、户型、空间尺寸、朝向等可能影响买受人所购商品房质量或使用功能情形的，买受人有权解除合同。
          </p>
          <p>
            2.买受人解除合同的，应当书面通知出卖人。出卖人应当自解除合同通知送达之日起15日内退还买受人已付全部房款（含已付贷款部分），并自买受人付款之日起，按照X%
            （不低于中国人民银行公布的同期贷款基准利率）
            计算给付利息；同时，出卖人按照全部房价款的X%向买受人支付违约金。
          </p>
        </div>
      ),
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_30')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_30')} />,
      remark: <Remark {...getParams('remark_30')} />,
    },
    {
      checkPoint: '预售合同备案登记',
      lawRiskControl: (
        <div>
          开发商应当自合同签订之日起XX日（不超过30日）办理商品房预售合同备案登记手续，并将本合同备案登记证明交付我公司。
        </div>
      ),
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_31')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_31')} />,
      remark: <Remark {...getParams('remark_31')} />,
    },
    {
      checkPoint: '房屋产权登记',
      lawRiskControl: (
        <div>
          在“房屋产权登记条款”中优先约定如下对公司有利的处理方式：
          <p>
            1.因出卖人的原因，买受人未能在该商品房交付之日起日内取得该商品房的房屋所有权证书的，买受人有权解除合同。买受人解除合同的，应当书面通知出卖人。出卖人应当自解除合同通知送达之日起15日内退还买受人已付全部房款（含已付贷款部分），并自买受人付款之日起，按照XX%（不低于中国人民银行公布的同期贷款基准利率）计算给付利息。
          </p>
          <p>
            2.买受人不解除合同的，自买受人应当完成房屋所有权登记的期限届满之次日起至实际完成房屋所有权登记之日止，出卖人按日计算向买受人支付全部房价款XX%的违约金。（违约金计算比例应与公司逾期付款时违约金的计算比例基本一致）
          </p>
        </div>
      ),
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_32')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_32')} />,
      remark: <Remark {...getParams('remark_32')} />,
    },
    {
      checkPoint: '争议解决办法',
      lawRiskControl: (
        <div>
          合同在履行过程中发生的争议，由双方当事人协商解决，协商不成的，由房屋所在地人民法院管辖。
        </div>
      ),
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_33')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_33')} />,
      remark: <Remark {...getParams('remark_33')} />,
    },
    {
      checkPoint: '外墙、外立面广告位使用权问题',
      lawRiskControl: (
        <div>
          <p>
            1.《民法典》第二百七十一条规定：“业主对建筑物专有部分以外的共有部分享有共有和共同管理的权利”，即外墙、外立面、公共露台等公共区域属于全体业主共有，如需使用应当经过该栋建筑的2/3以上业主的同意。但要经过2/3以上业主的同意基本不具有可操作性，实践中大多数通过与开发商或者物业公司签订协议，明确对上述区域有使用权。
          </p>
          <p>
            2.因我公司营业厅布置门头、开展营销活动时，不可避免的需要使用上述公共区域，建议最好能在合同中与开发商明确使用权问题。
          </p>
        </div>
      ),
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_34')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_34')} />,
      remark: <Remark {...getParams('remark_34')} />,
    },
    {
      checkPoint: '商铺门前公共区域使用权问题',
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_35')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_35')} />,
      remark: <Remark {...getParams('remark_35')} />,
    },
    {
      checkPoint: '公共露台使用权问题',
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_36')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_36')} />,
      remark: <Remark {...getParams('remark_36')} />,
    },
  ]
}
