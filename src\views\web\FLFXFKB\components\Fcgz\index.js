import React, { useEffect, useState, useImperativeHandle } from 'react'
import { Input, Select, Table, Radio } from 'antd'
import moment from 'moment'

import useMyInfo from 'ROOT/hooks/useMyInfo'
import { columns, getDataSource } from './columns'
import Desc from './components/Desc'
import Upload from '../../components/Upload'
import { fcgzTitle } from '../../config'

import './index.scss'

const { Option } = Select
const { TextArea } = Input

// zone(区域): area | city | county
// type(类型): start | approve
const Fcgz = React.forwardRef(
  ({ zone, type, initValue = {}, access = {}, debug = false, readOnly }, ref) => {
    const myInfo = useMyInfo({ isSetStorage: true })
    const [deptList, setDeptList] = useState([])
    const [formData, setFormData] = useState({})
    const [dataSource, setDataSource] = useState([])

    // 获取【部门列表】数据
    useEffect(() => {
      if (myInfo && Object.keys(myInfo).length > 0) {
        const { linkPath = [] } = myInfo

        setDeptList(
          linkPath && linkPath.length > 0
            ? linkPath.map(item => ({
                value: item.deptId,
                label: item.linkPath,
                deptName: item.rootDeptName,
              }))
            : [],
        )
      }
    }, [myInfo])

    // 初始化 formData 数据
    useEffect(() => {
      // 兼容历史数据
      // 判断是不是历史数据 - 修正起草时间
      const { draftDate = '' } = initValue || {}
      if (draftDate && String(draftDate).indexOf('年') >= 0) {
        initValue.draftDate = moment(
          draftDate.replace('年', '-').replace('月', '-').replace('日', ''),
        ).valueOf()
      }

      setFormData(initValue)
    }, [JSON.stringify(initValue)])

    // 获取【表格数据】
    useEffect(() => {
      setDataSource(getDataSource(initValue, access, handleChange, debug))
    }, [JSON.stringify(initValue), access, debug])

    // 设置页面标题
    useEffect(() => {
      document.title = fcgzTitle
      if (document.getElementById('breadnav')) {
        document.getElementById('breadnav').innerHTML = document.title
      }
    }, [])

    useImperativeHandle(ref, () => ({
      getFormData: () => {
        return formData
      },
    }))

    const handleChange = (fieldName, value) => {
      setFormData(formData => {
        return {
          ...formData,
          [fieldName]: value,
        }
      })
    }

    // 字段是否禁用
    const fieldDisabled = fieldName => {
      // 调式模式，允许编辑
      if (debug) return false

      // 流程节点中配置了【只读】或者取不到配置（比如：流程结束）
      return access[fieldName] === 'READ' || !access[fieldName]
    }

    // 附件上传
    const handleAttachmentUpload = (value = []) => {
      setFormData(formData => {
        return {
          ...formData,
          attachment: value,
        }
      })
    }

    return (
      <div className="fcgz">
        <h1 className="form-title">{fcgzTitle}</h1>
        <div className="baseinfo">
          <div className="baseinfo-item">
            <div className="label">起草人</div>
            <div className="value">
              <Input value={formData.drafterName} disabled />
            </div>
          </div>
          <div className="baseinfo-item">
            <div className="label">起草部门</div>
            <div className="value">
              <Select
                value={formData.drafterDeptName}
                onChange={value => {
                  handleChange('drafterDeptName', value)
                }}
                disabled={type === 'approve' || readOnly === '1'}
              >
                {deptList.map(dept => {
                  return (
                    <Option key={dept.value} value={dept.value}>
                      {dept.label}
                    </Option>
                  )
                })}
              </Select>
            </div>
          </div>
          <div className="baseinfo-item">
            <div className="label">起草时间</div>
            <div className="value">
              <Input value={moment(formData.draftDate).format('YYYY年MM月DD日')} disabled />
            </div>
          </div>
        </div>
        <div className="table-container">
          <div className="table-desc">
            <h3>表格使用说明</h3>
            <p>
              1.本表由业务主办部门经办人、业务主办部门负责人、法律管理员、法律管理部门负责人签字确认，作为材料附件存档备查；
            </p>
            <p>
              2.业务主办部门采纳法律管理部门意见的，应当根据法律意见完善上会材料，并体现在“法律风险评估情况”内容中，供会议决策参考；不采纳法律管理部门意见的，应当如实在上会材料“法律风险评估情况”部分记录法律意见，并说明不采纳的原因，供会议决策参考；
            </p>
            <p>
              3.本表的审核意见有效期为6个月；若超过6个月，需重新评估出具本表；若购置期间供应商的经营情况出现破产、重组、被列入失信名单等重大变化，需重新评估风险并出具本表。
            </p>
          </div>
          {access['bizTitle'] !== 'NONE' && (
            <div className="biz-title">
              <label>
                <span className="required">*</span>标题:
              </label>
              <div className="biz-title-input-container">
                <Input
                  maxLength={50}
                  placeholder="关于购置XX房屋的法律风险防控表"
                  value={formData.bizTitle}
                  onChange={e => handleChange('bizTitle', e.target.value)}
                  disabled={fieldDisabled('bizTitle')}
                />
              </div>
            </div>
          )}
          <Table
            columns={columns}
            dataSource={dataSource}
            bordered
            pagination={false}
            sticky
          ></Table>
        </div>
        {access['attachment'] !== 'NONE' && (
          <div className="fxfk-attachment">
            <label>附件:</label>
            <div className="fxfk-attachment-uploader-container">
              <Upload
                disabled={fieldDisabled('attachment')}
                value={formData.attachment}
                onChange={handleAttachmentUpload}
              ></Upload>
            </div>
          </div>
        )}
        <Desc />
        {access['lawAuditOpinion'] !== 'NONE' && (
          <div className="law-audit-opinion">
            <label>
              <span className="required">*</span>是否采纳法律审核意见:
            </label>
            <div>
              <Radio.Group
                value={formData.lawAuditOpinion}
                onChange={e => handleChange('lawAuditOpinion', e.target.value)}
                disabled={fieldDisabled('lawAuditOpinion')}
              >
                <Radio value={1}>是</Radio>
                <Radio value={0}>否</Radio>
              </Radio.Group>
            </div>
          </div>
        )}
        {access['notUseReason'] !== 'NONE' && (
          <div className="not-use-reason">
            <label>
              {formData.lawAuditOpinion === 0 && <span className="required">*</span>}
              不采纳法律审核意见的原因:
            </label>
            <TextArea
              placeholder="请输入内容"
              rows={4}
              value={formData.notUseReason}
              onChange={e => handleChange('notUseReason', e.target.value)}
              disabled={fieldDisabled('notUseReason')}
              maxLength={200}
            />
          </div>
        )}
      </div>
    )
  },
)

export default Fcgz
