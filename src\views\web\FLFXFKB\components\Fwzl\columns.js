import React from 'react'

import BizDeptVerify from '../BizDeptVerify'
import LawDeptOpinion from '../LawDeptOpinion'
import Remark from '../Remark'

import { getRowSpanNos } from '../../utils'

const shareOnCell = (_, index) => {
  if (index === 0) {
    return {
      rowSpan: 4,
    }
  }

  if (getRowSpanNos(0, 4).includes(index)) {
    return {
      rowSpan: 0,
    }
  }

  if (index === 5) {
    return {
      rowSpan: 2,
    }
  }

  if (getRowSpanNos(5, 2).includes(index)) {
    return {
      rowSpan: 0,
    }
  }

  if (index === 7) {
    return {
      rowSpan: 4,
    }
  }

  if (getRowSpanNos(7, 4).includes(index)) {
    return {
      rowSpan: 0,
    }
  }

  return {}
}

export const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    onCell: shareOnCell,
    width: 70,
  },
  {
    title: '类别',
    dataIndex: 'categoryName',
    onCell: shareOnCell,
    width: 140,
  },
  {
    title: '审核要点',
    dataIndex: 'checkPoint',
  },
  {
    title: '法律风险防控要求',
    dataIndex: 'lawRiskControl',
    width: 300,
  },
  {
    title: '业务部门核实情况',
    dataIndex: 'bizDeptVerify',
  },
  {
    title: '法律部门复核意见',
    dataIndex: 'lawDeptOpinion',
  },
  {
    title: '备注',
    dataIndex: 'remark',
  },
]

export const getDataSource = (data = {}, access = {}, onChange, debug = false) => {
  const getParams = fieldName => {
    return {
      fieldName,
      value: data[fieldName],
      access,
      debug,
      onChange,
    }
  }

  return [
    {
      index: 1,
      categoryName: '拟租赁房屋基本情况',
      checkPoint: '业主名称/营业执照',
      lawRiskControl: '记录业主名称全称/营业执照。',
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_0')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_0')} />,
      remark: <Remark {...getParams('remark_0')} />,
    },
    {
      checkPoint: '拟租赁房屋详细地址',
      lawRiskControl:
        '记录拟租赁房屋的具体地址，具体到房号：XX市XX县（区）XX路XX号XX楼盘XX组团XX栋XX单元XX号。',
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_1')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_1')} />,
      remark: <Remark {...getParams('remark_1')} />,
    },
    {
      checkPoint: '规划用途（住宅/办公/商业）',
      lawRiskControl: (
        <div>
          <ol>
            <li>
              1.《民法典》第二百七十九条规定：业主不得违反法律、法规以及管理规约，将住宅改变为经营性用房。业主将住宅改变为经营性用房的，除遵守法律、法规以及管理规约外，应当经有利害关系的业主一致同意。
            </li>
            <li>
              2.租赁房屋作为传输汇聚机房、营业厅，法律性质上属于经营性用房，应优先选择规划用途为“办公/商业”的房屋，否则可能引发其他业主投诉或阻挠。
            </li>
            <li>
              3.在城市小区及在乡镇、城郊场景，如需租赁用途为“住宅”的房屋时，建议做好以下风险评估及防控措施：
              <ol>
                <li>（1）优先征得有利害关系的全体业主同意；</li>
                <li>
                  （2）若确实无法取得全体利害关系业主同意的，应在购买前充分评估可能发生的业主投诉、诉讼以及因投诉、诉讼引发的管道光缆、引电、设备进场安装等施工受阻、维护进出入受阻、拆除机房、赔偿损失等风险；
                </li>
                <li>
                  （3）在购房合同中约定租赁后施工过程受阻或被投诉时，卖方应积极出面协调、协助公司化解纠纷；若无法协调化解纠纷，公司有权在通知后解除租赁合同并不承担违约责任；
                </li>
                <li>
                  4）在建设过程中做好机房隔音降噪措施，最大限度减少风险的发生，并做好风险应对预案供决策参考。
                </li>
              </ol>
            </li>
          </ol>
        </div>
      ),
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_2')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_2')} />,
      remark: <Remark {...getParams('remark_2')} />,
    },
    {
      checkPoint: '租赁面积',
      lawRiskControl: '写明拟租赁房屋的具体面积，以租赁合同记载的面积为准',
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_3')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_3')} />,
      remark: <Remark {...getParams('remark_3')} />,
    },
    {
      index: 2,
      categoryName: '拟租赁房屋产权归属',
      checkPoint: '房屋产权证',
      lawRiskControl: (
        <ol>
          <li>
            1.公司应当审核拟租赁房屋的产权证，确保租赁权来源合法；如果对方不是房屋产权人，而是转租人，还应当要求对方提供产权人同意其转租的书面证明原件。
          </li>
          <li>
            2.对于个别必须租赁且业主因客观原因确实无法提供相关产权证明的，需求部门/采购实施部门应说明必须租赁的原因，并全面评估潜在风险、制定相应的风险防范措施，按公司相关流程提交审批或会议审议和决策。
          </li>
        </ol>
      ),
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_4')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_4')} />,
      remark: <Remark {...getParams('remark_4')} />,
    },
    {
      index: 3,
      categoryName: '他项权情况',
      checkPoint: '是否是转租',
      lawRiskControl: (
        <ol>
          <li>
            1.若为转租，应审核原租赁合同中是否有同意第一承租人转租的条款，以及同意转租后可以从事的经营范围；
          </li>
          <li>
            2.若原租赁合同中没有约定是否同意转租，应要求公司拟向其租赁的出租人取得房屋所有人或者原出租人同意转租的书面材料。
          </li>
        </ol>
      ),
      bizDeptVerify: (
        <BizDeptVerify
          {...getParams('bizDeptVerify_5')}
          extraText="否是转租、是否已取得同意转租的材料："
        />
      ),
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_5')} />,
      remark: <Remark {...getParams('remark_5')} />,
    },
    {
      checkPoint: '是否存在房屋抵押情形',
      lawRiskControl: (
        <div>
          <p>应注意产权证上是否注明有抵押登记，是否被司法机关查封。</p>
          <ol>
            <li>
              1.如在租赁前房屋就存在抵押,且办理了抵押登记，租赁关系不得对抗已登记的抵押权。如房屋被拍卖后产权人变更的，租赁关系将面临终止，因此租赁风险相对较高。
            </li>
            <li>
              2.如房屋租赁关系在前抵押在后的，即使房屋被拍卖后产权人变更的，租赁关系不受影响。
            </li>
            <li>
              3.如发现拟租赁房屋存在被司法机关查封情形的，租赁后合同履行状态将极不稳定，法律风险较高。
            </li>
            <li>
              4.建议在租赁合同中增加出租人保证租赁房屋无抵押、无查封等他项权的条款及相应违约责任。
            </li>
          </ol>
        </div>
      ),
      bizDeptVerify: (
        <BizDeptVerify {...getParams('bizDeptVerify_6')} extraText="是否存在房屋抵押情形：" />
      ),
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_6')} />,
      remark: <Remark {...getParams('remark_6')} />,
    },
    {
      index: 4,
      categoryName: '主要合同条款',
      checkPoint: '关于房屋租赁合同的版本',
      lawRiskControl: '要求使用公司房屋租赁合同模板。',
      bizDeptVerify: (
        <BizDeptVerify
          {...getParams('bizDeptVerify_7')}
          extraText="是否使用公司房屋租赁合同模板："
        />
      ),
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_7')} />,
      remark: <Remark {...getParams('remark_7')} />,
    },
    {
      checkPoint: '关于租赁期限',
      lawRiskControl: '租赁期限最长不得超过二十年，超过二十年的，超出部分无效。',
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_8')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_8')} />,
      remark: <Remark {...getParams('remark_8')} />,
    },
    {
      checkPoint: '关于租金支付期限和方式',
      lawRiskControl:
        '明确租金支付的期限，支付方式可选季度支付/半年支付/年度支付；若为一次性支付，应对风险进行预判，并制定风险防范措施。',
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_9')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_9')} />,
      remark: <Remark {...getParams('remark_9')} />,
    },
    {
      checkPoint: '是否同意我公司转租',
      lawRiskControl:
        '公司租赁房屋的，应当与出租方协商，要求其同意我公司有权转租，并在租赁合同中明确约定。',
      bizDeptVerify: (
        <BizDeptVerify {...getParams('bizDeptVerify_10')} extraText="是否同意我公司转租：" />
      ),
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_10')} />,
      remark: <Remark {...getParams('remark_10')} />,
    },
  ]
}
