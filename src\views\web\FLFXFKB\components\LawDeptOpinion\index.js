import React, { useEffect, useState } from 'react'

import { Input } from 'antd'

import './index.scss'

const { TextArea } = Input

const lawDeptOpinion = ({
  value,
  access,
  fieldName,
  onChange,
  placeholder = '请输入',
  debug = false,
}) => {
  let props = { placeholder }

  const fieldAccess = access[fieldName]
  const [newValue, setNewValue] = useState('')

  useEffect(() => {
    setNewValue(value)
  }, [value])

  // 字段配置成不可见
  if (fieldAccess === 'NONE') {
    return null
  }

  props = {
    disabled: fieldAccess !== 'WRITE' && !debug,
    placeholder: fieldAccess !== 'WRITE' && !debug ? '' : placeholder,
    autoSize: true,
    // minRows: 4,
    onChange: e => {
      setNewValue(e.target.value)
      onChange(fieldName, e.target.value)
    },
  }

  return (
    <div className="law-dept-opinion">
      <div>
        <TextArea {...props} value={newValue} />
      </div>
    </div>
  )
}

export default lawDeptOpinion
