import React from 'react'

import BizDeptVerify from '../BizDeptVerify'
import LawDeptOpinion from '../LawDeptOpinion'
import Remark from '../Remark'

import { getRowSpanNos } from '../../utils'

const shareOnCell = (_, index) => {
  if (index === 0) {
    return {
      rowSpan: 4,
    }
  }

  if (getRowSpanNos(0, 4).includes(index)) {
    return {
      rowSpan: 0,
    }
  }

  if (index === 5) {
    return {
      rowSpan: 6,
    }
  }

  if (getRowSpanNos(5, 6).includes(index)) {
    return {
      rowSpan: 0,
    }
  }

  return {}
}

export const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    onCell: shareOnCell,
    width: 70,
  },
  {
    title: '类别',
    dataIndex: 'categoryName',
    onCell: shareOnCell,
    width: 140,
  },
  {
    title: '提示要点',
    dataIndex: 'checkPoint',
  },
  {
    title: '法律风险防控要求',
    dataIndex: 'lawRiskControl',
    width: 300,
  },
  {
    title: '业务部门核实情况',
    dataIndex: 'bizDeptVerify',
  },
  {
    title: '法律部门复核意见',
    dataIndex: 'lawDeptOpinion',
  },
  {
    title: '备注',
    dataIndex: 'remark',
  },
]

export const getDataSource = (data = {}, access = {}, onChange, debug = false) => {
  const getParams = fieldName => {
    return {
      fieldName,
      value: data[fieldName],
      access,
      debug,
      onChange,
    }
  }

  return [
    {
      index: 1,
      categoryName: '拟租赁土地基本情况',
      checkPoint: '业主名称/营业执照',
      lawRiskControl: '记录业主名称全称/营业执照，身份证号/统一社会信用代码。',
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_0')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_0')} />,
      remark: <Remark {...getParams('remark_0')} />,
    },
    {
      checkPoint: '拟租赁土地详细地址',
      lawRiskControl:
        '记录拟租赁土地的具体地址，具体到坐落位置：XX市XX县（区）XX乡镇XX路XX号（明确具体方位，如东至XX，西至XX，南至XX，北至XX）',
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_1')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_1')} />,
      remark: <Remark {...getParams('remark_1')} />,
    },
    {
      checkPoint: '规划用途（建设用地/宅基地/荒坡地/废弃地/闲置土地）',
      lawRiskControl: (
        <div>
          租赁土地应优先选择建设用地，无建设用地的，可选择宅基地用地；无宅基地的，可适当考虑荒坡地、废弃地、闲置地等未利用地，但不得租用耕地、林地等农用地。
          <ol>
            <li>
              1.《土地管理法》规定：国家编制土地利用总体规划，规定土地用途，将土地分为农用地、建设用地和未利用地。严格限制农用地转为建设用地，控制建设用地总量，对耕地实行特殊保护。
            </li>
            <li>
              2.《中共中央、国务院关于进一步加强土地管理切实保护耕地的通知》规定：要严格控制各类建设占地，特别要控制占用耕地、林地，少占好地，充分利用现有建设用地和废弃地等。
            </li>
            <li>
              3.《国务院办公厅关于加强土地转让管理严禁炒卖土地的通知》规定：严格控制城乡建设用地总量，坚决制止非农建设非法占用土地，各项建设可利用闲置土地的，必须使用闲置土地。
            </li>
          </ol>
        </div>
      ),
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_2')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_2')} />,
      remark: <Remark {...getParams('remark_2')} />,
    },
    {
      checkPoint: '面积（租赁面积/使用面积）',
      lawRiskControl: '以平方米为单位',
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_3')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_3')} />,
      remark: <Remark {...getParams('remark_3')} />,
    },
    {
      index: 2,
      categoryName: '拟租赁土地权利归属',
      checkPoint: '土地权属证明材料',
      lawRiskControl: (
        <ol>
          <li>1.原则上应提供土地使用权证；</li>
          <li>
            2.确实无法提供土地使用权证又必须租赁的，可提供乡镇政府、村委会等机构出具的证明材料并由出租人对土地权属无纠纷做出承诺和保证；
          </li>
          <li>3.同时通过实地走访调查等辅助措施确定土地权属情况；权属证明可参考模板。</li>
        </ol>
      ),
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_4')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_4')} />,
      remark: <Remark {...getParams('remark_4')} />,
    },
    {
      index: 3,
      categoryName: '主要合同条款',
      checkPoint: '关于土地租赁合同的版本',
      lawRiskControl: '要求使用公司土地租赁合同模板。',
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_5')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_5')} />,
      remark: <Remark {...getParams('remark_5')} />,
    },
    {
      checkPoint: '关于租赁期限',
      lawRiskControl: '租赁期限最长不得超过二十年，超过二十年的，超出部分无效。',
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_6')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_6')} />,
      remark: <Remark {...getParams('remark_6')} />,
    },
    {
      checkPoint: '关于租金支付期限和方式',
      lawRiskControl:
        '明确租金支付的期限，支付方式可选季度支付/半年支付/年度支付；若为一次性支付，应对风险进行预判，并制定风险防范措施。',
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_7')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_7')} />,
      remark: <Remark {...getParams('remark_7')} />,
    },
    {
      checkPoint: '关于权属情况',
      lawRiskControl:
        '建议在租赁合同中增加出租人保证租赁土地无权属纠纷、无抵押、无查封等他项权限制的条款及相应违约责任。',
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_8')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_8')} />,
      remark: <Remark {...getParams('remark_8')} />,
    },
    {
      checkPoint: '关于拆迁补偿条款',
      lawRiskControl: (
        <ol>
          <li>
            1.合同应明确对合同期内发生的政府征地或出租人违约造成的不动产损失补偿条款，最大限度降低不动产资产报废损失。
          </li>
          <li>
            2.合同应明确若发生拆迁，公司有权获得合法补偿；明确拆迁时房屋所得补偿应支付给我公司，若出租人违约应承担较重的违约责任。
          </li>
        </ol>
      ),
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_9')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_9')} />,
      remark: <Remark {...getParams('remark_9')} />,
    },
    {
      checkPoint: '关于辅助协助义务约定',
      lawRiskControl: (
        <div>
          <p>合同应对以下内容做出约定：</p>
          <ol>
            <li>
              1.在建设和设备安装期间，出租人需积极配合，做好车辆进出、光缆引入、市电引入、消防及安全保障工作；
            </li>
            <li>
              2.合同期间出租人应保证机房的正常运行，不得擅自中断通信电源，不得阻挠我方工作及维护人员进出机房。
            </li>
            <li>
              3.明确相应的违约责任，如若租赁后公司无法正常开展建设，且出租方未积极出面协调解决的，公司可以单方面解除租赁合同，并要求出租方退还已支付的租金。
            </li>
          </ol>
        </div>
      ),
      bizDeptVerify: <BizDeptVerify {...getParams('bizDeptVerify_10')} />,
      lawDeptOpinion: <LawDeptOpinion {...getParams('lawDeptOpinion_10')} />,
      remark: <Remark {...getParams('remark_10')} />,
    },
  ]
}
