import React, { useState, useEffect, useRef, useMemo } from 'react'
import { message, Space } from 'antd'

import { Buttons } from 'ROOT/components/Process'
import api from 'ROOT/service'
import { getQueryString, closeTheWindow } from 'ROOT/utils'
import Fwzl from '../../components/Fwzl'
import { config as formConfig } from '../../../FLFXFKB/components/Fwzl/config'
import { commonBizCheck } from '../../utils'

const Approve = props => {
  const { userTaskId, procFormDataKey, debug } = useMemo(
    () => getQueryString(props.location.search),
    [],
  )

  const formdataRef = useRef({})
  const [access, setAccess] = useState({})
  const [initValue, setInitValue] = useState({})
  const [ableEditBtn, setAbleEditBtn] = useState([])
  const [buttonGroup, setButtonGroup] = useState([])

  useEffect(() => {
    if (debug) {
      ableEditForm()
    }
  }, [])

  const ableEditForm = async () => {
    const res = await api.getEditableForm()

    if (res.success && res.data) {
      setAbleEditBtn([
        {
          name: '更新数据',
          async: true,
          onClick: async () => {
            await saveForm(() => {
              closeTheWindow(props.location.search)
            }, true)
          },
        },
      ])
    }
  }

  const saveForm = async callback => {
    const res = await commonCommit()
    if (res.success) {
      message.success('操作成功', () => {
        if (typeof callback === 'function') callback(res)
      })
    }
  }

  const commonCommit = async () => {
    const formData = formdataRef.current.getFormData()

    const result = checkFormData(formData, access)

    if (!result.success) {
      return result
    }

    const res = await api.upDateForm({
      config: formConfig,
      data: formData,
      reportId: procFormDataKey,
    })

    return res
  }

  // 提交数据校验
  const checkFormData = (formData = {}) => {
    return commonBizCheck(formData, access)
  }

  const finishSubmit = () => {
    setTimeout(() => {
      closeTheWindow(props.location.search)
    }, 2000)
  }

  useEffect(() => {
    window.addEventListener('process', e => {
      switch (e.detail.type) {
        case 'saved':
          break
        case 'approved': // 审批后
        case 'added': // 加签后
        case 'stoped': // 终止后
        case 'rejected': // 退回后
        case 'forworded': // 转交后
        case 'cced': // 抄送后
        default:
          finishSubmit()
      }
    })
  }, [])

  // 数据回填
  useEffect(() => {
    if (procFormDataKey) {
      api
        .getFormData({
          reportId: procFormDataKey,
          userTaskId,
        })
        .then(res => {
          if (res && res.data) {
            let data = res.data.data

            setInitValue(data)
          }
        })
    }
  }, [])

  const submitForm = async () => {
    const res = await commonCommit()
    if (res.success) {
      return Promise.resolve()
    }
    throw new Error('保存出错')
  }

  const onMount = ({ access, editMode, draft, process }) => {
    const { nodeName, taskStatus } = process || {}

    setAccess(access)

    if (nodeName === '开始节点' && taskStatus === 'running') {
      const btns = [
        {
          name: '保存退出',
          async: false,
          onClick: () => {
            saveForm(() => {
              closeTheWindow(props.location.search)
            })
          },
        },
        {
          name: '保存',
          async: false,
          onClick: () => {
            saveForm('')
          },
        },
      ]

      setButtonGroup([...btns])
    }
  }

  return (
    <div className="fwzl-approve">
      <Fwzl
        zone="area"
        type="approve"
        initValue={initValue}
        access={access}
        ref={formdataRef}
        debug={debug}
      />
      <Space>
        <Buttons
          userTaskId={userTaskId}
          onMount={onMount}
          onSubmitOpen={async () => {
            // 表单数据
            const formData = formdataRef.current.getFormData()

            if (!checkFormData(formData).success) {
              return Promise.reject(new Error(checkFormData(formData).msg))
            }

            return formData
          }}
          extraButtons={buttonGroup.concat(ableEditBtn)}
          onSubmit={submitForm}
        />
      </Space>
    </div>
  )
}

export default Approve
