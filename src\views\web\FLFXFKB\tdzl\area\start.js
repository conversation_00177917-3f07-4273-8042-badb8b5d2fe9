import React, { useMemo, useState, useRef, useEffect } from 'react'
import moment from 'moment'
import { message, Modal, Space } from 'antd'

import { Buttons } from 'ROOT/components/Process'
import useMyInfo from 'ROOT/hooks/useMyInfo'
import { getQueryString, closeTheWindow, emergencyLevel } from 'ROOT/utils'
import api from 'ROOT/service'
import Tdzl from '../../components/Tdzl'
import { tdzlType, tdzlTitle } from '../../config'
import { config as formConfig } from '../../../FLFXFKB/components/Tdzl/config'
import { commonBizCheck } from '../../utils'

const { confirm } = Modal

const Start = props => {
  const { procKey, appId, procFormDataKey, readOnly } = useMemo(
    () => getQueryString(props.location.search),
    [props.location.search],
  )

  const myInfo = useMyInfo({ isSetStorage: true })
  const formId = useRef(procFormDataKey)
  const formdataRef = useRef({})
  const [access, setAccess] = useState({})
  const [userInfoReady, setUserInfoReady] = useState(false)

  const [initValue, setInitValue] = useState({
    _taskLevel: '3',
    lawAuditOpinion: undefined, // 采用法律审核意见，默认：不选择
    draftDate: moment().valueOf(), // 起草时间，默认当前时间
  })

  useEffect(() => {
    const { linkPath = [], loginName } = myInfo
    if (myInfo && Object.keys(myInfo).length > 0) {
      setUserInfoReady(true)

      setInitValue({
        ...initValue,
        drafterName: loginName, // 起草人
        // 起草部门，默认选中第一项
        drafterDeptName:
          linkPath && linkPath.length > 0
            ? {
                value: linkPath[0].deptId,
                label: linkPath[0].linkPath,
                deptName: linkPath[0].rootDeptName,
              }
            : { value: '', label: '', deptName: '' },
      })
    }
  }, [myInfo])

  useEffect(() => {
    if (!userInfoReady) return

    // 表示从【草稿箱】进入，获取一次草稿箱数据
    if (procFormDataKey) {
      api
        .getFormData({
          reportId: procFormDataKey, // 获取详情，也就是初始值
        })
        .then(res => {
          if (res && res.data) {
            let data = res.data.data

            // 从【草稿箱】回填数据
            setInitValue(data)
          }
        })
    } else {
      // 默认保存，生成 formId
      api
        .saveForm({
          type: tdzlType[0].type,
          classify: {
            name: tdzlType[0].name,
            englishName: tdzlType[0].englishName,
          },
          config: formConfig,
          data: {
            ...initValue,
          },
          otherData: {
            procKey,
            appId,
          },
        })
        .then(res => {
          const { data } = res

          // 获取新的 formId 数据，并保存
          formId.current = data
        })
    }
  }, [procFormDataKey, userInfoReady])

  useEffect(() => {
    window.addEventListener('process', event => {
      switch (event.detail.type) {
        default:
          finishSubmit()
      }
    })
  }, [])

  const finishSubmit = () => {
    deleteDraft()
  }

  const deleteDraft = async () => {
    const info = localStorage.getItem('userInfo')
      ? JSON.parse(localStorage.getItem('userInfo'))
      : {}

    const res = await api.deleteDraft({
      appId,
      appTasks: [
        {
          appTaskId: formId.current.toString(),
          handlerIds: [info.loginUid],
        },
      ],
    })
    if (res.success) {
      setTimeout(() => {
        message.success('操作成功', () => {
          closeTheWindow(props.location.search)
        })
      }, 4000)
    }
  }

  const onMount = ({ access, editMode, draft, process }) => {
    // 只读模式，只能查看数据
    if (readOnly === '1') {
      setAccess({})
      return
    }

    setAccess(access)
  }

  const commonCommit = async () => {
    const formData = formdataRef.current.getFormData()

    const result = checkFormData(formData)

    if (!result.success) {
      return result
    }

    const res = await api.upDateForm({
      config: formConfig,
      data: formData,
      reportId: formId.current,
      otherData: {
        procKey,
        appId,
      },
    })

    return res
  }

  const saveDraft = async callback => {
    const info =
      myInfo || localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo')) : {}

    const formData = formdataRef.current.getFormData()

    const res = await api.saveDraft({
      appId,
      appTasks: [
        {
          appTaskId: formId.current.toString(),
          businessType: 2,
          emergencyLevel: emergencyLevel[+formData._taskLevel],
          handleEntry: [
            {
              handleType: 0, // 草稿
              handlerId: info.loginUid,
            },
          ],
          processType: tdzlType[0].name,
          sponsorId: info.loginUid,
          jumpToDetail: 1,
          title: tdzlTitle,
          detailUrl: decodeURIComponent(
            procFormDataKey
              ? `${location.href
                  .replace('/extra-forms/', '/extra-forms-h5/')
                  .replace(/backurl=.*?&|backurl=.*?$/, '')}`
              : `${location.href
                  .replace('/extra-forms/', '/extra-forms-h5/')
                  .replace(/backurl=.*?&|backurl=.*?$/, '')}&procFormDataKey=${formId.current}`,
          ),
          webDetailUrl: decodeURIComponent(
            procFormDataKey
              ? `${location.href.replace(/backurl=.*?&|backurl=.*?$/, '')}`
              : `${location.href.replace(/backurl=.*?&|backurl=.*?$/, '')}&procFormDataKey=${
                  formId.current
                }`,
          ),
        },
      ],
    })

    if (res.success) {
      message.success('操作成功', () => {
        if (typeof callback === 'function') callback(res)
      })
    }
  }

  const saveForm = async (callback, isDraft) => {
    const res = await commonCommit()

    if (res.success) {
      if (isDraft) {
        saveDraft(callback)
      } else {
        message.success('操作成功', () => {
          if (typeof callback === 'function') callback(res)
        })
      }
    }
  }

  const deleteForm = async () => {
    confirm({
      content: '你选择了注销当前文档，系统将删除该文档，是否继续？',
      onOk: async () => {
        const res = await api.deleteForm({
          reportId: procFormDataKey,
        })
        if (res.success) {
          deleteDraft()
        } else {
          message.error(data.msg)
        }
      },
    })
  }

  const submitForm = async () => {
    const res = await commonCommit()

    if (res && res.success) {
      return +formId.current
    }

    throw new Error('保存出错')
  }

  const buttonGroup = () => {
    const array = [
      {
        name: '保存退出',
        async: false,
        onClick: () => {
          saveForm(() => {
            closeTheWindow(props.location.search)
          }, true)
        },
      },
      {
        name: '保存',
        async: false,
        onClick: () => {
          saveForm('', true)
        },
      },
    ]
    if (procFormDataKey) {
      array.push({
        name: '注销文档',
        async: false,
        onClick: () => {
          deleteForm()
        },
      })
    }
    return array
  }

  // 提交数据校验
  const checkFormData = (formData = {}) => {
    return commonBizCheck(formData, access)
  }

  return (
    <div className="tdzl-start">
      <Tdzl
        zone="area"
        type="start"
        initValue={initValue}
        access={access}
        ref={formdataRef}
        readOnly={readOnly}
      />
      {/* 只读模式，不展示流程按钮 */}
      {readOnly !== '1' && (
        <Space>
          <Buttons
            appId={appId}
            procKey={procKey}
            onSubmit={submitForm}
            onMount={onMount}
            onSubmitOpen={async () => {
              // 表单数据
              const formData = formdataRef.current.getFormData()

              if (!checkFormData(formData).success) {
                return Promise.reject(new Error(checkFormData(formData).msg))
              }

              return formData
            }}
            extraButtons={buttonGroup()}
          />
        </Space>
      )}
    </div>
  )
}

export default Start
