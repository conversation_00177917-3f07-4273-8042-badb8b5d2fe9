import { message } from 'antd'

export const getRowSpanNos = (index, rowSpan) => {
  return Array.from({ length: rowSpan - 1 }).map((_, idx) => idx + index + 1)
}

export const commonBizCheck = (formData = {}, access = {}) => {
  const { lawAuditOpinion, notUseReason, bizTitle } = formData || {}

  // 标题必填验证
  if (access && access['bizTitle'] === 'WRITE' && !(bizTitle || '').trim()) {
    const msg = '请输入标题'

    message.error(msg)

    return {
      success: false,
      msg,
    }
  }

  // 【采用法律审核意见】 字段设置为可写，并且【采用法律审核意见】未选择，不允许提交
  if (access && access['lawAuditOpinion'] === 'WRITE' && typeof lawAuditOpinion === 'undefined') {
    const msg = '请选择【采纳法律审核意见】'

    message.error(msg)

    return {
      success: false,
      msg,
    }
  }

  // 【采用法律审核意见】为【否】，并且【不采用法律审核意见的原因】为空时，不允许提交
  if (
    access &&
    access['notUseReason'] === 'WRITE' &&
    Number(lawAuditOpinion) === 0 &&
    !notUseReason
  ) {
    const msg = '【不采用法律审核意见的原因】不能为空'

    message.error(msg)

    return {
      success: false,
      msg,
    }
  }

  return {
    success: true,
    msg: '',
  }
}
