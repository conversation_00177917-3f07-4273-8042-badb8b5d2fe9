import React, { useCallback, useEffect, useState } from 'react'
import { Provider } from 'react-redux'

import {
  SchemaMarkupForm,
  FormEffectHooks,
  createFormActions,
  createAsyncFormActions,
} from '@formily/antd'
import { FormMegaLayout, Input, DatePicker, Select, Checkbox } from '@formily/antd-components'
import { css } from 'emotion'
import { Tabs, message, Button } from 'antd'
import { driver } from 'driver.js'
import 'driver.js/dist/driver.css'

import { Steps } from 'ROOT/components/Process'
import Editable from 'ROOT/components/Formily/Editable'
import Dept from 'ROOT/components/Formily/Dept'
import useOrgType from 'ROOT/hooks/useOrgType'
import Upload from '../component/Upload'
import Desc from '../component/Desc'
import SubForm from '../component/SubForm'
import Comment from '../component/Comment'
import TemplateFile from '../component/TemplateFile'
import SelectMember from '../component/SelectMember'
import Duties from '../component/Duties'
import Post from '../component/Post'
import UserSelector from '../component/UserSelector'
import schema from './schema'
import store from '../component/MoaTree/store'
import api from 'ROOT/service'

const { TabPane } = Tabs

// 禁止滚动函数
function disableScroll() {
  document.body.style.overflow = 'hidden'
}

// 恢复滚动函数
function enableScroll() {
  document.body.style.overflow = 'auto'
}

const driverObj = driver({
  showButtons: ['close'],
  onPopoverRender: (popover, { config, state }) => {
    const div = document.createElement('div')
    div.style.textAlign = 'right'
    div.style.marginTop = '10px'

    const button = document.createElement('button')
    button.style.color = '#fff'
    button.style.backgroundColor = 'var(--adm-color-primary)'
    button.style.padding = '4px 10px'
    button.style.border = 'none'
    button.style.outline = 'none'
    button.innerHTML = '我知道了'
    button.addEventListener('click', () => {
      // 避免重复提示
      localStorage.setItem('driver', 'true')

      driverObj.destroy()
    })

    div.appendChild(button)
    popover.description.appendChild(div)

    // 隐藏关闭按钮
    popover.closeButton.style.display = 'none'
  },
  steps: [
    {
      element: '.step1',
      popover: {
        title: '资料上传',
        description: '产品提供',
        side: 'top',
        align: 'end',
      },
    },
  ],
  onHighlightStarted: () => disableScroll(), // 引导开始时禁止滚动
  onDestroyed: () => enableScroll(), // 引导结束时恢复滚动
  onCloseClick: () => {
    // 避免重复提示
    localStorage.setItem('driver', 'true')

    driverObj.destroy()
  }, // 关闭时触发
})

const { onFieldValueChange$, onFieldInputChange$ } = FormEffectHooks
export const actions = createAsyncFormActions()

const Form = props => {
  const { initValue, editable, disabled, access = {}, debug, userTaskId } = props

  // useEffect(() => {
  //   // 检测 step1 元素是否存在，如果存在，则开始引导，超时时间为：10s
  //   const timeout = 10 * 1000
  //   const start = Date.now()

  //   const timer = setInterval(() => {
  //     // 超时后，直接退出
  //     if (Date.now() - start >= timeout) {
  //       clearInterval(timer)
  //     }

  //     if (document.querySelector('.step1')) {
  //       if (localStorage.getItem('driver') !== 'true') {
  //         driverObj.drive()
  //       }

  //       clearInterval(timer)
  //     }
  //   }, 0.2 * 1000)
  // }, [])

  const useManyEffects = useCallback(() => {
    const { setFieldState, getFormState, getFieldValue } = createFormActions()

    // 人员互斥处理
    // target: uids 列表，字符串类型
    const mutualExclusiveUser = (source = [], target = [], key = 'uid') => {
      if (!target || (Array.isArray(target) && target.length === 0)) {
        return source
      }

      return source.filter(user => !target.includes(String(user[key])))
    }

    // 约谈类型
    onFieldValueChange$('type').subscribe(({ value }) => {
      setFieldState('templateFile', state => {
        state.props['x-component-props'] = {
          type: value,
        }
      })
    })

    // 约谈主谈人职务
    onFieldInputChange$('appointmentName').subscribe(({ value }) => {
      setFieldState('appointmentDuty', state => {
        const [user] = value || []
        const { id, orgId } = user || {}

        state.props['x-component-props'].user = { uid: id, orgId }
      })
    })

    // 被委托人职务
    onFieldInputChange$('mandatary').subscribe(({ value }) => {
      setFieldState('mandataryDuty', state => {
        const [user] = value || []
        const { id, orgId } = user || {}

        state.props['x-component-props'].user = { uid: id, orgId }
      })
    })

    // 党委书记审核
    onFieldInputChange$('secretaryReview').subscribe(async ({ value }) => {
      // 【约谈对象】数据
      const appointmentSubList = await getFieldValue('appointmentSubList')

      // 【约谈对象】uids
      const uids = (appointmentSubList || []).map(x => x.uid)

      // 拉接口判断【约谈对象】中是否存在【市党委书记】，有的话，不能选择【否】
      const result = await api.checkRole({ uids })

      setFieldState('secretaryReview', state => {
        // 被约谈人中是否还有【市党委书记】，有的话，不能选择【否】
        if (result) {
          state.value = '1'
          message.warning('被约谈人中存在党委书记，无法取消')
        }
      })
    })

    // 约谈对象
    onFieldInputChange$('appointmentSubList').subscribe(async ({ value }) => {
      // 约谈参加人
      const participants = await getFieldValue('participants')

      // 【约谈对象】uids
      const uids = (value || []).map(x => x.uid)

      // 拉接口判断【约谈对象】中是否存在【市党委书记】，有的话，不能选择【否】
      const checkResult = await api.checkRole({ uids })

      setFieldState('secretaryReview', state => {
        if (checkResult) {
          state.value = '1'
          // message.warning('被约谈人中存在党委书记，无法取消')
        }
      })

      // 约谈对象和约谈参加人互斥

      // 约谈参加人 uids
      const participantsUids = (participants || []).map(x => String(x.id))

      // 过滤掉跟约谈对象重复的人员
      const result = mutualExclusiveUser(value, participantsUids)

      // 约谈对象和约谈参加人存在互斥
      if (result.length !== (value || []).length) {
        message.warning('约谈参加人和被约谈人不能重复')

        setFieldState('appointmentSubList', state => {
          state.value = result
        })
      }
    })

    // 约谈参加人和约谈对象互斥
    onFieldInputChange$('participants').subscribe(async ({ value }) => {
      const appointmentSubList = await getFieldValue('appointmentSubList')

      // 约谈对象 uids
      const appointmentUids = appointmentSubList.map(x => String(x.uid))

      // 过滤掉跟约谈参加人重复的人员
      const result = mutualExclusiveUser(value, appointmentUids, 'id')

      // 约谈对象和约谈参加人存在互斥
      if (result.length !== (value || []).length) {
        message.warning('约谈参加人和被约谈人不能重复')

        setFieldState('participants', state => {
          state.value = result
        })
      }
    })
  }, [])

  useEffect(() => {
    if (Object.keys(initValue).length > 0) {
      actions.setFormState(state => {
        state.values = initValue
      })
    }
  }, [initValue])

  useEffect(() => {}, [access])

  // 发起人的单位类型
  const orgType = useOrgType((initValue || {}).drafterOrgId)

  const getLnCodes = () => {
    // 区公司
    if (orgType === 1) {
      return ['zw_1_0', 'zw_2_0', 'zw_3_0']
    }
    // 市公司
    if (orgType === 2) {
      return ['zw_2_0', 'zw_3_0', 'zw_4_0', 'zw_5_0']
    }
    return []
  }

  Editable.isFieldComponent = true
  Upload.isFieldComponent = true
  Dept.isFieldComponent = true
  Desc.isFieldComponent = true
  SubForm.isFieldComponent = true
  Comment.isFieldComponent = true
  TemplateFile.isFieldComponent = true
  SelectMember.isFieldComponent = true
  Duties.isFieldComponent = true
  Post.isFieldComponent = true
  UserSelector.isFieldComponent = true

  return (
    <Provider store={store}>
      <div
        className={css`
          .preview-text {
            word-wrap: break-word;
            word-break: break-all;
            white-space: pre-wrap;
          }
        `}
      >
        <p className="form-title">约谈审批</p>
        <SchemaMarkupForm
          schema={schema}
          actions={actions}
          components={{
            DatePicker,
            Input,
            FormMegaLayout,
            Select,
            TextArea: Input.TextArea,
            Editable,
            Upload,
            Dept,
            CheckboxGroup: Checkbox.Group,
            Desc,
            SubForm,
            Comment,
            TemplateFile,
            SelectMember,
            Duties,
            Post,
            UserSelector,
          }}
          editable={editable}
          previewPlaceholder="无"
          effects={useManyEffects}
          expressionScope={{
            labelAlign: editable ? 'top' : 'left',
            disabled,
            access,
            getLnCodes,
            debug,
            drafterOrgId: (initValue || {}).drafterOrgId, // 起草人单位 id
            drafterOrgName: (initValue || {}).drafterOrgName, // 起草人单位名称
            log: value => {
              console.log(value, 11111)
            },
          }}
        />
        {userTaskId && (
          <Tabs defaultActiveKey="1" className="mb10">
            <TabPane tab="审批流程" key="1">
              <Steps userTaskId={userTaskId} />
            </TabPane>
          </Tabs>
        )}
      </div>
    </Provider>
  )
}

export default Form
