import React, { useMemo, useState, useEffect, useRef } from 'react'

import { message, Modal } from 'antd'

import { getQueryString, patchData, closeTheWindow, emergencyLevel } from 'ROOT/utils'
import api from 'ROOT/service'
import { Buttons } from 'ROOT/components/Process'
import useMyInfo from 'ROOT/hooks/useMyInfo'
import Form, { actions } from './form'
import { parseJson } from '../component/SubForm/util'
import { transformRequestParams, transformResponseData } from './util'

const Start = props => {
  const { procKey, appId, procFormDataKey } = useMemo(
    () => getQueryString(props.location.search),
    [props.location.search],
  )

  const myInfo = useMyInfo({ isSetStorage: true })
  const [disabled, setDisabled] = useState(false)
  const [editable] = useState(true)
  const formId = useRef(procFormDataKey)
  const [access, setAccess] = useState({})

  const resetUserInfo = () => {
    const { linkPath = [], loginName } = myInfo
    actions.setFieldState('drafterDeptName', state => {
      state.props.enum =
        linkPath && linkPath.length > 0
          ? linkPath.map(item => ({
              value: item.deptId,
              label: item.linkPath,
              deptName: item.rootDeptName,
            }))
          : []
      if (initValue.drafterDeptName && initValue.drafterDeptName.value) {
        state.value = initValue.drafterDeptName
      } else {
        state.value =
          linkPath && linkPath.length > 0
            ? {
                value: linkPath[0].deptId,
                label: linkPath[0].linkPath,
                deptName: linkPath[0].rootDeptName,
              }
            : { value: '', label: '', deptName: '' }
      }
    })
    actions.setFieldState('drafterName', state => {
      state.value = loginName
    })
  }

  const getDetail = async () => {
    const res = await api.getAppointmentInfo({
      id: procFormDataKey,
    })

    if (res && res.data) {
      const result = transformResponseData(res.data, myInfo)
      setInitValue(result)
    }
  }

  useEffect(() => {
    if (myInfo && Object.keys(myInfo).length > 0) {
      resetUserInfo()
    }
  }, [myInfo])

  const [initValue, setInitValue] = useState({})

  useEffect(() => {
    if (access.isEdit === 'WRITE') {
      setDisabled(false)
    }
  }, [access])

  useEffect(() => {
    // 草稿箱
    if (procFormDataKey) {
      getDetail()
    }
  }, [procFormDataKey])

  useEffect(() => {
    document.title = '约谈审批'

    window.addEventListener('process', () => {
      deleteDraft()
    })
  }, [])

  // 业务校验
  const bizValidate = (data = {}) => {
    const { appointmentSubList = [] } = data

    // 验证是否加了【约谈对象】数据
    if ((appointmentSubList || []).length === 0) {
      message.error('约谈对象不能为空，请添加')

      return false
    }

    // 判断【约谈对象】中是否存在【约谈参加人】【约谈人职务】为空的场景
    const isEmptyParticipant = false
    const isEmptyDuty = false

    // eslint-disable-next-line no-restricted-syntax
    for (const item of appointmentSubList) {
      const { participants, duty } = item
      const users = parseJson(participants, [])

      // 未添加【约谈参加人】
      // if (users.length === 0) {
      //   isEmptyParticipant = true

      //   break
      // }

      // 【约谈人职务】为空
      // if ((duty || '').trim().length === 0) {
      //   isEmptyDuty = true
      //   break
      // }
    }

    // if (isEmptyParticipant) {
    //   message.error('约谈参加人不能为空，请完善约谈参加人')

    //   return false
    // }

    // if (isEmptyDuty) {
    //   message.error('被约谈人职务不能为空，请完善被约谈人职务')

    //   return false
    // }

    return true
  }

  const commonCommit = async () => {
    const { values } = await actions.submit()
    const data = patchData(values, initValue)

    console.log(values, data, 55555)

    if (!bizValidate(data)) {
      return Promise.reject(new Error('业务校验失败了'))
    }

    const params = transformRequestParams(data)
    const res = await api.saveAppointment({
      id: formId.current,
      ...params,
    })
    formId.current = res.data
    return res
  }

  const submitForm = async () => {
    const res = await commonCommit()

    if (res && res.success) {
      return +formId.current
    }
    throw new Error('保存出错')
  }

  const onSubmitOpen = async () => {
    const { values } = await actions.submit()
    const result = patchData(values, initValue)

    // 执行业务逻辑校验
    if (!bizValidate(result)) {
      return Promise.reject(new Error('业务校验失败了'))
    }

    return { ...result, _dept: values.drafterDeptName }
  }

  const saveDraft = async callback => {
    const info =
      myInfo || localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo')) : {}

    const { values } = await actions.submit()
    const result = patchData(values, initValue)
    const res = await api.saveDraft({
      appId,
      appTasks: [
        {
          appTaskId: formId.current.toString(),
          businessType: 1, // 2 => 公文类，1 => 审批类，4 => 财务类，5 => 其它
          emergencyLevel: emergencyLevel[+result._taskLevel],
          handleEntry: [
            {
              handleType: 0, // 草稿
              handlerId: info.loginUid,
            },
          ],
          processType: '约谈审批',
          sponsorId: info.loginUid,
          jumpToDetail: 1,
          title: '约谈审批',
          detailUrl: decodeURIComponent(
            procFormDataKey
              ? `${location.href
                  .replace('/extra-forms/', '/extra-forms-h5/')
                  .replace(/backurl=.*?&|backurl=.*?$/, '')}`
              : `${location.href
                  .replace('/extra-forms/', '/extra-forms-h5/')
                  .replace(/backurl=.*?&|backurl=.*?$/, '')}&procFormDataKey=${formId.current}`,
          ),
          webDetailUrl: decodeURIComponent(
            procFormDataKey
              ? `${location.href.replace(/backurl=.*?&|backurl=.*?$/, '')}`
              : `${location.href.replace(/backurl=.*?&|backurl=.*?$/, '')}&procFormDataKey=${
                  formId.current
                }`,
          ),
        },
      ],
    })
    if (res.success) {
      message.success('操作成功', () => {
        if (typeof callback === 'function') callback(res)
      })
    }
  }

  const saveForm = async (callback, isDraft) => {
    const res = await commonCommit()
    if (res && res.success) {
      if (isDraft) {
        saveDraft(callback)
      } else {
        message.success('操作成功', () => {
          if (typeof callback === 'function') callback(res)
        })
      }
    }
  }

  const deleteDraft = async () => {
    const info = localStorage.getItem('userInfo')
      ? JSON.parse(localStorage.getItem('userInfo'))
      : {}

    const res = await api.deleteDraft({
      appId,
      appTasks: [
        {
          appTaskId: formId.current.toString(),
          handlerIds: [info.loginUid],
        },
      ],
    })
    if (res.success) {
      message.success('操作成功', () => {
        closeTheWindow(props.location.search)
      })
    }
  }

  const deleteForm = async () => {
    Modal.confirm({
      content: '你选择了注销当前文档，系统将删除该文档，是否继续？',
      onOk: async () => {
        const res = await api.deleteForm({
          reportId: procFormDataKey,
        })
        if (res.success) {
          deleteDraft()
        } else {
          message.error(data.msg)
        }
      },
    })
  }
  const buttonGroup = () => {
    const array = [
      {
        name: '保存退出',
        async: false,
        onClick: () => {
          saveForm(() => {
            closeTheWindow(props.location.search)
          }, true)
        },
      },
      {
        name: '保存',
        async: false,
        onClick: () => {
          saveForm('', true)
        },
      },
    ]
    if (procFormDataKey) {
      array.push({
        name: '注销文档',
        async: false,
        onClick: () => {
          deleteForm()
        },
      })
    }
    return array
  }

  const onMount = ({ access }) => {
    setAccess(access)
  }

  return (
    <div>
      <Form editable={editable} disabled={disabled} initValue={initValue} access={access} />
      <Buttons
        appId={appId}
        procKey={procKey}
        onMount={onMount}
        onSubmit={submitForm}
        onSubmitOpen={onSubmitOpen}
        extraButtons={buttonGroup()}
      />
    </div>
  )
}

export default Start
