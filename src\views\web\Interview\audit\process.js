import React, { useMemo, useState, useEffect, useRef } from 'react'

import { isEmpty, get } from 'lodash'
import { message, Modal } from 'antd'
import moment from 'moment'

import { getQueryString, patchData, closeTheWindow } from 'ROOT/utils'
import api from 'ROOT/service'
import { Buttons } from 'ROOT/components/Process'
import useMyInfo from 'ROOT/hooks/useMyInfo'
import Form, { actions } from './form'
import OneClickDistribution from '../component/OneClickDistribution/index'
import { transformRequestParams, transformResponseData } from './util'
import { parseJson } from '../component/SubForm/util'

const Process = props => {
  const { userTaskId, procFormDataKey, handlerId, debug } = useMemo(
    () => getQueryString(props.location.search),
    [props.location.search],
  )
  const myInfo = useMyInfo({ isSetStorage: true })
  const [visible, setVisible] = useState(false)
  const [disabled, setDisabled] = useState(true)
  const [editable, setEditable] = useState(true)
  const [ableEditBtn, setAbleEditBtn] = useState([])
  const [buttonGroupList, setButtonGroupList] = useState([])
  const formId = useRef(procFormDataKey)
  const [process, setProcess] = useState({})
  const [access, setAccess] = useState({})
  const [draftData, setDraftData] = useState({})

  // 流程是否 ready 的标识
  const [wfReady, setWfReady] = useState(false)

  const [initValue, setInitValue] = useState({})

  useEffect(() => {
    document.title = '约谈审批'

    window.addEventListener('process', async e => {
      switch (e.detail.type) {
        case 'ytsp_yjff':
          await commonCommit()
          setVisible(true)
          break
        default:
          finishSubmit()
      }
    })
  }, [])

  useEffect(() => {
    if (access.isEdit === 'WRITE') {
      setDisabled(false)
    }
  }, [access])

  useEffect(() => {
    if (!isEmpty(process)) {
      const { nodeName, taskStatus } = process
      // 流程结束之后，字段全都不能编辑
      if (taskStatus === 'over') {
        setEditable(false)
      }
      if (nodeName !== '开始节点') {
        actions.setFieldState('drafterDeptName', state => {
          state.props['x-component-props'].disabled = true
        })
      }
      let btns = []
      if (nodeName === '开始节点' && taskStatus === 'running') {
        const _btns = [
          {
            name: '保存退出',
            async: false,
            onClick: () => {
              saveForm(() => {
                closeTheWindow(props.location.search)
              })
            },
          },
          {
            name: '保存',
            async: false,
            onClick: () => {
              saveForm('')
            },
          },
        ]
        btns = [...btns, ..._btns]
      }
      setButtonGroupList([...btns])
    }
  }, [process])

  const asyncConfirm = (content = '') => {
    return new Promise((resolve, reject) => {
      Modal.confirm({
        title: '提示',
        content,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          resolve()
        },
        onCancel: () => {
          reject()
        },
      })
    })
  }

  // 业务校验
  const bizValidate = (data = {}) => {
    const { appointmentSubList = [] } = data

    // 验证是否加了【约谈对象】数据
    if ((appointmentSubList || []).length === 0) {
      message.error('约谈对象不能为空，请添加')

      return false
    }

    // 判断【约谈对象】中是否存在【约谈参加人】【约谈人职务】为空的场景
    // 【上传约谈记录】节点，判断【约谈记录附件上传】是否为空
    // 【上传整改报告】节点，判断【整改报告记录上传】是否为空
    const isEmptyParticipant = false
    const isEmptyDuty = false
    let isEmptyInterviewRecord = false
    let isEmptyUploadCorrectiveActionReport = false

    // eslint-disable-next-line no-restricted-syntax
    for (const item of appointmentSubList) {
      const { participants, duty } = item
      const users = parseJson(participants, [])

      // 未添加【约谈参加人】
      // if (users.length === 0) {
      //   isEmptyParticipant = true

      //   break
      // }

      // 【约谈人职务】为空
      // if ((duty || '').trim().length === 0) {
      //   isEmptyDuty = true
      //   break
      // }

      // 【上传约谈记录】节点，判断【约谈记录附件上传】是否为空
      if (access.isInterviewRecord === 'WRITE') {
        const { config } = item
        const parseConfig = parseJson(config)

        const { recordAttachment = [] } = parseConfig

        if ((recordAttachment || []).length === 0) {
          isEmptyInterviewRecord = true
        }

        break
      }

      // 【上传整改报告】节点，判断【整改报告记录上传】是否为空
      if (access.isUploadCorrectiveActionReport === 'WRITE') {
        const { config } = item
        const parseConfig = parseJson(config)

        const { remediationAttachment = [] } = parseConfig

        if ((remediationAttachment || []).length === 0) {
          isEmptyUploadCorrectiveActionReport = true
        }

        break
      }
    }

    // if (isEmptyParticipant) {
    //   message.error('约谈参加人不能为空，请完善约谈参加人')

    //   return false
    // }

    // if (isEmptyDuty) {
    //   message.error('被约谈人职务不能为空，请完善被约谈人职务')

    //   return false
    // }

    if (isEmptyInterviewRecord) {
      message.error('约谈记录附件不能为空')

      return false
    }

    if (isEmptyUploadCorrectiveActionReport) {
      message.error('整改报告附件不能为空')

      return false
    }

    return true
  }

  const commonCommit = async () => {
    const { values } = await actions.submit()
    const data = patchData(values, initValue)

    if (!bizValidate(data)) {
      return Promise.reject(new Error('业务校验失败了'))
    }
    const params = transformRequestParams(data)
    let res
    if (formId.current) {
      // 【约谈参加人】【约谈对象收办】【上传约谈记录】【上传整改报告】以及【审核整改报告】节点，用户只能更改属于自己的子表单数据，只需要【更新子表单】的数据即可
      if (
        access.isTarget === 'WRITE' ||
        access.isParticipant === 'WRITE' ||
        access.isInterviewRecord === 'WRITE' ||
        access.isUploadCorrectiveActionReport === 'WRITE' ||
        access.isAuditCorrectiveActionReport === 'WRITE'
      ) {
        res = await api.updateSubForm(params.appointmentSubList || [])
      } else {
        // 更新约谈表单
        res = await api.saveAppointment({
          id: formId.current,
          ...params,
        })
      }
    } else {
      // 新增约谈表单
      res = await api.saveAppointment(params)
      formId.current = res.data
    }

    return res
  }

  const submitForm = async () => {
    const res = await commonCommit()
    if (res && res.success) {
      return +formId.current
    }
    throw new Error('保存出错')
  }

  const onSubmitOpen = async () => {
    const { values } = await actions.submit()
    const result = patchData(values, initValue)

    const getAppointmentTime = () => {
      const { appointmentSubList = [], appointmentTime } = result

      // 先找子表单中的约谈时间，再找主表单中的约谈时间
      return get(appointmentSubList, '[0].appointmentTime', appointmentTime)
    }

    // 执行业务逻辑校验
    if (!bizValidate(result)) {
      return Promise.reject(new Error('业务校验失败了'))
    }

    // 约谈对象收办
    if (access.isParticipant === 'WRITE') {
      const appointmentTime = getAppointmentTime()

      // 提示用户，等待用户确认
      await asyncConfirm(
        `请于${moment(appointmentTime).add(7, 'day').format('MM月DD日')}前提交约谈记录！`,
      )
    }

    // 上传约谈记录
    if (access.isInterviewRecord === 'WRITE') {
      const appointmentTime = getAppointmentTime()

      // 提示用户，等待用户确认
      await asyncConfirm(
        `请于${moment(appointmentTime).add(30, 'day').format('MM月DD日')}前提交整改报告！`,
      )
    }

    return { ...result, _dept: result.drafterDeptName }
  }

  const saveForm = async callback => {
    const res = await commonCommit()
    if (res && res.success) {
      message.success('操作成功', () => {
        if (typeof callback === 'function') callback(res)
      })
    }
  }

  const onMount = ({ access, process, draft }) => {
    const { taskStatus } = process

    // 保存草稿数据
    setDraftData(draft || {})

    if (taskStatus === 'over') {
      // 任务已结束的场景，流程不会返回 access 字段信息，此时要从 process 中的 access 字段中取值
      access = { ...access, ...process.access }
    }

    setAccess({ ...access })

    // 流程已 ready 标志
    setWfReady(true)

    setProcess(process)
  }

  const getDetail = async (params = {}) => {
    const res = await api.getAppointmentInfo({
      id: procFormDataKey,
      ...params,
    })

    if (res && res.data) {
      const result = transformResponseData(res.data, myInfo)
      setInitValue(result)
    }
  }

  const resetFormState = () => {
    setEditable(true)
    setDisabled(false)
  }

  const ableEditForm = async () => {
    const res = await api.getEditableForm()
    if (res.success && res.data) {
      resetFormState()
      setAbleEditBtn([
        {
          name: '更新数据',
          async: true,
          onClick: async () => {
            await saveForm(() => {
              closeTheWindow(props.location.search)
            })
          },
        },
      ])
    }
  }

  const finishSubmit = () => {
    setTimeout(() => {
      closeTheWindow(props.location.search)
    }, 2000)
  }

  const getOrgLinkPathForDebug = async () => {
    const res = await api.getOrgLinkPathForDebug({
      uid: initValue.drafterUid,
      orgId: initValue.drafterOrgId,
    })
    const { linkPath = [] } = res.data
    actions.setFieldState('drafterDeptName', state => {
      state.props.enum =
        linkPath && linkPath.length > 0
          ? linkPath.map(item => ({
              value: item.deptId,
              label: item.linkPath,
              deptName: item.rootDeptName,
            }))
          : []
      if (initValue.drafterDeptName && initValue.drafterDeptName.value) {
        state.value = initValue.drafterDeptName
      } else {
        state.value =
          linkPath && linkPath.length > 0
            ? {
                value: linkPath[0].deptId,
                label: linkPath[0].linkPath,
                deptName: linkPath[0].rootDeptName,
              }
            : { value: '', label: '', deptName: '' }
      }
    })
  }

  useEffect(() => {
    if (procFormDataKey && wfReady) {
      let params = {}

      // 当前是【约谈参加人收办】节点
      if (access.isTarget === 'WRITE') {
        params = {
          currentTag: 200,
        }
      }

      // 当前是【约谈对象收办】【上传约谈记录】【上传整改报告】节点
      if (
        access.isParticipant === 'WRITE' ||
        access.isInterviewRecord === 'WRITE' ||
        access.isUploadCorrectiveActionReport === 'WRITE'
      ) {
        params = {
          currentTag: 300,
        }
      }

      // 当前是【审核整改报告】节点
      if (access.isAuditCorrectiveActionReport === 'WRITE') {
        params = {
          currentTag: 400,
          handleId: handlerId, // 查看提交人的数据
        }
      }

      // debug 模式拉取所有的数据
      if (debug) {
        params = {}
      }

      getDetail(params)
    }
  }, [procFormDataKey, wfReady])

  useEffect(() => {
    if (debug && wfReady) {
      ableEditForm()
      if (initValue.drafterUid && initValue.drafterOrgId) {
        getOrgLinkPathForDebug()
      }
    }
  }, [debug, initValue, wfReady])

  return (
    <div>
      <Form
        editable={editable}
        disabled={disabled}
        initValue={!isEmpty(draftData) ? draftData : initValue}
        access={access}
        debug={debug}
        userTaskId={userTaskId}
      />
      <OneClickDistribution
        visible={visible}
        procFormDataKey={procFormDataKey}
        userTaskId={userTaskId}
        actions={actions}
        initValue={!isEmpty(draftData) ? draftData : initValue}
        onCancel={() => {
          setVisible(false)
        }}
      />
      <Buttons
        userTaskId={userTaskId}
        onMount={onMount}
        onSubmit={submitForm}
        onSubmitOpen={onSubmitOpen}
        extraButtons={buttonGroupList.concat(ableEditBtn)}
      />
    </div>
  )
}

export default Process
