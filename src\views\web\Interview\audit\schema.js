import React from 'react'

import locale from 'antd/lib/date-picker/locale/zh_CN'
import moment from 'moment'

export default {
  type: 'object',
  properties: {
    layout: {
      'x-component': 'mega-layout',
      'x-component-props': {
        grid: true,
        autoRow: true,
        labelAlign: '{{labelAlign}}',
        className: 'grid-gaps',
        responsive: {
          lg: 6,
          m: 3,
          s: 1,
        },
      },
      properties: {
        applyDate: {
          key: 'applyDate',
          name: 'applyDate',
          title: '起草时间',
          'x-component': 'Editable',
          default: moment().format('YYYY年MM月DD日'),
          'x-mega-props': {
            span: 2,
          },
        },
        drafterDeptName: {
          key: 'drafterDeptName',
          name: 'drafterDept<PERSON>ame',
          title: '约谈发起单位',
          'x-component': 'Dept',
          'x-component-props': {
            labelInValue: true,
            disabled: '{{disabled}}',
          },
          'x-mega-props': {
            span: 2,
          },
          'x-rules': [
            {
              validator: val => {
                if (!val.value) {
                  return '请选择约谈发起单位'
                }
              },
            },
          ],
        },
        drafterName: {
          key: 'drafterName',
          name: 'drafterName',
          title: '起草人',
          'x-component': 'Editable',
          'x-mega-props': {
            span: 2,
          },
        },
        topic: {
          key: 'topic',
          name: 'topic',
          title: '主题',
          required: true,
          'x-component': 'TextArea',
          'x-component-props': {
            placeholder: '关于**等部门主体/监督责任约谈',
            maxLength: 200,
            disabled: '{{disabled}}',
          },
          'x-mega-props': {
            span: 6,
          },
        },
        appointmentName: {
          key: 'appointmentName',
          name: 'appointmentName',
          title: '约谈主谈人姓名',
          required: true,
          'x-component': 'SelectMember',
          'x-component-props': {
            disabled: '{{disabled}}',
            placeholder: '请选择',
            lnCodes: '{{getLnCodes()}}',
          },
          'x-mega-props': {
            span: 3,
          },
        },
        appointmentDuty: {
          key: 'appointmentDuty',
          name: 'appointmentDuty',
          title: '约谈主谈人职务',
          // required: true,
          'x-component': 'Post',
          'x-component-props': {
            disabled: '{{disabled}}',
            type: 1,
          },
          'x-mega-props': {
            span: 3,
          },
        },
        entrust: {
          key: 'entrust',
          name: 'entrust',
          title: '是否委托',
          required: true,
          'x-component-props': {
            disabled: '{{disabled}}',
            placeholder: '请选择',
          },
          'x-mega-props': {
            span: 3,
          },
          'x-component': 'Select',
          default: false,
          enum: [
            { label: '是', value: true },
            { label: '否', value: false },
          ],
          'x-linkages': [
            {
              type: 'value:visible',
              target: '*(mandatary)',
              condition: '{{ $self.value }}',
            },
            {
              type: 'value:state',
              target: '*(mandatary)',
              condition: '{{ $self.value  }}',
              state: {
                required: true,
              },
              otherwise: {
                required: false,
              },
            },
            {
              type: 'value:visible',
              target: '*(mandataryDuty)',
              condition: '{{ $self.value }}',
            },
          ],
        },
        secretaryReview: {
          key: 'secretaryReview',
          name: 'secretaryReview',
          title: '是否需要党委书记审核',
          // required: true,
          'x-component-props': {
            disabled: '{{disabled}}',
            placeholder: '请选择',
          },
          'x-mega-props': {
            span: 3,
          },
          'x-component': 'Select',
          // default: '0',
          enum: [
            { label: '是', value: '1' },
            { label: '否', value: '0' },
          ],
        },
        mandatary: {
          key: 'mandatary',
          name: 'mandatary',
          title: '受委托人',
          required: true,
          'x-component': 'SelectMember',
          'x-component-props': {
            disabled: '{{disabled}}',
            placeholder: '请选择',
            lnCodes: '{{getLnCodes()}}',
          },
          'x-mega-props': {
            span: 3,
          },
        },
        mandataryDuty: {
          key: 'mandataryDuty',
          name: 'mandataryDuty',
          title: '受委托人职务',
          // required: true,
          'x-component': 'POST',
          'x-component-props': {
            disabled: '{{disabled}}',
            type: 2,
          },
          'x-mega-props': {
            span: 3,
          },
        },
        form: {
          key: 'form',
          name: 'form',
          title: '约谈形式',
          required: true,
          'x-component-props': {
            disabled: '{{disabled}}',
            placeholder: '请选择',
          },
          'x-mega-props': {
            span: 3,
          },
          'x-component': 'Select',
          enum: [
            { label: '个别约谈', value: 1 },
            { label: '集体约谈', value: 2 },
          ],
          // 'x-linkages': [
          //   {
          //     type: 'value:visible',
          //     target: '*(participants)',
          //     condition: '{{ [2].includes($self.value) }}', // 【集体约谈】才展示【约谈参加人】字段
          //   },
          // ],
        },
        type: {
          key: 'type',
          name: 'type',
          title: '约谈类型',
          required: true,
          'x-component': 'Select',
          'x-component-props': {
            disabled: '{{disabled}}',
            placeholder: '请选择',
          },
          enum: [
            { label: '主体责任约谈', value: 1 },
            { label: '监督责任约谈', value: 2 },
          ],
          'x-linkages': [
            {
              type: 'value:visible',
              target: '*(templateFile)',
              condition: '{{ [1, 2].includes($self.value) }}',
            },
          ],
          'x-mega-props': {
            span: 3,
          },
        },
        participants: {
          key: 'participants',
          name: 'participants',
          title: '约谈参加人(非主谈人、约谈对象)',
          // required: true,
          'x-component': 'UserSelector',
          'x-component-props': {
            disabled: '{{disabled}}',
            placeholder: '请选择',
            isChooseFromOtherOrgs: true,
            orgId: '{{drafterOrgId}}',
            orgName: '{{drafterOrgName}}',
            maxCount: 10, // 参加人最多10个
          },
          'x-mega-props': {
            span: 3,
          },
        },
        appointmentTime: {
          key: 'appointmentTime',
          name: 'appointmentTime',
          title: '约谈时间',
          required: true,
          'x-component': 'DatePicker',
          'x-component-props': {
            showTime: true,
            locale,
            disabled: '{{disabled}}',
            format: 'YYYY-MM-DD HH:mm',
            // placeholder: '请选择', // 跟产品沟通达成一致，先不改，用默认的即可
            style: {
              width: '100%',
            },
          },
          'x-mega-props': {
            span: 3,
          },
        },
        appointmentSubList: {
          key: 'appointmentSubList',
          name: 'appointmentSubList',
          title: '约谈对象信息',
          required: true,
          'x-component': 'SubForm',
          'x-component-props': {
            disabled: '{{disabled}}',
            access: '{{access}}',
            debug: '{{debug}}',
            drafterOrgId: '{{drafterOrgId}}',
          },
          'x-mega-props': {
            span: 6,
            labelAlign: 'top',
          },
        },
        problemSources: {
          key: 'problemSources',
          name: 'problemSources',
          title: '约谈问题来源',
          required: true,
          'x-component': 'CheckboxGroup',
          enum: [
            {
              label: '日常监督',
              value: 1,
            },
            {
              label: '巡视巡察',
              value: 2,
            },
            {
              label: '专项检查',
              value: 3,
            },
            {
              label: '信访案件',
              value: 4,
            },
            {
              label: '内部审计',
              value: 5,
            },
            {
              label: '党建考评',
              value: 7,
            },
            {
              label: '其他',
              value: 6,
            },
          ],
          'x-component-props': {
            disabled: '{{disabled}}',
          },
          'x-mega-props': {
            span: 4,
          },
          'x-linkages': [
            {
              type: 'value:visible',
              target: '*(other)',
              condition: '{{ ($self.value || []).includes(6) }}',
            },
          ],
        },
        other: {
          key: 'other',
          name: 'other',
          title: '其他内容',
          required: true,
          'x-component': 'TextArea',
          'x-component-props': {
            placeholder: '请输入',
            disabled: '{{disabled}}',
            maxLength: 100,
          },
          'x-mega-props': {
            span: 2,
          },
        },
        situation: {
          key: 'situation',
          name: 'situation',
          title: (
            <span>
              约谈情形
              <a
                target="_blank"
                href={`${location.origin}${location.pathname}#/web/interview/audit/detail`}
                rel="noreferrer"
                style={{ marginLeft: 6 }}
              >
                (查看具体详情说明)
              </a>
            </span>
          ),
          required: true,
          'x-component': 'CheckboxGroup',
          enum: [
            {
              label: '(一)',
              value: 1,
            },
            {
              label: '(二)',
              value: 2,
            },
            {
              label: '(三)',
              value: 3,
            },
            {
              label: '(四)',
              value: 4,
            },
            {
              label: '(五)',
              value: 5,
            },
            {
              label: '(六)',
              value: 6,
            },
            {
              label: '(七)',
              value: 7,
            },
            {
              label: '(八)',
              value: 8,
            },
            {
              label: '(九)',
              value: 9,
            },
            {
              label: '(十)',
              value: 10,
            },
          ],
          'x-component-props': {
            disabled: '{{disabled}}',
          },
          'x-mega-props': {
            span: 6,
          },
        },
        // situationDesc: {
        //   key: 'situationDesc',
        //   name: 'situationDesc',
        //   title: '查看具体情形说明',
        //   'x-component': 'Desc',
        //   'x-mega-props': {
        //     span: 2,
        //   },
        // },
        goal: {
          key: 'goal',
          name: 'goal',
          title: '约谈目的',
          required: true,
          'x-component': 'TextArea',
          'x-component-props': {
            placeholder: '请输入',
            maxLength: 10000,
            disabled: '{{disabled}}',
          },
          'x-mega-props': {
            span: 6,
          },
        },
        description: {
          key: 'description',
          name: 'description',
          title: '约谈内容简述',
          required: true,
          'x-component': 'TextArea',
          'x-component-props': {
            placeholder: '请输入',
            maxLength: 10000,
            disabled: '{{disabled}}',
          },
          'x-mega-props': {
            span: 6,
          },
        },
        uploadAttachment: {
          key: 'uploadAttachment',
          name: 'uploadAttachment',
          title: '上传附件',
          'x-component': 'Upload',
          'x-component-props': {
            disabled: '{{disabled}}',
          },
          'x-mega-props': {
            span: 6,
          },
        },
        comment: {
          key: 'comment',
          name: 'comment',
          title: '说明',
          'x-component': 'Comment',
          'x-mega-props': {
            span: 6,
          },
        },
        templateFile: {
          key: 'templateFile',
          name: 'templateFile',
          title: '模板文件',
          'x-component': 'TemplateFile',
          'x-mega-props': {
            span: 6,
          },
        },
      },
    },
  },
}
