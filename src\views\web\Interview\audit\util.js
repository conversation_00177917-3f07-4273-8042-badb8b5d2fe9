import { get, omit } from 'lodash'
import { safeParse } from 'ROOT/utils'
import moment from 'moment'

export const transformRequestParams = req => {
  const info = localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo')) : {}
  const drafterUid = req.drafterUid || info.loginUid
  const drafterDeptName = req.drafterDeptName.label
  const drafterDeptId = req.drafterDeptName.value
  const mandatary = get(req, 'mandatary[0]', {})
  const appointmentName = get(req, 'appointmentName[0]', {})

  // 约谈参加人
  const participants = (req.participants || []).map(item => {
    const { id, name, orgId, deptId } = item

    return {
      uid: id,
      departmentId: deptId,
      orgId,
      name,
      orgName: '', // 选人组件没有带单位名称，默认填充为空
      departmentName: '', // 选人组件没有带部门名称，默认填充为空
    }
  })

  const entrustConfig = JSON.stringify({
    entrust: req.entrust,
    mandatoryName: mandatary.name,
    mandatoryUid: mandatary.id,
    mandatoryOrgId: mandatary.orgId,
    mandatoryDeptId: mandatary.deptId,
    mandatoryOrgName: mandatary.orgName,
    mandatoryDeptName: mandatary.deptName,
    mandataryDuty: req.mandataryDuty,
  })
  const config = JSON.stringify({
    problemSources: req.problemSources,
    situation: req.situation,
    other: req.other,
  })
  const appointmentTime = req.appointmentTime
    ? moment(req.appointmentTime, 'YYYY-MM-DD HH:mm').valueOf()
    : undefined
  return {
    ...omit(req, [
      'drafterDeptName',
      'entrust',
      'problemSources',
      'situation',
      'other',
      'applyDate',
      'mandatary',
      'mandataryDuty',
      'participants',
    ]),
    appointmentTime,
    drafterDeptId,
    drafterDeptName,
    drafterUid,
    appointmentUid: appointmentName.id,
    appointmentName: appointmentName.name,
    appointmentDeptId: appointmentName.deptId,
    appointmentDeptName: appointmentName.deptName,
    appointmentOrgId: appointmentName.orgId,
    appointmentOrgName: appointmentName.orgName,
    entrustConfig,
    config,
    drafterOrgId: req.drafterOrgId || info.loginOrgId,
    participants: JSON.stringify(participants), // 约谈参加人，可以多个，数组形式
  }
}

export const transformResponseData = (res, myInfo) => {
  const { linkPath = [] } = myInfo
  const drafterObj = linkPath.find(item => item.deptId === res.drafterDeptId) || {}
  const drafterDeptName = {
    value: res.drafterDeptId,
    label: res.drafterDeptName,
    deptName: drafterObj.rootDeptName,
  }
  const applyDate = moment(res.gmtCreate).format('YYYY年MM月DD日')
  const { entrust, mandatoryName, mandatoryUid, mandatoryOrgId, mandataryDuty } = safeParse(
    res.entrustConfig,
  )
  const mandatary = [{ name: mandatoryName, id: mandatoryUid, orgId: mandatoryOrgId }]
  const appointmentName = [
    { name: res.appointmentName, id: res.appointmentUid, orgId: res.appointmentOrgId },
  ]
  const { problemSources, situation, other } = safeParse(res.config)
  const appointmentTime = moment(res.appointmentTime).format('YYYY-MM-DD HH:mm')
  const parseParticipants = safeParse(res.participants, [])

  return {
    ...res,
    appointmentName,
    mandatary,
    drafterDeptName,
    applyDate,
    entrust,
    problemSources,
    situation,
    other,
    appointmentTime,
    mandataryDuty,
    participants: (parseParticipants || []).map(item => {
      return {
        name: item.name,
        id: item.uid,
        orgId: item.orgId,
        deptId: item.departmentId,
      }
    }),
  }
}
