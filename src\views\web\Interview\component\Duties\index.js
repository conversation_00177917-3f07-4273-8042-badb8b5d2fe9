import React, { useEffect } from 'react'

import { Select } from 'antd'
import _ from 'lodash'
import api from 'ROOT/service'
import { PreviewText } from '@formily/react-shared-components'

const getOptions = type => {
  if (type === 1 || type === 4) {
    return [
      {
        label: '省公司党委书记',
        value: '省公司党委书记',
      },
      {
        label: '省公司党委委员',
        value: '省公司党委委员',
      },
      {
        label: '省公司纪委书记',
        value: '省公司纪委书记',
      },
      {
        label: '地市公司党委书记',
        value: '地市公司党委书记',
      },
      {
        label: '地市公司党委委员',
        value: '地市公司党委委员',
      },
      {
        label: '地市公司纪委书记',
        value: '地市公司纪委书记',
      },
    ]
  }
  if (type === 2) {
    return [
      {
        label: '省公司党委委员',
        value: '省公司党委委员',
      },
      {
        label: '省公司纪委书记',
        value: '省公司纪委书记',
      },
      {
        label: '地市公司党委委员',
        value: '地市公司党委委员',
      },
      {
        label: '地市公司纪委书记',
        value: '地市公司纪委书记',
      },
      {
        label: '省公司二级经理正职',
        value: '省公司二级经理正职',
      },
      {
        label: '省公司二级经理副职',
        value: '省公司二级经理副职',
      },
      {
        label: '地市公司三级经理正职',
        value: '地市公司三级经理正职',
      },
      {
        label: '地市公司三级经理副职',
        value: '地市公司三级经理副职',
      },
    ]
  }
  if (type === 3) {
    return [
      {
        label: '省公司二级经理正职',
        value: '省公司二级经理正职',
      },
      {
        label: '省公司二级经理副职',
        value: '省公司二级经理副职',
      },
      {
        label: '地市公司党委书记',
        value: '地市公司党委书记',
      },
      {
        label: '地市公司党委委员',
        value: '地市公司党委委员',
      },
      {
        label: '地市公司三级经理正职',
        value: '地市公司三级经理正职',
      },
      {
        label: '地市公司三级经理副职',
        value: '地市公司三级经理副职',
      },
      {
        label: '县分公司三级经理',
        value: '县分公司三级经理',
      },
      {
        label: '县分公司三级经理副职',
        value: '县分公司三级经理副职',
      },
    ]
  }
  return []
}
function Duties(props) {
  const { schema, mutators, value, editable } = props
  // type 1 -> 主表单约谈主谈人职务 2 -> 主表单受委托人职务 3 -> 子表单约谈对象职务 4 -> 子表单受委托人职务
  const { userInfos, type, placeholder, disabled } = schema['x-component-props']

  const getLabelNameByUids = async () => {
    const data = await api.getLabelNameByUids({
      userInfos,
      type: 1,
    })
    const { lnName } = _.get(data, '[0]', {})
    mutators.change(lnName)
  }

  useEffect(() => {
    if (userInfos && userInfos.length > 0) {
      getLabelNameByUids()
    }
  }, [userInfos])

  if (!editable) {
    return <PreviewText value={value} />
  }

  return (
    <Select
      value={value}
      onChange={mutators.change}
      options={getOptions(type)}
      placeholder={placeholder}
      disabled={disabled}
    />
  )
}

export default Duties
