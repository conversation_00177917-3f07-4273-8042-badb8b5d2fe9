import React from 'react'
import { connect } from 'react-redux'
import { bindActionCreators } from 'redux'

import { addDept } from '../../../reducer/moa-tree'
import { filterHtmlTag } from '../../../utils'
import Icon from '../../icon'

const DeptList = ({ data = [], actions }) => {
  const handleAddDept = dept => {
    const { docId, name, oid } = dept

    actions.addDept({
      orgId: oid,
      name: filterHtmlTag(name),
      id: docId.split('_').reverse()[0],
      isDept: true,
    })
  }

  return (
    <div className="moa-tree-dept-list">
      <h3 className="data-type">部门</h3>
      <ul>
        {data.map(item => {
          const { orgId, orgName, list = [] } = item

          return (
            <li key={orgId} className="org-dept-list">
              <p className="org-info">{decodeURIComponent(orgName)}</p>
              <ul>
                {list.map(it => {
                  const { name, docId } = it

                  return (
                    <li
                      key={docId}
                      className="org-dept-item"
                      onClick={handleAddDept.bind(null, it)}
                    >
                      <Icon type="team" color="#4b95fa" />
                      <div className="dept-info">
                        <span
                          dangerouslySetInnerHTML={{ __html: name }}
                          className="deptname"
                          title={filterHtmlTag(name)}
                        />
                      </div>
                    </li>
                  )
                })}
              </ul>
            </li>
          )
        })}
      </ul>
    </div>
  )
}

export default connect(null, dispatch => ({
  dispatch,
  actions: bindActionCreators(
    {
      addDept,
    },
    dispatch,
  ),
}))(DeptList)
