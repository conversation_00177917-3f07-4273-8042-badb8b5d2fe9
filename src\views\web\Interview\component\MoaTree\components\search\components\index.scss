:global {
  .moa-tree-result-list {
    font-size: 14px;
    margin: 0 6px;

    p {
      line-height: 1.5;
      margin: 0;
    }

    .data-type {
      color: #666;
      margin: 15px 0;
      font-weight: 400;
      font-size: 14px;
    }

    .org-info {
      height: 20px;
      line-height: 20px;
      margin-top: 10px;
      color: #959ba3;
      margin-bottom: 6px;
    }

    .moa-tree-user-list {
      .org-user-item {
        display: flex;
        align-items: center;
        padding: 4px 8px;
        cursor: pointer;

        &:hover {
          background-color: rgba(79, 132, 210, 0.1);
        }

        svg {
          margin-top: 0;
        }

        .user-info {
          margin-left: 6px;
          flex: 1;
          display: flex;
          align-items: center;

          .username {
            max-width: 60px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;

            span {
              color: #f00;
            }
          }

          .deptpath {
            width: 140px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }
    }

    .moa-tree-dept-list {
      .org-dept-item {
        display: flex;
        align-items: center;
        padding: 4px 8px;
        cursor: pointer;

        &:hover {
          background-color: rgba(79, 132, 210, 0.1);
        }

        svg {
          margin-top: 0;
        }

        .dept-info {
          margin-left: 6px;
          flex: 1;
          display: flex;
          align-items: center;

          .deptname {
            max-width: 200px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;

            span {
              color: #f00;
            }
          }
        }
      }
    }
  }
}
