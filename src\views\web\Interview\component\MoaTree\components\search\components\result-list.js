import React from 'react'
import { connect } from 'react-redux'
import { Spin } from 'antd'

import UserList from './user-list'
import DeptList from './dept-list'

import './index.scss'

const ResultList = ({ searchResultList = {}, searchLoading = false }) => {
  const { contactList = [], departmentList = [] } = searchResultList

  if (searchLoading)
    return (
      <div style={{ textAlign: 'center', paddingTop: 80 }}>
        <Spin spinning />
      </div>
    )

  return (
    <div className="moa-tree-result-list">
      {contactList.length > 0 && <UserList data={contactList} />}
      {departmentList.length > 0 && <DeptList data={departmentList} />}
      {contactList.length === 0 && departmentList.length === 0 && (
        <div style={{ color: '#666', marginTop: 30, textAlign: 'center' }}>未搜索到数据</div>
      )}
    </div>
  )
}

export default connect($$state => {
  const { moaTree } = $$state
  return {
    searchLoading: moaTree.searchLoading,
    searchResultList: moaTree.searchResultList,
  }
})(ResultList)
