import React from 'react'
import { connect } from 'react-redux'
import { bindActionCreators } from 'redux'

import Icon from '../../icon'
import { addUser } from '../../../reducer/moa-tree'
import { filterHtmlTag } from '../../../utils'

const UserList = ({ data = [], actions }) => {
  const handleAddUser = user => {
    const { oid, uid, name, mobile, deptIds } = user

    actions.addUser({
      orgId: oid,
      id: uid,
      name: filterHtmlTag(name),
      mobile,
      isUser: true,
      deptId: deptIds.split('-').reverse()[0],
    })
  }
  return (
    <div className="moa-tree-user-list">
      <h3 className="data-type">人员</h3>
      <ul>
        {data.map(item => {
          const { orgId, orgName, list = [] } = item

          return (
            <li key={orgId} className="org-user-list">
              <p className="org-info">{decodeURIComponent(orgName)}</p>
              <ul>
                {list.map(it => {
                  const { name, deptPath, uid } = it
                  return (
                    <li className="org-user-item" key={uid} onClick={handleAddUser.bind(null, it)}>
                      <Icon type="user" color="#c1cedd" />
                      <div className="user-info">
                        <span
                          dangerouslySetInnerHTML={{ __html: name }}
                          className="username"
                          title={filterHtmlTag(name)}
                          style={{
                            maxWidth: deptPath ? 60 : 150,
                          }}
                        />
                        {deptPath && (
                          <span>
                            <span style={{ margin: '0 4px' }}>-</span>
                            <span className="deptpath" title={deptPath}>
                              {deptPath}
                            </span>
                          </span>
                        )}
                      </div>
                    </li>
                  )
                })}
              </ul>
            </li>
          )
        })}
      </ul>
    </div>
  )
}

export default connect(null, dispatch => ({
  dispatch,
  actions: bindActionCreators(
    {
      addUser,
    },
    dispatch,
  ),
}))(UserList)
