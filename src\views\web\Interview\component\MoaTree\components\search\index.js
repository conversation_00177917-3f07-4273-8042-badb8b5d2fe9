import React, { useCallback } from 'react'

import { Input } from 'antd'
import _ from 'lodash'
import Cookies from 'js-cookie'
import { connect } from 'react-redux'
import { bindActionCreators } from 'redux'

import { setSearchResultList, setSearchMode, setSearchLoading } from '../../reducer/moa-tree'
import api from '../../service'

import './index.scss'

const Search = ({ actions }) => {
  const searchAll = useCallback(
    _.debounce(keyword => {
      actions.setSearchLoading(true)
      actions.setSearchMode(true)

      api
        .searchAllExt({
          option: 3, // 查询【人员】+【部门】数据
          keyword,
          uid: Cookies.get('uid'),
          timeStamp: Cookies.get('timeStamp'),
          size: 999999,
        })
        .then(res => {
          actions.setSearchResultList(res)
          actions.setSearchLoading(false)
        })
      // .catch(() => {
      //   actions.setSearchLoading(false);
      // });
    }, 500),
    [],
  )

  const handleInputChange = e => {
    const { value } = e.target

    if (value.trim()) {
      searchAll(value.trim())
    } else {
      // 清空时，恢复选人模式
      actions.setSearchMode(false)
      actions.setSearchResultList({})
    }
  }

  return (
    <div className="moa-tree-search">
      <Input.Search placeholder="搜索" allowClear loading onChange={handleInputChange} />
    </div>
  )
}

export default connect(null, dispatch => ({
  actions: bindActionCreators(
    {
      setSearchResultList,
      setSearchMode,
      setSearchLoading,
    },
    dispatch,
  ),
}))(Search)
