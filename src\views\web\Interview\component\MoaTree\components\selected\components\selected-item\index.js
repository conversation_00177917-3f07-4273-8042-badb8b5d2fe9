import React from 'react'
import { connect } from 'react-redux'
import { bindActionCreators } from 'redux'

import { removeOrg, removeDept, removeUser } from '../../../../reducer/moa-tree'
import TreeIcon from '../../../icon'

import style from './index.scss'

const SelectedItem = ({ data = {}, actions }) => {
  const { removeOrg, removeDept, removeUser } = actions
  const { name, isOrg, isDept, isUser, id } = data

  const getIconType = () => {
    if (isOrg) return 'apartment'
    if (isDept) return 'team'

    return 'user'
  }

  const handleRemove = () => {
    if (isOrg) {
      removeOrg(id)
    }

    if (isDept) {
      removeDept(id)
    }

    if (isUser) {
      removeUser(id)
    }
  }

  return (
    <div className={style['selected-item']}>
      <div className={style['selected-item-basicinfo']}>
        <TreeIcon
          type={getIconType()}
          className={style['selected-item-tree-icon']}
          color={{ team: '#6bb7ed', user: '#c1cedd', apartment: '#4b95fa' }[getIconType()]}
        />
        {name}
      </div>
      <span onClick={handleRemove} className={style['selected-item-action']}>
        <TreeIcon type="close" />
      </span>
    </div>
  )
}

export default connect(null, dispatch => ({
  actions: bindActionCreators(
    {
      removeOrg,
      removeDept,
      removeUser,
    },
    dispatch,
  ),
}))(SelectedItem)
