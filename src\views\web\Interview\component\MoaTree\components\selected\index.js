import React from 'react'
import { connect } from 'react-redux'

import SelectedOrg from './components/selected-org'
import SelectedDept from './components/selected-dept'
import SelectedUser from './components/selected-user'

const Selected = ({ selectedOrgList, selectedDeptList, selectedUserList }) => {
  return (
    <div>
      <SelectedOrg orgs={selectedOrgList} />
      <SelectedDept depts={selectedDeptList} />
      <SelectedUser users={selectedUserList} />
    </div>
  )
}

export default connect($$state => {
  const { moaTree } = $$state
  const { selectedOrgList, selectedDeptList, selectedUserList } = moaTree

  return {
    selectedOrgList,
    selectedDeptList,
    selectedUserList,
  }
})(Selected)
