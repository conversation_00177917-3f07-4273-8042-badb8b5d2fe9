import React, { useState, useEffect } from 'react'
import { Tree, Spin } from 'antd'
import _ from 'lodash'
import { connect } from 'react-redux'
import { bindActionCreators } from 'redux'

import { addOrg, addDept, addUser } from '../../reducer/moa-tree'
import api from '../../service'
import { to } from '../../utils'
import TreeIcon from '../icon'

import './index.scss'

const { TreeNode } = Tree

const MoaTree = ({ basicConfig, actions }) => {
  const [allOrgs, setAllOrgs] = useState([])
  const { addOrg, addDept, addUser } = actions
  const { orgId, orgName, type } = basicConfig

  // 找到指定单位的根节点
  const findRootOrg = (orgs = []) => {
    for (const org of orgs) {
      const { id, subOrgs = [] } = org

      if (String(id) === String(orgId)) {
        return org
      }
      const result = findRootOrg(subOrgs)

      if (result) return result
    }
  }

  // 获取外部单位
  const findExternalOrg = (orgs = []) => {
    return orgs.find(org => org.code === '70000000')
  }

  const formatOrgs = (orgs = [], result = [], ...args) => {
    const [prevOrgFullName = '', pid = -1] = args

    while (orgs.length > 0) {
      const org = orgs.shift()
      const { code, id, name, sequence, subOrgs = [] } = org || {}
      const fullname = prevOrgFullName.length > 0 ? `${prevOrgFullName}-${name}` : name

      const data = {
        key: id,
        id,
        code,
        name,
        sequence,
        title: name,
        fullname,
        isOrg: true,
        pid,
        orgId: id,
        children: (subOrgs || []).length > 0 ? formatOrgs(subOrgs || [], [], fullname, id) : [],
      }

      result.push(data)
    }

    return result
  }

  // 获取所有的单位数据，包含外部单位
  useEffect(() => {
    if (orgId) {
      api.getOtherOrgInfo({ notFilterThirdOrgCodes: false }).then(res => {
        // 根据当前登录的用户，获取对应的根节点
        // 区公司用户，获取的就是区公司的单位
        // 市公司单位，获取的就是市公司的单位
        // 县公司单位，获取的就是县公司的单位
        const rootOrg = findRootOrg(res) || { id: orgId, name: orgName }
        const externalOrg = findExternalOrg(res)

        if (rootOrg) {
          setAllOrgs(formatOrgs([rootOrg, externalOrg].filter(Boolean)))
        }
      })
    }
  }, [orgId])

  // 获取部门和用户数据
  const getDeptsAndUsers = async (deptId = 0, orgId) => {
    const formatDepts = (depts = []) => {
      return depts.map(dept => {
        const { code, id, name, userCount, sequence, parentId, parentIds, lower } = dept

        return {
          key: id,
          id,
          name,
          code,
          title: name,
          isDept: true,
          userCount,
          sequence,
          parentId,
          parentIds,
          lower,
          orgId,
          isLeaf: userCount === 0, // 判断是否是叶子节点
        }
      })
    }

    const formatUsers = (users = []) => {
      return users.map(user => {
        const {
          code,
          name,
          title, // 职务
          mobile,
          sequence,
          uid,
          email,
          departmentId,
        } = user

        return {
          key: `${orgId}-${departmentId}-${uid}`,
          id: uid,
          code,
          title: name,
          name,
          mobile,
          email,
          orgId,
          deptId: departmentId || 0,
          sequence,
          post: title,
          isUser: true,
          isLeaf: true,
        }
      })
    }

    const [, depts] = await to(api.getOtherOrgDeptInfo({ parentId: deptId, orgId }))

    const [, users] = await to(
      api.getOtherOrgUserInfo({ deptId, orgId, pageIndex: 1, pageSize: 9999 }),
    )

    return {
      depts: formatDepts(depts || []),
      users: formatUsers(users.users || []),
    }
  }

  const handleLoadData = treeNode => {
    return new Promise(resolve => {
      const { dataRef } = treeNode.props
      const { isDept, id, orgId, children = [] } = dataRef || {}

      // 单位数据是一次性获取的
      if (treeNode.props.dataRef.fetched) {
        resolve()

        return
      }

      getDeptsAndUsers(isDept ? id : 0, orgId).then(res => {
        const { depts = [], users = [] } = res

        // 展示数据的顺序: 人员, 部门，单位
        treeNode.props.dataRef.children = [...users, ...depts, ...children]

        // 表示当前节点下的数据已经获取完成
        treeNode.props.dataRef.fetched = true

        // 重新渲染树组件
        setAllOrgs([...allOrgs])

        resolve()
      })
    })
  }

  const renderTreeNodes = data => {
    return data.map(item => {
      const { isOrg, isDept, children = [], title, key, isLeaf } = item

      const getCustomIcon = () => {
        if (isOrg) {
          return <TreeIcon type="apartment" color="#4b95fa" />
        }

        if (isDept) {
          return <TreeIcon type="team" color="#6bb7ed" />
        }

        return <TreeIcon type="user" color="#c1cedd" />
      }

      if (children.length > 0) {
        return (
          <TreeNode title={title} key={key} dataRef={item} icon={getCustomIcon()} isLeaf={isLeaf}>
            {renderTreeNodes(children)}
          </TreeNode>
        )
      }

      return (
        <TreeNode key={item.key} {...item} dataRef={item} icon={getCustomIcon()} isLeaf={isLeaf} />
      )
    })
  }

  const handleSelect = (selectedKeys, event) => {
    const dataRef = _.get(event, 'node.props.dataRef', {})

    const { isUser, isDept, isOrg } = dataRef

    // todo: 暂时只处理选择人员的情况，其它的场景来不及开发
    // if (isOrg) {
    //   addOrg(dataRef)
    // }

    // if (isDept) {
    //   addDept(dataRef)
    // }

    if (isUser) {
      addUser(dataRef)
    }
  }

  if (!orgId && allOrgs.length > 0) return <Spin />

  return (
    <div className="moa-tree">
      <Tree
        loadData={handleLoadData}
        showIcon
        switcherIcon={<TreeIcon type="up" />}
        onSelect={handleSelect}
        defaultExpandedKeys={[String(orgId)]}
      >
        {renderTreeNodes(allOrgs)}
      </Tree>
    </div>
  )
}

export default connect(
  $$state => {
    const { moaTree } = $$state
    return {
      basicConfig: moaTree.basicConfig,
    }
  },
  dispatch => ({
    dispatch,
    actions: bindActionCreators(
      {
        addOrg,
        addDept,
        addUser,
      },
      dispatch,
    ),
  }),
)(MoaTree)
