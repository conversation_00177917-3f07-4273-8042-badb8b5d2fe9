import React, { useEffect } from 'react'
import Cookies from 'js-cookie'
import { Modal, Row, Col } from 'antd'
import { connect } from 'react-redux'
import { bindActionCreators } from 'redux'

import { setBasicConfig, clear, setSearchMode } from './reducer/moa-tree'
import { parseJson, getBrandColor } from './utils'
import Tree from './components/tree'
import Selected from './components/selected'
import Search from './components/search'
import SearchResultList from './components/search/components/result-list'

import style from './index.scss'

// orgId,
// orgName,
// needSearchBar = false,
// visible = false,
// range = 1000, // 最大可选择数量
// title = "请选择人员",
// okText = "确定",
// cancelText = "取消",
// type = "multiMulti", // singleOrg: 单选单位，multiOrg: 多选单位，singleUser: 单选人员， multiUser：多选人员，singleDept：单选部门，multiDept：多选部门，multiMulti：多选人员和部门，multiOrgAndDept：多选-单位和部门
// onConfirm = noop,
// onCancel = noop,
// defaultOrgList = [], // 默认选择的单位列表
// defaultDeptList = [], // 默认选择的部门列表
// defaultUserList = [], // 默认选择的人员列表

const MoaTree = props => {
  const {
    actions,
    treeState,
    visible,
    title = '选择人员',
    orgId,
    orgName,
    onConfirm = () => {},
    onCancel = () => {},
    range = 1000,
    cancelText = '取消',
    okText = '确定',
    needSearchBar = false,
    type = 'multiMulti',
    defaultOrgList,
    defaultDeptList,
    defaultUserList,
  } = props

  // 取当前用户的企业ID和企业名称
  const { loginOrgId, loginOrgName } = parseJson(Cookies.get('myself'))
  const { selectedOrgList, selectedDeptList, selectedUserList, isSearchMode } = treeState

  // todo，此处的逻辑待优化
  useEffect(() => {
    actions.setBasicConfig({
      orgId: orgId || loginOrgId,
      orgName: orgName || loginOrgName,
      title,
      type,
      needSearchBar,
      cancelText,
      okText,
      range,
      defaultOrgList,
      defaultDeptList,
      defaultUserList,
    })
  }, [defaultOrgList, defaultDeptList, defaultUserList])

  const handleOk = () => {
    onConfirm({
      orgList: selectedOrgList,
      deptList: selectedDeptList,
      userList: selectedUserList,
    })

    actions.setSearchMode(false)
  }

  const handleCancel = () => {
    onCancel()

    actions.setSearchMode(false)
  }

  const handleEmpty = () => {
    actions.clear()
  }

  return (
    <Modal
      key={visible}
      visible={visible}
      title={title}
      onOk={handleOk}
      onCancel={handleCancel}
      width={600}
      cancelText={cancelText}
      okText={okText}
      centered
      className={style['moa-tree-modal']}
    >
      <Row className={style['moa-tree-modal-row']}>
        <Col span={12} className={style['moa-tree-modal-col']}>
          <div className={style['moa-tree-box']}>
            <div className={style['moa-tree-box-inner']}>
              {needSearchBar && <Search />}
              {isSearchMode ? <SearchResultList /> : <Tree />}
            </div>
          </div>
        </Col>
        <Col span={12}>
          <div className={style['moa-tree-selected-box']}>
            <div className={style['moa-tree-selected-box-inner']}>
              <h3 className={style['moa-tree-selected-box-title']}>
                已选：
                {selectedOrgList.length + selectedDeptList.length + selectedUserList.length}
                <span style={{ marginLeft: 2, marginRight: 2 }}>/</span>
                {range}
              </h3>
              <a style={{ color: getBrandColor() }} onClick={handleEmpty}>
                清空
              </a>
            </div>
            <div
              style={{
                height: 400,
                overflow: 'auto',
              }}
            >
              <Selected />
            </div>
          </div>
        </Col>
      </Row>
    </Modal>
  )
}

export default connect(
  $$state => {
    const { moaTree } = $$state
    return {
      treeState: moaTree,
    }
  },
  dispatch => ({
    actions: bindActionCreators(
      {
        clear,
        setBasicConfig,
        setSearchMode,
      },
      dispatch,
    ),
  }),
)(MoaTree)
