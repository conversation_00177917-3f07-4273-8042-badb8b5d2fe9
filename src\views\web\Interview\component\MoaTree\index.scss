.moa-tree-modal {
  margin: 0;
  padding: 0;
  max-width: 100%;

  .moa-tree-modal-row {
    height: 100%;
  }

  .moa-tree-modal-col {
    height: 100%;
  }

  .moa-tree-box {
    border-right: 1px solid #e9ecf0;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 8px;
  }

  .moa-tree-box-inner {
    overflow: auto;
    flex: 1;
  }

  .moa-tree-selected-box {
    padding: 8px;
  }

  .moa-tree-selected-box-inner {
    display: flex;
    font-size: 14px;
  }

  .moa-tree-selected-box-title {
    color: #5c626b;
    flex: 1;
    font-weight: 400;
    font-size: 14px;
    padding: 0;
    margin: 0;
  }

  :global {
    .ant-modal-body {
      padding: 0;
      height: 482px;
      overflow: hidden;
    }
  }
}
