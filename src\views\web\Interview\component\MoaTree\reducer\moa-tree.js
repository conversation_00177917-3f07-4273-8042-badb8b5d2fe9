import { createAction, handleActions } from 'redux-actions'

const initialState = {
  basicConfig: {
    range: 1000,
    needSearchBar: false,
    type: 'multiMulti',
    okText: '确定',
    cancelText: '取消',
  },
  selectedOrgList: [],
  selectedDeptList: [],
  selectedUserList: [],
  searchLoading: false, // 搜索 loading
  isSearchMode: false, // 是否处于搜索模式
  searchResultList: {}, // 搜索结果列表
}

const CLEAR = 'MOA-TREE/CLEAR'
const ADD_ORG = 'MOA-TREE/ADD_ORG'
const ADD_DEPT = 'MOA-TREE/ADD_DEPT'
const ADD_USER = 'MOA-TREE/ADD_USER'
const REMOVE_ORG = 'MOA-TREE/REMOVE_ORG'
const REMOVE_DEPT = 'MOA-TREE/REMOVE_DEPT'
const REMOVE_USER = 'MOA-TREE/REMOVE_USER'
const SET_BASIC_CONFIG = 'MOA-TREE/SET_BASIC_CONFIG'
const SET_SEARCH_LOADING = 'MOA-TREE/SET_SET_SEARCH_LOADING'
const SET_SEARCH_MODE = 'MOA-TREE/SET_SEARCH_MODE'
const SET_SEARCH_RESULT_LIST = 'MOA-TREE/SET_SEARCH_RESULT_LIST'
const DESTORY = 'MOA-TREE/DESTORY'

export const clear = createAction(CLEAR)
export const addOrg = createAction(ADD_ORG)
export const addDept = createAction(ADD_DEPT)
export const addUser = createAction(ADD_USER)
export const removeOrg = createAction(REMOVE_ORG)
export const removeDept = createAction(REMOVE_DEPT)
export const removeUser = createAction(REMOVE_USER)
export const setBasicConfig = createAction(SET_BASIC_CONFIG)
export const setSearchLoading = createAction(SET_SEARCH_LOADING)
export const setSearchMode = createAction(SET_SEARCH_MODE)
export const setSearchResultList = createAction(SET_SEARCH_RESULT_LIST)
export const destory = createAction(DESTORY)

export default handleActions(
  {
    [SET_BASIC_CONFIG]: (state, action) => {
      const config = action.payload

      const { defaultOrgList = [], defaultDeptList = [], defaultUserList = [], ...rest } = config

      return {
        ...state,
        selectedOrgList: defaultOrgList || [],
        selectedDeptList: defaultDeptList || [],
        selectedUserList: defaultUserList || [],
        basicConfig: {
          ...state.basicConfig,
          ...rest,
        },
      }
    },
    [CLEAR]: state => {
      return {
        ...state,
        selectedOrgList: [],
        selectedDeptList: [],
        selectedUserList: [],
      }
    },
    [DESTORY]: state => {
      return {
        ...state,
        selectedOrgList: [],
        selectedDeptList: [],
        selectedUserList: [],
        searchLoading: false,
        isSearchMode: false,
        searchResultList: {},
      }
    },
    [SET_SEARCH_MODE]: (state, action) => {
      return {
        ...state,
        isSearchMode: action.payload,
      }
    },
    [SET_SEARCH_LOADING]: (state, action) => {
      return {
        ...state,
        searchLoading: action.payload,
      }
    },
    [SET_SEARCH_RESULT_LIST]: (state, action) => {
      return {
        ...state,
        searchResultList: action.payload || {},
      }
    },
    [ADD_ORG]: (state, action) => {
      const org = action.payload
      const {
        basicConfig: { range },
        selectedOrgList,
        selectedDeptList,
        selectedUserList,
      } = state

      // 单选
      if (range === 1) {
        return {
          ...state,
          selectedOrgList: [org],
          selectedDeptList: [],
          selectedUserList: [],
        }
      }
      // 超出最大限制
      if (selectedOrgList.length + selectedDeptList.length + selectedUserList.length >= range) {
        return state
      }

      // 已添加，不重复添加
      if (selectedOrgList.find(item => String(item.id) === String(org.id))) {
        return state
      }

      return {
        ...state,
        selectedOrgList: [...selectedOrgList, org],
      }
    },
    [ADD_DEPT]: (state, action) => {
      const dept = action.payload

      const {
        basicConfig: { range },
        selectedOrgList,
        selectedDeptList,
        selectedUserList,
      } = state

      // 单选
      if (range === 1) {
        return {
          ...state,
          selectedOrgList: [],
          selectedDeptList: [dept],
          selectedUserList: [],
        }
      }
      // 超出最大限制
      if (selectedOrgList.length + selectedDeptList.length + selectedUserList.length >= range) {
        return state
      }

      // 已添加，不重复添加
      if (selectedDeptList.find(item => String(item.id) === String(dept.id))) {
        return state
      }

      return {
        ...state,
        selectedDeptList: [...selectedDeptList, dept],
      }
    },
    [ADD_USER]: (state, action) => {
      const user = action.payload

      const {
        basicConfig: { range },
        selectedOrgList,
        selectedDeptList,
        selectedUserList,
      } = state

      // 单选
      if (range === 1) {
        return {
          ...state,
          selectedOrgList: [],
          selectedDeptList: [],
          selectedUserList: [user],
        }
      }
      // 超出最大限制
      if (selectedOrgList.length + selectedDeptList.length + selectedUserList.length >= range) {
        return state
      }

      // 已添加，不重复添加
      if (selectedUserList.find(item => String(item.id) === String(user.id))) {
        return state
      }

      return {
        ...state,
        selectedUserList: [...selectedUserList, user],
      }
    },
    [REMOVE_ORG]: (state, action) => {
      const id = action.payload
      const { selectedOrgList = [] } = state

      const index = selectedOrgList.findIndex(org => org.id === id)

      if (index > -1) {
        selectedOrgList.splice(index, 1)
      }

      return {
        ...state,
        selectedOrgList: [...selectedOrgList],
      }
    },
    [REMOVE_DEPT]: (state, action) => {
      const id = action.payload
      const { selectedDeptList = [] } = state

      const index = selectedDeptList.findIndex(dept => dept.id === id)

      if (index > -1) {
        selectedDeptList.splice(index, 1)
      }

      return {
        ...state,
        selectedDeptList: [...selectedDeptList],
      }
    },
    [REMOVE_USER]: (state, action) => {
      const id = action.payload
      const { selectedUserList = [] } = state

      console.log(selectedUserList, 90000)

      const index = selectedUserList.findIndex(user => String(user.id) === String(id))

      if (index > -1) {
        selectedUserList.splice(index, 1)
      }

      return {
        ...state,
        selectedUserList: [...selectedUserList],
      }
    },
  },
  initialState,
)
