export default {
  GET_DEPTS: "/access/Contacts/getDepts", // 获取部门
  GET_USERS_SEGMENT: "/access/Contacts/getUsersSegment", // 获取部门下的成员
  GET_DEPART_USERS: "/access/Contacts/getDepartUsers", // 批量获取成员
  GET_FRIEND_DATA: "/access/FriendCenter/getFriendData", // 获取好友
  GET_JOINED_GROUPS: "/access/GroupChat/getJoinedGroups", // 获取群组
  GET_RECENT_CONTACTS: "/access/UserStorageCenter/getRecentContacts", // 获取最近接收人
  GET_CHAT_LIST: "/access/OfflineMsg/getChatList", // 获取会话列表
  SEARCH_ALL_ORGS: "/web-search/web/searchAllExt", // 搜索全企业
  SEARCH: "/web-search/web/search.json", // ?token=8d23348478a7ac285398d5d3fcd830ce&timeStamp=1575448516076&userId=101010014119993&orgId=86745&keyword=1&option=5&size=100
  GET_OTHER_ORG_INFO: "/baas-admin/web/org/tree", // 获取集团下其他企业信息
  GET_OTHER_ORG_DEPT_INFO: "/baas-admin/web/department/list", // 获取集团下其他企业部门信息
  GET_OTHER_ORG_USER_INFO: "/baas-admin/web/department/users", // 获取集团下其他企业人员信息
  GET_IS_OPEN_MUTI_ORG: "/web-search/web/isSearchAllExt", // 查询是否开启多企业选择
  GET_CONFIG: "/baas-common/web/baasConfig/get", // 获取开关配置
  MY_SELF: "/baas-account/web/myself", // 获取登录用户的基本信息
};
