import request from './request'
import api from './api'

export default {
  getDepts(params) {
    return request.post(api.GET_DEPTS, params).then(res => res.data)
  },
  getUsersSegment(params) {
    return request.post(api.GET_USERS_SEGMENT, params).then(res => res.data)
  },
  getDepartUsers(params) {
    return request.post(api.GET_DEPART_USERS, params)
  },
  getFriendData(params) {
    return request.post(api.GET_FRIEND_DATA, params)
  },
  getJoinedGroups(params) {
    return request.post(api.GET_JOINED_GROUPS, params)
  },
  getRecentContacts(params) {
    return request.post(api.GET_RECENT_CONTACTS, params)
  },
  getChatList(params) {
    return request.post(api.GET_CHAT_LIST, params)
  },
  searchAllExt(params) {
    return request.get(api.SEARCH_ALL_ORGS, params).then(res => res.data)
  },
  search(params) {
    return request.get(api.SEARCH, params)
  },
  getOtherOrgInfo(params) {
    return request.get(api.GET_OTHER_ORG_INFO, params).then(res => res.data)
  },
  getOtherOrgDeptInfo(params) {
    return request.get(api.GET_OTHER_ORG_DEPT_INFO, params).then(res => res.data)
  },
  getOtherOrgUserInfo(params) {
    return request.get(api.GET_OTHER_ORG_USER_INFO, params).then(res => res.data)
  },
  getIsOpenMutiOrg(params) {
    return request.get(api.GET_IS_OPEN_MUTI_ORG, params)
  },
  getConfig(params) {
    return request.post(api.GET_CONFIG, params).then(res => res.data)
  },
  getMyself(params) {
    return request.post(api.MY_SELF, params).then(res => res.data)
  },
}
