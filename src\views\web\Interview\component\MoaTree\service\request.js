import axios from 'axios'
import { message } from 'antd'

const LOGIN_URL = '/#login'
const pendingRequest = new Map()

/**
 * 跳转到登录页
 * 携带当前登陆信息，便于在登陆后能返回到当前页面
 */
const toLogin = () => {
  top.location.href = `${LOGIN_URL}?redirect=${location.href}`
}

/**
 * 请求失败后的错误统一处理
 * @param {Number} status 请求失败状态码
 * @param {*} other
 */
const errorHandle = (status, other) => {
  switch (status) {
    case 404:
      message.error('请求的资源不存在')
      break
    default:
      console.log(other)
  }
}

// 根据当前请求的信息，生成请求 Key
function generateReqKey(config) {
  const { method, url, params, data } = config

  // JSON.stringify(params),
  return [method, url, JSON.stringify(data)].join('&')
}

// 把当前请求信息添加到 pendingRequest 对象中
function addPendingRequest(config) {
  const requestKey = generateReqKey(config)
  config.cancelToken =
    config.cancelToken ||
    new axios.CancelToken(cancel => {
      if (!pendingRequest.has(requestKey)) {
        pendingRequest.set(requestKey, cancel)
      }
    })
}

// 检查是否存在重复请求，若存在则取消已发的请求
function removePendingRequest(config) {
  const requestKey = generateReqKey(config)

  if (pendingRequest.has(requestKey)) {
    const cancelToken = pendingRequest.get(requestKey)
    cancelToken(requestKey)
    pendingRequest.delete(requestKey)
  }
}

// 创建 axios 实例
const instance = axios.create({ timeout: 1000 * 30 })

// 设置post请求头
// instance.defaults.headers.post["Content-Type"] =
//   "application/x-www-form-urlencoded"

/**
 * 请求拦截器
 */
instance.interceptors.request.use(
  config => {
    // 比如这里可以设置请求头
    // config.headers.token = token
    const { needCancelToken } = config

    if (needCancelToken) {
      removePendingRequest(config)
      addPendingRequest(config)
    }

    return config
  },
  error => {
    pendingRequest.clear()

    return Promise.error(error)
  },
)

instance.interceptors.response.use(
  res => {
    const { data } = res

    const { needCancelToken } = res.config

    if (needCancelToken) {
      removePendingRequest(res.config)
    }

    // 请求成功
    if (data.success === true || data.retcode === 0) {
      const { formatter } = res.config

      // 存在格式化函数，执行格式化函数
      if (formatter && typeof formatter === 'function') {
        return formatter(data)
      }

      return data
    }
    // 未登陆判断
    if (data.retcode === 100 || data.code === -702) {
      toLogin()
    } else {
      // 获取 error 信息
      const { msg, errMsg } = data

      // 配置隐藏错误信息的开关
      const { hideMessage } = res.config

      // 配置了不展示错误信息或者没有错误信息
      if (hideMessage || !(msg || errMsg)) {
        return
      }

      message.error(msg || errMsg)

      Promise.reject(data)
    }
  },
  error => {
    const { response } = error

    removePendingRequest(error.config || {})

    if (axios.isCancel(error)) {
      const { message } = error
      console.log(message)

      return
    }

    if (response) {
      // 请求已发送，但是状态码不是 2xx
      errorHandle(response.status, '网络错误')

      return Promise.reject(response)
    }
    // 处理断网的情况
    if (!window.navigator.onLine) {
      message.error('网络已断开连接')
    } else {
      return Promise.reject(error)
    }
  },
)

export default {
  get: (url, params, extraParams = { needCancelToken: true }) => {
    return instance.get(url, {
      // 加上时间戳参数，避免 get 请求被浏览器缓存
      params: { ...params, t: Date.now() },
      ...extraParams,
    })
  },
  post: (url, params, extraParams = { needCancelToken: true }) => {
    return instance.post(url, params, extraParams)
  },
  instance,
}
