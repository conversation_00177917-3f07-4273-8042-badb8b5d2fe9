export const to = promise => {
  return promise
    .then(data => {
      return [null, data]
    })
    .catch(err => [err, null])
}

export const noop = () => {}

export const parseJson = json => {
  try {
    return JSON.parse(json) || {}
  } catch (err) {
    return {}
  }
}

export const getBrandColor = () => {
  return parseJson(localStorage.getItem('color')).brand || '#1890ff'
}

export const getQuery = () => {
  const url = decodeURI(location.search) // 获取url中"?"符后的字串(包括问号)
  const query = {}
  if (url.indexOf('?') !== -1) {
    const str = url.substr(1)
    const pairs = str.split('&')
    for (let i = 0; i < pairs.length; i++) {
      const pair = pairs[i].split('=')
      query[pair[0]] = decodeURIComponent(pair[1])
    }
  }

  return query // 返回对象
}

export const filterHtmlTag = str => {
  const htmlreg = /<.*?>/gi
  return str.replace(htmlreg, '')
}
