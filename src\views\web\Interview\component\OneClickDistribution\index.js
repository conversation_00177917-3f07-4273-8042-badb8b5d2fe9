import React, { useEffect, useState } from 'react'
import { Modal, message } from 'antd'
import api from 'ROOT/service'
import useMyInfo from 'ROOT/hooks/useMyInfo'
import { patchData, closeTheWindow } from 'ROOT/utils'
import { withRouter } from 'react-router-dom'
import style from './index.module.scss'

function OneClickDistribution({
  visible,
  onCancel,
  procFormDataKey,
  userTaskId,
  actions,
  initValue,
  location,
}) {
  const [confirmLoading, setConfirmLoading] = useState(false)
  const myInfo = useMyInfo({ isSetStorage: true })
  const [
    {
      appointmentTargets = [],
      appointmentParticipants = [],
      mandatoryUsers = [],
      managements = [],
      sponsoredUnits = [],
    },
    setData,
  ] = useState({})
  const comment = JSON.stringify({ text: '同意。', attachments: [] })
  const operator = {
    id: myInfo.loginUid,
    name: myInfo.loginName,
    orgId: myInfo.loginOrgId,
    orgName: myInfo.loginOrgName,
  }
  const getManualCompleteInfos = (allNode = []) => {
    const target1 = allNode.find(v => v.actinstName === '约谈对象收办') || {}
    const target2 = allNode.find(v => v.actinstName === '约谈参加人收办') || {}
    const target3 = allNode.find(v => v.actinstName === '约谈受委托人收办') || {}
    const target4 = allNode.find(v => v.actinstName === '归口管理部门收办') || {}
    const target5 = allNode.find(v => v.actinstName === '约谈发起单位收办') || {}
    return [
      {
        actinstId: target1.id,
        assigneeInfo: {
          assignees: appointmentTargets.map(item => {
            return {
              deptId: item.departmentId,
              id: item.uid,
              name: item.name,
              orgId: item.orgId,
            }
          }),
        },
      },
      {
        actinstId: target2.id,
        assigneeInfo: {
          assignees: appointmentParticipants.map(item => {
            return {
              deptId: item.departmentId,
              id: item.uid,
              name: item.name,
              orgId: item.orgId,
            }
          }),
        },
      },
      {
        actinstId: target3.id,
        assigneeInfo: {
          assignees: mandatoryUsers.map(item => {
            return {
              deptId: item.departmentId,
              id: item.uid,
              name: item.name,
              orgId: item.orgId,
            }
          }),
        },
      },
      {
        actinstId: target4.id,
        assigneeInfo: {
          assignees: managements.map(item => {
            return {
              deptId: item.departmentId,
              id: item.uid,
              name: item.name,
              orgId: item.orgId,
            }
          }),
        },
      },
      {
        actinstId: target5.id,
        assigneeInfo: {
          assignees: sponsoredUnits.map(item => {
            return {
              deptId: item.departmentId,
              id: item.uid,
              name: item.name,
              orgId: item.orgId,
            }
          }),
        },
      },
    ].filter(item => item.assigneeInfo.assignees.length > 0)
  }
  const onOk = async () => {
    setConfirmLoading(true)
    try {
      const { values } = await actions.submit()
      const data = patchData(values, initValue)
      // 当前节点下的所有分发节点
      const nodes = await api.getStartNode({
        taskId: userTaskId,
        operator,
        varJson: JSON.stringify(data),
      })
      await api.completeManualTask({
        taskId: userTaskId,
        // informerInfo: {
        //   assignees: [],
        // },
        manualCompleteInfos: getManualCompleteInfos(nodes.data), // todo
        varJson: JSON.stringify(data),
        userTaskLevel: data._taskLevel,
        comment,
      })
      setConfirmLoading(false)
      message.success('一键分发成功', () => {
        onCancel()
        closeTheWindow(location.search)
      })
    } catch (error) {
      setConfirmLoading(false)
    }
  }

  const getDetail = async procFormDataKey => {
    const res = await api.getAppointmentMemberInfo({ id: procFormDataKey })
    if (res && res.data) {
      setData(res.data)
    }
  }

  useEffect(() => {
    if (procFormDataKey && visible) {
      getDetail(procFormDataKey)
    }
  }, [procFormDataKey, visible])

  return (
    <Modal
      width={800}
      visible={visible}
      title="一键分发"
      onOk={onOk}
      onCancel={onCancel}
      confirmLoading={confirmLoading}
    >
      <div className={style.title}>流程分布信息</div>
      <table className={style.table}>
        <tbody>
          <tr>
            <td>约谈对象收办</td>
            <td>
              {appointmentTargets.map((v, idx) => {
                return (
                  <div key={idx}>
                    {v.orgName}\{v.departmentName}({v.name})
                  </div>
                )
              })}
            </td>
          </tr>
          <tr>
            <td>约谈参加人收办</td>
            <td>
              {appointmentParticipants.map((v, idx) => {
                return (
                  <div key={idx}>
                    {v.orgName}\{v.departmentName}({v.name})
                  </div>
                )
              })}
            </td>
          </tr>
          <tr>
            <td>约谈受委托人收办</td>
            <td>
              {mandatoryUsers.map((v, idx) => {
                return (
                  <div key={idx}>
                    {v.orgName}\{v.departmentName}({v.name})
                  </div>
                )
              })}
            </td>
          </tr>
          <tr>
            <td>归口管理部门收办</td>
            <td>
              {managements.map((v, idx) => {
                return (
                  <div key={idx}>
                    {v.orgName}\{v.departmentName}({v.name})
                  </div>
                )
              })}
            </td>
          </tr>
          <tr>
            <td>约谈发起单位收办</td>
            <td>
              {sponsoredUnits.map((v, idx) => {
                return (
                  <div key={idx}>
                    {v.orgName}\{v.departmentName}({v.name})
                  </div>
                )
              })}
            </td>
          </tr>
        </tbody>
      </table>
    </Modal>
  )
}

export default withRouter(OneClickDistribution)
