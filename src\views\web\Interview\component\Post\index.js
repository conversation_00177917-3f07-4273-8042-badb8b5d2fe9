import React, { useEffect } from 'react'

import { Input } from 'antd'
import { PreviewText } from '@formily/react-shared-components'

import api from 'ROOT/service'

const Post = props => {
  const { schema, mutators, value, editable } = props

  const { user, placeholder = '请输入', disabled = true } = schema['x-component-props']

  const getUserPost = async () => {
    const { orgId, uid } = user || {}

    const result = await api.getOneUser({ selectedOrgId: orgId, selectedUid: uid, rootId: 0 })
    const { deptUserList = [] } = result || []

    const title = (deptUserList.find(item => item.sort2 === 1) || {}).title || ''

    mutators.change(title || '无')
  }

  useEffect(() => {
    if (user) {
      getUserPost()
    }
  }, [user])

  if (!editable) {
    return <PreviewText value={value} />
  }

  return <Input value={value} disabled placeholder={placeholder} />
}

export default Post
