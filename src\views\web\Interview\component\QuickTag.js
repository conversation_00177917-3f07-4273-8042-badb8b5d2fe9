import React from 'react'

import { Space, Button } from 'antd'

import api from 'ROOT/service'
import { xhrWrapper } from 'ROOT/utils'
import useOrgType from 'ROOT/hooks/useOrgType'
import useMyInfo from 'ROOT/hooks/useMyInfo'

const QuickTag = ({ onChange = () => {}, setLoading, drafterOrgId }) => {
  // 获取起草人所属的单位类型
  const orgType = useOrgType(drafterOrgId)
  const myInfo = useMyInfo({ isSetStorage: true })

  // orgType：单位类型，1 => 区公司 2 => 市公司 3 => 县公司 4 => 其他
  const orgTypeLnCodesMap = {
    1: ['zj_2_2_1'], // 区公司各部门负责人
    2: ['zj_1_2_1'], // 各市公司负责人
  }

  // 根据区标签获取人员
  const getUsersByAreaTag = async type => {
    const [, data] = await xhrWrapper(
      api.getUsersWithLnGroupsByLnCodes({ type: 1, lnCodes: orgTypeLnCodesMap[type] }),
    )

    return data || []
  }

  // 根据地市公司获取人员
  const getUsersByCityTag = async () => {
    const [, data] = await xhrWrapper(
      api.getOrgAllUserByLnCodes({ queryType: 1, lnCodes: ['zw_3_1'], orgId: myInfo.loginOrgId }),
    )

    return (data || []).map(user => {
      const { userId, userName, orgId, orgName, deptId, deptName } = user

      return {
        uid: userId,
        userName,
        orgId,
        orgName,
        deptId,
        deptName,
      }
    })
  }

  const quickFetchUsers = async type => {
    setLoading(true)

    let data = []
    // 公司各部门负责人（本市公司下、相关县所有的三级正
    if (type === 3) {
      data = await getUsersByCityTag()
    } else {
      data = await getUsersByAreaTag(type)
    }

    const userInfos = (data || []).map(user => {
      const { uid, orgId } = user

      return {
        uid,
        orgId,
      }
    })

    // 未查询到人员时，不去调用职务接口
    if (userInfos.length === 0) {
      setLoading(false)

      return
    }

    // // 根据用户获取标签名
    // const [, labelNameByUids] = await xhrWrapper(api.getLabelNameByUids({ userInfos }))

    // // 转换成 map 结构，方便使用
    // const labelNameMap = (labelNameByUids || []).reduce((acc, cur) => {
    //   acc[cur.userId] = cur.lnName
    //   return acc
    // }, {})

    // 批量获取用户的职位信息
    const [, result] = await xhrWrapper(
      api.getMultiUser({
        orgUserExtParams: userInfos.map(user => ({ orgId: user.orgId, userId: user.uid })),
      }),
    )

    const deptUserList = (result || []).map(item => {
      const { uid, deptUserList } = item

      // 带上 uid 信息
      return deptUserList.map(item => ({ ...item, userId: uid }))
    })

    const usersPost = []

    deptUserList.forEach(item => {
      usersPost.push(item[0] || {})
      // usersPost.push(item.find(data => data.sort2 === 1) || {})
    })

    // 转换成 map 结构，方便使用
    const usersPostMap = usersPost.reduce((acc, cur) => {
      acc[`${cur.orgId}-${cur.userId}`] = cur.title
      return acc
    }, {})

    setLoading(false)

    // 格式化成子表单需要的数据结构
    const subFormItemData = (data || []).map(item => {
      const { uid, userName, orgId, orgName, deptId, deptName } = item

      return {
        uid: String(uid),
        name: userName,
        orgId,
        deptId,
        orgName: [orgName, deptName].join('\\'), // 后端现在返回不了全路径的信息， todo
        deptName,
        duty: usersPostMap[`${orgId}-${uid}`] || '无', // labelNameMap[uid],
        participants: JSON.stringify([]), // 约谈参加人
        appointmentTime: undefined, // 约谈时间
        entrustConfig: JSON.stringify({ entrust: false }), //  委托相关信息
        config: JSON.stringify({}), // 附件相关信息
      }
    })

    onChange(subFormItemData)
  }

  return (
    <div style={{ marginBottom: 10 }}>
      {/* 区公司 */}
      {orgType === 1 && (
        <Space>
          <Button
            shape="round"
            onClick={() => {
              quickFetchUsers(1)
            }}
          >
            区公司各部门负责人
          </Button>
          <Button
            shape="round"
            onClick={() => {
              quickFetchUsers(2)
            }}
          >
            各市公司负责人
          </Button>
        </Space>
      )}
      {/* 地市公司 */}
      {orgType === 2 && (
        <Button
          shape="round"
          onClick={() => {
            quickFetchUsers(3)
          }}
        >
          公司各部门负责人
        </Button>
      )}
    </div>
  )
}

export default QuickTag
