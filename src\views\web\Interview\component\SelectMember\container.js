import React, { useEffect, useContext } from 'react'

import _ from 'lodash'
import { Row, Col } from 'antd'
import { css } from 'emotion'

import { parseJson, xhrWrapper, noop, getBrandColor } from './utils'
import Index from './store/index/index'
import { ADD_TYPE } from './constants'
import Selectd from './selected'
import CustomTree from './tree'

const Container = props => {
  const {
    type,
    range,
    lnCodes = [],
    defaultOrgList = [],
    defaultDeptList = [],
    defaultUserList = [],
    disabledData = {},
    orgId,
  } = props

  const { state: indexState, dispatch: indexDispatch } = useContext(Index.Context)

  useEffect(() => {
    setAddType()
    setUserConfig()
  }, [])

  useEffect(() => {
    const users = defaultUserList.map(user => {
      const { id, orgId } = user

      return {
        ...user,
        key: `${orgId}-${id}`,
        isUser: true,
      }
    })

    const depts = defaultDeptList.map(dept => {
      const { id, orgId } = dept

      return {
        ...dept,
        key: `${orgId}-${id}`,
        isDept: true,
      }
    })

    const orgs = defaultOrgList.map(org => {
      return {
        ...org,
        isOrg: true,
      }
    })

    indexDispatch({
      type: 'INIT_DEFAULT',
      payload: {
        users,
        depts,
        orgs,
      },
    })
  }, [])

  const { selectedData } = indexState

  const handleEmpty = () => {
    indexDispatch({ type: 'EMPTY_SELECTED' })
  }

  const setUserConfig = () => {
    indexDispatch({
      type: 'SET_USER_CONFIG',
      payload: {
        type,
        range,
      },
    })
  }

  const setAddType = () => {
    let addType = ADD_TYPE.APPEND

    if (['singleUser', 'singleDept', 'singleOrg'].includes(type)) {
      addType = ADD_TYPE.REPLACE
    }

    indexDispatch({ type: 'SET_ADD_TYPE', payload: addType })
  }

  return (
    <Row
      className={css`
        height: 100%;
      `}
    >
      <Col
        span={12}
        className={css`
          height: 100%;
        `}
      >
        <div
          className={css`
            border-right: 1px solid #e9ecf0;
            padding: 16px;
            height: 100%;
            display: flex;
            flex-direction: column;
            overflow: auto;
          `}
        >
          <div
            className={css`
              flex: 1;
              display: flex;
              flex-direction: column;
            `}
          >
            <CustomTree lnCodes={lnCodes} disabledData={disabledData} orgId={orgId} />
          </div>
        </div>
      </Col>
      <Col span={12}>
        <div
          className={css`
            padding: 16px;
          `}
        >
          <div
            className={css`
              display: flex;
              font-size: 14px;
              margin-bottom: 10px;
            `}
          >
            <h3
              className={css`
                color: #5c626b;
                flex: 1;
                font-weight: 400;
                font-size: 14px;
                padding: 0;
                margin: 0;
              `}
            >
              已选：(
              {_.get(selectedData, 'users', []).length +
                _.get(selectedData, 'orgs', []).length +
                _.get(selectedData, 'depts', []).length}
              <span
                className={css`
                  margin: 0 2px;
                `}
              >
                /
              </span>
              {range})
            </h3>
            <a
              className={css`
                color: ${getBrandColor()};
              `}
              onClick={handleEmpty}
            >
              清空
            </a>
          </div>
          <div
            className={css`
              height: 400px;
              overflow: auto;
            `}
          >
            <Selectd />
          </div>
        </div>
      </Col>
    </Row>
  )
}

export default Container
