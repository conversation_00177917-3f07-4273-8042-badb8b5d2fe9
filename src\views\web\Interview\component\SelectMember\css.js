import { css } from 'emotion'

export const treeStyle = css`
  font-size: 14px;
  > li:first-child {
    padding-top: 2px;
  }

  li {
    padding: 2px;
  }

  .ant-tree-child-tree > li:first-child {
    padding-top: 2px;
  }

  .ant-tree-node-content-wrapper.ant-tree-node-content-wrapper.ant-tree-node-content-wrapper {
    &:hover {
      background-color: #edf2fd;
    }
  }

  .ant-tree-node-selected.ant-tree-node-selected.ant-tree-node-selected {
    background-color: #fff;
  }

  .ant-tree-switcher.ant-tree-switcher.ant-tree-switcher.ant-tree-switcher.ant-tree-switcher_open
    .ant-tree-switcher-icon {
    transform: scale(0.83333333) rotate(90deg);
  }

  .ant-tree-node-content-wrapper.ant-tree-node-content-wrapper.ant-tree-node-content-wrapper {
    padding-left: 0;
    white-space: nowrap;
  }
`

export const spinStyle = css`
  flex: 1;
`