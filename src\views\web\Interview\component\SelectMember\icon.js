import React from 'react'
import { css } from 'emotion'

const icons = {
  'usergroup-add':
    'M382.848 697.408C408.96 863.36 467.136 960 512 960c44.928 0 103.04-96.576 129.152-262.592H382.848zM718.784 512c0 38.72-1.92 77.632-5.44 115.968h231.04A448.448 448.448 0 0 0 960 512c0-40.192-5.76-78.976-15.68-115.968h-231.04c3.584 38.336 5.504 77.376 5.504 115.968z m-64 0c0-41.216-1.984-79.744-5.312-115.968H374.528a1264.384 1264.384 0 0 0 0 231.936h274.944c3.328-36.224 5.312-74.752 5.312-115.968z m50.56-185.408h214.4a448.896 448.896 0 0 0-295.488-248.96c38.528 60.16 66.048 149.248 81.024 248.96zM318.72 697.408H104.32a448.512 448.512 0 0 0 295.424 249.024c-38.592-60.224-66.176-149.248-81.024-249.024z m386.56 0c-14.976 99.712-42.496 188.8-81.024 249.024a448.448 448.448 0 0 0 295.488-249.024h-214.4z m-625.6-301.44A448.448 448.448 0 0 0 64 512c0 40.192 5.76 78.976 15.68 116.032h231.04C307.2 589.632 305.28 550.592 305.28 512c0-38.656 1.92-77.632 5.376-115.968h-231.04zM318.72 326.656c14.976-99.648 42.432-188.8 81.024-248.96a448.64 448.64 0 0 0-295.488 248.96h214.4z m322.368 0C615.04 160.64 556.8 64 512 64 467.2 64 408.96 160.576 382.848 326.592h258.24z',
  apartment:
    'M286.86300001 128a80 80 0 0 1 56.568 23.431l95.196 65.196A32 32 0 0 0 461.25500001 226H816.00000001c44.183 0 80 35.817 80 80v510c0 44.183-35.817 80-80 80H80.00000001c-44.183 0-80-35.817-80-80V208c0-44.183 35.817-80 80-80h206.863z m195.325 208h-67.274c-24.77 0-44.85 20.061-44.85 44.808v67.211c0 24.747 20.08 44.808 44.85 44.808h13.454v54.072H294.38100001l-0.705 0.006c-23.206 0.376-41.902 19.287-41.902 42.561v74.75h-3.925c-24.77 0-44.849 20.061-44.849 44.808v48.168C203.00000001 781.94 223.08000001 802 247.85000001 802h48.213c24.77 0 44.849-20.061 44.849-44.808v-48.168c0-24.747-20.08-44.808-44.85-44.808h-3.924v-74.75l0.003-0.111a2.242 2.242 0 0 1 2.24-2.129h133.987v76.99h-4.475c-24.77 0-44.849 20.061-44.849 44.808v48.168c0 24.747 20.08 44.808 44.85 44.808h48.213c24.77 0 44.849-20.061 44.849-44.808v-48.168c0-24.747-20.08-44.808-44.85-44.808h-3.373v-76.99h132.886l0.112 0.003a2.241 2.241 0 0 1 2.13 2.237v74.75h-3.924c-24.77 0-44.849 20.061-44.849 44.808v48.168c0 24.747 20.08 44.808 44.85 44.808h48.213C672.92000001 802 693.00000001 781.939 693.00000001 757.192v-48.168c0-24.747-20.08-44.808-44.85-44.808h-3.924v-74.75l-0.005-0.704c-0.377-23.184-19.306-41.863-42.602-41.863H468.73300001v-54.072h13.455c24.77 0 44.85-20.061 44.85-44.808v-67.211c0-24.747-20.08-44.808-44.85-44.808z',
  team: 'M10.66666667 229.57812499V109.57812499h420l60 120h480v660.00000001H10.66666667V229.57812499z',
  user: 'M942.12063478 855.771429c-7.31428601-36.571429-58.51428601-65.828571-160.914286-131.65714299-21.942857-7.31428601-43.88571399-21.942857-65.828571-36.57142901-29.257143-14.628571-95.08571399-43.88571399-87.771429-102.4 0-14.628571 7.31428601-29.257143 14.628572-43.885714 21.942857-29.257143 29.257143-51.2 43.885714-73.14285699 21.942857-43.88571399 43.88571399-80.457143 43.885714-160.91428601C730.00634878 190.171429 649.54920678 65.828571 525.20634878 65.828571H510.57777778C386.23492078 65.828571 305.77777778 190.171429 305.77777778 307.2c0 58.51428601 7.31428601 95.08571399 21.942857 124.342857 7.31428601 14.628571 14.628571 36.571429 29.257143 51.2 0 0 0 7.31428601 7.314286 7.314286v7.314286c7.31428601 14.628571 14.628571 21.942857 21.942857 36.571428 14.628571 21.942857 21.942857 36.571429 21.942857 51.2 0 51.2-58.51428601 80.457143-87.771429 95.085714-29.257143 14.628571-43.88571399 29.257143-65.828571 36.571429-102.4 58.51428601-153.6 95.08571399-160.91428601 131.657143v73.142857c0 21.942857 14.628571 36.571429 36.57142901 36.571429h775.314286c21.942857 0 36.571429-14.628571 36.571428-36.571429v-65.828571c0 7.31428601 0 0 0 0z',
  down: 'M512 743.74285888L141.21142578 280.25714112h741.57714844z',
  up: 'M743.74285888 512L280.25714112 882.78857422V141.21142578z',
}

function Icon({ className = '', type, size = 16, color = '#949AA2' }) {
  const detailIcon = css`
    line-height: 1;
    vertical-align: middle;
    margin-top: -3px;
  `

  return (
    <svg
      className={`${detailIcon} ${className}`}
      viewBox="0 0 1024 1024"
      width={size}
      height={size}
      fill={color}
    >
      <path d={icons[type]} />
    </svg>
  )
}

export default Icon
