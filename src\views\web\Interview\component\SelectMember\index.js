import React from 'react'

import { injectGlobal } from 'emotion'
import { enableES5 } from 'immer'
import { PreviewText } from '@formily/react-shared-components'

import Layout from './layout'
import TreeWrapper from './treeWrapper'

// 启用 immer 的 ie11 支持
enableES5()

// 自定义滚动条样式
injectGlobal`
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    border-left: none;
  }

  ::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background: rgba(149, 155, 163, 0.2);

    &:hover {
      background: rgba(149, 155, 163, 0.4);
    }
  }

  ::-webkit-scrollbar-button {
    height: 0;
    width: 0;
  }

  ::-webkit-scrollbar-track {
    background-color: transparent;
  }
`

const SelectMember = cProps => {
  const { value = [], mutators, props, editable } = cProps
  const xProps = props['x-component-props']
  if (!editable) {
    return <PreviewText value={value.map(x => x.name).join('、')} />
  }
  // 先仅考虑支持【人员】的场景
  return (
    <div style={{ width: '100%' }}>
      <Layout>
        <TreeWrapper {...xProps} onChange={mutators.change} defaultUserList={value} />
      </Layout>
    </div>
  )
}

export default SelectMember
