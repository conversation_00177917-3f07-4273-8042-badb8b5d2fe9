import React, { useContext } from 'react'

import Index from './store/index/index'
import SelectedOrg from './selectedOrg'
import SelectedDept from './selectedDept'
import SelectedUser from './selectedUser'

const Selected = () => {
  const { state: indexState } = useContext(Index.Context)

  const { selectedData = {} } = indexState
  const { depts = [], users = [], orgs = [] } = selectedData

  return (
    <div>
      <SelectedOrg orgs={orgs} />
      <SelectedDept depts={depts} />
      <SelectedUser users={users} />
    </div>
  )
}

export default Selected
