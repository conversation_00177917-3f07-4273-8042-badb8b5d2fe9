import React, { useContext } from 'react'
import { css } from 'emotion'
import { CloseOutlined } from '@ant-design/icons'
import TreeIcon from './icon'

import Index from './store/index/index'

const SelectItem = ({ data }) => {
  const { dispatch: indexDispatch } = useContext(Index.Context)

  const { name, isOrg, isDept } = data

  const handleClose = () => {
    indexDispatch({ type: 'REMOVE_ITEM', payload: data })
  }

  const getIconType = () => {
    if (isOrg) return 'apartment'
    if (isDept) return 'team'

    return 'user'
  }

  return (
    <div
      className={css`
        margin: 10px 0;
        display: flex;
        align-items: center;
      `}
    >
      <div
        className={css`
          flex: 1;
        `}
      >
        <TreeIcon
          type={getIconType()}
          className={css`
            margin-right: 5px;
          `}
          color={{ team: '#6bb7ed', user: '#c1cedd', apartment: '#4b95fa' }[getIconType()]}
        />
        {name}
      </div>
      <CloseOutlined
        className={css`
          flex-shrink: 0;
          cursor: pointer;
        `}
        onClick={handleClose}
      />
    </div>
  )
}

export default SelectItem
