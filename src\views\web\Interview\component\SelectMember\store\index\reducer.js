import produce from 'immer'
import _ from 'lodash'

export const initState = { isOpenMutiOrg: false, deptsLoading: true }

const isExceedMaxRange = (selectedData = {}, range) => {
  if (
    _.get(selectedData, 'users', []).length +
      _.get(selectedData, 'depts', []).length +
      _.get(selectedData, 'orgs', []).length ===
    range
  ) {
    return true
  }

  return false
}

const reducer = (state, action) => {
  const { type, payload } = action

  switch (type) {
    case 'SET_USER_CONFIG': {
      return {
        ...state,
        userConfig: payload,
      }
    }
    case 'ADD_DEPTS':
      return { ...state, depts: payload || [], deptsLoading: false }
    case 'SET_SEARCH_RESULT': {
      return {
        ...state,
        searchResult: payload,
      }
    }
    case 'SEARCH_STATUS': {
      return {
        ...state,
        searchStatus: payload, // true: 搜索 false: 未搜索
      }
    }
    case 'SET_ADD_TYPE': {
      return {
        ...state,
        addType: payload,
      }
    }
    case 'REMOVE_ITEM': {
      const { key, isDept, isUser, isOrg } = payload

      const nextState = produce(state, draft => {
        const { selectedData = {} } = draft
        const { depts = [], users = [], orgs = [] } = selectedData

        if (isUser) {
          const index = users.findIndex(user => user.key === key)

          users.splice(index, 1)
        }

        if (isDept) {
          const index = depts.findIndex(dept => dept.key === key)

          depts.splice(index, 1)
        }

        if (isOrg) {
          const index = orgs.findIndex(org => org.key === key)

          orgs.splice(index, 1)
        }

        draft.selectedData = {
          depts,
          users,
          orgs,
        }
      })

      return nextState
    }
    case 'EMPTY_SELECTED': {
      const nextState = produce(state, draft => {
        draft.selectedData = {
          orgs: [],
          depts: [],
          users: [],
        }
      })

      return nextState
    }
    case 'INIT_DEFAULT': {
      const nextState = produce(state, draft => {
        draft.selectedData = payload
      })

      return nextState
    }
    case 'ADD_USER': {
      const { type, user, range } = payload
      const { key } = user

      const isReplace = range === 1

      const nextState = produce(state, draft => {
        const { selectedData = {} } = draft

        if (!selectedData.users) {
          selectedData.users = []
        }

        if (isReplace) {
          selectedData.users = [user]
          selectedData.depts = []
          selectedData.orgs = []

          return
        }

        if (isExceedMaxRange(selectedData, range)) return

        if (selectedData.users.find(user => user.key === key)) {
          // 存在相同的用户，不重复添加
          return
        }

        if (type === 'append') {
          selectedData.users.push(user)
        } else {
          selectedData.users = [user]
        }

        draft.selectedData = selectedData
      })

      return nextState
    }
    case 'ADD_DEPT': {
      const { type, dept, range } = payload
      const { key } = dept

      const isReplace = range === 1

      const nextState = produce(state, draft => {
        const { selectedData = {} } = draft

        if (!selectedData.depts) {
          selectedData.depts = []
        }

        if (isReplace) {
          selectedData.depts = [dept]
          selectedData.users = []
          selectedData.orgs = []

          return
        }

        if (isExceedMaxRange(selectedData, range)) return

        // 存在相同的用户，不重复添加
        if (selectedData.depts.find(dept => dept.key === key)) {
          return
        }

        if (type === 'append') {
          selectedData.depts.push(dept)
        } else {
          selectedData.depts = [dept]
        }

        draft.selectedData = selectedData
      })

      return nextState
    }
    case 'ADD_ORG': {
      const { type, org, range } = payload
      const { key } = org

      const isReplace = range === 1

      const nextState = produce(state, draft => {
        const { selectedData = {} } = draft

        if (!selectedData.orgs) {
          selectedData.orgs = []
        }

        if (isReplace) {
          selectedData.orgs = [org]
          selectedData.users = []
          selectedData.depts = []

          return
        }

        if (isExceedMaxRange(selectedData, range)) return

        // 存在相同的单位，不重复添加
        if (selectedData.orgs.find(org => org.key === key)) {
          return
        }

        if (type === 'append') {
          selectedData.orgs.push(org)
        } else {
          selectedData.orgs = [org]
        }

        draft.selectedData = selectedData
      })

      return nextState
    }
    default:
      return state
  }
}

export default reducer
