import React, { useState, useContext, useEffect } from 'react'

import { Tree, Spin } from 'antd'
import _ from 'lodash'
import Cookies from 'js-cookie'

import api from 'ROOT/service'
// import form from 'ROOT/views/web/Bus/form'
import {
  xhrWrapper,
  isChooseDept,
  isChooseUser,
  isChooseAll,
  isChooseOrg,
  isChooseOrgAndDept,
  formatApiDataToTree,
} from './utils'
import Index from './store/index/index'
import { treeStyle, spinStyle } from './css'
import TreeIcon from './icon'

const { TreeNode } = Tree

const CustomTree = ({ lnCodes, disabledData, orgId }) => {
  const { state: indexState, dispatch: indexDispatch } = useContext(Index.Context)

  const { userConfig = {} } = indexState
  const { type } = userConfig

  const [treeData, setTreeData] = useState([])
  const [mapDeptsByOrg, setMapDeptsByOrg] = useState({})
  const [loading, setLoading] = useState(false)
  const [allOrgs, setAllOrgs] = useState([])

  const rootOrgId = orgId || Cookies.get('orgId')

  const getTreeRootData = async () => {
    setLoading(true)
    const [, data] = await xhrWrapper(api.getOrgByLnCodes({ lnCodes, orgId: rootOrgId }))
    setLoading(false)

    setAllOrgs(data)

    const treeData = (data || [])
      .filter(item => String(item.orgId) === String(rootOrgId))
      .map(item => formatApiDataToTree(item, 'org'))

    setTreeData(treeData)
  }

  // 拉取根节点数据
  useEffect(() => {
    getTreeRootData()
  }, [lnCodes])

  // 通过单位获取部门和人员数据
  const getDeptsAndUsersByOrg = async (orgId, orgName, fullPaths = []) => {
    // 1. 拉取单位下【所有的部门】数据，并缓存起来，同时找出归属在当前节点下的部门数据，规则：parentId = 0
    const [, allDepts] = await xhrWrapper(api.getDeptByLnCodesAndOrg({ orgId, lnCodes }))

    // 缓存此单位下的部门数据
    setMapDeptsByOrg({
      ...mapDeptsByOrg,
      [orgId]: allDepts,
    })

    // 筛选出单位下的部门(parentId === 0)数据并补充上 orgId, orgName 信息
    const depts = (allDepts || [])
      .filter(item => item.parentId === 0)
      .map(item => ({ ...item, orgId, orgName }))

    // 查询单位下的人员
    const [, users] = await xhrWrapper(api.getUsersByLnCodesAndDept({ orgId, deptId: 0, lnCodes }))

    // 查询单位下的子单位
    const orgs = allOrgs.filter(item => String(item.parentOrgId) === String(orgId))

    return [
      ...users
        .map(item => ({ ...item, orgId, orgName, fullPaths: (fullPaths || []).concat(orgName) })) // 补单位信息
        .map(item => formatApiDataToTree(item, 'user')),
      ...depts
        .map(item => ({ ...item, fullPaths: (fullPaths || []).concat(orgName) }))
        .map(item => formatApiDataToTree(item, 'dept')),
      ...orgs
        .map(item => ({ ...item, fullPaths: (fullPaths || []).concat(orgName) }))
        .map(item => formatApiDataToTree(item, 'org')),
    ]
  }

  const handleLoadData = treeNode => {
    return new Promise(resolve => {
      if (treeNode.props.children) {
        resolve()

        return
      }

      const { id, name, type, fullPaths = [] } = treeNode.props.dataRef || {}

      // 点击【单位】节点
      if (type === 'org') {
        getDeptsAndUsersByOrg(id, name, fullPaths).then(data => {
          // 添加数据到当前节点的 children 属性下
          treeNode.props.dataRef.children = data

          setTreeData([...treeData])

          resolve()
        })
      }

      // 点击【部门】节点
      if (type === 'dept') {
        // 通过缓存的数据，找出归属在当前节点下的部门数据

        const { orgId, orgName } = treeNode.props.dataRef || {}

        // 1. 先获取当前部门下的子部门数据，同时补充上单位信息（orgId, orgName)
        const deptsByDept = (mapDeptsByOrg[orgId] || [])
          .filter(item => item.parentId === id)
          .map(item => ({ ...item, orgId, orgName, fullPaths: (fullPaths || []).concat(name) }))
          .map(item => formatApiDataToTree(item, 'dept'))

        // 2. 获取当前部门下人员数据, 并补充上 orgId, orgName, deptId, deptName 信息
        api
          .getUsersByLnCodesAndDept({ orgId, deptId: id, lnCodes })
          .then(res => {
            const data = res || []

            // 添加数据到当前节点的 children 属性下
            const usersByDept = data
              .map(item => ({
                ...item,
                orgId,
                orgName,
                deptId: id,
                deptName: name,
                fullPaths: (fullPaths || []).concat(name),
              }))
              .map(item => formatApiDataToTree(item, 'user'))

            treeNode.props.dataRef.children = [...usersByDept, ...deptsByDept]

            setTreeData([...treeData])

            resolve()
          })
          .catch(err => {
            console.log(err)
          })
      }
    })
  }

  const handleSelect = (selectedKeys, event) => {
    const dataRef = _.get(event, 'node.props.dataRef', {})
    const { isUser, isDept, isOrg } = dataRef
    const range = _.get(indexState, 'userConfig.range', 1)

    // 添加用户
    if ((isChooseUser(type) || isChooseAll(type)) && isUser) {
      indexDispatch({
        type: 'ADD_USER',
        payload: {
          type: indexState.addType,
          user: dataRef,
          range,
        },
      })
    }

    // 添加部门
    if ((isChooseDept(type) || isChooseAll(type) || isChooseOrgAndDept(type)) && isDept) {
      indexDispatch({
        type: 'ADD_DEPT',
        payload: {
          type: indexState.addType,
          dept: _.cloneDeep(dataRef), // 主要是处理点击了部门，再点击 icon 时，报错问题，大致是 immer 导致的，后续有时间再排查
          range,
        },
      })
    }

    // 添加单位
    if ((isChooseOrg(type) || isChooseAll(type) || isChooseOrgAndDept(type)) && isOrg) {
      indexDispatch({
        type: 'ADD_ORG',
        payload: {
          type: indexState.addType,
          org: _.cloneDeep(dataRef), // 主要是处理点击了部门，再点击 icon 时，报错问题，大致是 immer 导致的，后续有时间再排查
          range,
        },
      })
    }
  }

  const getDisabled = item => {
    const { id, type } = item
    const { users = [], depts = [], orgs = [] } = disabledData

    let disabled = false

    if (type === 'org') {
      disabled = orgs.some(orgId => String(orgId) === String(id))
    }

    if (type === 'dept') {
      disabled = depts.some(deptId => String(deptId) === String(id))
    }

    if (type === 'user') {
      disabled = users.some(uid => String(uid) === String(id))
    }

    return disabled
  }

  const renderTreeNodes = data => {
    return data.map(item => {
      const { isOrg, isDept, isLeaf } = item

      const getCustomIcon = () => {
        if (isOrg) {
          return <TreeIcon type="apartment" color="#4b95fa" />
        }

        if (isDept) {
          return <TreeIcon type="team" color="#6bb7ed" />
        }

        return <TreeIcon type="user" color="#c1cedd" />
      }

      if (item.children) {
        return (
          <TreeNode
            title={item.title}
            key={item.key}
            dataRef={item}
            icon={getCustomIcon()}
            isLeaf={isLeaf}
            disabled={getDisabled(item)}
          >
            {renderTreeNodes(item.children)}
          </TreeNode>
        )
      }

      return (
        <TreeNode
          key={item.key}
          {...item}
          disabled={getDisabled(item)}
          dataRef={item}
          icon={getCustomIcon()}
          isLeaf={isLeaf}
        />
      )
    })
  }

  return (
    <Spin spinning={loading} tip="Loading..." wrapperClassName={spinStyle}>
      <Tree
        loadData={handleLoadData}
        showIcon
        switcherIcon={<TreeIcon type="up" />}
        // defaultExpandedKeys={[String(orgId)]}
        className={treeStyle}
        onSelect={handleSelect}
      >
        {renderTreeNodes(treeData)}
      </Tree>
    </Spin>
  )
}

export default CustomTree
