import React, { useState, useContext } from 'react'

import { Input, Modal, message } from 'antd'
import { css } from 'emotion'

import Index from './store/index/index'
import Container from './container'

const TreeWrapper = ({
  title = '选择人员',
  okText = '确定',
  cancelText = '取消',
  range = 1,
  width = 600,
  type = 'singleUser', // singleOrg: 单选单位，multiOrg: 多选单位，singleUser: 单选人员， multiUser：多选人员，singleDept：单选部门，multiDept：多选部门，multiMulti：多选人员和部门
  disabledData = { users: [], depts: [], orgs: [] }, // 禁用数据
  defaultUserList = [],
  defaultDeptList = [], // 本期不支持
  defaultOrgList = [], // 本期不支持
  orgId, // 指定单位拉取数据
  lnCodes = [], // 标签列表
  disabled = false, // 是否禁用
  placeholder = '请选择',
  onChange = () => {},
}) => {
  const [visible, setVisible] = useState(false)
  const { state: indexState } = useContext(Index.Context)

  const { selectedData = {} } = indexState
  const { depts = [], users = [], orgs = [] } = selectedData

  const handleShowTree = () => {
    setVisible(true)
  }

  const handleCancel = () => {
    setVisible(false)
  }

  const handleConfirm = () => {
    if (users.length === 0) {
      message.error('请先选择人员')

      return
    }

    setVisible(false)

    onChange([...users, ...depts, ...orgs])
  }

  const getUserName = () => {
    const data = [...defaultUserList, ...defaultDeptList, ...defaultOrgList]
    const maxCount = 3

    return data.map(item => item.name).join(',') + (data.length > maxCount ? '...' : '')
  }

  return (
    <div key={visible}>
      <Input
        readOnly
        onClick={handleShowTree}
        value={getUserName()}
        placeholder={placeholder}
        disabled={disabled}
      />
      <Modal
        title={title}
        visible={visible}
        onOk={handleConfirm}
        onCancel={handleCancel}
        width={width}
        okText={okText}
        cancelText={cancelText}
        centered
        className={css`
          margin: 0;
          padding: 0;
          max-width: 100%;

          .ant-modal-body {
            padding: 0;
            height: 482px;
            overflow: hidden;
          }
        `}
      >
        <Container
          range={range}
          type={type}
          lnCodes={lnCodes}
          defaultDeptList={defaultDeptList}
          defaultOrgList={defaultOrgList}
          defaultUserList={defaultUserList}
          disabledData={disabledData}
          orgId={orgId}
        />
      </Modal>
    </div>
  )
}

export default TreeWrapper
