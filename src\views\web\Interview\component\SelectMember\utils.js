import produce from 'immer'

export const xhrWrapper = promise => {
  return promise
    .then(data => {
      return [null, data]
    })
    .catch(err => [err, null])
}

export const noop = () => {}

export const parseJson = json => {
  try {
    return JSON.parse(json) || {}
  } catch (err) {
    return {}
  }
}

export const getBrandColor = () => {
  return parseJson(localStorage.getItem('color')).brand || '#1890ff'
}

export const addOrgs = (orgs = [], pid, baseState) => {}

export const addDepts = ({ depts = [], pid, orgId, baseState }) => {
  const getDetpsByPid = pid => {
    return depts
      .filter(dept => dept.parent_id === pid)
      .map(dept => {
        const { id, name } = dept

        return {
          id,
          name,
          pid,
          orgId,
          isDept: true,
          key: `${orgId}-${id}`,
          title: name,
        }
      })
  }

  const nextState = produce(baseState, draft => {
    const { id, children = [] } = draft

    // 父节点是根节点的场景
    if (id === pid) {
      const currentDepts = getDetpsByPid(0)

      draft.children = children.concat(currentDepts)
    } else {
      // todo
    }
  })

  return nextState
}

export const addUsers = ({ users = [], pid, orgId, deptId, baseState }) => {
  const nextState = produce(baseState, draft => {
    const { id, children = [] } = draft

    // 父节点是根节点的场景
    if (id === pid) {
      const mapUsers = users.map(user => {
        const { uid, name, mobile } = user

        return {
          id: uid,
          pid: 0,
          name,
          orgId,
          deptId,
          mobile,
          isUser: true,
          key: `${orgId}-${deptId}-${uid}`,
          title: name,
          isLeaf: true,
        }
      })

      draft.children = children.concat(mapUsers)
    } else {
      // todo
    }
  })

  return nextState
}

export const isChooseUser = type => {
  return ['singleUser', 'multiUser'].includes(type)
}

export const isChooseDept = type => {
  return ['singleDept', 'multiDept'].includes(type)
}

export const isChooseOrg = type => {
  return ['singleOrg', 'multiOrg'].includes(type)
}

export const isChooseOrgAndDept = type => {
  return ['multiOrgAndDept'].includes(type)
}

export const isChooseAll = type => {
  return ['multiMulti'].includes(type)
}

export const getQuery = () => {
  const url = decodeURI(location.search) // 获取url中"?"符后的字串(包括问号)
  const query = {}
  if (url.indexOf('?') != -1) {
    const str = url.substr(1)
    const pairs = str.split('&')
    for (let i = 0; i < pairs.length; i++) {
      const pair = pairs[i].split('=')
      query[pair[0]] = decodeURIComponent(pair[1])
    }
  }

  return query // 返回对象
}

export const formatApiDataToTree = (data = {}, type) => {
  switch (type) {
    case 'org': {
      const { orgId, orgName, fullPaths = [] } = data

      return {
        id: orgId,
        name: orgName,
        key: orgId,
        title: orgName,
        orgId,
        orgName,
        fullPaths,
        isOrg: true,
        type: 'org',
      }
    }
    case 'dept': {
      // 需要冗余增加 orgId, orgName 信息
      const { name, parentId, id, sequence, orgId, orgName, fullPaths = [] } = data

      return {
        id,
        name,
        key: id,
        title: name,
        pid: parentId,
        sequence,
        orgId,
        orgName,
        fullPaths,
        isDept: true,
        type: 'dept',
      }
    }
    case 'user': {
      // 需要冗余增加 orgId, orgName, deptId, deptName 信息
      const { userId, userName, orgId, orgName, deptId, deptName, fullPaths = [] } = data

      return {
        id: userId,
        name: userName,
        key: `${orgId}-${deptId}-${userId}`, // 同一人员可能存在多部门下
        title: userName,
        orgId,
        orgName,
        deptId: deptId || 0, // 单位下的人员，没有部门 id，默认传 0
        deptName,
        fullPaths, // 完整的组织结构路径
        isUser: true,
        type: 'user',
        isLeaf: true,
      }
    }
    default:
      return data
  }
}
