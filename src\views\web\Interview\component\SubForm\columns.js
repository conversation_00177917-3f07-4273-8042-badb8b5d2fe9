import React from 'react'

import { Button, Space } from 'antd'
import { UploadOutlined } from '@ant-design/icons'
import moment from 'moment'
import { downloadFileByGet } from 'ROOT/utils/download'
import { parseJson } from './util'

const Required = () => <strong style={{ color: '#f00' }}>*</strong>

export const getColumns = ({
  onEdit = () => {},
  onDelete = () => {},
  access = {},
  debug = false,
  editable,
} = {}) => {
  const attachmentMaxLength = 10

  const isShowModifiedBtn =
    (access.isEdit === 'WRITE' ||
      access.isInterviewRecord === 'WRITE' ||
      access.isUploadCorrectiveActionReport === 'WRITE' ||
      debug) &&
    editable
  const isShowDeleteBtn = (access.isEdit === 'WRITE' || debug) && editable
  const operator = {
    title: '操作',
    key: 'op',
    width: 160,
    fixed: 'left',
    className: 'op-cell',
    render: (value, record, index) => {
      return (
        <div key={index}>
          <Space>
            {isShowModifiedBtn && (
              <Button onClick={() => onEdit(record, index)} type="primary" key="edit">
                {access.isInterviewRecord === 'WRITE' ||
                access.isUploadCorrectiveActionReport === 'WRITE' ? (
                  <span>
                    <UploadOutlined />
                    文档上传
                  </span>
                ) : (
                  '修改'
                )}
              </Button>
            )}
            {isShowDeleteBtn && (
              <Button onClick={() => onDelete(record, index)} style={{ color: '#f00' }} key="del">
                删除
              </Button>
            )}
          </Space>
        </div>
      )
    },
  }
  let columns = [
    {
      title: (
        <span>
          约谈对象（被约谈人姓名）
          <Required />
        </span>
      ),
      key: 'name',
      dataIndex: 'name',
    },
    {
      title: (
        <span>
          约谈对象职务（被约谈人职务）
          {/* <Required /> */}
        </span>
      ),
      key: 'duty',
      dataIndex: 'duty',
    },
    {
      title: (
        <span>
          被约谈单位
          <Required />
        </span>
      ),
      key: 'orgName',
      dataIndex: 'orgName',
    },
    // {
    //   title: (
    //     <span>
    //       约谈参加人
    //       {/* <Required /> */}
    //     </span>
    //   ),
    //   key: 'participants',
    //   dataIndex: 'participants',
    //   render: value => {
    //     // 将多个【约谈参加人】信息用逗号隔开
    //     return parseJson(value, [])
    //       .map(item => item.name)
    //       .join(',')
    //   },
    // },
    {
      title: '实际约谈时间',
      key: 'appointmentTime',
      dataIndex: 'appointmentTime',
      render: value => {
        return value ? moment(value).format('YYYY-MM-DD HH:mm') : ''
      },
    },
    {
      title: '是否委托',
      key: 'entrust',
      dataIndex: 'entrust',
      render: (value, record) => {
        const { entrust } = parseJson(record.entrustConfig)

        return entrust ? '是' : '否'
      },
    },
    {
      title: '受委托人姓名',
      key: 'mandatary',
      dataIndex: 'mandatary',
      render: (value, record) => {
        const { mandatoryName } = parseJson(record.entrustConfig)

        return mandatoryName
      },
    },
    {
      title: '受委托人职务',
      key: 'mandatoryDuty',
      dataIndex: 'mandatoryDuty',
      render: (value, record) => {
        const { mandatoryDuty } = parseJson(record.entrustConfig)

        return mandatoryDuty
      },
    },
    {
      title: (
        <span>
          约谈记录附件上传
          {(access.isInterviewRecord === 'WRITE' ||
            access.isUploadCorrectiveActionReport === 'WRITE') && <Required />}
        </span>
      ),
      key: 'recordAttachment',
      dataIndex: 'recordAttachment',
      render: (value, record, index) => {
        const { config } = record
        const { recordAttachment } = parseJson(config)

        return (
          <div key={index}>
            {(recordAttachment || []).map(item => {
              const { name, url } = item

              return (
                <div key={url}>
                  <a
                    onClick={e => {
                      e.preventDefault()
                      downloadFileByGet(url, {}, name)
                    }}
                    title={name}
                    className="sub-form-attachment-item"
                  >
                    {name.length > attachmentMaxLength
                      ? `${name.substr(0, attachmentMaxLength)}...`
                      : name}
                  </a>
                </div>
              )
            })}
          </div>
        )
      },
    },
    {
      title: (
        <span>
          整改报告附件上传
          {access.isUploadCorrectiveActionReport === 'WRITE' && <Required />}
        </span>
      ),
      key: 'remediationAttachment',
      dataIndex: 'remediationAttachment',
      render: (value, record, index) => {
        const { config } = record
        const { remediationAttachment } = parseJson(config)

        return (
          <div key={index}>
            {(remediationAttachment || []).map(item => {
              const { name, url } = item

              return (
                <div key={url}>
                  <a
                    onClick={e => {
                      e.preventDefault()
                      downloadFileByGet(url, {}, name)
                    }}
                    className="sub-form-attachment-item"
                    title={name}
                  >
                    {name.length > attachmentMaxLength
                      ? `${name.substr(0, attachmentMaxLength)}...`
                      : name}
                  </a>
                </div>
              )
            })}
          </div>
        )
      },
    },
  ]
  if (isShowModifiedBtn || isShowDeleteBtn) {
    // columns = columns.concat(operator)
    // 操作按钮放到前面
    columns.unshift(operator)
  }
  return columns
}
