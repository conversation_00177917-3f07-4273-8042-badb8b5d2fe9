import React, { useCallback, useEffect } from 'react'

import {
  SchemaMarkupForm,
  FormEffectHooks,
  createFormActions,
  createAsyncFormActions,
} from '@formily/antd'
import { FormMegaLayout, Input, DatePicker, Select, Checkbox } from '@formily/antd-components'

// import UserSelector from 'ROOT/components/Formily/userSelect'
import useOrgType from 'ROOT/hooks/useOrgType'
import Upload from '../Upload'
import SelectMember from '../SelectMember'
import Duties from '../Duties'
import Post from '../Post'
import UserSelector from '../UserSelector'
import schema from './schema'

const { onFieldInputChange$, onFieldValueChange$ } = FormEffectHooks
export const actions = createAsyncFormActions()

const Form = props => {
  const {
    initValue,
    drafterOrgId,
    drafterOrgName,
    disabled = false,
    access = {},
    debug,
    subInfos = [],
  } = props

  // 是否是【编辑】态
  const isFormEdit = Object.keys(initValue).length > 0

  // 发起人所属的单位类型（区公司，市公司等）
  const orgType = useOrgType(drafterOrgId)

  // 【约谈对象】职务 code
  const getUserLnCodes = () => {
    // 区公司, 所有二级经理、三级经理
    if (orgType === 1) {
      return ['zw_2_0', 'zw_3_0']
    }

    // 市公司, 所有三级经理、四级经理、包含县公司
    if (orgType === 2) {
      return ['zw_3_0', 'zw_4_0']
    }

    return []
  }

  const getDisabledData = () => {
    return {
      users: (subInfos || []).map(item => item.uid),
    }
  }

  // 【受委托人】职务 code
  const getMandataryLnCodes = () => {
    // 区公司，所有移动的一级经理、二级经理、三级经理
    if (orgType === 1) {
      return ['zw_1_0', 'zw_2_0', 'zw_3_0']
    }

    // 市公司，本公司及下级县公司的二级经理、三级经理、四级经理、五级经理
    if (orgType === 2) {
      return ['zw_2_0', 'zw_3_0', 'zw_4_0', 'zw_5_0']
    }

    return []
  }

  const useManyEffects = useCallback(() => {
    const { setFieldState } = createFormActions()

    // 【约谈对象】字段值变化时，更新【约谈对象职务】和【被约谈单位】字段值
    onFieldInputChange$('user').subscribe(async ({ value }) => {
      // 【约谈对象职务】
      setFieldState('duty', state => {
        const [user] = value || []
        const { id, orgId } = user || {}

        state.props['x-component-props'] = {
          ...state.props['x-component-props'],
          user: { uid: id, orgId },
          type: 3,
        }
      })

      // 【被约谈单位】
      setFieldState('orgName', state => {
        state.value = value.length > 0 ? `${(value[0].fullPaths || []).join('\\')}` : ''
      })
    })

    // 【受委托人】字段值变化时，更新【受委托人职务】字段值
    onFieldInputChange$('mandatary').subscribe(async ({ value }) => {
      // 【受委托人职务】
      setFieldState('mandatoryDuty', state => {
        const [user] = value || []
        const { id, orgId } = user || {}

        state.props['x-component-props'] = {
          ...state.props['x-component-props'],
          user: { uid: id, orgId },
          type: 4,
        }
      })
    })
  }, [])

  useEffect(() => {
    if (Object.keys(initValue).length > 0) {
      // 子表单中的【默认值】配置
      const defaultValue = { entrust: false }
      actions.setFormState(state => {
        state.values = { ...defaultValue, ...initValue }
      })
    }
  }, [initValue])

  // 自定义组件注册给 formily 使用
  Upload.isFieldComponent = true
  SelectMember.isFieldComponent = true
  UserSelector.isFieldComponent = true
  Duties.isFieldComponent = true
  Post.isFieldComponent = true

  // 表单字段状态根据 access 控制
  const fieldStateMap = {
    user: access.isEdit === 'WRITE' || debug,
    duty: access.isEdit === 'WRITE' || debug,
    orgName: access.isEdit === 'WRITE' || debug,
    participant: access.isEdit === 'WRITE' || debug,
    entrust:
      access.isInterviewRecord === 'WRITE' ||
      access.isUploadCorrectiveActionReport === 'WRITE' ||
      debug,
    appointmentTime:
      access.isInterviewRecord === 'WRITE' ||
      access.isUploadCorrectiveActionReport === 'WRITE' ||
      debug,
    mandatary:
      access.isInterviewRecord === 'WRITE' ||
      access.isUploadCorrectiveActionReport === 'WRITE' ||
      debug,
    mandatoryDuty:
      access.isInterviewRecord === 'WRITE' ||
      access.isUploadCorrectiveActionReport === 'WRITE' ||
      debug,
    recordAttachment:
      access.isInterviewRecord === 'WRITE' ||
      access.isUploadCorrectiveActionReport === 'WRITE' ||
      debug,
    remediationAttachment: access.isUploadCorrectiveActionReport === 'WRITE' || debug,
  }

  return (
    <SchemaMarkupForm
      schema={schema}
      actions={actions}
      components={{
        DatePicker,
        Input,
        FormMegaLayout,
        Select,
        TextArea: Input.TextArea,
        Upload,
        Checkbox,
        SelectMember, // 自定义选人组件
        UserSelector, // 标准化选人组件
        Duties, // 职务组件
        Post, // 职级组件
      }}
      previewPlaceholder="无"
      effects={useManyEffects}
      expressionScope={{
        labelAlign: 'top',
        disabled,
        debug,
        access,
        fieldStateMap,
        getUserLnCodes,
        getMandataryLnCodes,
        getDisabledData,
        isFormEdit,
        drafterOrgId,
        drafterOrgName,
        log: value => {
          console.log(value, 11111)
        },
      }}
    />
  )
}

export default Form
