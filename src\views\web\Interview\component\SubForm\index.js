import React, { useState, useEffect } from 'react'

import { Table, Button, Modal } from 'antd'
import _ from 'lodash'
import { Provider } from 'react-redux'

import { getColumns } from './columns'
import Form, { actions } from './form'
import Comment from '../Comment'
import QuickTag from '../QuickTag'
import { formatItemToApi, formatApiItemToUi } from './util'
import store from '../MoaTree/store'

import './index.css'

const SubForm = cProps => {
  const [visible, setVisible] = useState(false)
  const [item, setItem] = useState({})
  const [access, setAccess] = useState({})
  const [debug, setDebug] = useState(false)
  const [loading, setLoading] = useState(false)

  const { value = [], mutators, props, editable } = cProps
  const xProps = props['x-component-props']
  const cloneValue = [...value]
  const index = cloneValue.findIndex(v => String(v.uid) === String(_.get(item, 'user[0].id')))

  // 判断是【新增】还是【编辑】
  const isEdit = index !== -1

  // 监听 access 值的变化
  useEffect(() => {
    setAccess(xProps.access)
  }, [xProps.access])

  // 监听 debug 值的变化
  useEffect(() => {
    setDebug(xProps.debug || false)
  }, [xProps.debug])

  const handleAdd = () => {
    setItem({})
    setVisible(true)
  }

  const handleEdit = data => {
    setVisible(true)
    setItem(formatApiItemToUi(data))
  }

  const handleDel = data => {
    // 先浅拷贝一下数据，避免删除时不生效问题
    const items = [...value]

    // 找出对应的索引
    const index = items.findIndex(item => String(item.uid) === String(data.uid))

    // 删除当前数据
    items.splice(index, 1)

    mutators.change([...items])
  }

  const handleOk = async () => {
    try {
      const { values } = await actions.submit()
      const user = formatItemToApi(values || {})
      if (isEdit) {
        // 替换对应的数据
        cloneValue.splice(index, 1, user)

        mutators.change([...cloneValue])
      } else {
        mutators.change([...cloneValue, { ...user }])
      }

      setVisible(false)
    } catch (err) {
      console.log(err)
    }
  }

  const handleCancel = () => {
    setVisible(false)
  }

  // 快速标签切换时的回调函数
  const handleTagChange = (data = []) => {
    console.log(value, data, 90000)

    // 将通过快速标签选择的人员数据和已经添加的人员数据合并，已添加的数据为基准
    mutators.change(
      _.unionBy([...value.map(user => ({ ...user, uid: String(user.uid) }))], data, 'uid'),
    )
  }

  const columns = getColumns({ onEdit: handleEdit, onDelete: handleDel, access, debug, editable })
  return (
    <Provider store={store}>
      <div>
        {(access.isEdit === 'WRITE' || debug) && editable && (
          <QuickTag
            onChange={handleTagChange}
            setLoading={setLoading}
            drafterOrgId={xProps.drafterOrgId}
          />
        )}
        <Table
          columns={columns}
          pagination={{ hideOnSinglePage: true }}
          dataSource={value || []}
          scroll={{ x: '150%' }}
          loading={loading}
          rowKey="id"
        />
        {(access.isEdit === 'WRITE' || debug) && editable && (
          <div style={{ marginTop: 10 }}>
            <Button style={{ width: '100%' }} onClick={handleAdd} type="primary">
              添加约谈对象信息
            </Button>
          </div>
        )}
        <Modal
          title={isEdit ? '修改' : '添加'}
          visible={visible}
          onOk={handleOk}
          onCancel={handleCancel}
          width={600}
          okText="确认"
          cancelText="取消"
          className="add-interview-modal"
          destroyOnClose
        >
          <Form
            initValue={item}
            access={access}
            subInfos={value}
            debug={debug}
            drafterOrgId={xProps.drafterOrgId}
            drafterOrgName={xProps.drafterOrgName}
          />
          <Comment />
        </Modal>
      </div>
    </Provider>
  )
}

export default SubForm
