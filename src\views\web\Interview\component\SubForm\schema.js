import locale from 'antd/lib/date-picker/locale/zh_CN'

export default {
  type: 'object',
  properties: {
    layout: {
      'x-component': 'mega-layout',
      'x-component-props': {
        grid: true,
        autoRow: true,
        labelAlign: '{{labelAlign}}',
        className: 'grid-gaps',
        responsive: {
          lg: 3,
        },
      },
      properties: {
        user: {
          key: 'user',
          name: 'user',
          title: '约谈对象（被约谈人姓名）',
          required: true,
          'x-component': 'SelectMember',
          'x-component-props': {
            disabled: '{{!fieldStateMap.user}}',
            placeholder: '请选择',
            lnCodes: '{{getUserLnCodes()}}',
            disabledData: '{{getDisabledData()}}',
            orgId: '{{drafterOrgId}}',
          },
          'x-mega-props': {
            span: 3,
          },
        },
        duty: {
          key: 'duty',
          name: 'duty',
          title: '约谈对象职务（被约谈人职务）',
          // required: true,
          'x-component': 'Post',
          'x-component-props': {
            disabled: '{{!fieldStateMap.duty}}',
            type: 3,
          },
          'x-mega-props': {
            span: 3,
          },
        },
        orgName: {
          key: 'orgName',
          name: 'orgName',
          title: '被约谈单位',
          required: true,
          'x-component': 'Input',
          'x-component-props': {
            disabled: true, // 被约谈单位通过人员带出来，所以不能改
            // disabled: '{{!fieldStateMap.unit}}',
          },
          'x-mega-props': {
            span: 3,
          },
        },
        entrust: {
          key: 'entrust',
          name: 'entrust',
          title: '是否委托',
          required:
            "{{access.isInterviewRecord === 'WRITE' || access.isUploadCorrectiveActionReport === 'WRITE' || debug}}",
          'x-component-props': {
            disabled: '{{!fieldStateMap.entrust}}',
          },
          'x-mega-props': {
            span: 3,
          },
          'x-component': 'Select',
          default: false,
          enum: [
            { label: '是', value: true },
            { label: '否', value: false },
          ],
          'x-linkages': [
            {
              type: 'value:visible',
              target: '*(mandatary, mandatoryDuty)',
              condition: '{{ $self.value }}',
            },
            {
              type: 'value:state',
              target: '*(mandatary, mandatoryDuty)',
              condition: '{{ $self.value }}',
              state: {
                required: true,
              },
              otherwise: {
                required: false,
              },
            },
          ],
        },
        mandatary: {
          key: 'mandatary',
          name: 'mandatary',
          title: '受委托人姓名',
          required:
            "{{access.isInterviewRecord === 'WRITE' || access.isUploadCorrectiveActionReport === 'WRITE' || debug}}",
          'x-component': 'SelectMember',
          'x-component-props': {
            placeholder: '请选择',
            disabled: '{{!fieldStateMap.mandatary}}',
            lnCodes: '{{getMandataryLnCodes()}}',
            orgId: '{{drafterOrgId}}',
          },
          'x-mega-props': {
            span: 3,
          },
        },
        mandatoryDuty: {
          key: 'mandatoryDuty',
          name: 'mandatoryDuty',
          title: '受委托人职务',
          // required:
          //   "{{access.isInterviewRecord === 'WRITE' || access.isUploadCorrectiveActionReport === 'WRITE' || debug}}",
          'x-component': 'Post',
          'x-component-props': {
            disabled: '{{!fieldStateMap.mandatoryDuty}}',
            type: 4,
          },
          'x-mega-props': {
            span: 3,
          },
        },
        appointmentTime: {
          key: 'appointmentTime',
          name: 'appointmentTime',
          title: '实际约谈时间',
          required:
            "{{access.isInterviewRecord === 'WRITE' || access.isUploadCorrectiveActionReport === 'WRITE' || debug}}",
          'x-component': 'DatePicker',
          'x-component-props': {
            showTime: true,
            locale,
            disabled: '{{!fieldStateMap.appointmentTime}}',
            format: 'YYYY-MM-DD HH:mm',
            style: {
              width: '100%',
            },
          },
          'x-mega-props': {
            span: 3,
          },
        },
        recordAttachment: {
          key: 'recordAttachment',
          name: 'recordAttachment',
          title: '约谈记录附件上传',
          required:
            "{{access.isInterviewRecord === 'WRITE' || access.isUploadCorrectiveActionReport === 'WRITE' || debug}}",
          'x-component': 'Upload',
          'x-component-props': {
            fileFormat: ['pdf'],
            disabled: '{{!fieldStateMap.recordAttachment}}',
          },
          'x-mega-props': {
            span: 3,
          },
        },
        remediationAttachment: {
          key: 'remediationAttachment',
          name: 'remediationAttachment',
          title: '整改报告附件上传',
          required: "{{access.isUploadCorrectiveActionReport === 'WRITE' || debug}}",
          'x-component': 'Upload',
          'x-component-props': {
            fileFormat: ['pdf'],
            disabled: '{{!fieldStateMap.remediationAttachment}}',
          },
          'x-mega-props': {
            span: 3,
          },
        },
      },
    },
  },
}
