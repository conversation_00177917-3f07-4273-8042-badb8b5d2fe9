import moment from 'moment'

// 将表单上收集到的数据，转换成接口所需要的格式
export const formatItemToApi = (item = {}) => {
  const {
    id, // 子表单id
    duty, // 约谈对象职务
    orgName, // 被约谈单位
    appointmentTime, // 实际约谈时间
    entrust, // 是否委托
    mandatoryDuty, // 委托人职务
    recordAttachment, // 约谈记录附件上传
    remediationAttachment, // 整改报告附件上传
  } = item

  // 约谈对象, 数组类型， id, name, orgId, deptId
  const user = (item.user || [])[0] || {}

  // 约谈参加人
  // const participants = (item.participants || []).map(item => {
  //   const { id, name, orgId, deptId } = item

  //   return {
  //     uid: id,
  //     departmentId: deptId,
  //     orgId,
  //     name,
  //     orgName: '', // 选人组件没有带单位名称，默认填充为空
  //     departmentName: '', // 选人组件没有带部门名称，默认填充为空
  //   }
  // })

  // 受委托人
  const mandatary = (item.mandatary || [])[0] || {}
  return {
    id,
    name: user.name,
    uid: user.id,
    duty,
    orgName,
    orgId: user.orgId,
    deptId: user.deptId,
    deptName: user.deptName,
    // participants: JSON.stringify(participants), // 约谈参加人，可以多个，数组形式
    appointmentTime: appointmentTime
      ? moment(appointmentTime, 'YYYY-MM-DD HH:mm').valueOf()
      : undefined,
    entrustConfig: JSON.stringify({
      entrust,
      mandatoryName: mandatary.name,
      mandatoryDuty,
      mandatoryUid: mandatary.id,
      mandatoryOrgId: mandatary.orgId,
      mandatoryDeptId: mandatary.deptId,
      mandatoryOrgName: mandatary.orgName,
      mandatoryDeptName: mandatary.deptName,
    }),
    config: JSON.stringify({
      recordAttachment,
      remediationAttachment,
    }),
  }
}

export const formatApiItemToUi = (item = {}) => {
  const {
    id,
    name,
    uid,
    duty,
    orgName,
    orgId,
    deptName,
    deptId,
    // participants,
    appointmentTime,
    entrustConfig,
    config,
  } = item

  const parseEntrustConfig = parseJson(entrustConfig)
  const parseConfig = parseJson(config)
  // const parseParticipants = parseJson(participants, [])

  return {
    id,
    user: [
      {
        name,
        id: uid,
        orgId,
        deptId,
        deptName,
      },
    ],
    duty,
    orgName,
    // participants: (parseParticipants || []).map(item => {
    //   return {
    //     name: item.name,
    //     id: item.uid,
    //     orgId: item.orgId,
    //     deptId: item.departmentId,
    //   }
    // }),
    appointmentTime: appointmentTime ? moment(appointmentTime).format('YYYY-MM-DD HH:mm') : '',
    entrust: parseEntrustConfig.entrust || false,
    mandatary: parseEntrustConfig.mandatoryUid
      ? [
          {
            name: parseEntrustConfig.mandatoryName,
            id: parseEntrustConfig.mandatoryUid,
            orgId: parseEntrustConfig.mandatoryOrgId,
            deptId: parseEntrustConfig.mandatoryDeptId,
          },
        ]
      : [],
    mandatoryDuty: parseEntrustConfig.mandatoryDuty,
    recordAttachment: parseConfig.recordAttachment,
    remediationAttachment: parseConfig.remediationAttachment,
  }
}

export const formatDataToApi = (data = []) => {
  return data.map(item => {
    return formatItemToApi(item)
  })
}

// 将接口数据，格式化成表单所需要的格式
export const formatApiDataToUi = (data = []) => {
  return data.map(item => {
    return formatApiItemToUi(item)
  })
}

export const parseJson = (value, initValue) => {
  try {
    return JSON.parse(value)
  } catch (error) {
    return initValue || {}
  }
}
