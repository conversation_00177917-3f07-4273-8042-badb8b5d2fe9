import React from 'react'
import { Card } from 'antd'
import { openDownloadDialog } from 'ROOT/utils'

const TemplateFile = cProps => {
  const { props } = cProps
  const { type } = props['x-component-props']
  const renderContent = () => {
    if (type === 1) {
      return (
        <a
          onClick={() =>
            openDownloadDialog(
              '/extra-forms/template/全面从严治党主体责任约谈记录表.xlsx',
              '全面从严治党主体责任约谈记录表.xlsx',
            )
          }
        >
          模板：全面从严治党主体责任约谈记录表.xlsx
        </a>
      )
    }
    if (type === 2) {
      return (
        <a
          onClick={() =>
            openDownloadDialog(
              '/extra-forms/template/全面从严治党监督责任约谈记录表.xlsx',
              '全面从严治党监督责任约谈记录表.xlsx',
            )
          }
        >
          模板：全面从严治党监督责任约谈记录表.xlsx
        </a>
      )
    }
    return null
  }
  return (
    <Card style={{ width: '100%' }} title="下载模板">
      {renderContent()}
    </Card>
  )
}

export default TemplateFile
