import React, { useState, useRef, useCallback } from 'react'

import { message } from 'antd'
import { css } from 'emotion'

import UploadFileList from '@xm/upload-file-list'

import Service from 'ROOT/service'
import Loading from 'ROOT/components/Loading'

export default itemProps => {
  const InputImport = useRef()
  const { value = [], mutators, props, schema, form, editable } = itemProps
  const {
    noSpecialName = true,
    maxCount = 10, //  最多能上传的附件数
    maxSize = 50, //  附件大小 50M 以下
    disabled,
    fileFormat = [], // 支持的文件格式
  } = props['x-component-props'] || {}
  const [loading, setLoading] = useState(false)

  const selectFiles = () => {
    InputImport.current.click()
  }

  const fileChange = e => {
    const files = Array.from(e.target.files)
    const arr1 = []
    const arr2 = []
    const arr3 = []
    const arr4 = []
    const ableUpload = []

    if (files.length + (value || []).length > maxCount) {
      message.warning(`附件不能超过${maxCount}个`)
      return false
    }

    files.map(item => {
      const { name, size } = item || {}

      // 文件后缀
      const suffix = name.substr(name.lastIndexOf('.') + 1)

      // 只能上传指定格式的文件
      if (fileFormat.length > 0 && !fileFormat.includes(suffix)) {
        arr4.push(name)

        return
      }

      const shortName = name.substring(0, name.lastIndexOf('.'))
      if (!noSpecialName && !/^\d+([.．])/.test(shortName)) {
        arr1.push(name)
        return
      }
      if (size <= 0) {
        arr2.push(name)
        return
      }
      // 文件大小 50M 以下
      if (size > maxSize * 1024 * 1024) {
        arr3.push(name)
        return
      }
      ableUpload.push(item)
    })
    if (arr1.length > 0) {
      message.warning(`${arr1.join(',')}文件不符合附件命名要求，附件名称以数字序号+“.”或“．”开头`)
    } else if (arr2.length > 0) {
      message.warning(`${arr2.join(',')}0kb或0kb以下大小的文件，请重新选择文件上传`)
    } else if (arr3.length > 0) {
      message.warning(`${arr3.join(',')}文件大小需 ${maxSize}M 以下`)
    } else if (arr4.length > 0) {
      message.warning(`请上传PDF格式文件`)
    }
    if (files.length > 0) {
      ableUpload.forEach(i => uploadFiles(i))
    }
    InputImport.current.value = null
  }

  const uploadFiles = files => {
    const formData = new FormData()
    formData.append('file', files)
    setLoading(true)
    Service.uploadFile(formData).then(res => {
      if (res.code === 200) {
        mutators.push({
          name: files.name,
          size: files.size,
          url: res.fileUrl,
          suffix: files.name.substring(files.name.lastIndexOf('.') + 1),
          uid: files.lastModified,
        })
        setLoading(false)
      }
    })
  }

  const uploadOptions = {
    enableSort: editable,
    enableDownload: true,
    enableDelete: editable,
    enableRename: editable,
    enablePreview: true,
  }

  const combineFileList = list => {
    return !Array.isArray(list)
      ? []
      : list.map((file, index) => ({
          name: !noSpecialName
            ? `附件：${(file.name || file.fileName).replace(/^附件：/, '')}`
            : `${file.name || file.fileName}`,
          size: file.size || file.fileSize,
          url: file.url || file.fileUrl,
          suffix: (file.name || file.fileName).substring(
            (file.name || file.fileName).lastIndexOf('.') + 1,
          ),
          uid: file.uid,
        }))
  }

  const onDelete = fileList => {
    mutators.change(combineFileList(fileList))
  }

  const onSortEnd = fileList => {
    mutators.change(combineFileList(fileList))
  }

  const onRename = fileList => {
    mutators.change(combineFileList(fileList))
  }

  const beforeRename = file => {
    if (!noSpecialName && !/^\d+([.．])/.test(file.name.replace(/^附件：/, ''))) {
      message.warning('上传文件不符合附件命名要求，附件名称以数字序号+“.”或“．”开头')
      return false
    }
    return true
  }

  const combineFileName = useCallback(() => {
    return combineFileList(value)
  }, [value])

  return (
    <div style={{ width: '100%' }}>
      {editable && value.length < maxCount ? (
        <div style={{ width: '100%', marginBottom: 10 }}>
          <span
            onClick={selectFiles}
            className={`upload-file-btn ${disabled ? 'upload-file-btn-disabled' : ''}`}
          >
            上传文件
          </span>
          <input
            type="file"
            ref={InputImport}
            onChange={fileChange}
            className={css`
              position: absolute;
              top: -9999px;
              left: -9999px;
            `}
            multiple="multiple"
          />
          {loading && <Loading isFullScreen />}
        </div>
      ) : null}
      <UploadFileList
        options={uploadOptions}
        dataSource={combineFileName()}
        onDelete={onDelete}
        onSortEnd={onSortEnd}
        onRename={onRename}
        beforeRename={beforeRename}
      />
    </div>
  )
}
