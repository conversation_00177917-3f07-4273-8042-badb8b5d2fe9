import React, { useState } from 'react'

import { PreviewText } from '@formily/react-shared-components'
import { Input } from 'antd'

import MoaTree from '../MoaTree'

const UserSelector = cProps => {
  const [visible, setVisible] = useState(false)

  const { value = [], mutators, props, editable } = cProps
  const xProps = props['x-component-props']

  const { placeholder = '请选择', maxCount = 1000, disabled, orgId, orgName } = xProps

  const onCancel = () => {
    setVisible(false)
  }

  const onConfirm = ({ userList = [] }) => {
    console.log(userList, 2323232)
    mutators.change(userList)

    setVisible(false)
  }

  const getUserNames = () => {
    let names = ''

    if (Array.isArray(value)) {
      names = value.map(x => x.name).join('，')
    }

    return names
  }

  return editable ? (
    <div style={{ width: `${100}%` }}>
      <Input
        readOnly
        disabled={disabled}
        value={getUserNames()}
        onClick={() => setVisible(true)}
        placeholder={placeholder}
      />
      {visible ? (
        <MoaTree
          type="multiUser"
          range={maxCount}
          defaultUserList={value}
          needSearchBar={false}
          orgId={orgId}
          orgName={orgName}
          visible={visible}
          onCancel={onCancel}
          onConfirm={onConfirm}
        />
      ) : null}
    </div>
  ) : (
    <PreviewText value={getUserNames()} />
  )
}

export default UserSelector
