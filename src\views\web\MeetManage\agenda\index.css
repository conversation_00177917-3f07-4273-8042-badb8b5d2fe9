/* 议题管理页面样式 */
.agenda-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 24px;
}

.meeting-info-card {
  background: #fff;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.meeting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.meeting-title {
  display: flex;
  align-items: center;
  gap: 16px;
}

.meeting-title h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.meeting-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.info-item {
  margin-bottom: 16px;
}

.info-label {
  color: #666;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-value {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.topics-container {
  background: #fff;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.topics-title {
  margin: 0 0 24px 0;
  font-size: 16px;
  font-weight: 600;
}

.topic-timeline-item {
  position: relative;
  margin-bottom: 24px;
  padding-left: 32px;
}

.timeline-connector {
  position: absolute;
  left: 15px;
  top: 24px;
  bottom: -24px;
  width: 2px;
  background: #e8e8e8;
}

.timeline-dot {
  position: absolute;
  left: 9px;
  top: 18px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 3px solid #fff;
  z-index: 2;
}

.timeline-dot.completed {
  background: #52c41a;
  box-shadow: 0 0 0 2px #52c41a;
}

.timeline-dot.active {
  background: #1890ff;
  box-shadow: 0 0 0 2px #1890ff;
}

.timeline-dot.pending {
  background: #d9d9d9;
  box-shadow: 0 0 0 2px #d9d9d9;
}

.topic-card {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.topic-card.active {
  border: 2px solid #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.topic-card.normal {
  border: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.topic-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.topic-tag {
  margin: 0;
  padding: 4px 12px;
  border-radius: 12px;
  font-weight: 500;
}

.topic-status-tag {
  padding: 4px 12px;
  border-radius: 12px;
  font-weight: 500;
}

.topic-name {
  margin-bottom: 16px;
  font-size: 16px;
}

.topic-name-label {
  font-weight: 600;
  margin-right: 8px;
  color: #262626;
}

.topic-name-value {
  color: #262626;
}

.topic-details {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px;
  color: #666;
  font-size: 14px;
  margin-bottom: 12px;
}

.topic-details-row2 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  color: #666;
  font-size: 14px;
}

.detail-label {
  font-weight: 500;
}

.meeting-steps {
  display: flex;
  align-items: center;
  gap: 8px;
}

.step-item {
  display: flex;
  align-items: center;
  padding: 6px 16px;
  border-radius: 16px;
  font-size: 12px;
  transition: all 0.3s ease;
}

.step-item.active {
  background: #1890ff;
  color: white;
  font-weight: 500;
}

.step-item.completed {
  background: #52c41a;
  color: white;
}

.step-item.pending {
  background: #f5f5f5;
  color: #999;
}

.step-connector {
  width: 20px;
  height: 1px;
  transition: all 0.3s ease;
}

.step-connector.completed {
  background: #52c41a;
}

.step-connector.pending {
  background: #d9d9d9;
}

.empty-state {
  text-align: center;
  color: #999;
  padding: 40px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .agenda-container {
    padding: 16px;
  }
  
  .meeting-info-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .topic-details {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .topic-details-row2 {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .meeting-steps {
    flex-wrap: wrap;
    gap: 4px;
  }
  
  .step-item {
    font-size: 11px;
    padding: 4px 8px;
  }
  
  .step-connector {
    width: 10px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.topic-timeline-item {
  animation: fadeIn 0.5s ease-out;
}

.topic-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}
