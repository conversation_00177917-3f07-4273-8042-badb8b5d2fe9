import React, { useState, useEffect } from 'react'
import { useParams, useHistory } from 'react-router-dom'
import { Button, Steps, Card, Tag, message, Spin, Modal, Dropdown, Menu } from 'antd'
import { ArrowLeftOutlined, EditOutlined, MoreOutlined, PlayCircleOutlined, PauseCircleOutlined, CheckCircleOutlined } from '@ant-design/icons'
import { css } from 'emotion'
import moment from 'moment'
import service from 'ROOT/service'
import './index.css'

const { Step } = Steps

const AgendaManage = () => {
  const { id } = useParams()
  const history = useHistory()
  const [loading, setLoading] = useState(false)
  const [meetingData, setMeetingData] = useState(null)
  const [currentStep, setCurrentStep] = useState(0)

  // 获取会议详情
  useEffect(() => {
    const fetchMeetingData = async () => {
      if (id) {
        try {
          setLoading(true)
          const response = await service.getMeetingDetail({ meetingId: id })
          console.log('会议详情:', response)
          setMeetingData(response)

          // 根据会议状态设置当前步骤
          if (response && response.status !== undefined) {
            setCurrentStep(response.status)
          }
        } catch (error) {
          console.error('获取会议详情失败:', error)
          message.error('获取会议详情失败')
        } finally {
          setLoading(false)
        }
      }
    }
    fetchMeetingData()
  }, [id])

  // 议题状态映射
  const getTopicStatus = (status) => {
    const statusMap = {
      0: { text: '未开始', color: 'default' },
      1: { text: '请假场', color: 'orange' },
      2: { text: '进行中', color: 'processing' },
      3: { text: '已结束', color: 'success' }
    }
    return statusMap[status] || statusMap[0]
  }

  // 渲染会议流程步骤
  const renderMeetingSteps = () => {
    const steps = [
      { key: 0, text: '未开始' },
      { key: 1, text: '请假场' },
      { key: 2, text: '进行中' },
      { key: 3, text: '已结束' }
    ]

    return (
      <div className={css`
        display: flex;
        align-items: center;
        gap: 8px;
      `}>
        {steps.map((step, index) => {
          const isActive = currentStep === step.key
          const isCompleted = currentStep > step.key

          return (
            <React.Fragment key={step.key}>
              <div className={css`
                display: flex;
                align-items: center;
                padding: 6px 16px;
                background: ${isActive ? '#1890ff' : isCompleted ? '#52c41a' : '#f5f5f5'};
                color: ${isActive || isCompleted ? 'white' : '#999'};
                border-radius: 16px;
                font-size: 12px;
                font-weight: ${isActive ? '500' : '400'};
                transition: all 0.3s ease;
              `}>
                {step.text}
              </div>
              {index < steps.length - 1 && (
                <div className={css`
                  width: 20px;
                  height: 1px;
                  background: ${isCompleted ? '#52c41a' : '#d9d9d9'};
                  transition: all 0.3s ease;
                `} />
              )}
            </React.Fragment>
          )
        })}
      </div>
    )
  }

  // 渲染会议基本信息
  const renderMeetingInfo = () => {
    if (!meetingData) return null

    return (
      <div className={css`
        background: #fff;
        padding: 24px;
        border-radius: 8px;
        margin-bottom: 24px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      `}>
        <div className={css`
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 24px;
        `}>
          <div className={css`
            display: flex;
            align-items: center;
            gap: 16px;
          `}>
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => history.goBack()}
              type="text"
            />
            <h2 className={css`
              margin: 0;
              font-size: 20px;
              font-weight: 600;
            `}>议题管理</h2>
          </div>
          <Button
            icon={<EditOutlined />}
            onClick={() => history.push(`/web/MeetManage/meetDetail/${id}`)}
          >
            取消议题
          </Button>
        </div>

        <div className={css`
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 24px;
        `}>
          <div>
            <div className={css`
              color: #666;
              margin-bottom: 8px;
            `}>会议名称</div>
            <div className={css`
              font-size: 16px;
              font-weight: 500;
            `}>{meetingData.name || '-'}</div>
          </div>

          <div>
            <div className={css`
              color: #666;
              margin-bottom: 8px;
            `}>会议流程</div>
            {renderMeetingSteps()}
          </div>

          <div>
            <div className={css`
              color: #666;
              margin-bottom: 8px;
            `}>开始时间</div>
            <div>{meetingData.startTime ? moment(meetingData.startTime).format('YYYY年MM月DD日') : '-'}</div>
          </div>

          <div>
            <div className={css`
              color: #666;
              margin-bottom: 8px;
            `}>会议类型</div>
            <div>{meetingData.meetingType || '-'}</div>
          </div>

          <div>
            <div className={css`
              color: #666;
              margin-bottom: 8px;
            `}>会议室</div>
            <div>{meetingData.meetingRoom || '-'}</div>
          </div>

          <div>
            <div className={css`
              color: #666;
              margin-bottom: 8px;
            `}>会议日期</div>
            <div>{meetingData.startTime ? moment(meetingData.startTime).format('YYYY年MM月DD日') : '-'}</div>
          </div>

          <div>
            <div className={css`
              color: #666;
              margin-bottom: 8px;
            `}>参会领导</div>
            <div>
              {meetingData.leaderList && meetingData.leaderList.length > 0
                ? meetingData.leaderList.map(leader => leader.name).join('、')
                : '-'
              }
            </div>
          </div>
        </div>
      </div>
    )
  }

  // 更新议题状态
  const updateTopicStatus = async (topicId, newStatus) => {
    try {
      setLoading(true)
      await service.editMeeting({
        topicId: topicId,
        status: newStatus
      })
      message.success('议题状态更新成功')

      // 重新获取会议数据
      const response = await service.getMeetingDetail({ meetingId: id })
      setMeetingData(response)

      // 更新会议流程状态
      if (response && response.status !== undefined) {
        setCurrentStep(response.status)
      }
    } catch (error) {
      console.error('更新议题状态失败:', error)
      message.error('更新议题状态失败')
    } finally {
      setLoading(false)
    }
  }

  // 渲染议题操作菜单
  const renderTopicActions = (topic, index) => {
    const currentStatus = topic.status || 0

    const menuItems = []

    // 根据当前状态显示可用操作
    if (currentStatus === 0) { // 未开始
      menuItems.push({
        key: 'start',
        icon: <PlayCircleOutlined />,
        label: '开始议题',
        onClick: () => updateTopicStatus(topic.id, 2)
      })
    } else if (currentStatus === 2) { // 进行中
      menuItems.push({
        key: 'complete',
        icon: <CheckCircleOutlined />,
        label: '结束议题',
        onClick: () => updateTopicStatus(topic.id, 3)
      })
    }

    if (menuItems.length === 0) return null

    return (
      <Dropdown
        menu={{ items: menuItems }}
        trigger={['click']}
        placement="bottomRight"
      >
        <Button
          type="text"
          icon={<MoreOutlined />}
          className={css`
            position: absolute;
            top: 16px;
            right: 16px;
            z-index: 10;
          `}
        />
      </Dropdown>
    )
  }

  // 渲染议题卡片
  const renderTopicCard = (topic, index) => {
    const status = getTopicStatus(topic.status || 0)
    const isActive = topic.status === 2 // 进行中状态
    const isCompleted = topic.status === 3 // 已结束状态

    return (
      <div
        key={index}
        className={css`
          position: relative;
          margin-bottom: 24px;
          padding-left: 32px;
        `}
      >
        {/* 时间线连接线 */}
        <div className={css`
            position: absolute;
            left: 15px;
            top: 24px;
            bottom: -24px;
            width: 2px;
            background: #e8e8e8;
          `} />

        {/* 状态指示器 */}
        <div className={css`
          position: absolute;
          left: 9px;
          top: 18px;
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: ${isCompleted ? '#52c41a' : isActive ? '#1890ff' : '#d9d9d9'};
          border: 3px solid #fff;
          box-shadow: 0 0 0 2px ${isCompleted ? '#52c41a' : isActive ? '#1890ff' : '#d9d9d9'};
          z-index: 2;
        `} />

        <Card
          className={css`
            border: ${isActive ? '2px solid #1890ff' : '1px solid #e8e8e8'};
            border-radius: 8px;
            box-shadow: ${isActive ? '0 4px 12px rgba(24, 144, 255, 0.15)' : '0 2px 8px rgba(0, 0, 0, 0.06)'};
            position: relative;

            .ant-card-body {
              padding: 20px;
            }
          `}
        >
          {/* 议题操作按钮 */}
          {renderTopicActions(topic, index)}
          {/* 议题状态标签 */}
          <div className={css`
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
          `}>
            <Tag color="blue" className={css`
              margin: 0;
              padding: 4px 12px;
              border-radius: 12px;
              font-weight: 500;
            `}>
              议题{index + 1}
            </Tag>
            <Tag color={status.color} className={css`
              padding: 4px 12px;
              border-radius: 12px;
              font-weight: 500;
            `}>
              {status.text}
            </Tag>
          </div>

          {/* 议题名称 */}
          <div className={css`
            margin-bottom: 16px;
            font-size: 16px;
          `}>
            <span className={css`
              font-weight: 600;
              margin-right: 8px;
              color: #262626;
            `}>议题名称：</span>
            <span className={css`
              color: #262626;
            `}>{topic.topicsName || '内容内容内容内容内容内容内容内容内容'}</span>
          </div>

          {/* 议题详细信息 */}
          <div className={css`
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 16px;
            color: #666;
            font-size: 14px;
            margin-bottom: 12px;
          `}>
            <div>
              <span className={css`font-weight: 500;`}>议题时长：</span>
              <span>{topic.topicsDuration || 30}分钟</span>
            </div>
            <div>
              <span className={css`font-weight: 500;`}>议题开始时间：</span>
              <span>{topic.topicsStartTime ? moment(topic.topicsStartTime).format('YYYY年MM月DD日 HH:mm') : '2025年12月12日 12:33'}</span>
            </div>
            <div>
              <span className={css`font-weight: 500;`}>汇报人：</span>
              <span>{topic.reportList && topic.reportList.length > 0 ? topic.reportList.map(r => r.name).join('、') : '张彬'}</span>
            </div>
          </div>

          <div className={css`
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            color: #666;
            font-size: 14px;
          `}>
            <div>
              <span className={css`font-weight: 500;`}>列席联系人：</span>
              <span>{topic.attendanceList && topic.attendanceList.length > 0 ? topic.attendanceList.map(a => a.name).join('、') : '张彬'}</span>
            </div>
            <div>
              <span className={css`font-weight: 500;`}>列席单位：</span>
              <span>{topic.attendanceDept && topic.attendanceDept.length > 0 ? topic.attendanceDept.map(d => d.deptName || d.name).join('、') : '内容内容'}</span>
            </div>
          </div>
        </Card>
      </div>
    )
  }

  return (
    <div className={css`
      min-height: 100vh;
      background: #f5f5f5;
      padding: 24px;
    `}>
      <Spin spinning={loading}>
        {renderMeetingInfo()}

        {/* 议题列表 */}
        <div className={css`
          background: #fff;
          padding: 24px;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        `}>
          <h3 className={css`
            margin: 0 0 24px 0;
            font-size: 16px;
            font-weight: 600;
          `}>议题流程</h3>

          {meetingData && meetingData.topicsList && meetingData.topicsList.length > 0 ? (
            meetingData.topicsList.map((topic, index) => renderTopicCard(topic, index))
          ) : (
            <div className={css`
              text-align: center;
              color: #999;
              padding: 40px;
            `}>
              暂无议题
            </div>
          )}
        </div>
      </Spin>
    </div>
  )
}

export default AgendaManage
