import React from 'react'
import { css, cx } from 'emotion'
import { Button } from 'antd'

const PageHeader = ({
  title,
  extra,
  hasBack,
  onBack = () => {
    history.back()
  },
  className,
}) => {
  return (
    <div
      className={cx(
        css`
          padding: 12px 0px;
          color: #262a30;
          font-size: 16px;
          display: flex;
          background-color: #fff;
          align-items: center;
        `,
        className,
      )}
    >
      <div
        className={css`
          flex: 1;
          display: flex;
          align-items: center;
          font-size: 16px;
          color: #000000;
          font-weight: 500;
        `}
      >
        {hasBack && (
          <Button
            size="small"
            onClick={onBack}
            className={css`
              margin-right: 8px;

            `}
          >
            返回
          </Button>
        )}
        {title}
      </div>
      {extra && (
        <div
          className={css`
            flex-shrink: 0;
          `}
        >
          {extra}
        </div>
      )}
    </div>
  )
}

export default PageHeader
