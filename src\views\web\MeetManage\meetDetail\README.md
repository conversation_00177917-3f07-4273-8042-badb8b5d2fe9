# 会议管理详情页

## 功能概述

这是一个会议管理的详情页面，用于创建和编辑会议信息。整个页面是一个完整的表单，包含会议基本信息和议题列表，支持拖拽排序和自动时间计算。

## 页面结构

### 1. 会议基本信息
- **会议名称**: 必填，输入会议的名称
- **经办人**: 必填，输入会议的经办人
- **会议日期**: 必填，选择会议举行的日期
- **会议室**: 必填，输入会议室信息
- **参会领导**: 必填，选择参会的领导
- **会议类型**: 必填，输入会议的类型

### 2. 议题列表（表单数组字段）
- **下载模板**: 下载议题导入模板
- **批量导入**: 批量导入议题信息
- **新增议题**: 手动添加单个议题
- **拖拽排序**: 支持拖拽调整议题顺序
- **自动计算时间**: 根据上一个议题的开始时间和时长自动计算新议题的开始时间

#### 议题信息包含：
- 汇报人（必填）
- 开始时间（必填，自动计算）
- 汇报时长（必填，单位：分钟，默认30分钟）
- 议题名称（必填）
- 汇报单位（必填）
- 列席联系人（可选）
- 列席单位（可选）

### 3. 操作按钮
- **取消**: 返回上一页
- **确定**: 保存整个表单数据（包括基本信息和议题列表）

## 核心特性

### 🕒 智能时间计算
- 第一个议题默认从09:00开始
- 新增议题时自动计算开始时间 = 上一个议题开始时间 + 时长
- 修改议题时间或时长时，自动更新后续所有议题的开始时间
- 拖拽排序后重新计算所有议题的时间顺序

### 🎯 拖拽排序
- 使用react-sortable-hoc实现拖拽排序
- 拖拽后自动重新计算时间顺序
- 视觉反馈：拖拽时显示虚线边框

### 📝 表单一体化
- 整个页面是一个Formily表单
- 议题列表作为数组字段集成在表单中
- 统一验证和提交

### 🎨 内联编辑
- 新增议题直接在列表下方添加卡片
- 点击编辑按钮切换到编辑模式
- 编辑状态下显示表单控件
- 保存/取消操作就近显示

### 📋 表单分块
- 会议基本信息和议题列表分为两个独立的块
- 每个块都有明确的标题
- 清晰的视觉层次和信息组织

## 组件结构

```
meetDetail/
├── index.js              # 主页面组件
├── index.css             # 页面样式
├── schema.js             # Formily表单配置
├── README.md             # 说明文档
└── components/
    ├── AgendaSection.js   # 议题数组字段组件（包含显示和编辑卡片）
    ├── FormActions.js     # 底部操作按钮组件
    └── FormBlock.js       # 表单块组件（带标题）
```

## 技术栈

- **React**: 主要框架
- **Formily**: 表单解决方案
- **Ant Design**: UI组件库
- **Emotion**: CSS-in-JS样式方案

## 使用方法

1. 填写会议基本信息
2. 添加议题信息（可通过新增按钮或批量导入）
3. 编辑或删除已添加的议题
4. 点击确定保存会议信息

## 特性

- **响应式设计**: 支持移动端和桌面端
- **表单验证**: 必填字段验证
- **议题管理**: 支持增删改查操作
- **批量操作**: 支持模板下载和批量导入
- **用户友好**: 操作确认和成功提示

## 扩展功能

可以根据需要扩展以下功能：
- 会议室预约冲突检测
- 参会人员邮件通知
- 会议纪要模板生成
- 议题时间自动计算
- 会议状态流转管理
