import React from 'react'
import { Button, Space } from 'antd'
import { css } from 'emotion'

const FormActions = ({
  onCancel,
  onSubmit,
  loading = false,
  cancelText = '取消',
  submitText = '确定'
}) => {
  return (
    <div className={css`
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      display: flex;
      justify-content: center;
      padding: 8px;
      background: #fff;
      border-top: 1px solid #f0f0f0;
      box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
      z-index: 1000;
    `}>
      <Space size="middle">
        <Button
          size="small"
          onClick={onCancel}
          className={css`
            height: 30px;
          `}
        >
          {cancelText}
        </Button>
        <Button
          type="primary"
          size="small"
          loading={loading}
          onClick={onSubmit}
          className={css`
            height: 30px;
          `}
        >
          {submitText}
        </Button>
      </Space>
    </div>
  )
}

export default FormActions
