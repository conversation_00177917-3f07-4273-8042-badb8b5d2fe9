/* 会议管理页面样式 */
.meet-manage-container {
  background: #f7f8f9;
  /* min-height: 100vh; */
}

.meet-manage-form {
  background: #fff;
  border-radius: 4px;
  padding: 24px;
  margin: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.meet-manage-title {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 24px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

/* 表单项样式优化 */
.ant-formily-item-label {
  font-weight: 500;
  color: #262626;
}

.ant-formily-item-required::before {
  color: #ff4d4f;
}

/* 议题表格样式 */
.agenda-table .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
  color: #262626;
}

.agenda-table .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

/* 操作按钮样式 */
.form-actions {
  text-align: center;
  padding: 24px 0;
  border-top: 1px solid #f0f0f0;
  margin-top: 24px;
}

.form-actions .ant-btn {
  min-width: 88px;
  height: 40px;
  font-size: 14px;
}

/* 拖拽排序样式 */
.row-dragging {
  background: #fafafa !important;
  border: 1px dashed #1890ff !important;
}

.row-dragging td {
  padding: 16px !important;
  border-bottom: none !important;
}

/* 议题卡片样式 */
.agenda-item {
  transition: all 0.3s ease;
}

.agenda-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 拖拽手柄样式 */
.drag-handle {
  cursor: grab;
  color: #999;
  font-size: 12px;
  padding: 4px;
}

.drag-handle:hover {
  color: #1890ff;
}

.drag-handle:active {
  cursor: grabbing;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .meet-manage-form {
    margin: 8px;
    padding: 16px;
  }

  .agenda-table {
    font-size: 12px;
  }

  .form-actions .ant-btn {
    min-width: 80px;
    height: 36px;
  }

  /* 移动端议题卡片布局调整 */
  .agenda-item .ant-row {
    flex-direction: column;
  }

  .agenda-item .ant-col {
    width: 100% !important;
    margin-bottom: 8px;
  }
}
