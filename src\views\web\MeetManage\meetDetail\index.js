import React, { useEffect, useState, useMemo } from 'react'
import { Spin, message, TimePicker, InputNumber } from 'antd'
import { Input, Select, Radio, FormItemGrid, DatePicker, FormCard, FormBlock, FormMegaLayout } from '@formily/antd-components'
import { SchemaMarkupForm, createAsyncFormActions } from '@formily/antd'
import { useParams, useHistory } from 'react-router-dom'
import SelectDept from 'ROOT/components/Formily/deptSelect'
import UserSelect from 'ROOT/components/Formily/userSelect'
import EditableInput from 'ROOT/components/Formily/Editable'
import { css } from 'emotion'
// import PageHeader from '../components/pageHeader'
import schema from './schema'
import AgendaArrayField from './components/AgendaSection'
import FormActions from './components/FormActions'
import service from 'ROOT/service'
import './index.css'
const MeetManage = () => {
  const { id } = useParams()
  const history = useHistory()
  const [loading, setLoading] = useState(false)
  const [editable, setEditable] = useState(true)
  const [initValue, setInitValue] = useState({})
  const [isEdit, setIsEdit] = useState(false)
  const actions = useMemo(() => createAsyncFormActions(), [])

  const uesEffects = () => {
    // 可以在这里添加表单联动效果
  }

  // 数据回填
  useEffect(() => {
    const fetchData = async () => {
      if (id && id !== 'new') {
        try {
          setLoading(true)
          setIsEdit(true)
          const response = await service.getMeetingDetail({ meetingId: })

          if (response) {
            setInitValue(response)
            // 设置表单初始值
            actions.setFormState(state => {
              state.values = response
            })
          }
        } catch (error) {
          console.error('获取会议详情失败:', error)
          message.error('获取会议详情失败')
        } finally {
          setLoading(false)
        }
      } else {
        // 新建模式
        setIsEdit(false)
        const defaultValues = {
          topicsList: [] // 默认空的议题列表
        }
        setInitValue(defaultValues)
        actions.setFormState(state => {
          state.values = defaultValues
        })
      }
    }
    fetchData()
  }, [id, actions])
  const expressionScope = {

  }

  // 标记自定义组件为字段组件
  AgendaArrayField.isFieldComponent = true

  // 自定义组件注册给 formily 使用
  UserSelect.isFieldComponent = true

  const components = {
    Input,
    Select,
    DatePicker,
    FormCard,
    SelectDept,
    UserSelect,
    EditableInput,
    AgendaArrayField,
    TimePicker,
    InputNumber,
    FormBlock,
    FormMegaLayout,
    'mega-layout': FormMegaLayout,
  }

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      setLoading(true)

      // 验证表单
      await actions.validate()

      // 获取表单数据
      const formState = await actions.getFormState()
      const values = formState.values
      console.log('表单数据:', values)

      // 额外验证议题数据
      if (values.agendaList && values.agendaList.length > 0) {
        const invalidAgendas = []
        values.agendaList.forEach((agenda, index) => {
          const errors = []
          if (!agenda.reporter) errors.push('汇报人')
          if (!agenda.startTime) errors.push('开始时间')
          if (!agenda.duration) errors.push('汇报时长')
          if (!agenda.agendaName) errors.push('议题名称')
          if (!agenda.reportUnit) errors.push('汇报单位')

          if (errors.length > 0) {
            invalidAgendas.push(`议题${index + 1}: ${errors.join('、')}不能为空`)
          }
        })

        if (invalidAgendas.length > 0) {
          message.error(`请完善以下信息：\n${invalidAgendas.join('\n')}`)
          return
        }
      }
      // 数据转换函数：将当前数据结构转换为目标数据结构
      const transformDataForSubmit = (formData) => {
        // 转换用户数据
        const transformUserList = (userList) => {
          if (!Array.isArray(userList)) return []
          return userList.map(user => ({
            uid: user.id,
            name: user.name
          }))
        }

        // 转换部门数据
        const transformDeptList = (deptList) => {
          if (!Array.isArray(deptList)) return []
          return deptList.map(dept => ({
            orgId: dept.orgId,
            orgName: dept.fullname ? dept.fullname.split('-')[0] : '区公司', // 从 fullname 中提取组织名称
            deptId: dept.id,
            deptName: dept.name
          }))
        }

        // 转换议题列表
        const transformTopicsList = (topicsList) => {
          if (!Array.isArray(topicsList)) return []
          return topicsList.map((topic, index) => ({
            topicsName: topic.topicsName,
            topicsTitle: `议题${index + 1}`,
            topicsStartTime: new Date(topic.topicsStartTime).getTime(),
            topicsDuration: topic.topicsDuration,
            sequence: index + 1,
            reportDept: transformDeptList(topic.reportDept),
            reportList: transformUserList(topic.reportList),
            attendanceDept: transformDeptList(topic.attendanceDept),
            attendanceList: transformUserList(topic.attendanceList)
          }))
        }

        return {
          name: formData.name,
          meetingDate: new Date(formData.startTime).getTime(),
          meetingType: formData.meetingType,
          meetingRoom: formData.meetingRoom,
          startTime: new Date(formData.startTime).getTime(),
          leaderList: transformUserList(formData.leaderList),
          operatorList: transformUserList(formData.operatorList),
          topicsList: transformTopicsList(formData.topicsList)
        }
      }

      // 使用转换函数处理数据
      const param = transformDataForSubmit(values)

      console.log('转换后的数据:', param)

      await service.saveMeeting(param)
      message.success(isEdit ? '更新成功' : '创建成功')

      // 保存成功后返回列表页
      history.push('/web/MeetManage/meetList')
    } catch (error) {
      console.error('提交失败:', error)
      if (error.name === 'ValidateError') {
        console.log('验证错误详情:', error)
        message.error('请检查表单填写是否完整')
      } else {
        message.error(isEdit ? '更新失败' : '创建失败')
      }
    } finally {
      setLoading(false)
    }
  }

  // 处理取消
  const handleCancel = () => {
    history.push('/web/MeetManage/meetList')
  }
  return (
    <div className="meet-manage-container">
      <Spin
        spinning={loading}
        style={{ height: '100vh', overflow: 'hidden', maxHeight: 'initial' }}
      >
        {/* <PageHeader
          title={'创建会议'}
          hasBack={true}
        /> */}
        <div
          className={css`
          overflow: auto;
          height: 100%;
        `}
        >
          <div className="meet-manage-form" style={{ paddingBottom: '80px' }}>
            <div className={css`
                font-size: 18px;
                font-weight: 500;
                color: #262626;
                margin-bottom: 24px;
                text-align: center;
              `}>{isEdit ? '编辑会议' : '创建会议'}</div>
            <div className={css`
                font-size: 16px;
                font-weight: 500;
                color: #262626;
                margin-bottom: 16px;
              `}>会议基本信息</div>
            <SchemaMarkupForm
              schema={schema()}
              components={components}
              actions={actions}
              effects={() => {
                uesEffects()
              }}
              initialValues={initValue}
              expressionScope={{ ...expressionScope }}
              previewPlaceholder='-'
              editable={editable}
            >
            </SchemaMarkupForm>
          </div>

          <FormActions
            onCancel={handleCancel}
            onSubmit={handleSubmit}
            loading={loading}
          />
        </div>
      </Spin>
    </div>
  )
}

export default MeetManage
