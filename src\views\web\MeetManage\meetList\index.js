import React, { useEffect, useState, useRef } from 'react'
import { Table, Button, Spin, Modal, message } from 'antd'
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons'
import { css } from 'emotion'
import { useHistory } from 'react-router-dom'
import PageHeader from '../components/pageHeader'
import Search from '../components/search'
import service from 'ROOT/service'
import moment from 'moment'
import './index.less'

const MeetManage = () => {
  const history = useHistory()
  const [loading, setLoading] = useState(false)
  const [searchValues, setSearchValues] = useState({})
  const [dataSource, setDataSource] = useState([])
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  })

  const searchRef = useRef(null)
  const columns = [
    { title: '序号', dataIndex: 'index', key: 'index', align: 'center' },
    { title: '会议日期', dataIndex: 'meetingDate', key: 'meetingDate', align: 'center', render: (text) => { 
      // 时间戳转时间
      return moment(text).format('YYYY-MM-DD')
     } },
    { title: '会议名称', dataIndex: 'name', key: 'name', align: 'center' },
    { title: '会议状态', dataIndex: 'meetingStatus', key: 'meetingStatus', align: 'center', render: (text) => {
      // 状态码转文字
      const statusMap = {
       0: '未开始',
       1: '准备中',
       2: '进行中',
       3: '已结束',
      }
      return statusMap[text] || '未知'
     } },
    { title: '会议类型', dataIndex: 'meetingType', key: 'meetingType', align: 'center' },
    { title: '会议室', dataIndex: 'meetingRoom', key: 'meetingRoom', align: 'center' },
    { title: '开始时间', dataIndex: 'startTime', key: 'startTime', align: 'center', render: (text) => {
      // 时间戳转时间
      return moment(text).format('YYYY-MM-DD HH:mm')
     } },
    { title: '参会领导', dataIndex: 'leaderList', key: 'leaderList', align: 'center', render: (text) => {
      // 处理参会领导
      return text? text.map(item => item.name).join(',') : ''
     } },
    { title: '参会经办', dataIndex: 'operatorList', key: 'operatorList', align: 'center', render: (text) => {
      // 处理参会经办
      return text ? text.map(item => item.name).join(',') : ''
     } },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      width: 260,
      render: (_, record) => {
        return (
          <div>
            <Button
              type="link"
              onClick={() => handleAgenda(record)}
            >
              议题管理
            </Button>
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            >
              编辑
            </Button>
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record)}
            >
              删除
            </Button>
          </div>
        )
      }
    }
  ]
  const handleAgenda = (record) => {
    history.push(`/web/MeetManage/agenda/${record.id}`)
  }
  // 获取会议列表数据
  const getData = async (params = {}) => {
    try {
      setLoading(true)
      const requestParams = {
        ...searchValues,
        ...params,
        pageIndex: pagination.current,
        pageSize: pagination.pageSize,
      }

      const response = await service.getMeetingList(requestParams)

      if (response && response.list) {
        // 添加序号
        const dataWithIndex = response.list.map((item, index) => ({
          ...item,
          index: (pagination.current - 1) * pagination.pageSize + index + 1,
        }))

        setDataSource(dataWithIndex)
        setPagination(prev => ({
          ...prev,
          total: response.total || 0,
        }))
      } else {
        setDataSource([])
        setPagination(prev => ({
          ...prev,
          total: 0,
        }))
      }
    } catch (error) {
      console.error('获取会议列表失败:', error)
      message.error('获取会议列表失败')
      setDataSource([])
    } finally {
      setLoading(false)
    }
  }

  // 处理编辑
  const handleEdit = (record) => {
    history.push(`/web/MeetManage/meetDetail/${record.id}`)
  }

  // 处理删除
  const handleDelete = (record) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除会议"${record.meetingName}"吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          setLoading(true)
          await service.deleteMeeting({ meetingId: record.id })
          message.success('删除成功')
          getData() // 重新获取数据
        } catch (error) {
          console.error('删除失败:', error)
          message.error(`删除失败: ${error.msg || '未知错误'}`)
        } finally {
          setLoading(false)
        }
      },
    })
  }

  // 处理创建会议
  const handleCreate = () => {
    history.push('/web/MeetManage/meetDetail/new')
  }
  // 处理搜索提交
  const onSubmit = (values) => {
    setSearchValues(values)
    setPagination(prev => ({ ...prev, current: 1 }))
    getData(values)
  }

  // 处理搜索重置
  const onReset = () => {
    setSearchValues({})
    setPagination(prev => ({ ...prev, current: 1 }))
    getData({})
  }

  // 处理分页变化
  const handleTableChange = (paginationInfo) => {
    setPagination(prev => ({
      ...prev,
      current: paginationInfo.current,
      pageSize: paginationInfo.pageSize,
    }))
    getData()
  }

  useEffect(() => {
    getData()
  }, [pagination.current, pagination.pageSize])
  return (
    <div className={css`
      margin-top: -86px;
      margin-left: -59px;
      margin-right: -59px;
      padding: 20px;
    `}>
      <Spin
        spinning={loading}
        style={{ height: '100vh', overflow: 'hidden', maxHeight: 'initial' }}
      >
        <PageHeader
          title={'会议管理'}
          hasBack={true}
          extra={
            <Button icon={<PlusOutlined/>} type='primary' onClick={handleCreate}>
              创建会议
            </Button>
          }
        />
        <div
          className={css`
          overflow: auto;
        `}
        >
          <div className="box meetManage-search-table">
            <Search onSubmit={onSubmit} onReset={onReset} ref={searchRef} />
            <Table
              columns={columns}
              rowKey="id"
              dataSource={dataSource}
              pagination={{
                ...pagination,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
              }}
              onChange={handleTableChange}
            />
          </div>
        </div>
      </Spin>
    </div>

  )
}

export default MeetManage
