import React, { useEffect, useState, useMemo } from 'react';
import { SchemaMarkupForm, FormButtonGroup, createAsyncFormActions, createFormActions, FormEffectHooks } from '@formily/antd'
import { Input, NumberPicker, FormMegaLayout, Select, Radio, FormItemGrid, DatePicker } from '@formily/antd-components'
import schema from './schema'
import {
  Tabs,
  message
} from 'antd'
import { Buttons, Steps } from 'ROOT/components/Process'
import { getQueryString, closeTheWindow } from 'ROOT/utils'
// import useSigner from 'ROOT/hooks/useSigner'
import service from 'ROOT/service';
import { getLocalTime, getLocalTimeDay } from 'ROOT/constants/index'
import { meetingType } from '../module/config'
import { getWpsUrl } from 'ROOT/utils/wps'
import SelectDept from 'ROOT/components/Formily/SelectDept'
import PersonalInfo from 'ROOT/components/Formily/PersonalInfo'
import Upload from 'ROOT/components/Formily/Upload'
import Dept from 'ROOT/components/Formily/Dept'
import SelectMember from 'ROOT/components/Formily/userSelect'
import Text from 'ROOT/components/Formily/Text'
import Editor from 'ROOT/components/Formily/Editor'
import { formConfig } from '../common/PCDIconfig'
import EditableInput from 'ROOT/components/Formily/Editable'
import WpsEditor from 'ROOT/components/Formily/WpsEditor'
import moment from 'moment'
import MainText from 'ROOT/components/MainText'
import { getHtmlCode } from 'ROOT/utils'
import { debounce, isEmpty } from 'lodash'
import { patchData } from 'ROOT/utils/index'

const { TabPane } = Tabs
const { onFieldValueChange$, onFormValuesChange$, onFieldInputChange$ } = FormEffectHooks

export default (props) => {
  const typeIndex = 4;
  const [initValue, setInitValue] = useState({});
  const [buttonGroupList, setButtonGroupList] = useState([])
  const [content, setContent] = useState('')
  const [editable, setEditable] = useState(false)
  const [domKey, setDomKey] = useState()
  const [meetTitle, setMeetTitle] = useState('')
  const actions = useMemo(() => createAsyncFormActions(), []);
  const { userTaskId, userTaskStutus, appId, procFormDataKey, userTaskFlags, debug } = useMemo(() => getQueryString(props.location.search), []);
  // const { signer, date } = useSigner({
  //   userTaskId,
  //   nodeName: meetingType[typeIndex].signerNodeName,
  // });

  SelectDept.isFieldComponent = true;
  PersonalInfo.isFieldComponent = true;
  Dept.isFieldComponent = true;
  Upload.isFieldComponent = true
  SelectMember.isFieldComponent = true
  Text.isFieldComponent = true
  Editor.isFieldComponent = true
  EditableInput.isFieldComponent = true
  WpsEditor.isFieldComponent = true

  const [ableEditBtn, setAbleEditBtn] = useState([])

  useEffect(() => {
    if (debug) {
      ableEditForm()
    }
  }, [])

  useEffect(() => {
    document.title = meetTitle || '纪委会议通知'
    if (document.getElementById('breadnav')) {
      document.getElementById('breadnav').innerHTML = meetTitle || '纪委会议通知'
    }
  }, [meetTitle])

  const ableEditForm = async () => {
    const res = await service.getEditableForm()
    if (res.success && res.data) {
      resetFormState()
      // writableAllFields()
      setAbleEditBtn([{
        name: '更新数据',
        async: true,
        onClick: async () => {
          await saveForm(() => {
            closeTheWindow(props.location.search)
          }, true)
        }
      }])
    }
  }

  function writableAllFields() {
    actions.setFieldState('*', state => {
      state.editable = true
      state.disabled = false
    })
  }

  // useEffect(() => {
  //   if (Object.keys(initValue).length > 0) {
  //     actions.setFormState(state => {
  //       state.values = (signer || date) ? { ...initValue, signer, issuanceTime: getLocalTimeDay(date) } : initValue
  //     })
  //   }
  // }, [initValue])

  // 判断是否有签发人以及签发时间
  // useEffect(() => {
  //   actions.setFieldState('*(signer)', state => {
  //     state.value = signer || '-'
  //   })
  //   actions.setFieldState('*(issuanceTime)', state => {
  //     state.value = getLocalTimeDay(date) || '-'
  //   })
  // }, [signer, date])

  //  数据回填
  useEffect(async () => {
    actions.setFieldState('*', state => {
      state.props.description = null;
    });
    if (procFormDataKey) {
      const wpsContent = await getWpsUrl(procFormDataKey)
      service.getFormData({
        reportId: procFormDataKey,
        userTaskId,
      }).then((res) => {
        if (res && res.data) {
          if (wpsContent) {
            res.data.data.fileList[0] = wpsContent
          }
          let data = res.data.data;
          if (data && data.rangePicker) {
            data.startTime = data.hasOwnProperty('startTime') ? data.startTime : data.rangePicker[0]
            data.endTime = data.hasOwnProperty('endTime') ? data.endTime : data.rangePicker[1]
          }
          let operator = res.data.operator;
          actions.setFormState(state => {
            state.values = { ...data }
          })
          setInitValue(data)
          const { formTitle, meetingCount } = data || {}
          setMeetTitle(formTitle) // 审批表单走 详情数据
          if (meetingCount) {
            actions.getFieldValue('serialNumber').then(data => {
              actions.setFieldValue('serialNumber', data.replace(' ', `${meetingCount > 9 ? meetingCount : "0" + meetingCount}`))
            })
          }
          actions.setFieldState('fileList', state => {
            state.props['x-component-props'].reportId = procFormDataKey
          })
          const businessData = res.data.businessData
          if (businessData) {
            console.log(businessData, 'businessData')
            actions.setFieldState('*(signer)', state => {
              state.value = businessData.IssuerName || '-'
            })
            actions.setFieldState('*(issuanceTime)', state => {
              state.value = businessData.IssuerTime || '-'
            })
          }
        }
      })
    }
  }, [])

  const commonCommit = async () => {
    const data = await actions.submit()
    const result = patchData(data.values, initValue)
    const res = await service.upDateForm({
      config: formConfig,
      data: result,
      reportId: procFormDataKey,
    })
    return res
  }

  const submitForm = async () => {
    const res = await commonCommit()
    if (res.success) {
      return Promise.resolve()
    }
    throw new Error('保存出错')
  }

  const saveForm = async (callback) => {
    const res = await commonCommit()
    if (res.success) {
      message.success('操作成功', () => {
        if (typeof callback === 'function') callback(res)
      })
    }
  }

  const resetFormState = () => {
    setEditable(true)
    actions.setFieldState('*(_dept)', state => {
      state.props['x-component'] = 'EditableInput'
    })
  }

  const onMount = ({ access, editMode, draft, process }) => {
    console.log('process', process);
    const { nodeName, taskStatus } = process || {}
    if (nodeName === '开始节点' && taskStatus === 'running') {
      const btns = [
        {
          name: '保存退出',
          async: false,
          onClick: () => {
            saveForm(() => {
              closeTheWindow(props.location.search)
            })
          }
        },
        {
          name: '保存',
          async: false,
          onClick: () => {
            saveForm('')
          }
        }
      ]
      setButtonGroupList([...btns])
    }
    if (access && Object.keys(access).length > 0 && editMode && editMode.includes('web')) {
      Object.keys(access).forEach(key => {
        if (key === 'isEditable' && access[key] === 'WRITE') {
          resetFormState()
          return
        }
        actions.setFieldState(key, state => {
          switch (access[key]) {
            case 'NONE':
              state.display = false
              break
            case 'READ':
              state.editable = false
              break
            case 'WRITE':
              state.editable = true
              state.display = true
              break
            default: break
          }
        })
      })
    } else {

    }

    if (draft) {
      actions.setFormState(state => {
        state.values = draft
      })
    }
  }
  useEffect(() => {
    window.addEventListener('process', e => {
      switch (e.detail.type) {
        case 'saved': break;
        case 'approved': // 审批后
        case 'added': // 加签后
        case 'stoped': // 终止后
        case 'rejected': // 退回后
        case 'forworded': // 转交后
        case 'cced': // 抄送后
        default: finishSubmit()
      }
    })

  }, [])

  const finishSubmit = () => {
    setTimeout(() => {
      closeTheWindow(props.location.search)
    }, 2000)
  }

  const uesEffects = () => {
    const debounceHandleFormValueChange = debounce((data) => {
      setDomKey(Math.random())
    }, 500)

    onFormValuesChange$().subscribe(debounceHandleFormValueChange)

    onFieldValueChange$('fileList').subscribe(({ value }) => {
      if (value && value.length > 0) {
        if (value[0].needConvert) {
          getHtmlCode(value[0].url, (html) => {
            setContent(html)
          })
        } else {
          setContent(value[0].html)
        }
      }
    })

  }

  const expressionScope = {
    getColor: (text, color) => { return <div style={{ color: `${color}` }}>{text}</div> },
    labelAlign: 'left',
    disabledDate: (current) => { return current && current <= moment().subtract(1, 'days').endOf('day') },
  }

  const components = {
    TextArea: Input.TextArea,
    Input,
    NumberPicker,
    FormMegaLayout,
    Upload,
    Select,
    Radio,
    RadioGroup: Radio.Group,
    RangePicker: DatePicker.RangePicker,
    DatePicker,
    SelectDept,
    PersonalInfo,
    Dept,
    SelectMember,
    Text,
    Editor,
    EditableInput,
    WpsEditor
  }
  return (
    <div>
      <h1 className='form-title'>{meetTitle}</h1>
      <SchemaMarkupForm
        schema={schema(debug)}
        components={components}
        actions={actions}
        expressionScope={{ ...expressionScope }}
        previewPlaceholder='-'
        editable={editable}
        effects={() => {
          uesEffects()
        }}
      >
        <div>
          {
            !isEmpty(initValue) && (
              <Buttons
                userTaskId={userTaskId}
                onMount={onMount}
                onSubmitOpen={async () => {
                  const data = await actions.submit()
                  const result = patchData(data.values, initValue)
                  return result
                }}
                extraButtons={buttonGroupList.concat(ableEditBtn)}
                onSubmit={submitForm}
              />
            )
          }
        </div>
      </SchemaMarkupForm>
      <Tabs defaultActiveKey="1">
        <TabPane tab="审批流程" key="1">
          <Steps
            userTaskId={userTaskId}
          />
        </TabPane>
      </Tabs>
      <MainText key={domKey} title={meetTitle} content={content} formType='meetNotice' actions={actions} />
    </div>
  )
}