import React, { useEffect, useState, useMemo, useRef } from 'react'
import { SchemaMarkupForm, FormButtonGroup, Submit, Reset, createAsyncFormActions, createFormActions, FormEffectHooks } from '@formily/antd'
import { Input, NumberPicker, FormMegaLayout, Select, Radio, FormItemGrid, DatePicker } from '@formily/antd-components'
import { Button, message, Modal, Space } from 'antd'
import { getQueryString, closeTheWindow, emergencyLevel } from 'ROOT/utils'
import getUserOrgType from 'ROOT/hooks/getUserOrgType'
import SelectDept from 'ROOT/components/Formily/SelectDept'
import Upload from 'ROOT/components/Formily/Upload'
import SelectMember from 'ROOT/components/Formily/userSelect'
import PersonalInfo from 'ROOT/components/Formily/PersonalInfo'
import Text from 'ROOT/components/Formily/Text'
import Dept from 'ROOT/components/Formily/Dept'
import { Buttons } from 'ROOT/components/Process'
import Editor from 'ROOT/components/Formily/Editor'
import service from 'ROOT/service'
import { getWpsUrl } from 'ROOT/utils/wps'
import { getLocalTime, getLocalTimeDay } from 'ROOT/constants/index'
import { closeBtn, deleteBtn, saveAndExit } from '../module/buttons'
import { formConfig, getMeetingSerialNumber } from '../common/PCDIconfig'
import { meetingType, getMeetingTitle } from '../module/config'
import schema from './schema'
import useMyInfo from 'ROOT/hooks/useMyInfo'
import EditableInput from 'ROOT/components/Formily/Editable'
import WpsEditor from 'ROOT/components/Formily/WpsEditor'
import moment from 'moment'
import MainText from 'ROOT/components/MainText'
import { getHtmlCode } from 'ROOT/utils'
import { debounce, isEmpty } from 'lodash'
import { patchData } from 'ROOT/utils/index'

const { confirm } = Modal

const { onFieldValueChange$, onFieldInputChange$, onFormValuesChange$ } = FormEffectHooks


export default (props) => {
  const typeIndex = 2
  const typeName = meetingType[typeIndex].name
  const [meetTitle, setMeetTitle] = useState('')
  const [initValue, setInitValue] = useState({});
  const [editable, setEditable] = useState(true);
  const [content, setContent] = useState('')
  const [domKey, setDomKey] = useState()
  const actions = useMemo(() => createAsyncFormActions(), []);
  const userOrgList = getUserOrgType()
  const { userTaskId, procKey, appId, procFormDataKey } = useMemo(() => getQueryString(props.location.search), []);
  const myInfo = useMyInfo({ isSetStorage: true })
  const formId = useRef(+procFormDataKey)

  SelectDept.isFieldComponent = true;
  PersonalInfo.isFieldComponent = true;
  Dept.isFieldComponent = true;
  Upload.isFieldComponent = true
  SelectMember.isFieldComponent = true
  Text.isFieldComponent = true
  Editor.isFieldComponent = true
  EditableInput.isFieldComponent = true
  WpsEditor.isFieldComponent = true

  useEffect(() => {
    document.title = meetTitle || '党委会议通知'
    if (document.getElementById('breadnav')) {
      document.getElementById('breadnav').innerHTML = meetTitle || '党委会议通知'
    }
  }, [meetTitle])

  useEffect(() => {
    const { loginOrgId, cityOrgName, loginOrgName } = myInfo
    if (userOrgList && loginOrgId) {
      actions.setFieldValue('serialNumber', getMeetingSerialNumber(userOrgList, loginOrgId, cityOrgName))
    }
    const title = getMeetingTitle(userOrgList, loginOrgId, cityOrgName, loginOrgName, typeName)
    setMeetTitle(title)
    actions.setFieldValue('formTitle', title)
  }, [myInfo, userOrgList])

  //  数据回填
  useEffect(async() => {
    if (procFormDataKey) {
      const wpsContent = await getWpsUrl(procFormDataKey)
      service.getFormData({
        reportId: procFormDataKey, // 获取详情，也就是初始值
      }).then((res) => {
        if (res && res.data) {
          if (wpsContent) {
            res.data.data.fileList[0] = wpsContent
          }
          let data = res.data.data;
          if (data && data.rangePicker) {
            data.startTime = data.hasOwnProperty('startTime') ? data.startTime : data.rangePicker[0]
            data.endTime = data.hasOwnProperty('endTime') ? data.endTime : data.rangePicker[1]
          }
          setInitValue(data)
          resetContentProps(procFormDataKey)
          actions.setFormState(state => {
            state.values = data
          });
        }
      })
    }
  }, []);

  useEffect(() => {
    if (!procFormDataKey) {
      // actions.setFieldState('rangePicker', state => {
      //   state.value = [moment().format('YYYY-MM-DD HH:mm'), moment().add(1, 'days').format('YYYY-MM-DD HH:mm')]
      // })
      actions.setFieldState('startTime', state => {
        state.value = moment().format('YYYY-MM-DD HH:mm')
      })
      actions.setFieldState('endTime', state => {
        state.value = moment().add(1, 'days').format('YYYY-MM-DD HH:mm')
      })
      service.saveForm({
        type: meetingType[typeIndex].type,
        classify: {
          name: meetingType[typeIndex].name,
          englishName: meetingType[typeIndex].englishName,
        },
        config: formConfig,
        data: {
          _taskLevel: '3',
          isFeedback: '2',
          draftDate: moment().format('YYYY年MM月DD日'),
        },
      }).then(res => {
        const { data } = res
        resetContentProps(data)
        formId.current = data
      })
    }
  }, [])

  const resetUserInfo = () => {
    const { linkPath, loginName, loginOrgId, loginMobile } = myInfo
    if ((loginOrgId === 10032 || loginOrgId === 10016) && !procFormDataKey) {
      actions.setFieldState('meetingName', state => {
        state.value = `广西公司党委${moment().year()}年第次会议`
      })
      actions.setFieldState('meetingPlace', state => {
        state.value = '公司十一楼1101会议室'
      })
      actions.setFieldState('participants', state => {
        state.value = '党委委员、党委秘书'
      })
      actions.setFieldState('meetingHost', state => {
        state.value = '党委书记 王一秋'
      })
    }
    actions.setFieldState('user', state => {
      state.value = loginName
    })
    actions.setFieldState('phone', state => {
      state.value = loginMobile
    })
    actions.setFieldState('_dept', state => {
      state.props.enum = linkPath && linkPath.length > 0 ? linkPath.map(item => ({
        value: item.deptId,
        label: item.linkPath,
      })) : []
      if (initValue._dept && initValue._dept.value) {
        state.value = initValue._dept
      } else {
        state.value = linkPath && linkPath.length > 0 ? {
          value: linkPath[0].deptId,
          label: linkPath[0].linkPath,

        } : { value: '', label: '' }
      }
    })

  }
  useEffect(() => {
    resetUserInfo()
  }, [myInfo, procFormDataKey])

  const onMount = ({ access, editMode }) => {
    if (access && Object.keys(access).length > 0 && editMode && editMode.includes('web')) {
      Object.keys(access).forEach(key => {
        actions.setFieldState(key, state => {
          switch (access[key]) {
            case 'NONE':
              state.display = false
              break
            case 'READ':
              state.editable = false
              break
            case 'WRITE':
              state.editable = true
              state.display = true
              break
            default: break
          }
        })
      })
    } else {

    }
  }

  const saveDraft = async (callback) => {
    const info = localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo')) : {}

    const data = await actions.submit()
    const result = patchData(data.values, initValue)
    const res = await service.saveDraft({
      appId,
      appTasks: [{
        appTaskId: formId.current.toString(),
        businessType: 2,
        emergencyLevel: emergencyLevel[+result._taskLevel],
        handleEntry: [{
          handleType: 0, // 草稿
          handlerId: info.loginUid,
        }],
        processType: meetingType[typeIndex].name,
        sponsorId: info.loginUid,
        jumpToDetail: 1,
        title: meetTitle,
        detailUrl: decodeURIComponent(procFormDataKey ? `${location.href.replace('/extra-forms/', '/extra-forms-h5/').replace(/backurl=.*?&|backurl=.*?$/, '')}` : `${location.href.replace('/extra-forms/', '/extra-forms-h5/').replace(/backurl=.*?&|backurl=.*?$/, '')}&procFormDataKey=${formId.current}`),
        webDetailUrl: decodeURIComponent(procFormDataKey ? `${location.href.replace(/backurl=.*?&|backurl=.*?$/, '')}` : `${location.href.replace(/backurl=.*?&|backurl=.*?$/, '')}&procFormDataKey=${formId.current}`),
      }],
    })
    if (res.success) {
      message.success('操作成功', () => {
        if (typeof callback === 'function') callback(res)
      })
    }
  }

  const commonCommit = async () => {
    // todo 需要判断是新建表单的保存还是二次编辑表单的保存，走的接口不一样
    const data = await actions.submit()
    const result = patchData(data.values, initValue)
    const res = service.upDateForm({
      config: formConfig,
      data: result,
      reportId: formId.current,
    })

    return res
  }

  const resetContentProps = (procFormDataKey) => {
    actions.setFieldState('fileList', (state) => {
      state.props['x-component-props'] = {
        ...state.props['x-component-props'],
        reportId: procFormDataKey,
        type: meetingType[typeIndex].type,
        config: formConfig,
      }
    })
  }

  const submitForm = async () => {
    const res = await commonCommit()
    const data = await actions.submit()
    let values = data.values;
    if (res.success) {
      return +formId.current
    }
    throw new Error('保存出错')
  }

  const saveForm = async (callback, isDraft) => {
    const res = await commonCommit()
    if (res.success) {
      if (isDraft) {
        saveDraft(callback)
      } else {
        message.success('操作成功', () => {
          if (typeof callback === 'function') callback(res)
        })
      }
    }
  }

  const deleteDraft = async () => {
    const info = localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo')) : {}

    const res = await service.deleteDraft({
      appId,
      appTasks: [{
        appTaskId: formId.current.toString(),
        handlerIds: [info.loginUid],
      }],
    })
    if (res.success) {
      setTimeout(() => {
        message.success('操作成功', () => {
          closeTheWindow(props.location.search)
        })
      }, 4000)
    }
  }

  const deleteForm = async () => {
    confirm({
      content: '你选择了注销当前文档，系统将删除该文档，是否继续？',
      onOk: async () => {
        const res = await service.deleteForm({
          reportId: procFormDataKey,
        })
        if (res.success) {
          deleteDraft()
        } else {
          message.error(data.msg)
        }
      },
    })
  }
  useEffect(() => {
    window.addEventListener('process', () => finishSubmit())
  }, [])

  const finishSubmit = () => {
    deleteDraft()
  }

  const buttonGroup = () => {
    const array = [
      {
        name: '保存退出',
        async: false,
        onClick: () => {
          saveForm(() => {
            closeTheWindow(props.location.search)
          }, true)
        }
      },
      {
        name: '保存',
        async: false,
        onClick: () => {
          saveForm('', true)
        }
      }
    ]
    if (procFormDataKey) {
      array.push({
        name: '注销文档',
        async: false,
        onClick: () => {
          deleteForm()
        }
      })
    }
    return array
  }
  const expressionScope = {
    getColor: (text, color) => { return <div style={{ color: `${color}` }}>{text}</div> },
    labelAlign: 'top',
    disabledDate: (current) => { return current && current <= moment().subtract(1, 'days').endOf('day') },
  }

  const uesEffects = () => {
    const debounceHandleFormValueChange = debounce((data) => {
      setDomKey(Math.random())
    }, 500)

    onFormValuesChange$().subscribe(debounceHandleFormValueChange)

    onFieldValueChange$('fileList').subscribe(({ value }) => {
      if (value && value.length > 0) {
        if (value[0].needConvert) {
          getHtmlCode(value[0].url, (html) => {
            setContent(html)
          })
        } else {
          setContent(value[0].html)
        }
      }
    })
  }
  const components = {
    TextArea: Input.TextArea,
    Input,
    NumberPicker,
    FormMegaLayout,
    Upload,
    Select,
    Radio,
    RadioGroup: Radio.Group,
    RangePicker: DatePicker.RangePicker,
    DatePicker,
    SelectDept,
    PersonalInfo,
    Dept,
    Upload,
    SelectMember,
    Text,
    Editor,
    EditableInput,
    WpsEditor
  }
  return (
    <div>
      <h1 className='form-title'>{meetTitle}</h1>
      <SchemaMarkupForm
        schema={schema()}
        components={components}
        actions={actions}
        initialValues={initValue}
        expressionScope={{ ...expressionScope }}
        previewPlaceholder='-'
        editable={editable}
        effects={() => {
          uesEffects()
        }}
      >
        <div >
          {editable &&
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              {
                (!isEmpty(initValue) || !procFormDataKey) && (
                  <Space>
                    <Buttons
                      procKey={procKey}
                      appId={appId}
                      onMount={onMount}
                      onSubmitOpen={async () => {
                        const data = await actions.submit()
                        const result = patchData(data.values, initValue)
                        return result
                      }}
                      onSubmit={submitForm}
                      extraButtons={buttonGroup()}
                    />
                  </Space>
                )
              }
            </div>
          }
        </div>
      </SchemaMarkupForm>
      <MainText key={domKey} title={meetTitle} content={content} formType='meetNotice' actions={actions} />
    </div>
  )
}