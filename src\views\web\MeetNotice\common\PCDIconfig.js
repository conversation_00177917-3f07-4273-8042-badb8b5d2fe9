export const formConfig = {
  'formTitle': '表单标题',
  '_dept': '起草单位',
  'time': '拟稿时间',
  'user': '会议经办人',
  'signer': '签发人',
  'issuanceTime': '签发时间',
  'phone': '联系电话',
  'serialNumber': '编号',
  // 'meetingType': '会议类型',
  '_taskLevel': '缓急程度',
  'meetingName': '会议名称',
  'rangePicker': '开始以及结束时间',
  'meetingPlace': '会议地点',
  'participants': '参会人员',
  'meetingHost': '会议主持人',
  'remark': '备注',
  'upload': '附件',
  // 'message': '提示',
  'fileList': '会议内容',
}

export const getMeetingSerialNumber = (list, orgId, cityOrgName = '') => {
  console.log('list', list);
  if (list && orgId) {
    console.log(Object.keys(list), '----', list, orgId);
    let a = Object.keys(list).filter(data => {
      return list[data].includes(Number(orgId))
    })
    if (a.join() === 'areaOrgIds') {
      return `会议通知[${new Date().getFullYear()}] 号`
    }

    if (['cityOrgIds', 'otherOrgIds'].includes(a.join()) && cityOrgName) {
      if (cityOrgName === '桂林公司') {
        return `市移会议通知[${new Date().getFullYear()}] 号`
      }
      return `${cityOrgName[0]}移会议通知[${new Date().getFullYear()}] 号`
    }
  }
}