import React, { useEffect, useMemo } from 'react';
import { UPLOAD_URL } from 'ROOT/constants'
import locale from 'antd/lib/date-picker/locale/zh_CN';
import { formConfig } from './PCDIconfig'
import moment from 'moment'
export default ({
  listAllAction,
  myListAction,
  searchAction
}, debug) => {
  return {
    type: "object",
    properties: {
      layout: {
        "x-component": "mega-layout",
        type: 'object',
        "x-component-props": {
          autoRow: true,
          grid: true,
          // labelWidth: 120,
          labelAlign: '{{labelAlign}}',
          full: true,
          // labelCol: 24
        },
        properties: {
          formTitle: {
            key: 'formTitle',
            name: 'formTitle',
            display: false,
            default: '会议通知',
            'x-component': 'Input',
          },
          user: {
            type: "string",
            title: '起草人',
            'x-component': 'EditableInput',
            // editable: false,
          },
          _dept: {
            type: "string",
            title: '起草部门',
            'x-component': 'Dept',
            'x-component-props': {
              labelInValue: true,
            },
          },
          phone: {
            type: "string",
            title: '联系电话',
            'x-component': 'EditableInput',
            "x-rules": [{ format: "phone", message: "手机格式不正确" }],
            // editable: false,
          },
          time: {
            type: "string",
            title: '起草时间',
            'x-component': 'EditableInput',
            default: moment().format('YYYY年MM月DD日'),
          },
          signer: {
            type: "string",
            title: '签发人',
            'x-component': 'EditableInput',
            default: '',
            // editable: false
          },
          issuanceTime: {
            type: "string",
            title: '签发时间',
            'x-component': 'EditableInput',
            // editable: false
          },

          serialNumber: {
            type: "string",
            title: '编号',
            'x-component': 'EditableInput',
            // editable: false
          },
          _taskLevel: {
            type: "string",
            title: '缓急程度',
            'x-component': 'Select',
            default: '3',
            enum: [
              { label: '一般', value: '3' },
              { label: '急件', value: '2' },
              { label: '特急', value: '1' },
            ],
            'x-component-props': {
              style: {
                width: 100
              }
            }
          },
          startTime: {
            type: 'string',
            title: '开始时间',
            required: true,
            'x-component': 'DatePicker',
            'x-mega-props': {
              span: 1,
              // labelAlign: 'top',
            },
            "x-component-props": {
              showTime: true,
              locale,
              format: 'YYYY-MM-DD HH:mm',
              // disabledDate: debug ? null : '{{disabledDate}}',
            },
          },
          endTime: {
            type: 'string',
            title: '结束时间',
            required: true,
            'x-component': 'DatePicker',
            'x-mega-props': {
              span: 1,
              // labelAlign: 'top',
            },
            "x-component-props": {
              showTime: true,
              locale,
              format: 'YYYY-MM-DD HH:mm',
              // disabledDate: debug ? null : '{{disabledDate}}',
            },
          },
          // rangePicker: {
          //   typ: 'string',
          //   required: true,
          //   title: '开始以及结束时间',
          //   'x-component': 'RangePicker',
          //   "x-component-props": {
          //     showTime: true,
          //     locale: locale,
          //     format: 'YYYY-MM-DD HH:mm',
          //     disabledDate: debug ? null : '{{disabledDate}}',
          //   },
          //   'x-mega-props': {
          //     span: 1
          //   },

          // },
          meetingName: {
            type: 'string',
            title: '会议名称',
            'x-component': 'Input',
            required: true,
            maxLength: 200,
            style: {
              width: 200,
            },
            'x-mega-props': {
              span: 3
            },
          },


          meetingPlace: {
            type: 'string',
            title: '会议地点',
            'x-component': 'Input',
            'x-component-props': {
              style: { marginBottom: 10 },
            },
            required: true,
            maxLength: 50,
            description: "{{getColor('注：优先公司自有场地或者协议酒店。','#5C626B')}}",
            style: {
              width: 200,
            },
            'x-mega-props': {
              span: 3
            },
          },

          participants: {
            type: 'string',
            title: '会议参会人员',
            'x-component': 'TextArea',
            required: true,
            'x-component-props': {
              listAllAction: listAllAction,
              myListAction: myListAction,
              searchAction: searchAction,
            },
            'x-mega-props': {
              span: 3
            },
          },
          meetingHost: {
            type: 'string',
            title: '会议主持人',
            'x-component': 'Input',
            maxLength: 20,
            'x-mega-props': {
              span: 3
            },
          },

          remark: {
            type: 'string',
            title: '备注',
            'x-component': 'TextArea',
            'x-mega-props': {
              span: 3
            },
            maxLength: 500,
            'x-component-props': {
              style: {
                width: 600
              }
            },
          },
          upload: {
            type: 'string',
            title: '附件',
            'x-component': 'Upload',
            description: "{{getColor('注：可点击下载查看上传附件','#5C626B')}}",
            'x-component-props': {
              action: UPLOAD_URL,
              listType: 'text'
            },
            'x-mega-props': {
              span: 3
            },
          },
          fileList: {
            key: 'fileList',
            name: 'fileList',
            title: '会议内容',
            'x-component': 'WpsEditor',
            'x-mega-props': {
              span: 3,
            },
            'x-component-props': {
              orgId: '',
              reportId: '',
              type: '',
              config: formConfig,
            },
          },
        }
      }
    }
  }
}

