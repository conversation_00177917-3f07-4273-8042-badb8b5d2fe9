export default {
  type: "object",
  properties: {
    layout: {
      "x-component": "mega-layout",
      type: 'object',
      "x-component-props": {
        autoRow: true,
        grid: true,
        labelAlign: 'left',
        full: true,
      },
      properties: {
        formTitle: {
          key: 'formTitle',
          name: 'formTitle',
          display: false,
          default: '会议通知',
          'x-component': 'Input',
        },
        meetingName: {
          type: 'string',
          title: '会议名称',
          'x-component': 'Input',
        },
        rangePicker: {
          typ: 'string',
          title: '会议时间',
          'x-component': 'RangePicker',
          'x-mega-props': {
            span: 3,
          },
          "x-component-props": {
            showTime: true,
            // locale:locale
          }
        },
        meetingPlace: {
          type: 'string',
          title: '会议地点',
          'x-component': 'Input',
          required: true,
          maxLength: 50,
          'x-component-props': {
            placeholder: '请输入',
          }
        },
        participants: {
          type: 'string',
          title: '参会人员',
          required: true,
          'x-component': 'TextArea',
          'x-mega-props': {
            span: 3
          },
          'x-component-props': {
            listAllAction: '/baas-admin/web/listAll',
            myListAction: '/baas-admin/web/org/myList?flag=1',
            searchAction: '/web-search/web/search?size=100',// web-search/web/searchAll?groupCode=&keyword=&option=3&size=100
            style: {
              width: 400
            }
          },
        },
        meetingHost: {
          type: 'string',
          title: '会议主持人',
          'x-component': 'Input',
          maxLength: 20,
          'x-mega-props': {
            span: 3,
          },
        },
        content: {
          key: 'content',
          name: 'content',
          title: '会议内容',
          'x-component': 'Editor',
          'x-mega-props': {
            span: 3,
          },
        },
        remark: {
          type: 'string',
          title: '备注',
          'x-component': 'TextArea',
          'x-mega-props': {
            span: 3
          },
        },
      }
    }
  }
}