export default {
  type: "object",
  properties: {
    layout: {
      "x-component": "mega-layout",
      type: 'object',
      "x-component-props": {
        autoRow: true,
        grid: true,
        labelAlign: 'left',
        full: true,
      },
      properties: {
        formTitle: {
          key: 'formTitle',
          name: 'formTitle',
          display: false,
          default: '会议通知',
          'x-component': 'Input',
        },
        meetingName: {
          type: 'string',
          title: '会议名称',
          'x-component': 'Input',
        },
        rangePicker: {
          typ: 'string',
          title: '会议时间',
          'x-component': 'RangePicker',
          'x-mega-props': {
            span: 3,
          },
          "x-component-props": {
            showTime: true,
          }
        },
        meetingAddr: {
          typ: 'string',
          title: '现场主会场地点',
          'x-component': 'Input',
          'x-mega-props': {
            span: 3
          }
        },
        videoBranchVenueLocation: {
          typ: 'string',
          title: '分会场地点',
          'x-component': 'Input',
        },
        meetingPerson: {
          type: 'string',
          title: '主会场参会人员',
          'x-component': 'TextArea',
          'x-mega-props': {
            span: 3
          },
        },
        videoBranchVenuePerson: {
          type: 'string',
          title: '分会场参会人员',
          'x-component': 'TextArea',
          'x-mega-props': {
            span: 3
          },
        },
        meetingHost: {
          type: 'string',
          title: '会议主持人',
          'x-component': 'Input',
          maxLength: 20,
          'x-mega-props': {
            span: 3,
          },
        },
        content: {
          key: 'content',
          name: 'content',
          title: '会议内容',
          'x-component': 'Editor',
          'x-mega-props': {
            span: 3,
          },
        },

        flag: {
          key: 'flag',
          name: 'flag',
          display: false,
          'x-component': 'Input'
        }
      }
    }
  }
}