import React, { useEffect, useState, useMemo } from 'react'
import { SchemaMarkupForm, FormButtonGroup, Submit, Reset, createAsyncFormActions, createFormActions, FormEffectHooks } from '@formily/antd'
import { Input, NumberPicker, FormMegaLayout, Select, Radio, FormItemGrid, DatePicker } from '@formily/antd-components'
import { Button, message, Tabs, Space } from 'antd'
import { getQueryString, closeTheWindow } from 'ROOT/utils'
// import useSigner from 'ROOT/hooks/useSigner'
import SelectDept from 'ROOT/components/Formily/SelectDept'
import { Buttons, Steps } from 'ROOT/components/Process'
import service from 'ROOT/service'
import PersonalInfo from 'ROOT/components/Formily/PersonalInfo'
import { getLocalTime, getLocalTimeDay } from 'ROOT/constants/index'
import { formConfig } from './config'
import { meetingType } from '../module/config'
import schema from './schema'
import { getWpsUrl } from 'ROOT/utils/wps'
import useMyInfo from 'ROOT/hooks/useMyInfo'
import Dept from 'ROOT/components/Formily/Dept'
import SelectMember from 'ROOT/components/Formily/userSelect'
import Text from 'ROOT/components/Formily/Text'
import Upload from 'ROOT/components/Formily/Upload'
import Editor from 'ROOT/components/Formily/Editor'
import EditableInput from 'ROOT/components/Formily/Editable'
import WpsEditor from 'ROOT/components/Formily/WpsEditor'
import moment from 'moment'
import MainText from 'ROOT/components/MainText'
import { getHtmlCode } from 'ROOT/utils'
import { debounce, isEmpty } from 'lodash'
import { patchData } from 'ROOT/utils/index'

const { TabPane } = Tabs

const { onFieldValueChange$, onFieldInputChange$, onFormValuesChange$ } = FormEffectHooks

export default (props) => {
  const typeIndex = 1
  const [initValue, setInitValue] = useState({});
  const [draftValue, setDraftValue] = useState({})
  const [editable, setEditable] = useState(false)
  const [buttonGroupList, setButtonGroupList] = useState([])
  const [content, setContent] = useState('')
  const [domKey, setDomKey] = useState()
  const [meetTitle, setMeetTitle] = useState('')
  const actions = useMemo(() => createAsyncFormActions(), []);
  const { userTaskId, procFormDataKey, debug } = useMemo(() => getQueryString(props.location.search), []);
  // const { signer, date } = useSigner({
  //   userTaskId,
  //   nodeName: meetingType[typeIndex].signerNodeName,
  // })

  SelectDept.isFieldComponent = true;
  PersonalInfo.isFieldComponent = true;
  Dept.isFieldComponent = true;
  SelectMember.isFieldComponent = true
  Upload.isFieldComponent = true
  Text.isFieldComponent = true
  Editor.isFieldComponent = true
  EditableInput.isFieldComponent = true
  WpsEditor.isFieldComponent = true

  const [ableEditBtn, setAbleEditBtn] = useState([])

  useEffect(() => {
    if (debug) {
      ableEditForm()
    }
  }, [])

  useEffect(() => {
    document.title = meetTitle || '外单位会议通知'
    if (document.getElementById('breadnav')) {
      document.getElementById('breadnav').innerHTML = meetTitle || '外单位会议通知'
    }
  }, [meetTitle])

  useEffect(() => {
    if (document.getElementById('breadnav')) {
      document.getElementById('breadnav').innerHTML = '外单位会议通知'
    }
  }, [])

  const ableEditForm = async () => {
    const res = await service.getEditableForm()
    if (res.success && res.data) {
      resetFormState()
      // writableAllFields()
      setAbleEditBtn([{
        name: '更新数据',
        async: true,
        onClick: async () => {
          await saveForm(() => {
            closeTheWindow(props.location.search)
          }, true)
        }
      }])
    }
  }

  function writableAllFields() {
    actions.setFieldState('*', state => {
      state.editable = true
      state.disabled = false
    })
  }

  // useEffect(() => {
  //   if (Object.keys(initValue).length > 0) {
  //     actions.setFormState(state => {
  //       state.values = (signer || date) ? { ...initValue, signer, issuanceTime: getLocalTimeDay(date) } : initValue
  //     })
  //   }
  // }, [initValue])

  // 判断是否有签发人以及签发时间
  // useEffect(() => {
  //   actions.setFieldState('*(signer)', state => {
  //     state.value = signer || null
  //   })
  //   actions.setFieldState('*(issuanceTime)', state => {
  //     state.value = getLocalTime(date) || null
  //   })
  // }, [signer, date])

  //  数据回填
  useEffect(async () => {
    actions.setFieldState('*', state => {
      state.props.description = null;
    });
    if (procFormDataKey) {
      const wpsContent = await getWpsUrl(procFormDataKey)
      service.getFormData({
        reportId: procFormDataKey, // 获取详情，也就是初始值
        userTaskId,
      }).then((res) => {
        if (res && res.data) {
          if (wpsContent) {
            res.data.data.fileList[0] = wpsContent
          }
          let data = res.data.data;
          if (data && data.rangePicker) {
            data.startTime = data.hasOwnProperty('startTime') ? data.startTime : data.rangePicker[0]
            data.endTime = data.hasOwnProperty('endTime') ? data.endTime : data.rangePicker[1]
          }
          const businessData = res.data.businessData
          let operator = res.data.operator;
          actions.setFormState(state => {
            state.values = { ...data }
          })
          setInitValue({ ...data })
          const { formTitle, meetingCount } = data || {}
          const { orgId = '' } = operator || {}
          setMeetTitle(formTitle) // 审批表单走 详情数据
          // actions.setFormState(state => {
          //   state.values = signer ? { ...data, signer } : data
          // });
          if (meetingCount) {
            actions.getFieldValue('serialNumber').then(data => {
              actions.setFieldValue('serialNumber', data.replace(' ', `${meetingCount > 9 ? meetingCount : "0" + meetingCount}`))
            })
            // actions.setFieldValue('serialNumber', `${meetingType[typeIndex].name}[${new Date().getFullYear()}]${orgId}-${meetingCount || '-'}号`);
          }
          actions.setFieldState('fileList', state => {
            state.props['x-component-props'].reportId = procFormDataKey
          })
          if (businessData) {
            console.log(businessData, 'businessData')
            actions.setFieldState('*(signer)', state => {
              state.value = businessData.IssuerName || '-'
            })
            actions.setFieldState('*(issuanceTime)', state => {
              state.value = businessData.IssuerTime || '-'
            })
          }
        }
      })
    }
  }, []);
  const saveForm = async (callback) => {
    const res = await commonCommit()
    if (res.success) {
      message.success('操作成功', () => {
        if (typeof callback === 'function') callback(res)
      })
    }
  }

  const resetFormState = () => {
    setEditable(true)
    actions.setFieldState('*(_dept)', state => {
      state.props['x-component'] = 'EditableInput'
    })
  }

  const onMount = ({ access, editMode, draft, process }) => {
    console.log('process', process);
    const { nodeName, taskStatus } = process || {}
    if (nodeName === '开始节点' && taskStatus === 'running') {
      const btns = [
        {
          name: '保存退出',
          async: false,
          onClick: () => {
            saveForm(() => {
              closeTheWindow(props.location.search)
            })
          }
        },
        {
          name: '保存',
          async: false,
          onClick: () => {
            saveForm('')
          }
        }
      ]
      setButtonGroupList([...btns])
    }
    if (access && Object.keys(access).length > 0 && editMode && editMode.includes('web')) {
      Object.keys(access).forEach(key => {
        if (key === 'isEditable' && access[key] === 'WRITE') {
          resetFormState()
          return
        }
        actions.setFieldState(key, state => {
          switch (access[key]) {
            case 'NONE':
              state.display = false
              break
            case 'READ':
              state.editable = false
              break
            case 'WRITE':
              state.editable = true
              state.display = true
              break
            default: break
          }
        })
      })
    } else {
      // actions.setFormState((state) => {
      //   state.editable = false
      // })
    }

    if (draft) {
      actions.setFormState(state => {
        state.values = draft
      })
      // setDraftValue(draft)
    }
  }

  const commonCommit = async () => {
    // todo 需要判断是新建表单的保存还是二次编辑表单的保存，走的接口不一样
    const data = await actions.submit()
    let values = data.values;
    const result = patchData(values, initValue)
    const res = await procFormDataKey ? service.upDateForm({
      config: formConfig,
      data: result,
      reportId: procFormDataKey,
    }) : service.saveForm({
      type: meetingType[typeIndex].type,
      classify: {
        name: meetingType[typeIndex].name,
        englishName: meetingType[typeIndex].englishName,
      },
      config: formConfig,
      data: values,
    })
    return res
  }

  const submitForm = async () => {
    const res = await commonCommit()
    const data = await actions.submit()
    let values = data.values;
    if (res.success) {
      // return { id: procFormDataKey || res.data, values: values }
      return Promise.resolve()

    }
    throw new Error('保存出错')
  }

  useEffect(() => {
    window.addEventListener('process', e => {
      switch (e.detail.type) {
        case 'saved': break;
        case 'approved': // 审批后
        case 'added': // 加签后
        case 'stoped': // 终止后
        case 'rejected': // 退回后
        case 'forworded': // 转交后
        case 'cced': // 抄送后
        default: finishSubmit()
      }
    })

  }, [])

  const finishSubmit = () => {
    setTimeout(() => {
      closeTheWindow(props.location.search)
    }, 2000)
  }

  const uesEffects = () => {

    onFieldValueChange$('fileList').subscribe(({ value }) => {
      if (value && value.length > 0) {
        if (value[0].needConvert) {
          getHtmlCode(value[0].url, (html) => {
            setContent(html)
          })
        } else {
          setContent(value[0].html)
        }
      }
    })

    const debounceHandleFormValueChange = debounce((data) => {
      setDomKey(Math.random())
    }, 500)

    onFormValuesChange$().subscribe(debounceHandleFormValueChange)

    onFieldValueChange$('hasOtherParticipants').subscribe(({ value }) => {
      if (value == '1') {
        actions.setFieldState('remark', (state) => {
          state.required = true
        })
      } else {
        actions.setFieldState('remark', (state) => {
          state.required = false
        })
      }
    })
  }

  const expressionScope = {
    getColor: (text, color) => { return <div style={{ color: `${color}` }}>{text}</div> },
    labelAlign: 'left',
    disabledDate: (current) => { return current && current <= moment().subtract(1, 'days').endOf('day') },

  }
  const components = {
    TextArea: Input.TextArea,
    Input,
    NumberPicker,
    FormMegaLayout,
    Upload,
    Select,
    Radio,
    RadioGroup: Radio.Group,
    RangePicker: DatePicker.RangePicker,
    DatePicker,
    SelectDept,
    PersonalInfo,
    Dept,
    Text,
    SelectMember,
    Editor,
    EditableInput,
    WpsEditor
  }
  return (
    <div>
      <h1 className='form-title'>{meetTitle}</h1>
      <SchemaMarkupForm
        schema={schema(debug)}
        components={components}
        actions={actions}
        // initialValues={draftValue && Object.keys(draftValue).length > 0 ? draftValue : initValue}
        expressionScope={{ ...expressionScope }}
        effects={() => {
          uesEffects()
        }}
        previewPlaceholder='-'
        editable={editable}
      >
        <div>
          {
            !isEmpty(initValue) && (
              <Buttons
                userTaskId={userTaskId}
                // onFinish={finishSubmit}
                onMount={onMount}
                extraButtons={buttonGroupList.concat(ableEditBtn)}
                onSubmitOpen={async () => {
                  const data = await actions.submit()
                  const result = patchData(data.values, initValue)
                  return result
                }}
                onSubmit={submitForm}
              />
            )
          }
        </div>
      </SchemaMarkupForm>
      <Tabs defaultActiveKey="1">
        <TabPane tab="审批流程" key="1">
          <Steps
            userTaskId={userTaskId}
          />
        </TabPane>
      </Tabs>
      <MainText key={domKey} title={meetTitle} content={content} formType='meetNotice' actions={actions} />

    </div>
  )
}