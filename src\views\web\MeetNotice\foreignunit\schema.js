import React, { useEffect, useMemo } from 'react';
import { UPLOAD_URL } from 'ROOT/constants'
import locale from 'antd/lib/date-picker/locale/zh_CN';
import moment from 'moment'
import { formConfig } from '../common/PCDIconfig'
import schema from '../DI/schema';
export default (debug) => ({
  type: "object",
  properties: {
    layout: {
      "x-component": "mega-layout",
      type: 'object',
      "x-component-props": {
        autoRow: true,
        grid: true,
        // labelWidth: 150,
        labelAlign: '{{labelAlign}}',
        full: true,
        // labelCol: 24
      },
      properties: {
        formTitle: {
          key: 'formTitle',
          name: 'formTitle',
          display: false,
          default: '会议通知',
          'x-component': 'Input',
        },
        user: {
          type: "string",
          title: '起草人',
          'x-component': 'EditableInput',
          // editable: false,
        },
        _dept: {
          type: "string",
          title: '起草部门',
          'x-component': 'Dept',
          'x-component-props': {
            labelInValue: true,
          },
        },
        phone: {
          type: "string",
          title: '联系电话',
          'x-component': 'EditableInput',
          "x-rules": [{ format: "phone", message: "手机格式不正确" }],
          // editable: false,
        },
        time: {
          type: "string",
          title: '起草时间',
          'x-component': 'EditableInput',
          default: moment().format('YYYY年MM月DD日'),
          // editable: false
        },

        signer: {
          type: "string",
          title: '签发人',
          'x-component': 'EditableInput',
          default: '',
          // editable: false
        },
        issuanceTime: {
          type: "string",
          title: '签发时间',
          'x-component': 'EditableInput',
          // editable: false
        },

        serialNumber: {
          type: "string",
          title: '编号',
          'x-component': 'EditableInput',
          // editable: false
        },
        _taskLevel: {
          type: "string",
          title: '缓急程度',
          'x-component': 'Select',
          default: '3',
          enum: [
            { label: '一般', value: '3' },
            { label: '急件', value: '2' },
            { label: '特急', value: '1' },
          ],
          'x-component-props': {
            style: {
              width: 100
            }
          }
        },
        meetingName: {
          type: 'string',
          title: '会议名称',
          'x-component': 'Input',
          required: true,
          maxLength: 200,
          style: {
            width: 200,
          },
          'x-mega-props': {
            span: 3
          },
        },
        startTime: {
          type: 'string',
          title: '开始时间',
          required: true,
          'x-component': 'DatePicker',
          'x-mega-props': {
            span: 1,
            // labelAlign: 'top',
          },
          "x-component-props": {
            showTime: true,
            locale,
            format: 'YYYY-MM-DD HH:mm',
            // disabledDate: debug ? null : '{{disabledDate}}',
          },
        },
        endTime: {
          type: 'string',
          title: '结束时间',
          required: true,
          'x-component': 'DatePicker',
          'x-mega-props': {
            span: 1,
            // labelAlign: 'top',
          },
          "x-component-props": {
            showTime: true,
            locale,
            format: 'YYYY-MM-DD HH:mm',
            // disabledDate: debug ? null : '{{disabledDate}}',
          },
        },
        // rangePicker: {
        //   typ: 'string',
        //   required: true,
        //   title: '开始以及结束时间',
        //   'x-component': 'RangePicker',
        //   "x-component-props": {
        //     showTime: true,
        //     locale: locale,
        //     format: 'YYYY-MM-DD HH:mm',
        //     disabledDate: debug ? null : '{{disabledDate}}',
        //   },
        //   'x-mega-props': {
        //     span: 1
        //   },
        // },
        meetingPlace: {
          type: 'string',
          title: '会议地点',
          'x-component': 'Input',
          'x-component-props': {
            style: { marginBottom: 10 },
          },
          required: true,
          maxLength: 50,
          description: "{{getColor('注：优先公司自有场地或者协议酒店。','#5C626B')}}",
          style: {
            width: 200,
          },
          'x-mega-props': {
            span: 3
          },
        },

        participants: {
          type: 'string',
          title: '参会人员',
          'x-component': 'SelectMember',
          required: true,
          'x-component-props': {
            listAllAction: '/baas-easylabel/orgDepts/getAllDeptUsers',
            myListAction: '/baas-admin/web/org/myList?flag=1',
            searchAction: '/web-search/web/search?size=100',
            orgId: '',
            reportId: '',
            type: '',
            config: '',
            rootDeptId: ''
          },
          'x-mega-props': {
            span: 3
          },
        },
        hasOtherParticipants: {
          type: "string",
          title: '是否有其他参会人员',
          'x-component': 'RadioGroup',
          enum: [
            { label: '是', value: '1' },
            { label: '否', value: '2' },
          ],
          default: '2',
          'x-mega-props': {
            span: 3
          },
        },
        _participantsNumber: {
          typ: 'string',
          title: '参会人数',
          'x-component': 'NumberPicker',
          required: true,
          'x-mega-props': {
            "addonAfter": "人",
          },
          "x-component-props": {
            min: 1,
            max: 9999999,
            setp: 1,
            formatter: (value) => {

              return value.replace(/[^0-9]/g, '')
            }
          }
        },
        meetingDays: {
          typ: 'string',
          title: '会议天数',
          'x-component': 'Select',
          required: true,
          enum: [
            { label: '0.5', value: '0.5' },
            { label: '1', value: '1' },
            { label: '1.5', value: '1.5' },
            { label: '2', value: '2' },
            { label: '2.5', value: '2.5' },
            { label: '3', value: '3' },
            { label: '3以上', value: 'more' },
          ],
          'x-mega-props': {
            "addonAfter": "天",
          }
        },
        isProduceMeetingFee: {
          type: "string",
          title: '是否产生会议费用',
          'x-component': 'RadioGroup',
          required: true,
          enum: [
            { label: '是', value: '1' },
            { label: '否', value: '2' },
          ],
          default: '2',
          'x-linkages': [
            {
              type: "value:visible",
              target: '*(feeType,budgetAmount)',
              condition: '{{$self.value == 1}}'
            },
          ]
        },
        feeType: {
          type: 'string',
          title: '费用类型',
          'x-component': 'Select',
          default: '1',
          enum: [
            { label: '填写金额', value: '1' },
            { label: '根据会议安排据实报销', value: '2' },
          ],
          "x-component-props": {
            style: {
              width: 190
            }
          },
          'x-linkages': [
            {
              type: "value:visible",
              target: '*(budgetAmount)',
              condition: '{{$self.value == 1}}'
            },
          ]
        },

        budgetAmount: {
          title: '预算金额',
          "x-component": "NumberPicker",
          required: true,
          'x-mega-props': {
            "addonAfter": "万元",
          },
          'x-component-props': {
            min: 0.01,
            max: 9999999.99
          },
        },

        remark: {
          type: 'string',
          title: '备注',
          // required: true,
          'x-component': 'TextArea',
          'x-mega-props': {
            span: 3
          },
          maxLength: 500,
          'x-component-props': {
            style: {
              width: 600
            }
          },
        },
        upload: {
          type: 'string',
          title: '附件',
          'x-component': 'Upload',
          description: "{{getColor('注：可点击下载查看上传附件','#5C626B')}}",
          required: true,
          'x-component-props': {
            action: UPLOAD_URL,
            listType: 'text'
          },
          'x-mega-props': {
            span: 3
          },
        },
        message: {
          // title: '{{getColor("提示", "red")}}',
          'x-component': 'Text',
          'x-mega-props': {
            span: 3
          },
        },
        fileList: {
          key: 'fileList',
          name: 'fileList',
          title: '会议内容',
          'x-component': 'WpsEditor',
          'x-mega-props': {
            span: 3,
          },
          'x-component-props': {
            orgId: '',
            reportId: '',
            type: '',
            config: formConfig,
          },
        },
      }
    }
  }
})