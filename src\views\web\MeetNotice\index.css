.button-group {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 60px;
  /* padding-top: 14px; */
  text-align: center;
  background-color: white;
  box-shadow: 0 0 5px rgba(0, 0, 0, .2);
  z-index: 103;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-title {
  font-size: 18px;
  font-weight: bold;
  text-align: center;
}

.divider {
  padding-top: 32px;
  margin-bottom: 32px;
  border-bottom: 1px dashed #949494;
}

.sub-title {
  font-size: 16px;
  font-weight: 500;
}

.ant-descriptions-row>td, .ant-descriptions-row>th {
  padding: 0 !important;
}

.form-number {
  text-align: right;
  font-size: 14px;
}

.button-group .proc-buttons button {
  margin-left: 0 !important;
}

.meeting-wapper {
  display: flex;
  flex-direction: column;
  align-items:center;
}

.ant-form-item-explain-error {
  color: rgba(0,0,0,.45) !important;
  
  }
  
  .ant-form-item-has-error .ant-form-item-explain-error {
  
  color: #ff4d4f !important;
  
  }

  #__CONTROL_CONFIRM__ span {
    color: #dd5d1d;
  }

  #__CONTROL_CONFIRM_TEXT__ {
    position: fixed;
    right: 15px;
    top: 13px;
    z-index: 999;
  }

  #__CONTROL_CONFIRM_TEXT__ span {
    color: #dd5d1d;
  }