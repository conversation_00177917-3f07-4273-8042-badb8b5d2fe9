import React, { useMemo, useEffect } from 'react'
import { SchemaMarkupForm, createAsyncFormActions } from '@formily/antd'
import EditableInput from 'ROOT/components/Formily/Editable'
import { get } from 'lodash'
import { contentSchema } from './schema'

function OldContent({ data, settingParagraph, paragraphContent }) {
  const components = {
    EditableInput,
  }
  const actions = useMemo(() => createAsyncFormActions(), [])
  useEffect(() => {
    const { meetingAategory, meetingForm } = data
    let obj = {}
    // 会议类别为公司决策会议/公司领导专题办公会议/其他（会议形式：现场会议），获取字段包含：会议时间、会议地点、现场会议参会人员、会议主持人、正文
    if (
      meetingAategory === '1' ||
      meetingAategory === '2' ||
      (meetingAategory === '3' && meetingForm === '1')
    ) {
      const { startTime, endTime, meetingPlace, meetingPerson, meetingHost } = data
      obj = {
        startTime,
        endTime,
        meetingPlace,
        meetingPerson,
        meetingHost,
      }
    } else {
      // 会议类别为其他，会议形式：视频会议、现场+视频会议，获取字段包含：会议时间、主会场地点、现场会议参会人员、视频分会场地点、视频分会场参会人员、会议主持人、正文
      const {
        startTime,
        endTime,
        mainVenueLocation,
        meetingPerson,
        videoBranchVenueLocation,
        videoBranchVenuePerson,
        meetingHost,
      } = data
      obj = {
        startTime,
        endTime,
        mainVenueLocation,
        meetingPerson,
        videoBranchVenueLocation,
        videoBranchVenuePerson,
        meetingHost,
      }
    }
    Object.keys(obj).forEach(item => {
      if (data[item]) {
        actions.setFieldState(`${item}`, state => {
          state.visible = true
        })
      }
    })
    actions.setFormState(state => {
      state.values = { ...data }
    })
  }, [data])

  const content = settingParagraph ? paragraphContent : get(data, 'fileList[0].html', '')
  return (
    <div>
      <SchemaMarkupForm
        schema={contentSchema}
        initialValues={data}
        actions={actions}
        components={components}
        expressionScope={{
          labelAlign: 'top',
        }}
      />
      {content ? (
        <div style={{ marginTop: 11 }}>
          <div className="main-text-label">正文预览</div>
          <div className="main-text-container">
            <div
              className="main-text-content"
              dangerouslySetInnerHTML={{
                __html: content,
              }}
            />
          </div>
        </div>
      ) : null}
    </div>
  )
}

export default OldContent
