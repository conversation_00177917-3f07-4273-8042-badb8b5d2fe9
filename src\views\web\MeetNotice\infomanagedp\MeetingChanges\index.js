import React, { useEffect, useState, useMemo, useRef } from 'react'
import { Spin, Space, message, Modal } from 'antd'
import { SchemaMarkupForm, createAsyncFormActions, FormEffectHooks } from '@formily/antd'
import { Input, Select, DatePicker } from '@formily/antd-components'
import EditableInput from 'ROOT/components/Formily/Editable'
import Dept from 'ROOT/components/Formily/Dept'
import { withRouter } from 'react-router-dom'
import useMyInfo from 'ROOT/hooks/useMyInfo'
import { closeTheWindow, getQueryString, emergencyLevel } from 'ROOT/utils'
import { getWpsUrl } from 'ROOT/utils/wps'
import service from 'ROOT/service'
import MeetingNameChooser from 'ROOT/components/Formily/MeetingNameChooser'
import MainText from 'ROOT/components/MainText'
import { Buttons } from 'ROOT/components/Process'
import { patchData } from 'ROOT/utils/index'
import { isEmpty, has } from 'lodash'
import { useSessionStorageState } from 'ahooks'
import moment from 'moment'
import { formConfig } from '../config'
import OldContent from './OldContent'
import { getUpdateSchema } from './schema'

const { onFieldValueChange$, onFieldInputChange$ } = FormEffectHooks
const { domain, envType } = window._APP_CONFIG || {}
let env = 'test'
if (envType) {
  env = envType
} else {
  const envList = ['moa-dev.uban360.net', '10.0.13.232', '10.185.27.72', 'gxtymh.gx.cmcc']
  const envObj = ['dev', 'test', 'uat', 'online']

  const index = envList.findIndex(item => domain.includes(item))
  env = envObj[index]
}

const keysIndo = {
  dev: {
    procKey: 'WF_DE20230919195919987393',
    appId: '24f5137e905c4149a57dfb0f46066b1c',
  },
  test: {
    procKey: 'WF_DE20230904170838858544',
    appId: '9711a5144b4441f59b4e09017aeacdd5',
  },
  uat: {
    procKey: 'WF_DE20230927210455489359',
    appId: '8bc60eb58ffb4c6a965b0e94ae9b4662',
  },
  online: {
    procKey: 'WF_DE20230927170639926498',
    appId: '0a040e0ceada49ff99de691c9dee9db0',
  },
}

const PROCESS_CONFIG = { ...keysIndo[env] }

EditableInput.isFieldComponent = true
Dept.isFieldComponent = true
MeetingNameChooser.isFieldComponent = true

let upDateNewId = null
function MeetingChanges(props) {
  const [editable, setEditable] = useState(true)
  const [loading, setLoading] = useState(false)
  const [initValue, setInitValue] = useState({})
  const [operator, setOperator] = useState({})
  const myInfo = useMyInfo({ isSetStorage: true })
  const actions = useMemo(() => createAsyncFormActions(), [])
  const query = useMemo(() => getQueryString(props.location.search), [])
  const [buttonGroupList, setButtonGroupList] = useState([])
  const { userTaskId, debug } = query
  const [access, setAccess] = useState({ limit: 'NONE' })
  // 选线段落
  const [paragraphContent, setParagraphContent] = useState('')
  const [settingParagraph, setSettingParagraph] = useState(false)
  const settingParagraphRef = useRef(false)
  const [config, setConfig] = useState({})
  const [newId, setNewId] = useState('')
  const [oldData, setOldData] = useState({})
  const [ableEditBtn, setAbleEditBtn] = useState([])

  const [procFormDataKey, setProcFormDataKey] = useSessionStorageState(
    'use-sessiton-storage-state-reportId',
    {
      defaultValue: query.procFormDataKey,
    },
  )

  const oldMeetingId = procFormDataKey || ''

  const components = {
    EditableInput,
    Dept,
    DatePicker,
    Input,
    Select,
    MeetingNameChooser,
    MainText,
  }

  const resetUserInfo = () => {
    const { linkPath = [], loginMobile } = myInfo
    actions.setFieldState('_dept', state => {
      state.props.enum =
        linkPath && linkPath.length > 0
          ? linkPath.map(item => ({
              value: item.deptId,
              label: item.linkPath,
              deptName: item.rootDeptName,
            }))
          : []
      if (initValue._dept && initValue._dept.value) {
        state.value = initValue._dept
      } else {
        state.value =
          linkPath && linkPath.length > 0
            ? {
                value: linkPath[0].deptId,
                label: linkPath[0].linkPath,
                deptName: linkPath[0].rootDeptName,
              }
            : { value: '', label: '', deptName: '' }
      }
      setLoading(false)
    })
    actions.setFieldState('phone', state => {
      state.value = loginMobile
    })
  }

  const fetchData = async reportId => {
    if (reportId) {
      // 触发详情接口用什么做判断条件
      const wpsContent = await getWpsUrl(reportId)
      service
        .getFormData({
          reportId, // 获取详情，也就是初始值
        })
        .then(async res => {
          if (res && res.data) {
            if (wpsContent) {
              res.data.data.fileList[0] = wpsContent
            }
            const { data } = res.data
            const { operator } = res.data
            const { config } = res.data
            if (data && data.rangePicker) {
              data.startTime = data.hasOwnProperty('startTime')
                ? data.startTime
                : data.rangePicker[0]
              data.endTime = data.hasOwnProperty('endTime') ? data.endTime : data.rangePicker[1]
            }
            // 会议类别为其他，会议形式：视频会议、现场+视频会议
            if (data.meetingAategory === '3' && ['2', '3'].includes(data.meetingForm)) {
              data.mainVenueLocation = data.mainVenueLocation || data.meetingAddr
            } else {
              data.meetingPlace = data.meetingPlace || data.meetingAddr
            }
            setInitValue(data)
            setOperator(operator)
            setConfig(config)
            if (data && data.oldData) {
              setOldData({ ...data.oldData })
            }
          }
        })
    }
  }

  //  数据回填
  useEffect(() => {
    fetchData(procFormDataKey)
  }, [])

  useEffect(() => {
    actions.setFormState(state => {
      state.values = initValue
    })
    actions.setFieldState('changes', state => {
      // 会议类别为公司决策会议/公司领导专题办公会议，变更内容可选项：会议时间、会议地点、现场会议参会人员
      if (initValue.meetingAategory === '1' || initValue.meetingAategory === '2') {
        state.props.enum = [
          {
            label: '会议时间',
            value: 'meetingTime',
          },
          {
            label: '会议地点',
            value: 'meetingPlace',
          },
          {
            label: '现场会议参会人员',
            value: 'meetingPerson',
          },
        ]
      }
      // 会议类别为其他，会议形式：现场会议，变更内容可选项：会议时间、会议地点、现场会议参会人员
      else if (initValue.meetingAategory === '3' && initValue.meetingForm === '1') {
        state.props.enum = [
          {
            label: '会议时间',
            value: 'meetingTime',
          },
          {
            label: '会议地点',
            value: 'meetingPlace',
          },
          {
            label: '现场会议参会人员',
            value: 'meetingPerson',
          },
        ]
      }
      // 会议类别为其他，会议形式：视频会议、现场+视频会议，变更内容可选选项：会议时间、主会场地点、现场会议参会人员、视频分会场地点、视频分会场参会人员
      else if (initValue.meetingAategory === '3' && ['2', '3'].includes(initValue.meetingForm)) {
        state.props.enum = [
          {
            label: '会议时间',
            value: 'meetingTime',
          },
          {
            label: '主会场地点',
            value: 'mainVenueLocation',
          },
          {
            label: '现场会议参会人员',
            value: 'meetingPerson',
          },
          {
            label: '视频分会场地点',
            value: 'videoBranchVenueLocation',
          },
          {
            label: '视频分会场参会人员',
            value: 'videoBranchVenuePerson',
          },
        ]
      }
    })
    actions.setFormState(state => {
      state.values = { ...initValue }
    })
  }, [initValue])

  useEffect(() => {
    if (debug) {
      if (operator.uid && operator.orgId) {
        service
          .getOrgLinkPathForDebug({
            uid: operator.uid,
            orgId: operator.orgId,
          })
          .then(res => {
            const { linkPath = [] } = res.data
            actions.setFieldState('_dept', state => {
              state.props.enum =
                linkPath && linkPath.length > 0
                  ? linkPath.map(item => ({
                      value: item.deptId,
                      label: item.linkPath,
                      deptName: item.rootDeptName,
                    }))
                  : []
              if (initValue._dept && initValue._dept.value) {
                state.value = initValue._dept
              } else {
                state.value =
                  linkPath && linkPath.length > 0
                    ? {
                        value: linkPath[0].deptId,
                        label: linkPath[0].linkPath,
                        deptName: linkPath[0].rootDeptName,
                      }
                    : { value: '', label: '', deptName: '' }
              }
              setLoading(false)
            })
          })
      }
    }
  }, [debug, operator])

  useEffect(() => {
    if (myInfo && Object.keys(myInfo).length > 0) {
      resetUserInfo()
    }
  }, [myInfo])

  useEffect(() => {
    window.addEventListener('process', () => {
      // 起草节点
      if (!userTaskId) {
        deleteDraft()
      } else {
        setTimeout(() => {
          closeTheWindow(props.location.search)
        }, 2000)
      }
    })
  }, [])

  const asyncMsg = () => {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (resolve, reject) => {
      const meetingData = Object.keys(oldData).length > 0 ? oldData : initValue
      const { startTime: oldStartTime } = meetingData || {}

      if (oldStartTime && moment(oldStartTime, 'YYYY-MM-DD HH:mm').valueOf() < Date.now()) {
        message.error('会议已开始，不可修改')
        reject()
      }

      try {
        const res = await service.getProcInstInfoOfForm({
          procFormUnionKeys: [
            {
              procFormKey:
                sessionStorage.getItem('use-sessiton-storage-state-procFormKey') || 'meeting',
              proFormDataKey: oldMeetingId,
            },
          ],
        })
        const { status } = res[0] || {}
        if (status && (status.includes('终止') || status.includes('注销'))) {
          const text = status.includes('终止') ? '已终止' : '已注销'
          message.error(`流程${text}，无法发起会议变更通知`)
          reject()
        }
        resolve()
      } catch (err) {
        console.log('getProcInstInfoOfForm err', err)
      }
    })
  }

  const onSubmitOpen = async () => {
    await asyncMsg()

    const startTime = await actions.getFieldValue('startTime')
    const endTime = await actions.getFieldValue('endTime')
    if (startTime >= endTime) {
      message.error('开始时间必须小于结束时间')
      return Promise.reject()
    }
    const data = await actions.submit()
    const result = patchData(data.values, initValue)

    return result
  }

  const commonCommit = async () => {
    await asyncMsg()

    const startTime = await actions.getFieldValue('startTime')
    const endTime = await actions.getFieldValue('endTime')
    if (startTime >= endTime) {
      message.error('开始时间必须小于结束时间')
      return { success: false }
    }
    const data = await actions.submit()
    const { values } = data
    const result = patchData(values, initValue)

    // 起草
    if (!userTaskId && !upDateNewId && location.href.indexOf('backurl') === -1) {
      const oldData = JSON.parse(JSON.stringify(initValue))
      try {
        const res = await service.saveForm({
          type: '17',
          classify: {
            name: '会议变更通知',
            englishName: 'MEETING_CHANGES_NAME',
          },
          config,
          data: {
            ...result,
            oldFormId: procFormDataKey,
            oldData,
            oldMeetingId: oldMeetingId || procFormDataKey,
          },
          operator,
        })
        const { data: id } = res || {}
        setNewId(id)
        upDateNewId = id
      } catch (err) {
        console.log('saveForm err', err)
      }
    }

    const res = await service.upDateForm({
      config: formConfig,
      data: {
        ...result,
        oldMeetingId: oldMeetingId || initValue.oldMeetingId || procFormDataKey || '',
        // issuanceTime: nodeName === '部门总经理审核' ? getLocalTimeDay(date) : values.issuanceTime ? values.issuanceTime : undefined
      },
      reportId: upDateNewId || procFormDataKey || newId,
    })
    return res
  }

  const submitForm = async () => {
    const res = await commonCommit()
    if (res.success) {
      return +upDateNewId || +newId || +procFormDataKey
    }
    throw new Error('保存出错')
  }

  const saveDraft = async callback => {
    const info =
      myInfo || localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo')) : {}

    const data = await actions.submit()
    const result = patchData(data.values, initValue)
    const useId = upDateNewId || procFormDataKey
    const res = await service.saveDraft({
      appId: PROCESS_CONFIG.appId,
      appTasks: [
        {
          appTaskId: useId.toString(),
          businessType: 2,
          emergencyLevel: emergencyLevel[+result._taskLevel],
          handleEntry: [
            {
              handleType: 0, // 草稿
              handlerId: info.loginUid,
            },
          ],
          processType: '会议变更通知',
          sponsorId: info.loginUid,
          jumpToDetail: 1,
          title: initValue.meetingName,
          detailUrl: decodeURIComponent(
            upDateNewId || procFormDataKey || newId
              ? `${location.href
                  .replace('/extra-forms/', '/extra-forms-h5/')
                  .replace(/backurl=.*?&|backurl=.*?$/, '')}?procFormDataKey=${
                  upDateNewId || procFormDataKey || newId
                }`
              : `${location.href
                  .replace('/extra-forms/', '/extra-forms-h5/')
                  .replace(/backurl=.*?&|backurl=.*?$/, '')}`,
          ),
          webDetailUrl: decodeURIComponent(
            upDateNewId || procFormDataKey || newId
              ? `${location.href.replace(/backurl=.*?&|backurl=.*?$/, '')}?procFormDataKey=${
                  upDateNewId || procFormDataKey || newId
                }`
              : `${location.href.replace(/backurl=.*?&|backurl=.*?$/, '')}`,
          ),
        },
      ],
    })
    if (res.success) {
      message.success('操作成功', () => {
        if (typeof callback === 'function') callback(res)
      })
    }
  }

  const saveForm = async (callback, isDraft) => {
    const res = await commonCommit()
    if (res.success) {
      if (isDraft) {
        saveDraft(callback)
      } else {
        message.success('操作成功', () => {
          if (typeof callback === 'function') callback(res)
        })
      }
    }
  }

  const deleteDraft = async () => {
    const info = localStorage.getItem('userInfo')
      ? JSON.parse(localStorage.getItem('userInfo'))
      : {}
    const useId = upDateNewId || procFormDataKey
    const res = await service.deleteDraft({
      appId: PROCESS_CONFIG.appId,
      appTasks: [
        {
          appTaskId: useId.toString(),
          handlerIds: [info.loginUid],
        },
      ],
    })
    if (res.success) {
      setTimeout(() => {
        message.success('操作成功', () => {
          closeTheWindow(props.location.search)
        })
      }, 4000)
    }
  }

  const deleteForm = async () => {
    Modal.confirm({
      content: '你选择了注销当前文档，系统将删除该文档，是否继续？',
      onOk: async () => {
        const res = await service.deleteForm({
          reportId: procFormDataKey,
        })
        if (res.success) {
          deleteDraft()
        } else {
          message.error(data.msg)
        }
      },
    })
  }

  const onMount = ({ access, editMode, process }) => {
    const { nodeName, taskStatus } = process || {}
    // 打补丁 兼容流程结束了 就会不传给access的情况 本次需求根据流程返回的limit字段来决定是否调用查看正文的权限接口，直接展示所有内容 有需要 limit字段的情况
    if (taskStatus === 'over' && !has(access, 'limit') && has(process.access, 'limit')) {
      // 特殊情况 流程为over access为空 但是通过process.access 传过来了
      access.limit = process.access.limit
    }

    // 流程结束之后，字段全都不能编辑
    if (taskStatus === 'over') {
      actions.setFormState(state => {
        state.editable = false
      })
    }

    setAccess(access)
    let btns = []
    if (nodeName === '开始节点' && taskStatus === 'running') {
      const _btns = [
        {
          name: '保存退出',
          async: false,
          onClick: () => {
            saveForm(() => {
              closeTheWindow(props.location.search)
            }, true)
          },
        },
        {
          name: '保存',
          async: false,
          onClick: () => {
            saveForm('', true)
          },
        },
      ]
      btns = [...btns, ..._btns]
    }

    // 草稿
    if (location.href.indexOf('backurl') > 0) {
      btns.push({
        name: '注销文档',
        async: false,
        onClick: () => {
          deleteForm()
        },
      })
    }

    setButtonGroupList([...btns])
    if (access && Object.keys(access).length > 0 && editMode && editMode.includes('web')) {
      Object.keys(access).forEach(key => {
        actions.setFieldState(key, state => {
          // if (key === 'isEditable' && process.access[key] === 'WRITE') {
          //   resetFormState()
          //   return
          // }
          switch (access[key]) {
            case 'NONE':
              state.display = false
              break
            case 'READ':
              state.editable = false
              break
            case 'WRITE':
              state.editable = true
              state.display = true
              break
            default:
              break
          }
        })
      })
    }
    if (debug) {
      ableEditForm()
    }
  }

  const getPermissionDetail = async () => {
    // 先注销 通过debug拿到所有的字段
    if (access.limit !== 'NONE') {
      // 流程配置了limit的权限就不需要走下面了
      let customReportId
      // 起草
      if (!userTaskId) {
        customReportId = procFormDataKey
      }
      const res = await service.getParagraphDetail({
        formId: customReportId || initValue.oldFormId || procFormDataKey || newId,
        debug: debug ? 1 : null,
      })
      if (res && res.success) {
        const { data = {}, isSetting = false } = res.data || {}
        const { list = [] } = data || {}
        setSettingParagraph(isSetting)
        settingParagraphRef.current = isSetting
        const ids = list.map(item => item.id)
        if (ids && ids.length > 0 && isSetting) {
          const data = await service.getParagraphContent({ ids: [...ids] })
          if (data && data.success) {
            // 不在往正文里面塞字段 而是单独开了一个内容
            // actions.setFieldValue('fileList', [{ html: data.data }])
            setParagraphContent(data.data)
            setLoading(false)
          }
        } else {
          setLoading(false)
        }
      }
    }
    setLoading(false)
  }

  useEffect(() => {
    getPermissionDetail()
  }, [access, initValue, newId])

  useEffect(() => {
    if (debug) {
      ableEditForm()
    }
  }, [])

  const resetFormState = () => {
    setEditable(true)
    // actions.setFieldState('*(_dept)', state => {
    //   state.props['x-component'] = 'EditableInput'
    // })
    // actions.setFieldState('*(apply, applyList)', state => {
    //   state.props.editable = true;
    // })
  }

  const ableEditForm = async () => {
    const res = await service.getEditableForm()
    if (res.success && res.data) {
      resetFormState()
      // setEnableWrite(true)
      writableAllFields()
      setAbleEditBtn([
        {
          name: '更新数据',
          async: true,
          onClick: async () => {
            await saveForm(() => {
              closeTheWindow(props.location.search)
            }, true)
          },
        },
      ])
    }
  }

  function writableAllFields() {
    actions.setFieldState('*', state => {
      state.editable = true
      // state.disabled = false
    })
  }

  return (
    <Spin spinning={loading}>
      <h1 className="form-title">会议通知变更审批单</h1>
      <SchemaMarkupForm
        schema={getUpdateSchema({ operator })}
        initialValues={initValue}
        actions={actions}
        components={components}
        editable={editable}
        expressionScope={{
          labelAlign: 'top',
        }}
        effects={() => {
          onFieldInputChange$('.*(meetingName)').subscribe(state => {
            const { value } = state
            if (/^\d+$/.test(value)) {
              fetchData(value)
              setProcFormDataKey(value)
            }
          })
        }}
      />
      <div className="divider" />
      <div className="sub-title">原会议内容</div>
      <OldContent
        data={Object.keys(oldData).length > 0 ? oldData : initValue}
        settingParagraph={settingParagraph}
        paragraphContent={paragraphContent}
      />
      <div>
        {!isEmpty(initValue) && (
          <Space>
            {userTaskId ? (
              <Buttons
                procFormDataKey={procFormDataKey || newId}
                userTaskId={userTaskId}
                onSubmitOpen={onSubmitOpen}
                onMount={onMount}
                onSubmit={submitForm}
                extraButtons={buttonGroupList.concat(ableEditBtn)}
              />
            ) : (
              <Buttons
                procKey={PROCESS_CONFIG.procKey}
                procFormDataKey={procFormDataKey || newId}
                appId={PROCESS_CONFIG.appId}
                userTaskId=""
                onSubmitOpen={onSubmitOpen}
                onMount={onMount}
                onSubmit={submitForm}
                extraButtons={buttonGroupList}
              />
            )}
          </Space>
        )}
      </div>
    </Spin>
  )
}

export default withRouter(MeetingChanges)
