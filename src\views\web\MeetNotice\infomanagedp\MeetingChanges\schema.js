import locale from 'antd/lib/date-picker/locale/zh_CN'
import moment from 'moment'

export const getUpdateSchema = ({ operator }) => ({
  type: 'object',
  properties: {
    layout: {
      'x-component': 'mega-layout',
      type: 'object',
      'x-component-props': {
        autoRow: true,
        grid: true,
        columns: 6,
        labelAlign: '{{labelAlign}}',
        full: true,
      },
      properties: {
        user: {
          type: 'string',
          title: '起草人',
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'EditableInput',
        },
        _dept: {
          type: 'string',
          title: '起草部门',
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'Dept',
          'x-component-props': {
            labelInValue: true,
          },
        },
        phone: {
          type: 'string',
          title: '联系电话',
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'EditableInput',
          'x-rules': [{ format: 'phone', message: '手机格式不正确' }],
        },
        time: {
          type: 'string',
          title: '起草时间',
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'EditableInput',
          default: moment().format('YYYY年MM月DD日'),
        },
        meetingName: {
          type: 'string',
          title: '会议名称',
          'x-component': 'MeetingNameChooser',
          required: true,
          maxLength: 200,
          'x-mega-props': {
            span: 2,
          },
          'x-component-props': {
            uid: operator.uid,
          },
        },
        changes: {
          type: 'string',
          title: '变更内容',
          'x-mega-props': {
            span: 2,
          },
          required: true,
          'x-component-props': {
            mode: 'multiple',
          },
          'x-component': 'Select',
          'x-linkages': [
            {
              type: 'value:visible',
              target: '*(startTime, endTime)',
              condition: '{{ $self.value && $self.value.includes("meetingTime") }}',
            },
            {
              type: 'value:visible',
              target: '*(meetingPlace)',
              condition: '{{ $self.value && $self.value.includes("meetingPlace") }}',
            },
            {
              type: 'value:visible',
              target: '*(meetingPerson)',
              condition: '{{ $self.value && $self.value.includes("meetingPerson") }}',
            },
            {
              type: 'value:visible',
              target: '*(mainVenueLocation)',
              condition: '{{ $self.value && $self.value.includes("mainVenueLocation") }}',
            },
            {
              type: 'value:visible',
              target: '*(videoBranchVenueLocation)',
              condition: '{{ $self.value && $self.value.includes("videoBranchVenueLocation") }}',
            },
            {
              type: 'value:visible',
              target: '*(videoBranchVenuePerson)',
              condition: '{{ $self.value && $self.value.includes("videoBranchVenuePerson") }}',
            },
          ],
        },
        startTime: {
          type: 'string',
          title: '开始时间',
          required: true,
          'x-component': 'DatePicker',
          'x-mega-props': {
            span: 2,
          },
          'x-component-props': {
            showTime: true,
            locale,
            format: 'YYYY-MM-DD HH:mm',
          },
        },
        endTime: {
          type: 'string',
          title: '结束时间',
          required: true,
          'x-component': 'DatePicker',
          'x-mega-props': {
            span: 2,
          },
          'x-component-props': {
            showTime: true,
            locale,
            format: 'YYYY-MM-DD HH:mm',
          },
        },
        meetingPlace: {
          type: 'string',
          title: '会议地点',
          'x-component': 'Input',
          required: true,
          'x-component-props': {
            placeholder: '请输入',
            maxLength: 200,
          },
          'x-mega-props': {
            span: 6,
          },
        },
        mainVenueLocation: {
          typ: 'string',
          title: '主会场地点',
          'x-component': 'Input',
          required: true,
          'x-component-props': {
            placeholder: '请输入',
            maxLength: 200,
          },
          'x-mega-props': {
            span: 6,
          },
        },
        meetingPerson: {
          type: 'string',
          title: '现场会议参会人员',
          required: true,
          'x-component': 'Input',
          'x-mega-props': {
            span: 6,
          },
          'x-component-props': {
            placeholder: '请输入',
            maxLength: 500,
          },
        },
        videoBranchVenuePerson: {
          type: 'string',
          title: '视频分会场参会人员',
          'x-component': 'Input',
          required: true,
          maxLength: 500,
          'x-mega-props': {
            span: 6,
          },
          'x-component-props': {
            placeholder: '请输入',
            maxLength: 500,
          },
        },
        videoBranchVenueLocation: {
          typ: 'string',
          title: '视频分会场地点',
          required: true,
          'x-component': 'Input',
          'x-mega-props': {
            span: 6,
          },
          'x-component-props': {
            placeholder: '请输入',
            maxLength: 200,
          },
        },
      },
    },
  },
})

export const contentSchema = {
  type: 'object',
  properties: {
    layout: {
      'x-component': 'mega-layout',
      type: 'object',
      'x-component-props': {
        autoRow: true,
        grid: true,
        columns: 6,
        labelAlign: '{{labelAlign}}',
        full: true,
      },
      properties: {
        startTime: {
          type: 'string',
          title: '开始时间',
          visible: false,
          'x-component': 'EditableInput',
          'x-mega-props': {
            span: 2,
          },
          'x-component-props': {
            showTime: true,
            locale,
            format: 'YYYY-MM-DD HH:mm',
          },
        },
        endTime: {
          type: 'string',
          title: '结束时间',
          visible: false,
          'x-component': 'EditableInput',
          'x-mega-props': {
            span: 2,
          },
          'x-component-props': {
            showTime: true,
            locale,
            format: 'YYYY-MM-DD HH:mm',
          },
        },
        meetingPlace: {
          type: 'string',
          title: '会议地点',
          visible: false,
          'x-component': 'EditableInput',
          'x-component-props': {
            placeholder: '请输入',
            maxLength: 200,
          },
          'x-mega-props': {
            span: 6,
          },
        },
        mainVenueLocation: {
          typ: 'string',
          title: '主会场地点',
          visible: false,
          'x-component': 'EditableInput',
          'x-component-props': {
            placeholder: '请输入',
            maxLength: 200,
          },
          'x-mega-props': {
            span: 6,
          },
        },
        videoBranchVenueLocation: {
          typ: 'string',
          title: '视频分会场地点',
          visible: false,
          'x-component': 'EditableInput',
          'x-component-props': {
            placeholder: '请输入',
            maxLength: 200,
          },
          'x-mega-props': {
            span: 6,
          },
        },
        meetingPerson: {
          type: 'string',
          title: '现场会议参会人员',
          visible: false,
          'x-component': 'EditableInput',
          'x-component-props': {
            placeholder: '请输入',
            maxLength: 200,
          },
          'x-mega-props': {
            span: 6,
          },
        },
        videoBranchVenuePerson: {
          type: 'string',
          title: '视频分会场参会人员',
          visible: false,
          'x-component': 'EditableInput',
          'x-component-props': {
            placeholder: '请输入',
            maxLength: 200,
          },
          'x-mega-props': {
            span: 6,
          },
        },
        meetingHost: {
          type: 'string',
          title: '会议主持人',
          visible: false,
          'x-component': 'EditableInput',
          maxLength: 20,
          'x-component-props': {
            placeholder: '请输入',
            maxLength: 200,
          },
          'x-mega-props': {
            span: 6,
          },
        },
      },
    },
  },
}
