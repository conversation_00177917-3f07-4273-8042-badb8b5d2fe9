import React, { useEffect, useState } from 'react'
import Service from 'ROOT/service'
import { getQueryString } from 'ROOT/utils'

const SignIn = props => {
  const [meetInfo, setMeetInfo] = useState({})

  const { reportId, userTaskId, debug } = getQueryString(props.location.search)

  useEffect(() => {
    getDetail()
  }, [reportId, userTaskId, debug])

  const getDetail = async () => {
    const params = {
      reportId,
      userTaskId,
    }
    if (debug) {
      params.debug = 1
    }
    const res = await Service.meetingSignDetail(params)
    if (res.success) {
      const { endTime, meetingAddr, meetingName, qrCodeUrl, startTime } = res.data
      setMeetInfo({
        title: meetingName,
        startTime,
        endTime,
        meetingAddr,
        qrCodeUrl,
      })
    }
  }

  return (
    <div style={{ marginTop: '-60px', marginLeft: '-47px', fontSize: '16px' }}>
      <div style={{ fontSize: '18px' }}>{meetInfo.title}</div>
      <div style={{ margin: '8px 0' }}>
        会议时间：{meetInfo.startTime} 至 {meetInfo.endTime}
      </div>
      <div>会议地点：{meetInfo.meetingAddr}</div>
      <div
        style={{ display: 'flex', justifyContent: 'center', marginTop: '40px', marginLeft: '47px' }}
      >
        <img
          src={meetInfo.qrCodeUrl}
          alt=""
          style={{
            width: '200px',
            height: '200px',
            display: 'block',
            border: '1px solid #cbcfd6',
            borderRadius: '8px',
          }}
        />
      </div>
    </div>
  )
}

export default SignIn
