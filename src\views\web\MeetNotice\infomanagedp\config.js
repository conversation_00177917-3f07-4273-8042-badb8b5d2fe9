export const getDiffMeetType = type => {
  let meetList = []
  switch (type) {
    case 'area1':
      meetList = [
        '全区工作会议等',
        '专业线条会议等',
        '党代会、职代会等',
        '全国或区域性会议',
        '合并召开会议等',
      ]
      break
    case 'area2':
      meetList = ['专项会议等', '全国或区域性会议', '合并召开会议等']
      break
    case 'area3':
      meetList = ['业务性会议等', '全国或区域性会议', '编写类会议等']
      break
    case 'city1':
      meetList = [
        '市公司工作会议等',
        '市公司党代会、职代会等',
        '全国或区域性会议',
        '合并召开会议等',
      ]
      break
    case 'city2':
      meetList = ['专业会议等', '全区或区域性会议', '合并召开会议等']
      break
    case 'city3':
      meetList = ['业务性、基层重要会议']
      break
    case 'other1':
      meetList = ['业务性、基层重要会议']
      break
    default:
      meetList = []
  }
  return meetList
}

export const diffMeetLimit = (meetType, meetName) => {
  let meetLimit = []
  if (meetType === 'area1') {
    switch (meetName) {
      case '全区工作会议等':
        meetLimit = [2, 220, 750]
        break
      case '专业线条会议等':
        meetLimit = [2, 140, 750]
        break
      case '党代会、职代会等':
        meetLimit = [undefined, undefined, 750]
        break
      case '全国或区域性会议':
        meetLimit = [2, 140, 750]
        break
      case '合并召开会议等':
        meetLimit = [3, 220, 750]
    }
  }
  if (meetType === 'area2') {
    switch (meetName) {
      case '专项会议等':
        meetLimit = [2, 80, 650]
        break
      case '全国或区域性会议':
        meetLimit = [2, 80, 650]
        break
      case '合并召开会议等':
        meetLimit = [2, 80, 650]
        break
    }
  }
  if (meetType === 'area3') {
    switch (meetName) {
      case '业务性会议等':
        meetLimit = [1.5, 40, 550]
        break
      case '全国或区域性会议':
        meetLimit = [1.5, 55, 550]
        break
      case '编写类会议等':
        meetLimit = [undefined, 40, 550]
        break
    }
  }
  if (meetType === 'city1') {
    switch (meetName) {
      case '市公司工作会议等':
        meetLimit = [2, 80, 650]
        break
      case '市公司党代会、职代会等':
        meetLimit = [undefined, undefined, 650]
        break
      case '全国或区域性会议':
        meetLimit = [2, 80, 650]
        break
      case '合并召开会议等':
        meetLimit = [3, 80, 650]
        break
    }
  }
  if (meetType === 'city2') {
    switch (meetName) {
      case '专业会议等':
        meetLimit = [2, 50, 550]
        break
      case '全区或区域性会议':
        meetLimit = [2, 50, 550]
        break
      case '合并召开会议等':
        meetLimit = [2, 50, 550]
        break
    }
  }
  if (meetType === 'city3') {
    switch (meetName) {
      case '业务性、基层重要会议':
        meetLimit = [1.5, 30, 450]
        break
    }
  }
  if (meetType === 'other1') {
    switch (meetName) {
      case '业务性、基层重要会议':
        meetLimit = [1.5, 30, 450]
        break
    }
  }
  return meetLimit
}

export const getLimitDays = number => {
  let dayList = []
  if (!number) {
    dayList = [0.5, 1, 1.5, 2, 2.5, 3, '3以上']
  } else {
    for (let a = 0.5; a <= number; a = a + 0.5) {
      dayList.push(a)
    }
  }
  return dayList.map(v => v.toString())
}

export const meetTitleRule = (res, _meetTitle) => {
  const MA = res[0]
  const MF = res[1]
  const DA = res[2]
  if (MA == 1) {
    return _meetTitle
  } else if (MA == 2) {
    return _meetTitle
  } else if (MA == 3) {
    if (DA == 1) {
      return _meetTitle
    }
    if (DA == 2 && MF == 1) {
      return _meetTitle
    }
    if (DA == 2 && MF == 2) {
      return false
    }
  }
}

export const getBudgetAmount = data => {
  let limitNumber = ''
  const meetingDays = data[0]
  const staffNumber = data[1] || 0
  const meetingPersonNumber = data[2]
  const meetingLevel = data[3]
  const meetingType = data[4]
  const diffReult = diffMeetLimit(meetingLevel, meetingType)[2]
  const costs = diffReult ? diffReult : 0
  if (meetingDays && meetingPersonNumber && costs) {
    if (meetingDays == '3以上') {
      limitNumber = null
    } else {
      limitNumber =
        (Number(meetingPersonNumber) + Number(staffNumber)) * (Number(meetingDays) + 1) * costs
    }
  } else {
    limitNumber = null
  }
  return limitNumber
}

export const userMeetingLevel = (list, orgId) => {
  if (list && orgId) {
    let a = Object.keys(list).filter(data => {
      return list[data].includes(Number(orgId))
    })
    console.log('a========', a)
    if (a.join() === 'areaOrgIds') {
      return [
        { label: '区一类会议', value: 'area1' },
        { label: '区二类会议', value: 'area2' },
        { label: '区三类会议', value: 'area3' },
      ]
    }

    if (a.join() === 'cityOrgIds') {
      return [
        { label: '市一类会议', value: 'city1' },
        { label: '市二类会议', value: 'city2' },
        { label: '市三类会议', value: 'city3' },
      ]
    }

    if (a.join() === 'otherOrgIds') {
      return [{ label: '市三类会议', value: 'other1' }]
    }
  }
}

export const getMeetingTitle = (list, orgId, cityOrgName = '', loginOrgName = '') => {
  if (list && orgId) {
    let a = Object.keys(list).filter(data => {
      return list[data].includes(Number(orgId))
    })
    if (a.join() === 'areaOrgIds') {
      return '中国移动通信集团广西有限公司会议通知'
    }

    if (a.join() === 'cityOrgIds') {
      return `中国移动通信集团广西有限公司${loginOrgName}会议通知`
    }

    if (a.join() == 'otherOrgIds') {
      return `中国移动通信集团广西有限公司${cityOrgName}${loginOrgName}会议通知`
    }
  }
}

export const getMeetingSerialNumber = (list, orgId, cityOrgName = '') => {
  if (list && orgId) {
    let a = Object.keys(list).filter(data => {
      return list[data].includes(Number(orgId))
    })
    if (a.join() === 'areaOrgIds') {
      return `会议通知[${new Date().getFullYear()}] 号`
    }

    if (['cityOrgIds', 'otherOrgIds'].includes(a.join()) && cityOrgName) {
      if (cityOrgName === '桂林公司') {
        return `市移会议通知[${new Date().getFullYear()}] 号`
      }
      return `${cityOrgName[0]}移会议通知[${new Date().getFullYear()}] 号`
    }
  }
}

export const formConfig = {
  _dept: '起草单位',
  time: '拟稿时间',
  user: '会议经办人',
  signer: '签发人',
  issuanceTime: '签发时间',
  phone: '联系电话',
  serialNumber: '编号',
  _taskLevel: '缓急程度',
  meetingAategory: '会议类别',
  meetingForm: '会议形式',
  isNeedOtherDptAttend: '是否需要其他部门领导参会',
  isProduceMeetingFee: '是否产生会议费用',
  meetingLevel: '会议级别',
  meetingType: '会议类型',
  repayDepartment: '报账部门',
  isScheduledMeeting: '是否为计划内会议',
  isInDptFeeBudget: '是否在部门年度会议费预算内',
  budgetAmount: '预算金额',
  meetingName: '会议名称',
  rangePicker: '开始以及结束时间',
  meetingDays: '会议天数',
  meetingPlace: '会议地点',
  meetingPerson: '现场会议参会人员',
  meetingPersonNumber: '现场会议参加人数',
  meetingHost: '会议主持人',
  mainVenueLocation: '主会场地点',
  videoBranchVenueLocation: '视频分会场地点',
  videoBranchVenuePerson: '视频分会场参会人员',
  remark: '备注',
  upload: '附件',
  isNeedPutCards: '是否需要摆放桌牌',
  putRequire: '摆放要求',
  isNeedProjectionEquipment: '是否需要投影设备',
  branchIsNeedProjectionEquipment: '分会场是否需要投影设备',
  isNeedElectronicScreenShow: '是否需要电子屏显',
  screenShowContent: '屏显内容',
  fileList: '会议内容',
  staffNumber: '工作人员数量',
  meetingPlan: '会议计划',
  meetingNameAndPlanNotUniformDescription: '会议名称与计划名称不一致情况说明',
  isNeedPersonToUpDept: '是否需要员工到上级单位参会（不含同城）',
}

export const appointmentList = [
  { label: '同步预约', value: '1' },
  { label: '已预约', value: '2' },
  { label: '无需预约', value: '3' },
]
