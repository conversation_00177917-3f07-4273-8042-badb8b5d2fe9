import React, { useEffect, useState, useMemo, useRef } from 'react'
import { SchemaMarkupForm, FormButtonGroup, Submit, Reset, createAsyncFormActions, createFormActions, FormEffectHooks } from '@formily/antd'
import { Input, NumberPicker, FormMegaLayout, Select, Radio, FormItemGrid, DatePicker, FormBlock } from '@formily/antd-components'
import { message, Modal, Spin } from 'antd'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import { getQueryString, closeTheWindow, emergencyLevel, getHtmlCode } from 'ROOT/utils'
import { useGetTips } from 'ROOT/hooks'
import useSigner from 'ROOT/hooks/useSigner'
import getUserOrgType from 'ROOT/hooks/getUserOrgType'
import newDeptSelect from 'ROOT/components/Formily/newDeptSelect'
import SelectOrg from 'ROOT/components/Formily/Select'
import SelectDept from 'ROOT/components/Formily/deptSelect'
import SelectMember from 'ROOT/components/Formily/userSelect'
import { Buttons } from 'ROOT/components/Process'
import service from 'ROOT/service'
import { getWpsUrl } from 'ROOT/utils/wps'
import PersonalInfo from 'ROOT/components/Formily/PersonalInfo'
import Editor from 'ROOT/components/Formily/Editor'
import EditableInput from 'ROOT/components/Formily/Editable'
import SignCode from 'ROOT/components/Formily/SignCode'
import ApplyInfo from 'ROOT/components/Formily/ApplyInfo'
import ApplyHeader from 'ROOT/components/Formily/ApplyHeader'
import { getLocalTime, getLocalTimeDay } from 'ROOT/constants/index'
import Upload from 'ROOT/components/Formily/Upload'
// import Upload from 'ROOT/components/Formily/UploadOld'
import useMyInfo from 'ROOT/hooks/useMyInfo'
import Dept from 'ROOT/components/Formily/Dept'
import SeletMeetingPlan from 'ROOT/components/Formily/selectMeetingPlan'
import EvaluationForm from 'ROOT/components/Formily/EvaluationForm'
import WpsEditor from 'ROOT/components/Formily/WpsEditor'
import moment from 'moment'
import MainText from 'ROOT/components/MainText'
import Cookies from 'js-cookie'
import { debounce, get, isEmpty, has } from 'lodash'
import { patchData } from 'ROOT/utils/index'
import { withRouter } from 'react-router-dom'
import schema from './schema'
import PreviewModal from '../module/previewModal'
import { meetingType, signerNodeName } from '../module/config'
import { appointmentList, getDiffMeetType, diffMeetLimit, getLimitDays, formConfig, meetTitleRule, getBudgetAmount, userMeetingLevel, getMeetingTitle, getMeetingSerialNumber } from './config'
// import { closeBtn, deleteBtn, saveAndExit } from '../module/buttons'
const { confirm } = Modal

const { onFieldValueChange$, onFieldInputChange$, onFormValuesChange$ } = FormEffectHooks

SelectDept.isFieldComponent = true
PersonalInfo.isFieldComponent = true
Dept.isFieldComponent = true
Upload.isFieldComponent = true
Editor.isFieldComponent = true
SelectMember.isFieldComponent = true
// FormBlock.isFieldComponent = true
EditableInput.isFieldComponent = true
WpsEditor.isFieldComponent = true
SelectOrg.isFieldComponent = true
newDeptSelect.isFieldComponent = true
SignCode.isFieldComponent = true
ApplyInfo.isFieldComponent = true
ApplyHeader.isFieldComponent = true
SeletMeetingPlan.isFieldComponent = true
EvaluationForm.isFieldComponent = true

export default withRouter((props) => {
  const typeIndex = 0
  const [initValue, setInitValue] = useState({});
  const [editable, setEditable] = useState(true);
  const [meetTitle, setMeetTitle] = useState('')
  const [content, setContent] = useState('')
  const [domKey, setDomKey] = useState()
  const [showPreviewModal, setShowPreviewModal] = useState(false)
  const [budgetCashMax, setBudgetCashMax] = useState(0)
  const [previewSchema, setPreviewSchema] = useState()
  const [loading, setLoading] = useState(false)
  const [meetingAategory, setMeetingAategory] = useState(null)

  const [nodeName, setNodeName] = useState(null)
  const [meetingAppointmentId, setMeetingAppointmentId] = useState()
  const actions = useMemo(() => createAsyncFormActions(), []);
  const { userTaskId, procKey, appId, procFormDataKey, procFormKey, debug } = useMemo(() => getQueryString(props.location.search), []);
  const [meetingList, setMeetingList] = useState([])
  // const myInfo = useMyInfo({ isRequest: !procFormDataKey })
  const myInfo = useMyInfo({ isSetStorage: true })
  const formId = useRef(+procFormDataKey)
  const _meetTitle = useRef('')
  const userOrgList = getUserOrgType()
  const [enableWrite, setEnableWrite] = useState(false)
  const [access, setAccess] = useState({ limit: 'NONE' })
  const [currentDept, setCurrentDept] = useState({})
  const { signer, date } = useSigner({
    userTaskId,
    nodeName: signerNodeName,
  })
  let {
    orgId = Cookies.get('orgId'),
    orgName,
    id = Cookies.get('userId'),
    name = Cookies.get('username')
  } = JSON.parse(
    Cookies.get('uiapp') ||
    Cookies.get('WEBG_STORAGE') ||
    Cookies.get('EntAdminG_STORE') ||
    localStorage.WEBG_STORAGE ||
    localStorage.EntAdminG_STORE ||
    '{}'
  )

  useEffect(() => {
    actions.setFieldState('fileList', state => {
      state.props['x-component-props'].meetTitle = meetTitle
    })
    document.title = meetTitle || '信息技术管理部会议通知'
    if (document.getElementById('breadnav')) {
      document.getElementById('breadnav').innerHTML = meetTitle || '信息技术管理部会议通知'
    }
  }, [meetTitle])

  useEffect(() => {
    if (document.getElementById('breadnav')) {
      document.getElementById('breadnav').innerHTML = '信息技术管理部会议通知'
    }
    actions.setFieldState('*(upload)', state => {
      state.props['x-component-props'].isShowSetWatermark = true
    })
  }, [])

  // 判断是否有签发人以及签发时间
  useEffect(() => {
    actions.setFieldState('*(signer)', state => {
      state.value = signer || null
    })
    actions.setFieldState('*(issuanceTime)', state => {
      state.value = getLocalTimeDay(date) || null
    })
  }, [signer, date])

  //  数据回填
  useEffect(() => {
    const fetchData = async () => {
      // actions.setFieldState('*(draftDept, draftUser, draftPhone, signer, draftDate)', state => {
      //   state.editable = false
      // });
      if (procFormDataKey) { // 触发详情接口用什么做判断条件
        let isNew = false
        // 草稿箱中的数据也要兼容旧数据
        await service.checkNewData({
          reportId: procFormDataKey,
        }).then((res) => {
          isNew = res.data
          if (res.data === false) {
            actions.setFieldState('*(isSend, code, apply, applyList, bookRoom, meetingPlace1, mainVenueLocation1)', state => {
              state.visible = false
            })
          }
        })

        const wpsContent = await getWpsUrl(procFormDataKey)
        service.getFormData({
          reportId: procFormDataKey, // 获取详情，也就是初始值
        }).then((res) => {
          if (res && res.data) {
            if (wpsContent) {
              res.data.data.fileList[0] = wpsContent
            }
            let data = res.data.data;
            if (data && data.rangePicker) {
              data.startTime = data.hasOwnProperty('startTime') ? data.startTime : data.rangePicker[0]
              data.endTime = data.hasOwnProperty('endTime') ? data.endTime : data.rangePicker[1]
            }

            // 默认隐藏
            actions.setFieldState('*(meetingPlace1, mainVenueLocation, mainVenueLocation1)', state => {
              state.visible = false
            })

            if (isNew) {
              actions.setFieldState('applyList', state => {
                const titleMaps = []
                // eslint-disable-next-line no-unused-expressions
                data && data.apply && data.apply.length > 0 && data.apply.forEach((item) => {
                  if (item.checked) {
                    titleMaps.push({ [item.type]: item.name })
                  }
                })
                state.props['x-component-props'].applyList = titleMaps
              })
              actions.setFieldState('applyList', state => {
                state.props['x-component-props'].meetingName = data.meetingName ? data.meetingName : ''
              })
              actions.setFieldState('*(meetingPlace1,meetingPlace,mainVenueLocation1,mainVenueLocation)', state => {
                const errorTip = data.errorTip ? data.errorTip : ''
                state.props.description = <div style={{ color: '#ff4d4f' }}>{errorTip}</div>
              })
              actions.setFieldState('code', state => {
                state.props['x-component-props'].qrCodeUrl = data.qrCodeUrl ? data.qrCodeUrl : ''
                state.props['x-component-props'].meetingName = data.meetingName ? data.meetingName : ''
              })
              actions.getFieldValue('user').then(data => {
                actions.setFieldState('applyList', state => {
                  state.props['x-component-props'].user = data
                })
              })

              const bookRoomList = ['1', '2'].includes(data.meetingAategory) ? [appointmentList[2]] : appointmentList
              actions.setFieldState('bookRoom', state => {
                state.props.enum = bookRoomList
                state.value = data.bookRoom || '3'
              })
            }

            setInitValue(data)
            resetContentProps(procFormDataKey)
            actions.setFormState(state => {
              state.values = data
            })
            // actions.setFieldState('content', state => { // 避免editor组件重复触发
            //   if (res.data.data.content) {
            //     state.props['x-isUpdate'] = true
            //   }
            // })
          }
          // actions.setFieldState('content', state => { // 避免editor组件重复触发
          //   if (res.data.data.content) {
          //     state.props['x-isUpdate'] = true
          //   }
          // })
        })

        if (isNew) {
          await service.connection({
            reportId: procFormDataKey,
          }).then(res => {
            if (!res.data) {
              return false
            }
            setMeetingAppointmentId(res.data.id)
            actions.setFieldState('code', state => {
              state.props['x-component-props'].reportId = procFormDataKey
            })
            actions.setFieldState('applyList', state => {
              state.props['x-component-props'].meetingAppointmentId = res.data.id
              state.props['x-component-props'].reportId = procFormDataKey
            })
          })
        }
      }
      else {
        actions.setFieldState('code', state => {
          state.visible = false
        })
      }
    }
    fetchData()
  }, [])

  const initValueChange = (value) => {
    const { meetingAategory, meetingForm, bookRoom, startTime, endTime } = value || {}
    Promise.all([meetingAategory, meetingForm, bookRoom, startTime, endTime]).then(async (data) => {
      let meetList = []
      const startT = moment(data[3]).valueOf()
      const endT = moment(data[4]).valueOf()
      if (data[2] === '1' && startT && endT) {
        await service.getRoom({
          startTime: startT,
          endTime: endT,
        }).then(res => {
          meetList = res.data
        })
      } else if (data[2] === '2') {
        await service.getMeeting({}).then(res => {
          meetList = res.data
        })
      }
      let newMeetList = [...meetList]
      newMeetList = newMeetList.map(item => {
        return {
          label: item.roomName,
          value: data[2] !== '1' ? item.meetingInviteId : item.roomId
        }
      })
      setMeetingList(newMeetList)
      if (data[1] === '2' || data[1] === '3') {// 2,3时不展示meetingPlace
        actions.setFieldState('*(meetingPlace1, meetingPlace)', state => {
          state.visible = false
        })
        if (data[2] === '3') {// 不预约 input
          actions.setFieldState('mainVenueLocation1', state => {
            state.visible = false
          })
          actions.setFieldState('mainVenueLocation', state => {
            state.visible = true
          })
        } else {
          actions.setFieldState('mainVenueLocation', state => {
            state.visible = false
          })
          actions.setFieldState('mainVenueLocation1', state => {
            state.visible = true
            state.props.enum = newMeetList
          })
        }
      } else {// 1时展示mainVenueLocation
        actions.setFieldState('*(mainVenueLocation1, mainVenueLocation)', state => {
          state.visible = false
        })
        if (data[2] === '3') {// 不预约 input
          actions.setFieldState('meetingPlace1', state => {
            state.visible = false
          })
          actions.setFieldState('meetingPlace', state => {
            state.visible = true
          })
        } else {
          actions.setFieldState('meetingPlace', state => {
            state.visible = false
          })
          actions.setFieldState('meetingPlace1', state => {
            state.visible = true
            state.props.enum = newMeetList
          })
        }
      }
    })
  }

  useEffect(() => {
    if (Object.keys(initValue).length > 0) {
      initValueChange(initValue)
    }
  }, [initValue])

  useEffect(() => {
    if (!procFormDataKey) {
      service.saveForm({
        type: meetingType[typeIndex].type,
        classify: {
          name: meetingType[typeIndex].name,
          englishName: meetingType[typeIndex].englishName,
        },
        config: formConfig,
        data: {
          _taskLevel: '3',
          isFeedback: '2',
          isAdd: '1',
          draftDate: moment().format('YYYY年MM月DD日'),
        },
      }).then(res => {
        const { data } = res
        resetContentProps(data)
        formId.current = data

        // 添加默认值，避免空值的时候绑定关系丢失
        actions.setFieldValue('meetingAategory', '1')
        actions.setFieldState('*(meetingPlace1, mainVenueLocation, mainVenueLocation1)', state => {
          state.visible = false
        })

        // report和会议室的关联信息
        service.connection({
          reportId: data
        }).then(res => {
          if (!res.data) {
            return false
          }
          setMeetingAppointmentId(res.data.id)
          actions.setFieldState('code', state => {
            state.props['x-component-props'].reportId = data
          })
          actions.setFieldState('applyList', state => {
            state.props['x-component-props'].meetingAppointmentId = res.data.id
          })
        })
      })
    }
  }, [])

  useEffect(() => {
    const { loginOrgId, cityOrgName, loginOrgName } = myInfo
    if (userOrgList && loginOrgId) {
      actions.setFieldValue('drafterInfo', myInfo)
      // actions.setFieldValue('deptInfo',userOrgList)
      actions.setFieldState('meetingLevel', state => {
        state.props.enum = userMeetingLevel(userOrgList, loginOrgId)
      })
      _meetTitle.current = getMeetingTitle(userOrgList, loginOrgId, cityOrgName, loginOrgName)
      actions.setFieldValue('serialNumber', getMeetingSerialNumber(userOrgList, loginOrgId, cityOrgName))
    }
  }, [myInfo, userOrgList])

  const getColor = (text, color) => {
    return <div style={{ color: `${color}` }}>{text}</div>
  }

  const resetUserInfo = () => {
    const { linkPath = [], loginName, loginOrgName = '', loginMobile, cityOrgName } = myInfo
    if (!procFormDataKey) {
      // actions.setFieldState('rangePicker', state => {
      //   state.value = [moment().format('YYYY-MM-DD HH:mm'), moment().add(1, 'days').format('YYYY-MM-DD HH:mm')]
      // })
      actions.setFieldState('startTime', state => {
        state.value = moment().format('YYYY-MM-DD HH:mm')
      })
      actions.setFieldState('endTime', state => {
        state.value = moment().add(1, 'days').format('YYYY-MM-DD HH:mm')
      })
    }
    // actions.setFieldState('flag', state => {
    //   if (linkPath[0] && linkPath[0].rootDeptName) {
    //     state.value = `${linkPath[0].rootDeptName || ''}会议通知`
    //     setMeetTitle(`${linkPath[0].rootDeptName || ''}会议通知`)
    //     setLoading(false)
    //   }
    // })
    actions.setFieldState('user', state => {
      state.value = loginName
    })
    actions.setFieldState('phone', state => {
      state.value = loginMobile
    })
    actions.setFieldState('_dept', state => {
      // const org = orgList.map(i => i.name).join('\\')
      state.props.enum = linkPath && linkPath.length > 0 ? linkPath.map(item => ({
        value: item.deptId,
        label: item.linkPath,
        deptName: item.rootDeptName,
      })) : []
      if (initValue._dept && initValue._dept.value) {
        state.value = initValue._dept
      } else {
        state.value = linkPath && linkPath.length > 0 ? {
          value: linkPath[0].deptId,
          label: linkPath[0].linkPath,
          deptName: linkPath[0].rootDeptName,

        } : { value: '', label: '', deptName: '' }
      }
      setLoading(false)
    })

    actions.getFieldValue('meetingAategory').then((value) => {
      if (value === '2') {
        const year = new Date().getFullYear()
        actions.setFieldState('meetingName', state => {
          state.props.editable = false
          state.value = `中国移动广西公司${cityOrgName}${year}年第（-）次公司领导专题办公会议`
          state.props.description = getColor('注：公司领导专题办公会议次数在通知签发后自动生成', '#5C626B')
        })
      }
    })
  }

  useEffect(() => {
    if (myInfo && Object.keys(myInfo).length > 0) {
      resetUserInfo()
    }
  }, [myInfo])

  useEffect(() => {
    if (debug) {
      ableEditForm()
    }
  }, [])

  const ableEditForm = async () => {
    const res = await service.getEditableForm()
    if (res.success && res.data) {
      // resetFormState()
      setEnableWrite(true)
      // setAbleEditBtn([{
      //   name: '更新数据',
      //   async: true,
      //   onClick: async () => {
      //     await saveForm(() => {
      //       closeTheWindow(props.location.search)
      //     }, true)
      //   }
      // }])
    }
  }

  const onMount = ({ access, editMode, draft, process }) => {
    console.log('====---------->>>>>>>>>', process)
    const { nodeName, taskStatus } = process || {}
    if (taskStatus === 'over' && !has(access, 'limit') && has(process.access, 'limit')) {
      // 特殊情况 流程为over access为空 但是通过process.access 传过来了
      access.limit = process.access.limit
    }
    setNodeName(nodeName)
    setAccess(access)
    // if (nodeName === '起草人设置查看权限') {}

    if (access && Object.keys(access).length > 0 && editMode && editMode.includes('web')) {
      Object.keys(access).forEach(key => {
        actions.setFieldState(key, state => {
          switch (access[key]) {
            case 'NONE':
              state.display = false
              break
            case 'READ':
              state.editable = false
              break
            case 'WRITE':
              state.editable = true
              state.display = true
              break
            default: break
          }
        })
      })
    } else {
      // actions.setFormState((state) => {
      //   state.editable = false
      // })
    }
  }

  const preview = async () => {
    const data = await actions.getFormSchema()
    setPreviewSchema(data)
    setShowPreviewModal(true)
  }
  const formCheck = async () => {
    const startTime = await actions.getFieldValue('startTime')
    const endTime = await actions.getFieldValue('endTime')
    const isNeedPersonToUpDept = await actions.getFieldValue('isNeedPersonToUpDept')
    const meetingForm = await actions.getFieldValue('meetingForm')
    const isProduceMeetingFee = await actions.getFieldValue('isProduceMeetingFee')
    const isScheduledMeeting = await actions.getFieldValue('isScheduledMeeting')
    const meetingPlan = await actions.getFieldValue('meetingPlan')
    if (isScheduledMeeting === '2' && meetingPlan) {
      message.error('当前选择了会议计划，请修改是否为计划内会议为是')
      return false
    }
    if (meetingForm === '2' && isNeedPersonToUpDept === '1') {
      message.error('请修改会议形式为线下会议')
      return false
    }
    if (isNeedPersonToUpDept === '1' && isProduceMeetingFee === '2') {
      message.error('请修改是否产生会议费用为是')
      return false
    }

    if (startTime >= endTime) {
      message.error('开始时间必须小于结束时间')
      return false
    }
    if (isScheduledMeeting === '2') {
      const answer = ['1', '2', '1', '1', '1', '1', '2', '2', '1']
      const evaluationForm = await actions.getFieldValue('evaluationForm')
      // 把对象evaluationForm的所有key值转成数组
      const userAnswer = Object.keys(evaluationForm).map(key => evaluationForm[key])
      debugger
      if (answer.join('') !== userAnswer.join('')) {
        message.error('计划外会议为基层减负评估未通过，无法提交')
        return false
      } else {
        return 1
      }
    }
    const meetingPersonNumber = actions.getFieldValue('meetingPersonNumber')
    const staffNumber = actions.getFieldValue('staffNumber')
    const budgetAmount = actions.getFieldValue('budgetAmount')
    return Promise.all([meetingPersonNumber, staffNumber, budgetAmount]).then((list = []) => {
      if (list && list[1] > list[0] * 0.15) {
        message.error('工作人员数量不能超过现场会议参加人数的百分之十五')
        return undefined
      }

      if (budgetCashMax && list[2] > budgetCashMax) {
        message.error('预算金额不能超过最大值，请重新输入金额')
        return undefined
      }
      return 1
    })
  }
  const commonEvent = async (values) => {
    const bookRoom = values.bookRoom
    let isPermission = true
    if (bookRoom === '1') {
      await service.checkRoom({
        roomId: values.meetingPlace1 || values.mainVenueLocation1,
        beginTime: moment(values.startTime).valueOf(),
        endTime: moment(values.endTime).valueOf(),
        reportId: formId.current,
      }).then(res => {
        isPermission = res.data
      })
    } else if (bookRoom === '2') {
      if (!values.meetingPlace1 || values.mainVenueLocation1) {
        return false
      }
      await service.checkMeeting({
        meetingInviteId: values.meetingPlace1 || values.mainVenueLocation1,
        reportId: formId.current,
      }).then(res => {
        isPermission = res.data
      })
    }
    if (isPermission === false) {
      return false
    } else {
      const meetingId = values.meetingPlace || values.meetingPlace1 || values.mainVenueLocation1 || values.mainVenueLocation
      const meetingAddr = meetingList && meetingList.length > 0 ? get(meetingList.filter(item => item.value === meetingId), '[0].label', '') : ''
      await service.addMeeting({
        id: meetingAppointmentId,
        type: values.bookRoom,
        groupId: Cookies.get('groupId'),
        orgId: orgId,
        meetingAddr: bookRoom === '3' ? meetingId : meetingAddr,
        meetingId: bookRoom === '2' ? meetingId : null,
        roomId: bookRoom === '1' ? meetingId : null,
        reportId: formId.current,
        creatorId: Cookies.get('userId'),
        procFormKey: procFormKey || 'meeting'
      })
    }
  }
  const commonCommit = async () => {
    const data = await actions.submit()
    if (await formCheck()) {
      const values = data.values;
      // await commonEvent(values)
      values.errorTip = values.errorTip ? values.errorTip : ''
      values.qrCodeUrl = values.qrCodeUrl ? values.qrCodeUrl : ''
      if (!values.apply) {
        values.apply = [{ "name": "姓名", "type": "name", "key": "1", "age": "", "index": 0, "isFixed": true, "checked": true }, { "name": "手机号", "type": "phone", "key": "2", "age": "", "index": 1, "isFixed": true, "checked": true }, { "name": "所在单位", "type": "unit", "key": "3", "age": "", "index": 2, "isFixed": true, "checked": true }, { "name": "职务", "type": "position", "key": "4", "age": "", "index": 3, "isFixed": true, "checked": true }, { "name": "民族", "type": "nation", "key": "5", "age": "", "index": 4, "isFixed": true, "checked": false }, { "name": "是否用车", "type": "isPickUp", "key": "6", "age": "", "index": 5, "isFixed": true, "checked": false }, { "name": "车牌号", "type": "licenseNum", "key": "7", "age": "", "index": 6, "isFixed": true, "checked": false }, { "name": "备注", "type": "remark", "key": "8", "age": "", "index": 7, "isFixed": true, "checked": true }]
      }
      const meetingId = values.meetingPlace || values.meetingPlace1 || values.mainVenueLocation1 || values.mainVenueLocation
      const meetingAddrInfo = meetingList && meetingList.length > 0 ? get(meetingList.filter(item => item.value === meetingId), '[0].label', '') : ''
      const meetingAddr = values.bookRoom === '3' ? meetingId : meetingAddrInfo
      values.meetingAddr = meetingAddr
      const result = patchData(values, initValue)
      const res = service.upDateForm({
        config: formConfig,
        data: result,
        reportId: formId.current,
      })
      return res
    }
  }

  const submitForm = async () => {
    const res = await commonCommit()
    const data = await actions.submit()
    let values = data.values;
    if (res && res.success) {
      // return { id: +formId.current || res.data, values: values }
      return +formId.current
    }
    throw new Error('保存出错')
  }

  const saveDraft = async (callback) => {
    const info = myInfo || localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo')) : {}

    const data = await actions.submit()
    if (!data.values.apply) {
      data.values.apply = [{ "name": "姓名", "type": "name", "key": "1", "age": "", "index": 0, "isFixed": true, "checked": true }, { "name": "手机号", "type": "phone", "key": "2", "age": "", "index": 1, "isFixed": true, "checked": true }, { "name": "所在单位", "type": "unit", "key": "3", "age": "", "index": 2, "isFixed": true, "checked": true }, { "name": "职务", "type": "position", "key": "4", "age": "", "index": 3, "isFixed": true, "checked": true }, { "name": "民族", "type": "nation", "key": "5", "age": "", "index": 4, "isFixed": true, "checked": false }, { "name": "是否用车", "type": "isPickUp", "key": "6", "age": "", "index": 5, "isFixed": true, "checked": false }, { "name": "车牌号", "type": "licenseNum", "key": "7", "age": "", "index": 6, "isFixed": true, "checked": false }, { "name": "备注", "type": "remark", "key": "8", "age": "", "index": 7, "isFixed": true, "checked": true }]
    }
    const meetingId = data.values.meetingPlace || data.values.meetingPlace1 || data.values.mainVenueLocation1 || data.values.mainVenueLocation
    const meetingAddrInfo = meetingList && meetingList.length > 0 ? get(meetingList.filter(item => item.value === meetingId), '[0].label', '') : ''
    const meetingAddr = data.values.bookRoom === '3' ? meetingId : meetingAddrInfo
    data.values.meetingAddr = meetingAddr
    const result = patchData(data.values, initValue)
    const res = await service.saveDraft({
      appId,
      appTasks: [{
        appTaskId: formId.current.toString(),
        businessType: 2,
        emergencyLevel: emergencyLevel[+result._taskLevel],
        handleEntry: [{
          handleType: 0, // 草稿
          handlerId: info.loginUid,
        }],
        processType: meetingType[typeIndex].name,
        sponsorId: info.loginUid,
        jumpToDetail: 1,
        title: meetTitle,
        detailUrl: decodeURIComponent(procFormDataKey ? `${location.href.replace('/extra-forms/', '/extra-forms-h5/').replace(/backurl=.*?&|backurl=.*?$/, '')}` : `${location.href.replace('/extra-forms/', '/extra-forms-h5/').replace(/backurl=.*?&|backurl=.*?$/, '')}&procFormDataKey=${formId.current}`),
        webDetailUrl: decodeURIComponent(procFormDataKey ? `${location.href.replace(/backurl=.*?&|backurl=.*?$/, '')}` : `${location.href.replace(/backurl=.*?&|backurl=.*?$/, '')}&procFormDataKey=${formId.current}`),
      }],
    })
    if (res.success) {
      message.success('操作成功', () => {
        if (typeof callback === 'function') callback(res)
      })
    }
  }

  const saveForm = async (callback, isDraft) => {
    const res = await commonCommit()
    if (res.success) {
      if (isDraft) {
        saveDraft(callback)
      } else {
        message.success('操作成功', () => {
          if (typeof callback === 'function') callback(res)
        })
      }
    }
  }

  const deleteDraft = async () => {
    const info = localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo')) : {}

    const res = await service.deleteDraft({
      appId,
      appTasks: [{
        appTaskId: formId.current.toString(),
        handlerIds: [info.loginUid],
      }],
    })
    if (res.success) {
      setTimeout(() => {
        message.success('操作成功', () => {
          closeTheWindow(props.location.search)
        })
      }, 4000)
    }
  }

  const deleteForm = async () => {
    confirm({
      content: '你选择了注销当前文档，系统将删除该文档，是否继续？',
      onOk: async () => {
        const res = await service.deleteForm({
          reportId: procFormDataKey,
        })
        if (res.success) {
          deleteDraft()
        } else {
          message.error(data.msg)
        }
      },
    })
  }

  useEffect(() => {
    window.addEventListener('process', (event) => {
      switch (event.detail.type) {
        case 'hybg':
          message.error('表单状态异常，无法发起会议变更通知')
          break
        case 'hwdx':
          message.error('请先提交表单数据')
          break
        default:
          finishSubmit()
      }
    })
  }, [])

  const finishSubmit = () => {
    deleteDraft()
  }

  useEffect(() => {
    actions.setFieldState('repayDepartment', (state) => {
      state.props['x-component-props'] = {
        ...state.props['x-component-props'],
        orgId: orgId,
        orgName: orgName
      }
    })

  }, [orgId, orgName])


  const { controlTipsInfo } = useGetTips({ currentDept, procKey, meetingAategory, isMeeting: true })

  useEffect(() => {
    if (controlTipsInfo.type === 2) {
      Modal.confirm({
        icon: <ExclamationCircleOutlined style={{ color: '#4F84D2' }} />,
        title: '提示',
        content: (
          // eslint-disable-next-line react/no-danger
          <div id='__CONTROL_CONFIRM__' dangerouslySetInnerHTML={{ __html: controlTipsInfo.promptContent }} />
        ),
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          console.log('aaaaaa')
        },
      })
    }
  }, [controlTipsInfo])

  const resetContentProps = (procFormDataKey) => {
    actions.setFieldState('fileList', (state) => {
      state.props['x-component-props'] = {
        ...state.props['x-component-props'],
        reportId: procFormDataKey,
        type: meetingType[typeIndex].type,
        config: formConfig,
      }
    })
  }

  const buttonGroup = () => {
    const array = [
      {
        name: '保存退出',
        async: false,
        onClick: () => {
          saveForm(() => {
            closeTheWindow(props.location.search)
          }, true)
        }
      },
      {
        name: '保存',
        async: false,
        onClick: () => {
          saveForm('', true)
        }
      }
    ]
    if (procFormDataKey) {
      array.push({
        name: '注销文档',
        async: false,
        onClick: () => {
          deleteForm()
        }
      })
    }
    return array
  }

  const uesEffects = () => {

    const getColor = (text, color) => {
      return <div style={{ color: `${color}` }}>{text}</div>
    }

    const debounceHandleFormValueChange = debounce((data) => {
      setDomKey(Math.random())
    }, 500)

    onFormValuesChange$().subscribe(debounceHandleFormValueChange)

    onFieldValueChange$('fileList').subscribe(({ value }) => {
      if (value && value.length > 0) {
        if (value[0].needConvert) {
          getHtmlCode(value[0].url, (html) => {
            setContent(html)
          })
        } else {
          setContent(value[0].html)
        }
      }
    })

    onFieldValueChange$('*(meetingAategory, isProduceMeetingFee, isNeedOtherDptAttend)').subscribe((data) => {
      const meetingAategory = actions.getFieldValue('meetingAategory')
      const isProduceMeetingFee = actions.getFieldValue('isProduceMeetingFee')
      const isNeedOtherDptAttend = actions.getFieldValue('isNeedOtherDptAttend')
      Promise.all([meetingAategory, isProduceMeetingFee, isNeedOtherDptAttend]).then(res => {
        setMeetingAategory(res[0])
        const ruleResult = meetTitleRule(res, _meetTitle.current)
        if (ruleResult) {
          setMeetTitle(ruleResult)
          actions.setFieldValue('formTitle', ruleResult)
        } else {
          actions.getFieldValue('flag').then(data => {
            if (data) {
              setMeetTitle(data)
              actions.setFieldValue('formTitle', data)
            }
          })
        }
        if(['1', '2'].includes(res[0])) {
          actions.setFieldState('evaluationForm', state => {
            state.visible = false
          })
        }
      })
    })

    onFieldInputChange$('*(meetingDays,staffNumber,meetingPersonNumber,meetingLevel,meetingType)').subscribe(() => {
      const meetingDays = actions.getFieldValue('meetingDays')
      const staffNumber = actions.getFieldValue('staffNumber')
      const meetingPersonNumber = actions.getFieldValue('meetingPersonNumber')
      const meetingLevel = actions.getFieldValue('meetingLevel')
      const meetingType = actions.getFieldValue('meetingType')

      Promise.all([meetingDays, staffNumber, meetingPersonNumber, meetingLevel, meetingType]).then(data => {
        actions.setFieldState('budgetAmount', state => {
          const cashMax = getBudgetAmount(data) ? getBudgetAmount(data) / 10000 : null
          setBudgetCashMax(cashMax)
          if (cashMax) {
            state.props.description = `最大值为${cashMax}万元哦`
          } else {
            state.props.description = null
          }
        });
      })
    });

    onFieldInputChange$('meetingLevel').subscribe(() => {
      actions.setFieldValue('meetingType', null)
    });
    onFieldValueChange$('bookRoom').subscribe(() => {
      actions.setFieldValue('.*(meetingPlace, meetingPlace1, mainVenueLocation1, mainVenueLocation)', null)
    })
    onFieldInputChange$('apply').subscribe((data) => {
      actions.setFieldState('applyList', state => {
        const titleMaps = []
        data.value.forEach((item) => {
          if (item.checked) {
            titleMaps.push({ [item.type]: item.name })
          }
        })
        state.props['x-component-props'].applyList = titleMaps
      })
    })

    onFieldInputChange$('.*(meetingAategory, _dept)').subscribe((data) => {
      const meetingAategory = actions.getFieldValue('meetingAategory')
      const isProduceMeetingFee = actions.getFieldValue('_dept')
      Promise.all([meetingAategory, isProduceMeetingFee]).then((values) => {
        const year = new Date().getFullYear()
        if (values[0] === '2') {
          const label = get(values, '1.label', '')
          const cityOrgNameArr = label.split('\\')
          const cityOrgName = get(cityOrgNameArr, '0', orgName)
          actions.setFieldState('meetingName', state => {
            state.props.editable = false;
            state.value = `中国移动广西公司${cityOrgName}${year}年第（-）次公司领导专题办公会议`;
            state.props.description = getColor('注：公司领导专题办公会议次数在通知签发后自动生成', '#5C626B')
          });
        } else {
          actions.setFieldState('meetingName', state => {
            state.props.editable = true;
            state.value = null;
            state.props.description = null
          });
        }
        actions.getFieldValue('_dept').then((deptValue) => {
          const param = {
            orgId: orgId,
            deptId: deptValue ? deptValue.value : null, // 确保 deptValue 不为 null 或 undefined
          };
          service.getMeetingPlan(param).then(res => {
            console.log("res===", res);
            const newMeetingList = res.map(item => {
              return {
                label: item.meetingName,
                value: item.id,
                ...item
              };
            });
            actions.setFieldState('meetingPlan', state => {
              state.props.enum = newMeetingList;
              state.value = null;
            });
          });
        });
        const bookRoomList = ['1', '2'].includes(values[0]) ? [appointmentList[2]] : appointmentList
        actions.setFieldState('bookRoom', state => {
          state.props.enum = bookRoomList
          state.value = appointmentList[2].value
        })
      })
    })

    onFieldInputChange$('meetingType').subscribe((data) => {
      actions.getFieldValue('meetingLevel').then((value) => {
        console.log("data.value===", data.value)
        if (data.value && value) {
          const _diffMeetLimit = diffMeetLimit(value, data.value,)
          if (_diffMeetLimit && _diffMeetLimit.length > 0) {
            actions.setFieldState('meetingDays', state => {
              state.props.enum = getLimitDays(_diffMeetLimit[0])
              state.value = null
            })
            actions.setFieldState('meetingPersonNumber', state => {
              state.props['x-component-props'].max = _diffMeetLimit[1]
              state.value = null
            })
          }
        }
      })
    })

    onFieldValueChange$('_dept').subscribe(({ value, rootDeptId }) => { // onFieldValueChange
      if (value && value.label) {
        setCurrentDept(value)
        actions.setFieldState('flag', state => {
          state.value = `${value.deptName || ''}会议通知`
          setMeetTitle(`${value.deptName || ''}会议通知`)
        })
        actions.setFieldState('fileList', (state) => {
          state.props['x-component-props'].orgId = value.value
        })
      }
    })

    onFieldValueChange$('isScheduledMeeting').subscribe((data) => {
      if (data.value === '1') {
        actions.setFieldState('meetingPlan', state => {
          state.required = true
        })
      } else {
        actions.setFieldState('meetingPlan', state => {
          state.required = false
        })
      }
    })
    onFieldValueChange$('.*(meetingPlan, meetingName)').subscribe(data => {
      const meetingPlan = actions.getFieldValue('meetingPlan')
      const meetingName = actions.getFieldValue('meetingName')
      Promise.all([meetingPlan, meetingName]).then((values) => {
        if (values.length == 2 && values[0] && values[1]) {
          actions.setFieldState('meetingNameAndPlanNotUniform', state => {
            state.value = values[0].label !== values[1]
          })
          actions.setFieldState('meetingNameAndPlanNotUniformDescription', state => {
            state.required = values[0].label ? values[0].label !== values[1] : false
          })

        }
        // if (values.length == 2) {
        //   actions.setFieldState('isScheduledMeeting', state => {
        //     state.value = values[0] ? (values[0].label ? '1' : '2') : '2'
        //   })
        // }
        if (values.length == 2 && !values[0].label) {
          actions.setFieldState('meetingPlan', state => {
            state.value = null
          })
        }
      })
    })

    // onFieldValueChange$('.*(meetingPlan)').subscribe(data => {
    //   actions.setFieldState('isScheduledMeeting', state => {
    //     state.value = data.value?'1':'2'
    //   })
    //   actions.setFieldState('meetingNameAndPlanNotUniformDescription', state => {
    //     state.required = data.value?true:false
    //   })
    // })
    onFieldInputChange$('.*(meetingAategory, meetingForm, bookRoom, startTime, endTime)').subscribe(data => {
      const meetingAategory = actions.getFieldValue('meetingAategory')
      const meetingForm = actions.getFieldValue('meetingForm') //2,3时不展示meetingPlace
      const bookRoom = actions.getFieldValue('bookRoom')
      const startTime = actions.getFieldValue('startTime')
      const endTime = actions.getFieldValue('endTime')
      Promise.all([meetingAategory, meetingForm, bookRoom, startTime, endTime]).then(async (data) => {
        let meetList = []
        const startT = moment(data[3]).valueOf()
        const endT = moment(data[4]).valueOf()
        if (data[2] === '1' && startT && endT) {
          // 获取同步预约会议室列表
          await service.getRoom({
            startTime: startT,
            endTime: endT,
          }).then(res => {
            meetList = res.data
          })
        } else if (data[2] === '2') {
          // 获取已预约会议列表
          await service.getMeeting({}).then(res => {
            meetList = res.data
          })
        }
        let newMeetList = [...meetList]
        newMeetList = newMeetList.map(item => {
          return {
            label: item.roomName,
            value: data[2] !== '1' ? item.meetingInviteId : item.roomId
          }
        })
        setMeetingList(newMeetList)
        if (data[1] === '2' || data[1] === '3') {//2,3时不展示meetingPlace
          actions.setFieldState('*(meetingPlace1, meetingPlace)', state => {
            state.visible = false
          })
          if (data[2] === '3') {//不预约 input
            actions.setFieldState('mainVenueLocation1', state => {
              state.visible = false
            })
            actions.setFieldState('mainVenueLocation', state => {
              state.visible = true
            })
          } else {
            actions.setFieldState('mainVenueLocation', state => {
              state.visible = false
            })
            actions.setFieldState('mainVenueLocation1', state => {
              state.visible = true
              state.props.enum = newMeetList
            })
          }
        } else {//1时展示mainVenueLocation
          actions.setFieldState('*(mainVenueLocation1, mainVenueLocation)', state => {
            state.visible = false
          })
          if (data[2] === '3') {//不预约 input
            actions.setFieldState('meetingPlace1', state => {
              state.visible = false
            })
            actions.setFieldState('meetingPlace', state => {
              state.visible = true
            })
          } else {
            actions.setFieldState('meetingPlace', state => {
              state.visible = false
            })
            actions.setFieldState('meetingPlace1', state => {
              state.visible = true
              state.props.enum = newMeetList
            })
          }
        }
      })
    })

    onFieldValueChange$('.*(isProduceMeetingFee, meetingForm)').subscribe(data => {
      const isProduceMeetingFee = actions.getFieldValue('isProduceMeetingFee')
      const meetingForm = actions.getFieldValue('meetingForm')
      Promise.all([meetingForm, isProduceMeetingFee]).then(values => {
        actions.setFieldState('meetingDays', state => {
          if (values[0] == '1' || values[0] == '3') {
            if (values[1] == '1') {
              state.visible = true
            } else {
              state.visible = false
            }
          } else {
            state.visible = false
          }
        })
      })
    })

    onFieldValueChange$('.*(meetingAategory, isProduceMeetingFee)').subscribe(data => {
      const meetingAategory = actions.getFieldValue('meetingAategory')
      const isProduceMeetingFee = actions.getFieldValue('isProduceMeetingFee')
      Promise.all([meetingAategory, isProduceMeetingFee]).then(values => {
        actions.setFieldState('meetingPersonNumber', state => {
          if (values[0] == '1' || values[0] == '3') {
            if (values[1] == '1') {
              state.visible = true
            } else {
              state.visible = false
            }
          } else {
            state.visible = false
          }
        })
      })
    })

    const debounceHandleFieldInputChange = debounce((data) => {
      actions.setFieldState('fileList', state => {
        state.props['x-component-props'].currentSchema = data
      })
    }, 500)

    onFieldInputChange$().subscribe(debounceHandleFieldInputChange)
  }
  const expressionScope = {
    getColor: (text, color) => { return <div style={{ color: `${color}` }}>{text}</div> },
    getDiffMeetType: (type) => { return getDiffMeetType(type) },
    labelAlign: 'top',
    disabledDate: (current) => { return current && current <= moment().subtract(1, 'days').endOf('day') },
    disabledDateTime: () => {
      return {
        disabledHours: () => range(0, 24).splice(4, 20),
        disabledMinutes: () => range(30, 60),
        disabledSeconds: () => [55, 56],
      };
    },
    getActions: actions,
    // meetTitle: meetTitle,
  }
  const components = {
    TextArea: Input.TextArea,
    Input,
    NumberPicker,
    FormMegaLayout,
    Upload,
    Select,
    Radio,
    RadioGroup: Radio.Group,
    RangePicker: DatePicker.RangePicker,
    DatePicker,
    SelectDept,
    PersonalInfo,
    Dept,
    Editor,
    SelectMember,
    FormBlock,
    EditableInput,
    WpsEditor,
    SelectOrg,
    newDeptSelect,
    SignCode,
    ApplyInfo,
    ApplyHeader,
    SeletMeetingPlan,
    EvaluationForm
  }

  return (
    <div >
      {controlTipsInfo.type === 1 && <div id='__CONTROL_CONFIRM_TEXT__' dangerouslySetInnerHTML={{ __html: controlTipsInfo.promptContent }} />}
      <Spin spinning={loading}>
        <h1 className='form-title'>{meetTitle}</h1>
        <SchemaMarkupForm
          schema={schema({ debug, nodeName, limit: access.limit })}
          components={components}
          actions={actions}
          effects={() => {
            uesEffects()
          }}
          initialValues={initValue}
          expressionScope={{ ...expressionScope }}
          previewPlaceholder='-'
          editable={editable}
        >
          <div >
            {(!isEmpty(initValue) || !procFormDataKey) &&
              <div>
                <Buttons
                  procKey={procKey}
                  appId={appId}
                  onMount={onMount}
                  onSubmit={submitForm}
                  onSubmitOpen={async () => {
                    const data = await actions.submit()
                    if (await formCheck()) {
                      const formValues = data.values
                      await commonEvent(formValues)
                      formValues.errorTip = formValues.errorTip ? formValues.errorTip : ''
                      formValues.qrCodeUrl = formValues.qrCodeUrl ? formValues.qrCodeUrl : ''
                      if (!formValues.apply) {
                        formValues.apply = [{ "name": "姓名", "type": "name", "key": "1", "age": "", "index": 0, "isFixed": true, "checked": true }, { "name": "手机号", "type": "phone", "key": "2", "age": "", "index": 1, "isFixed": true, "checked": true }, { "name": "所在单位", "type": "unit", "key": "3", "age": "", "index": 2, "isFixed": true, "checked": true }, { "name": "职务", "type": "position", "key": "4", "age": "", "index": 3, "isFixed": true, "checked": true }, { "name": "民族", "type": "nation", "key": "5", "age": "", "index": 4, "isFixed": true, "checked": false }, { "name": "是否用车", "type": "isPickUp", "key": "6", "age": "", "index": 5, "isFixed": true, "checked": false }, { "name": "车牌号", "type": "licenseNum", "key": "7", "age": "", "index": 6, "isFixed": true, "checked": false }, { "name": "备注", "type": "remark", "key": "8", "age": "", "index": 7, "isFixed": true, "checked": true }]
                      }
                      const meetingId = formValues.meetingPlace || formValues.meetingPlace1 || formValues.mainVenueLocation1 || formValues.mainVenueLocation
                      const meetingAddrInfo = meetingList && meetingList.length > 0 ? get(meetingList.filter(item => item.value === meetingId), '[0].label', '') : ''
                      const meetingAddr = formValues.bookRoom === '3' ? meetingId : meetingAddrInfo
                      formValues.meetingAddr = meetingAddr
                      const { meetingForm } = formValues || {}
                      if (!meetingForm) {
                        return { ...formValues, meetingForm: '1', isNeedOtherDptAttend: '1', isProduceMeetingFee: '2' }
                      }
                      const result = patchData(formValues, initValue)
                      return result
                    }
                    return Promise.reject()
                  }}
                  extraButtons={buttonGroup()}
                />
              </div>
            }
          </div>
        </SchemaMarkupForm>
        <MainText key={domKey} title={meetTitle} content={content} formType='meetNotice' actions={actions} />
        {showPreviewModal &&
          <PreviewModal
            onCancel={() => setShowPreviewModal(false)}
            schame={previewSchema}
            actions={actions}
          />
        }
      </Spin>
    </div>
  )
})