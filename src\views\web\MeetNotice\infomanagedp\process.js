import React, { useEffect, useState, useMemo, useRef } from 'react';
import { <PERSON>hema<PERSON><PERSON>, SchemaMarkupForm, SchemaMarkup<PERSON>ield as Field, createAsyncFormActions, createFormActions, FormEffectHooks } from '@formily/antd'
import { Input, NumberPicker, FormMegaLayout, Select, Radio, FormItemGrid, DatePicker } from '@formily/antd-components'
import {
  Tabs,
  Space,
  Spin,
  message,
} from 'antd'
import { Buttons, Steps } from 'ROOT/components/Process'
import { getQueryString, closeTheWindow, getHtmlCode } from 'ROOT/utils'
import { getWpsUrl } from 'ROOT/utils/wps'
// import useSigner from 'ROOT/hooks/useSigner'
import SelectOrg from 'ROOT/components/Formily/Select'
import SelectDept from 'ROOT/components/Formily/deptSelect'
import getUserOrgType from 'ROOT/hooks/getUserOrgType'
import newDeptSelect from 'ROOT/components/Formily/newDeptSelect'
import service from 'ROOT/service'
import PersonalInfo from 'ROOT/components/Formily/PersonalInfo'
import Upload from 'ROOT/components/Formily/Upload'
import Editor from 'ROOT/components/Formily/Editor'
import SelectMember from 'ROOT/components/Formily/userSelect'
import EditableInput from 'ROOT/components/Formily/Editable'
import SignCode from 'ROOT/components/Formily/SignCode'
import ApplyInfo from 'ROOT/components/Formily/ApplyInfo'
import ApplyHeader from 'ROOT/components/Formily/ApplyHeader'
import WpsEditor from 'ROOT/components/Formily/WpsEditor'
import moment from 'moment'
import MainText from 'ROOT/components/MainText'
import Dept from 'ROOT/components/Formily/Dept'
import SeletMeetingPlan from 'ROOT/components/Formily/selectMeetingPlan'
import ControlModal from 'ROOT/components/ControlModal'
import EvaluationModal from 'ROOT/components/EvaluationModal';
import { debounce, get, isEmpty, has, unionBy } from 'lodash'
import Cookies from 'js-cookie'
import { patchData } from 'ROOT/utils/index'
import schema from './schema'
import { meetingType } from '../module/config'
import PermissionModal from '../module/permissionModal'
import Sms from './sms'
import { appointmentList, getDiffMeetType, getBudgetAmount, getMeetingTitle, formConfig, meetTitleRule, userMeetingLevel, diffMeetLimit, getLimitDays } from './config'
const { TabPane } = Tabs
const { onFieldValueChange$, onFieldInputChange$, onFormValuesChange$ } = FormEffectHooks

export default (props) => {
  const typeIndex = 0;
  // const needFilePermissionBtn = true
  let [initValue, setInitValue] = useState({});
  const [draftValue, setDraftValue] = useState({})
  const [content, setContent] = useState('')
  const [isCheckNewData, setIsCheckNewData] = useState(false)
  // 选线段落 
  const [paragraphContent, setParagraphContent] = useState('')
  // 是否设置过段落权限
  const [settingParagraph, setSettingParagraph] = useState(false)
  const settingParagraphRef = useRef(false)
  const [domKey, setDomKey] = useState()
  const [editable, setEditable] = useState(false)
  const [meetTitle, setMeetTitle] = useState('')
  const [budgetCashMax, setBudgetCashMax] = useState(0)
  const [showPermissionModal, setPermissionModal] = useState(false)
  const [buttonGroupList, setButtonGroupList] = useState([])
  const [backFillDataSouce, setBackFillDataSouce] = useState([])
  const [wpsFile, setWpsFile] = useState({})
  const _meetTitle = useRef('')
  const _fileList = useRef('')
  const startTimeRef = useRef(null)
  const [permissionObj, setPermissionObj] = useState({})
  const [loading, setLoading] = useState(true)
  const actions = useMemo(() => createAsyncFormActions(), []);
  const { userTaskId, procFormDataKey, debug, procFormKey } = useMemo(() => getQueryString(props.location.search), []);
  const [nodeName, setNodeName] = useState(null)
  const [isSigner, setIsSigner] = useState(false)
  const [operator, setOperator] = useState({})
  const [gmtCreate, setGmtCreate] = useState({})
  const [enableWrite, setEnableWrite] = useState(false)
  const [showControlModal, setShowControlModal] = useState(false)
  const [showEvaluationModal, setShowEvaluationModal] = useState(false)
  const userOrgList = getUserOrgType()
  const [access, setAccess] = useState({ limit: 'NONE' })
  const [deptInfo, setDeptInfo] = useState({})
  const [taskStatus, setTaskStatus] = useState('')
  const [needFilePermissionBtn, setNeedFilePermissionBtn] = useState(false)
  const [cacheApplyList, setCacheApplyList] = useState([])

  const [meetingAppointmentId, setMeetingAppointmentId] = useState()
  const [meetingList, setMeetingList] = useState([])
  let [isNewData, setIsNewData] = useState(false)
  const [visible, setVisible] = useState(false)
  const [meetingAategory, setMeetingAategory] = useState('')
  const [meetingForm, setMeetingForm] = useState('1')
  const [defaultUser, setDefaultUser] = useState([])
  const [smsContent, setSmsContent] = useState('')
  const [isAgree, setIsAgree] = useState('0')

  let {
    orgId = Cookies.get('orgId'),
    orgName,
    id = Cookies.get('userId'),
    name = Cookies.get('username')
  } = JSON.parse(
    Cookies.get('uiapp') ||
    Cookies.get('WEBG_STORAGE') ||
    Cookies.get('EntAdminG_STORE') ||
    localStorage.WEBG_STORAGE ||
    localStorage.EntAdminG_STORE ||
    '{}'
  )
  SelectDept.isFieldComponent = true;
  PersonalInfo.isFieldComponent = true;
  Dept.isFieldComponent = true;
  Upload.isFieldComponent = true
  Editor.isFieldComponent = true
  SelectMember.isFieldComponent = true
  EditableInput.isFieldComponent = true
  WpsEditor.isFieldComponent = true
  SelectOrg.isFieldComponent = true
  newDeptSelect.isFieldComponent = true
  SignCode.isFieldComponent = true
  ApplyInfo.isFieldComponent = true
  ApplyHeader.isFieldComponent = true
  SeletMeetingPlan.isFieldComponent = true

  const [ableEditBtn, setAbleEditBtn] = useState([])

  useEffect(() => {
    if (debug) {
      ableEditForm()
      actions.setFieldState('applyList', state => {
        state.props['x-component-props'].debug = debug
      })
      actions.setFieldState('*(upload)', state => {
        state.props['x-component-props'].isShowSetWatermark = true
      })
    }
  }, [])
  useEffect(() => {
    document.title = meetTitle || '信息技术管理部会议通知'
    if (document.getElementById('breadnav')) {
      document.getElementById('breadnav').innerHTML = meetTitle || '信息技术管理部会议通知'
    }
  }, [meetTitle])
  const ableEditForm = async () => {
    const res = await service.getEditableForm()
    if (res.success && res.data) {
      resetFormState()
      setEnableWrite(true)
      // writableAllFields()
      setAbleEditBtn([{
        name: '更新数据',
        async: true,
        onClick: async () => {
          await saveForm(() => {
            closeTheWindow(props.location.search)
          }, true)
        }
      }])
    }
  }

  function writableAllFields() {
    actions.setFieldState('*', state => {
      state.editable = true
      state.disabled = false
    })
  }

  // issuanceTime: getLocalTimeDay(date)
  // useEffect(() => {
  //   if (Object.keys(initValue).length > 0) {

  //     actions.setFormState(state => {
  //       state.values = (signer || date) ? { ...initValue, signer } : initValue
  //     })
  //   }
  // }, [initValue])

  useEffect(() => {
    if (document.getElementById('breadnav')) {
      document.getElementById('breadnav').innerHTML = '信息技术管理部会议通知'
    }
  }, [])

  useEffect(() => {
    if (isSigner) {
      actions.setFieldState('*(signer)', state => {
        state.value = '-'
      })
      actions.setFieldState('*(issuanceTime)', state => {
        state.value = '-'
      })
    }
  }, [isSigner])


  // 判断是否有签发人以及签发时间
  // useEffect(() => {
  //   actions.setFieldState('*(signer)', state => {
  //     state.value = signer || '-'
  //   })
  //   actions.setFieldState('*(issuanceTime)', state => {
  //     state.value = getLocalTime(date) || '-'
  //   })
  // }, [signer, date])

  const getPermissionDetail = async () => {
    // 先注销 通过debug拿到所有的字段
    if (access.limit !== 'NONE') { // 流程配置了limit的权限就不需要走下面了
      const res = await service.getParagraphDetail({ formId: procFormDataKey, debug: debug ? 1 : null })
      if (res && res.success) {
        const { data = {}, fileId, id, isSetting = false } = res.data || {}
        const { list = [] } = data || {}
        setPermissionObj({ list, fileId, id })
        setSettingParagraph(isSetting)
        settingParagraphRef.current = isSetting
        const ids = list.map(item => item.id)
        if (ids && ids.length > 0 && isSetting) {
          const data = await service.getParagraphContent({ ids: [...ids] })
          if (data && data.success) {
            // 不在往正文里面塞字段 而是单独开了一个内容
            // actions.setFieldValue('fileList', [{ html: data.data }])
            setParagraphContent(data.data)
            setLoading(false)
          }
        } else {
          setLoading(false)
        }
      }
    }
    setLoading(false)
  }

  useEffect(() => {
    setMeetTitleRule(initValue)
  }, [userOrgList, initValue])

  useEffect(() => {
    getPermissionDetail()
  }, [access, initValue])

  useEffect(() => {
    const { limit } = access || {}
    const { applyList = [] } = initValue || {}
    if (!limit) {
      actions.setFieldState('applyList', state => {
        state.value = applyList
      })
      return
    }
    if (limit === 'READ' || limit === 'WRITE') {
      const trueList = applyList.filter(item => {
        const { defaultUser: { orgId: userOrgId = '' } } = item || {}
        return userOrgId && `${userOrgId}` === `${orgId}`
      })
      actions.setFieldState('applyList', state => {
        state.value = trueList
      })
    } else {
      actions.setFieldState('applyList', state => {
        state.value = applyList
      })
    }
  }, [access])

  //  数据回填
  useEffect(async () => {
    actions.setFieldState('*', state => {
      state.props.description = null;
    });
    if (procFormDataKey) {
      // 检验新老数据
      await service.checkNewData({
        reportId: procFormDataKey,
      }).then((res) => {
        isNewData = res.data
        setIsNewData(res.data)
        if (res.data === false) {
          actions.setFieldState('*(isSend, code, apply, applyList, bookRoom, meetingPlace1, mainVenueLocation1)', state => {
            state.visible = false
          })
        }
      }).finally(() => {
        setIsCheckNewData(true)
      })
      const wpsContent = await getWpsUrl(procFormDataKey)
      await service.getFormData({
        reportId: procFormDataKey, // 获取详情，也就是初始值
        userTaskId,
        debug: debug ? 1 : null,
      }).then((res) => {
        if (res && res.data) {
          if (res.data.data) {
            setDeptInfo(res.data.data._dept)
          }
          if (wpsContent) {
            res.data.data.fileList[0] = wpsContent
          }
          // getPermissionDetail()
          let data = res.data.data;
          if (data && data.rangePicker) {
            data.startTime = data.hasOwnProperty('startTime') ? data.startTime : data.rangePicker[0]
            data.endTime = data.hasOwnProperty('endTime') ? data.endTime : data.rangePicker[1]
          }

          // 兼容老数据中存在没有该字段的情况
          if (!data.hasOwnProperty('isSend')) {
            data.isSend = false
          }

          startTimeRef.current = data.startTime
          const { operator, gmtCreate } = res.data
          initValue = { ...data }
          setInitValue({ ...data })
          setOperator(operator)
          setGmtCreate(gmtCreate)
          resetContentProps(procFormDataKey)
          setCacheApplyList(data.applyList)

          delete data.applyList

          actions.setFormState(state => {
            state.values = { ...data }
          })
          const { formTitle, specialCount, meetingCount, meetingAategory, fileList = [], meetingName, drafterInfo = {}, apply = [] } = data || {};
          const year = moment(gmtCreate).format('YYYY')
          setMeetTitle(formTitle) // 审批表单走 详情数据
          // _meetTitle.current = formTitle
          _fileList.current = fileList
          setWpsFile(fileList[0])
          if (isNewData) {
            if (!debug) {
              actions.setFieldState('*(apply)', state => {// 隐藏报名字段
                state.visible = false
              })
            }
            actions.setFieldState('applyList', state => {
              const titleMaps = []
              data && data.apply && data.apply.length > 0 && data.apply.forEach((item) => {
                if (item.checked) {
                  titleMaps.push({ [item.type]: item.name })
                }
              })
              state.props['x-component-props'].applyList = titleMaps
            })
            actions.setFieldState('applyList', state => {
              state.props['x-component-props'].meetingName = data.meetingName ? data.meetingName : ''
            })
            actions.setFieldState('*(meetingPlace1,meetingPlace,mainVenueLocation1,mainVenueLocation)', state => {
              let errorTip = data.errorTip ? data.errorTip : ''
              state.props.description = <div style={{ color: '#ff4d4f' }}>{errorTip}</div>
            })
            actions.setFieldState('code', state => {
              state.props['x-component-props'].qrCodeUrl = data.qrCodeUrl ? data.qrCodeUrl : ''
              state.props['x-component-props'].meetingName = data.meetingName ? data.meetingName : ''
            })
            actions.getFieldValue('user').then(data => {
              actions.setFieldState('applyList', state => {
                state.props['x-component-props'].user = data
              })
            })

            const bookRoomList = ['1', '2'].includes(data.meetingAategory) ? [appointmentList[2]] : appointmentList
            actions.setFieldState('bookRoom', state => {
              state.props.enum = bookRoomList
              state.value = data.bookRoom || '3'
            })
          }
          if (meetingCount) {
            actions.getFieldValue('serialNumber').then(data => {
              actions.setFieldValue('serialNumber', data.replace(' ', `${meetingCount > 9 ? meetingCount : "0" + meetingCount}`))
            })
          }
          if (specialCount && meetingAategory == '2') {
            const cityOrgName = get(drafterInfo, 'cityOrgName', '')
            actions.setFieldValue('meetingName', `中国移动广西公司${cityOrgName}${year}年第（${specialCount}）次公司领导专题办公会议`);
          }
          actions.setFieldState('fileList', state => {
            state.props['x-component-props'].reportId = procFormDataKey
          })

          actions.setFieldState('repayDepartment', (state) => {
            state.props['x-component-props'] = {
              ...state.props['x-component-props'],
              orgId: operator.orgId,
              orgName: operator.orgName
            }
          })

          const businessData = res.data.businessData
          if (businessData) {
            actions.setFieldState('*(signer)', state => {
              state.value = businessData.IssuerName || '-'
            })
            actions.setFieldState('*(issuanceTime)', state => {
              state.value = businessData.IssuerTime || '-'
            })
          }
          try {
            if (data && data.meetingType && data.meetingLevel) {
              const _diffMeetLimit = diffMeetLimit(data.meetingLevel, data.meetingType)
              if (_diffMeetLimit && _diffMeetLimit.length > 0) {
                actions.setFieldState('meetingDays', state => {
                  state.props.enum = getLimitDays(_diffMeetLimit[0])
                  // state.value = null
                })
                actions.setFieldState('meetingPersonNumber', state => {
                  state.props['x-component-props'].max = _diffMeetLimit[1]
                  // state.value = null
                })
              }
            }
          } catch (err) {
            console.log(err)
          }
        } else {
          setLoading(false)
        }
      })
      if (isNewData) {
        // report和会议室的关联信息
        await service.connection({
          reportId: procFormDataKey
        }).then(res => {
          if (!res.data) {
            return false
          }
          setMeetingAppointmentId(res.data.id)
          actions.setFieldState('code', state => {
            state.props['x-component-props'].reportId = procFormDataKey
          })
          actions.setFieldState('applyList', state => {
            state.props['x-component-props'].meetingAppointmentId = res.data.id
            state.props['x-component-props'].reportId = procFormDataKey
          })
        })
      }
    }
  }, [])

  const setMeetTitleRule = (res) => {
    const { drafterInfo, deptInfo, formTitle } = res || {}
    const { loginOrgId, cityOrgName, loginOrgName } = drafterInfo || {}

    if (userOrgList && loginOrgId) {
      actions.setFieldState('meetingLevel', state => {
        state.props.enum = userMeetingLevel(userOrgList, loginOrgId)
      })
      _meetTitle.current = getMeetingTitle(userOrgList, loginOrgId, cityOrgName, loginOrgName)
    } else {
      _meetTitle.current = formTitle
    }
  }
  const commonEvent = async (values) => {
    const bookRoom = values.bookRoom
    let isPermission = true
    if (bookRoom === '1') {
      await service.checkRoom({
        roomId: values.meetingPlace1 || values.mainVenueLocation1,
        beginTime: moment(values.startTime).valueOf(),
        endTime: moment(values.endTime).valueOf(),
        reportId: procFormDataKey,
      }).then(res => {
        isPermission = res.data
      })
    } else if (bookRoom === '2') {
      if (!values.meetingPlace1 || values.mainVenueLocation1) {
        return false
      }
      await service.checkMeeting({
        meetingInviteId: values.meetingPlace1 || values.mainVenueLocation1,
        reportId: procFormDataKey,
      }).then(res => {
        isPermission = res.data
      })
    }
    if (isPermission === false) {
      return false
    } else {
      const meetingId = values.meetingPlace || values.meetingPlace1 || values.mainVenueLocation1 || values.mainVenueLocation
      const findOne = meetingList && meetingList.length > 0 && meetingList.find(item => item.value === meetingId)
      const meetingAddr = findOne ? findOne.label : ''
      await service.addMeeting({
        id: meetingAppointmentId,
        type: values.bookRoom,
        groupId: Cookies.get('groupId'),
        orgId: orgId,
        meetingAddr: bookRoom === '3' ? meetingId : meetingAddr,
        meetingId: bookRoom === '2' ? meetingId : null,
        roomId: bookRoom === '1' ? meetingId : null,
        reportId: procFormDataKey,
        creatorId: Cookies.get('userId'),
        procFormKey: procFormKey || 'meeting'
      })
    }
  }
  const commonCommit = async () => {
    if (!await formCheck()) return false
    // todo 需要判断是新建表单的保存还是二次编辑表单的保存，走的接口不一样
    const data = await actions.submit()
    const { values } = data
    // await commonEvent(values)
    if (isNewData) {
      values.errorTip = values.errorTip ? values.errorTip : ''
      values.qrCodeUrl = values.qrCodeUrl ? values.qrCodeUrl : ''
      if (!values.apply) {
        values.apply = [{ "name": "姓名", "type": "name", "key": "1", "age": "", "index": 0, "isFixed": true, "checked": true }, { "name": "手机号", "type": "phone", "key": "2", "age": "", "index": 1, "isFixed": true, "checked": true }, { "name": "所在单位", "type": "unit", "key": "3", "age": "", "index": 2, "isFixed": true, "checked": true }, { "name": "职务", "type": "position", "key": "4", "age": "", "index": 3, "isFixed": true, "checked": true }, { "name": "民族", "type": "nation", "key": "5", "age": "", "index": 4, "isFixed": true, "checked": false }, { "name": "是否用车", "type": "isPickUp", "key": "6", "age": "", "index": 5, "isFixed": true, "checked": false }, { "name": "车牌号", "type": "licenseNum", "key": "7", "age": "", "index": 6, "isFixed": true, "checked": false }, { "name": "备注", "type": "remark", "key": "8", "age": "", "index": 7, "isFixed": true, "checked": true }]
      }
      const meetingId = values.meetingPlace || values.meetingPlace1 || values.mainVenueLocation1 || values.mainVenueLocation
      const meetingAddrInfo = meetingList && meetingList.length > 0 ? meetingList.filter(item => item.value === meetingId)[0].label : ''
      const meetingAddr = values.bookRoom === '3' ? meetingId : meetingAddrInfo
      values.meetingAddr = meetingAddr
      values.isSend = values.isSend || false
      data.values.isSend = values.isSend || false
      values.applyList = unionBy(values.applyList, cacheApplyList, 'defaultUser.id') || []
      data.values.applyList = unionBy(values.applyList, cacheApplyList, 'defaultUser.id') || []
    }

    if (!debug) {
      values.apply = initValue.apply
    }
    const result = patchData(values, initValue)
    const res = await procFormDataKey ? service.upDateForm({
      config: formConfig,
      data: {
        ...result,
        // issuanceTime: nodeName === '部门总经理审核' ? getLocalTimeDay(date) : values.issuanceTime ? values.issuanceTime : undefined
      },
      settingPermissions: needFilePermissionBtn || debug ? 1 : null,
      reportId: procFormDataKey,
    }) : service.saveForm({
      type: meetingType[typeIndex].type,
      classify: {
        name: meetingType[typeIndex].name,
        englishName: meetingType[typeIndex].englishName,
      },
      config: formConfig,
      data: data.values,
      settingPermissions: needFilePermissionBtn || debug ? 1 : null,
    })
    return res
  }

  const submitForm = async () => {
    const res = await commonCommit()
    if (res.success) {
      return Promise.resolve()
    }
    throw new Error('保存出错')
  }

  const saveForm = async (callback) => {
    const res = await commonCommit()
    if (res.success) {
      message.success('操作成功', () => {
        if (typeof callback === 'function') callback(res)
      })
    }
  }

  const resetFormState = () => {
    setEditable(true)
    actions.setFieldState('*(_dept)', state => {
      state.props['x-component'] = 'EditableInput'
    })
    actions.setFieldState('*(isSend)', state => {
      state.props.editable = true
    })
    actions.setFieldState('*(apply, applyList)', state => {
      state.props.editable = true
    })
  }

  const resetFormHideState = (taskStatus, process, editMode) => {
    if (process && process.access && Object.keys(process.access).length > 0 && editMode && editMode.includes('web')) {
      Object.keys(process.access).forEach(key => {
        if (taskStatus !== 'over') {
          if (key === 'isEditable' && process.access[key] === 'WRITE') {
            resetFormState()
            return
          }
        }
        actions.setFieldState(key, state => {
          switch (process.access[key]) {
            case 'NONE':
              state.display = taskStatus !== 'over' ? false : !!((taskStatus === 'over' && enableWrite))
              break
            case 'READ':
              state.editable = taskStatus !== 'over' ? false : !!((taskStatus === 'over' && enableWrite))
              break
            case 'WRITE':
              state.editable = taskStatus !== 'over' ? true : !!((taskStatus === 'over' && enableWrite))
              state.display = true
              break
            default: break
          }
        })
      })
    }
  }

  const onMount = async ({ access, editMode, draft, process }) => {
    if (Object.keys(initValue).length > 0) {
      handleInitChange(initValue)
    }
    const { nodeName, taskStatus, id, key } = process || {}
    // 打补丁 兼容流程结束了 就会不传给access的情况 本次需求根据流程返回的limit字段来决定是否调用查看正文的权限接口，直接展示所有内容 有需要 limit字段的情况
    if (taskStatus === 'over' && !has(access, 'limit') && has(process.access, 'limit')) {
      // 特殊情况 流程为over access为空 但是通过process.access 传过来了
      access.limit = process.access.limit
    }
    if ((access && access['isSigner'] && access['isSigner'] == 'NONE')) {
      setIsSigner(true)
    }
    setTaskStatus(taskStatus)
    setNodeName(nodeName)
    setAccess(access)
    let btns = []
    if ((nodeName === '起草人设置查看权限' && taskStatus === 'running') || (debug && enableWrite)) {
      console.log(nodeName, '起草人设置查看权限', taskStatus);
      setNeedFilePermissionBtn(true)
      btns.push({
        name: '设置正文段落权限',
        async: false,
        onClick: () => {
          if (_fileList.current && _fileList.current.length > 0) {
            setPermissionModal(true)
          } else {
            message.warn('当前没有正文内容，无法设置权限')
          }
        }
      })
    }
    // if(nodeName === '起草人分发'){
    //   const res = await service.selectProcinstByNameAndProcKey({actinstName: '部门总经理审核', procDeKey: id})
    //   console.log(res)
    //   actions.setFieldState('*(signer)', state => {
    //     state.value = res.data && res.data.length && res.data[0].assigneeName
    //   })
    // }
    if (nodeName === '开始节点' && taskStatus === 'running') {
      const _btns = [
        {
          name: '保存退出',
          async: false,
          onClick: () => {
            saveForm(() => {
              closeTheWindow(props.location.search)
            })
          }
        },
        {
          name: '保存',
          async: false,
          onClick: () => {
            saveForm('')
          }
        }
      ]
      btns = [...btns, ..._btns]
    }
    setButtonGroupList([...btns])

    // 处理字段显隐
    resetFormHideState(taskStatus, process, editMode)

    // 节点通过后不能编辑, 但debug可以编辑
    if (taskStatus === 'over') {
      actions.setFormState(state => {
        state.editable = enableWrite
      })
    }

    if (draft) {
      setDraftValue(draft)
      actions.setFormState(state => {
        state.values = draft
      })
    }
  }

  const handleInitChange = async (data) => {
    const { bookRoom, meetingForm, startTime, endTime } = data

    let meetList = []
    const startT = moment(startTime).valueOf()
    const endT = moment(endTime).valueOf()

    if (isNewData) {
      if (bookRoom === '1' && startT && endT) {
        // 获取同步预约会议室列表
        await service.getRoom({
          reportId: procFormDataKey,
          startTime: startT,
          endTime: endT,
        }).then(res => {
          meetList = res.data
        })
      } else if (bookRoom === '2') {
        // 获取已预约会议列表
        await service.getMeeting({
          reportId: procFormDataKey,
        }).then(res => {
          meetList = res.data
        })
      }

      let newMeetList = [...meetList]
      newMeetList = newMeetList.map(item => {
        return {
          label: item.roomName,
          value: bookRoom !== '1' ? item.meetingInviteId : item.roomId
        }
      })
      setMeetingList(newMeetList)
      if (['2', '3'].includes(meetingForm)) {//2,3时不展示meetingPlace
        actions.setFieldState('*(meetingPlace1, meetingPlace)', state => {
          state.visible = false
        })
        if (bookRoom === '3') {//不预约 input
          actions.setFieldState('mainVenueLocation1', state => {
            state.visible = false
          })
          actions.setFieldState('mainVenueLocation', state => {
            state.visible = true
          })
        } else {
          actions.setFieldState('mainVenueLocation', state => {
            state.visible = false
          })
          actions.setFieldState('mainVenueLocation1', state => {
            state.visible = true
            state.props.enum = newMeetList
          })
          setTimeout(() => {
            actions.setFieldState('mainVenueLocation', state => {
              state.visible = false
            })
          }, 1000)
        }
      } else {//1时展示mainVenueLocation
        actions.setFieldState('*(mainVenueLocation1, mainVenueLocation)', state => {
          state.visible = false
        })
        if (bookRoom === '3') {//不预约 input
          actions.setFieldState('meetingPlace1', state => {
            state.visible = false
          })
          actions.setFieldState('meetingPlace', state => {
            state.visible = true
          })
        } else {
          actions.setFieldState('meetingPlace', state => {
            state.visible = false
          })
          actions.setFieldState('meetingPlace1', state => {
            state.visible = true
            state.props.enum = newMeetList
          })
        }
      }
    } else {
      actions.setFieldState('meetingPlace', state => {
        if (['2', '3'].includes(meetingForm)) {
          state.visible = false
        } else {
          state.visible = true
        }
      })
    }
  }

  useEffect(() => {
    if (isNewData) {
      handleInitChange(initValue)
    } else {
      const { meetingForm } = initValue || {}
      if (!meetingForm) return
      actions.setFieldState('meetingPlace', state => {
        if (['2', '3'].includes(meetingForm)) {
          state.visible = false
        } else {
          state.visible = true
        }
      })
    }
  }, [isNewData, initValue])

  const resetContentProps = (procFormDataKey) => {
    actions.setFieldState('fileList', (state) => {
      state.props['x-component-props'] = {
        ...state.props['x-component-props'],
        reportId: procFormDataKey,
        type: meetingType[typeIndex].type,
        config: formConfig,
      }
    })
  }

  const formCheck = async (action) => {
    const startTime = await actions.getFieldValue('startTime')
    const endTime = await actions.getFieldValue('endTime')
    if (startTime >= endTime) {
      message.error('开始时间必须小于结束时间')
      return false
    }
    const meetingPersonNumber = await actions.getFieldValue('meetingPersonNumber')
    const staffNumber = await actions.getFieldValue('staffNumber')
    const budgetAmount = await actions.getFieldValue('budgetAmount')
    const isScheduledMeeting = await actions.getFieldValue('isScheduledMeeting')
    return Promise.all([meetingPersonNumber, staffNumber, budgetAmount]).then((list = []) => {
      if (list && list[1] > list[0] * 0.15) {
        message.error('工作人员数量不能超过现场会议参加人数的百分之十五')
        return undefined
      }

      if (budgetCashMax && list[2] > budgetCashMax) {
        message.error('预算金额不能超过最大值，请重新输入金额')
        return undefined
      }
      // 判断是否需要弹出基层减负一致性评估表，isScheduledMeeting==1计划内的不弹出
      if (isScheduledMeeting == '1' || access['isShowEvatluaionForm'] != 'WRITE') {
        return true
      } else {
        if (isAgree == '0' && action == '提交') {
          setShowEvaluationModal(true)
          return false
        } else if (isAgree == '1' && action == '退回') {
          message.error('您已勾选同意基层减负一致性评估表，无法操作退回')
          return undefined
        } else if (isAgree == '2' && action == '提交') {
          message.error('您已勾选不同意基层减负一致性评估表，无法操作提交')
          return undefined
        }
        return true
      }
    })
  }

  const toChange = async () => {
    const startTime = moment(startTimeRef.current, 'YYYY-MM-DD HH:mm').valueOf()
    if (startTime < Date.now()) {
      return message.error('会议已开始，不可修改')
    }

    try {
      const res = await service.getProcInstInfoOfForm({
        procFormUnionKeys: [
          { procFormKey, 'proFormDataKey': procFormDataKey },
        ],
      })
      const { status } = res[0] || {}
      if (status && (status.includes('终止') || status.includes('注销'))) {
        const text = status.includes('终止') ? '已终止' : '已注销'
        return message.error(`流程${text}，无法发起会议变更通知`)
      }
      sessionStorage.setItem('use-sessiton-storage-state-reportId', procFormDataKey)
      sessionStorage.setItem('use-sessiton-storage-state-procFormKey', procFormKey)
      props.history.push('/web/meetNotice/infomanagedp/meeting-changes')
    } catch (err) {
      console.log('getProcInstInfoOfForm err', err)
    }
  }

  useEffect(() => {
    window.addEventListener('process', e => {
      switch (e.detail.type) {
        case 'hybg':
          toChange()
          break
        case 'saved': break;
        case 'hwdx':
          smsOk()
          break
        case 'approved': // 审批后
        case 'added': // 加签后
        case 'stoped': // 终止后
        case 'rejected': // 退回后
        case 'forworded': // 转交后
        case 'cced': // 抄送后
        default: finishSubmit()
      }
    })

  }, [])

  const finishSubmit = () => {
    setTimeout(() => {
      closeTheWindow(props.location.search)
    }, 2000)
  }

  const onOKPromission = (dataSource) => {
    setPermissionModal(false)
    setBackFillDataSouce(dataSource)
  }


  const expressionScope = {
    getColor: (text, color) => { return <div style={{ color: `${color}` }}>{text}</div> },
    getDiffMeetType: (type) => { return getDiffMeetType(type) },
    labelAlign: 'left',
    getActions: actions,
    meetTitle: meetTitle,
    disabledDate: (current) => { return current && current <= moment().subtract(1, 'days').endOf('day') },
  }

  const uesEffects = () => {
    const getColor = (text, color) => {
      return <div style={{ color: `${color}` }}>{text}</div>
    }

    const debounceHandleFormValueChange = debounce((data) => {
      setDomKey(Math.random())
    }, 500)

    onFormValuesChange$().subscribe(debounceHandleFormValueChange)

    onFieldValueChange$('fileList').subscribe(({ value }) => {
      if (value && value.length > 0) {
        if (value[0].needConvert) {
          getHtmlCode(value[0].url, (html) => {
            // 判断如果已经有段落权限内容也对段落权限内容赋值
            if (settingParagraphRef.current) {
              setParagraphContent(html)
            }
            setContent(html)
          })
        } else {
          // 判断如果已经有段落权限内容也对段落权限内容赋值
          if (settingParagraphRef.current) {
            setParagraphContent(value[0].html)
          }
          setContent(value[0].html)
        }
        // 根据段落权限字段判断是否设置了正文权限段落 如果设置了调用接口
        if (settingParagraphRef.current) {
          service.paragraphClear({ formId: procFormDataKey })
          // 清空本地缓存
          setBackFillDataSouce([])
          setPermissionObj({})
        }
        setWpsFile(value[0])
      }
    })

    onFieldValueChange$('*(meetingAategory, isProduceMeetingFee, isNeedOtherDptAttend)').subscribe((data) => {
      const meetingAategory = actions.getFieldValue('meetingAategory')
      const isProduceMeetingFee = actions.getFieldValue('isProduceMeetingFee')
      const isNeedOtherDptAttend = actions.getFieldValue('isNeedOtherDptAttend')
      Promise.all([meetingAategory, isProduceMeetingFee, isNeedOtherDptAttend]).then(res => {
        const ruleResult = meetTitleRule(res, _meetTitle.current)
        if (ruleResult) {
          setMeetTitle(ruleResult)
          actions.setFieldValue('formTitle', ruleResult)
        } else {
          actions.getFieldValue('flag').then(data => {
            if (data) {
              setMeetTitle(data)
              actions.setFieldValue('formTitle', data)
            }
          })
        }
      })
    })

    onFieldInputChange$('*(meetingDays,staffNumber,meetingPersonNumber,meetingLevel,meetingType)').subscribe(() => {
      const meetingDays = actions.getFieldValue('meetingDays')
      const staffNumber = actions.getFieldValue('staffNumber')
      const meetingPersonNumber = actions.getFieldValue('meetingPersonNumber')
      const meetingLevel = actions.getFieldValue('meetingLevel')
      const meetingType = actions.getFieldValue('meetingType')

      Promise.all([meetingDays, staffNumber, meetingPersonNumber, meetingLevel, meetingType]).then(data => {
        actions.setFieldState('budgetAmount', state => {
          const cashMax = getBudgetAmount(data) ? getBudgetAmount(data) / 10000 : null
          setBudgetCashMax(cashMax)
          if (cashMax) {
            state.props.description = `最大值为${cashMax}万元哦`
          } else {
            state.props.description = null
          }
        });
      })
    });

    onFieldInputChange$('meetingLevel').subscribe(() => {
      actions.setFieldValue('meetingType', null)
    });
    onFieldValueChange$('bookRoom').subscribe(() => {
      console.log('onFieldValueChange===', initValue, Object.keys(initValue).length)
      if (Object.keys(initValue).length > 0) {
        const defaultBookRoom = `${initValue.bookRoom}` || ''
        const bookRoom = actions.getFieldValue('bookRoom')
        return Promise.all([bookRoom]).then((data) => {
          if ((data[0] !== undefined && defaultBookRoom !== `${data[0]}`)) {
            actions.setFieldValue('.*(meetingPlace, meetingPlace1, mainVenueLocation1, mainVenueLocation)', null)
          }
        })
      }
      // actions.setFieldValue('.*(meetingPlace, meetingPlace1, mainVenueLocation1, mainVenueLocation)', null)
    })
    onFieldValueChange$('.*( meetingPlace1, mainVenueLocation1)').subscribe(() => {
      console.log('onFieldValueChange===', initValue, Object.keys(initValue).length)
      if (Object.keys(initValue).length > 0) {
        const defaultMeetingPlace1 = initValue.meetingPlace1
        const defaultMainVenueLocation1 = initValue.mainVenueLocation1
        const meetingPlace1 = actions.getFieldValue('meetingPlace1')
        const mainVenueLocation1 = actions.getFieldValue('mainVenueLocation1')
        return Promise.all([meetingPlace1, mainVenueLocation1]).then((data) => {
          console.log('Promise11', defaultMeetingPlace1, data[0], defaultMainVenueLocation1, data[1])
          if ((data[0] !== undefined && defaultMeetingPlace1 !== data[0]) || (data[1] !== undefined && defaultMainVenueLocation1 !== data[1])) {
            actions.setFieldState('.*( meetingPlace1, mainVenueLocation1)', state => {
              state.props.description = null
            })
          }
        })
      }
    })
    onFieldInputChange$('meetingAategory').subscribe((data) => {
      const year = moment(gmtCreate).format('YYYY')
      if (data.value === '2') {
        const { drafterInfo = {} } = initValue
        const cityOrgName = get(drafterInfo, 'cityOrgName', '')
        actions.setFieldState('meetingName', state => {
          state.props.editable = false;
          state.value = `中国移动广西公司${cityOrgName}${year}年第（-）次公司领导专题办公会议`;
          state.props.description = getColor('注：公司领导专题办公会议次数在通知签发后自动生成', '#5C626B')
        });
      } else {
        actions.setFieldState('meetingName', state => {
          state.props.editable = true;
          state.value = null;
          state.props.description = null
        });
      }

      const bookRoomList = ['1', '2'].includes(data.value) ? [appointmentList[2]] : appointmentList
      actions.setFieldState('bookRoom', state => {
        state.props.enum = bookRoomList
        state.value = appointmentList[2].value
      })
    });

    onFieldInputChange$('meetingType').subscribe((data) => {
      actions.getFieldValue('meetingLevel').then((value) => {
        if (data.value && value) {
          const _diffMeetLimit = diffMeetLimit(value, data.value,)
          if (_diffMeetLimit && _diffMeetLimit.length > 0) {
            actions.setFieldState('meetingDays', state => {
              state.props.enum = getLimitDays(_diffMeetLimit[0])
              state.value = null
            })
            actions.setFieldState('meetingPersonNumber', state => {
              state.props['x-component-props'].max = _diffMeetLimit[1]
              state.value = null
            })
            // actions.setFieldState('budgetAmount', state => {
            //   state.props['x-component-props'].max = _diffMeetLimit[2]
            //   state.value = null
            // })
          }
        }
      })
    })


    onFieldValueChange$('.*(meetingAategory, isProduceMeetingFee)').subscribe(data => {
      const meetingAategory = actions.getFieldValue('meetingAategory')
      const isProduceMeetingFee = actions.getFieldValue('isProduceMeetingFee')
      Promise.all([meetingAategory, isProduceMeetingFee]).then(values => {
        actions.setFieldState('meetingPersonNumber', state => {
          if (values[0] == '1' || values[0] == '3') {
            if (values[1] == '1') {
              state.visible = true
            } else {
              state.visible = false
            }
          } else {
            state.visible = false
          }
        })
      })
    })
    onFieldInputChange$('apply').subscribe((data) => {
      actions.setFieldState('applyList', state => {
        const titleMaps = []
        data.value.forEach((item) => {
          if (item.checked) {
            titleMaps.push({ [item.type]: item.name })
          }
        })
        state.props['x-component-props'].applyList = titleMaps
      })
    })

    onFieldInputChange$('.*(meetingAategory, meetingForm, bookRoom, startTime, endTime)').subscribe(data => {
      const meetingAategory = actions.getFieldValue('meetingAategory')
      const meetingForm = actions.getFieldValue('meetingForm') //2,3时不展示meetingPlace
      const bookRoom = actions.getFieldValue('bookRoom')
      const startTime = actions.getFieldValue('startTime')
      const endTime = actions.getFieldValue('endTime')
      Promise.all([meetingAategory, meetingForm, bookRoom, startTime, endTime]).then(async (data) => {
        if (isNewData) {
          let meetList = []

          const startT = moment(data[3]).valueOf()
          const endT = moment(data[4]).valueOf()

          if (data[2] === '1' && startT && endT) {
            // 获取同步预约会议室列表
            await service.getRoom({
              reportId: procFormDataKey,
              startTime: startT,
              endTime: endT,
            }).then(res => {
              meetList = res.data
            })
          } else if (data[2] === '2') {
            // 获取已预约会议列表
            await service.getMeeting({
              reportId: procFormDataKey,
            }).then(res => {
              meetList = res.data
            })
          }
          let newMeetList = [...meetList]
          newMeetList = newMeetList.map(item => {
            return {
              label: item.roomName,
              value: data[2] !== '1' ? item.meetingInviteId : item.roomId
            }
          })
          setMeetingList(newMeetList)
          if (data[1] === '2' || data[1] === '3') {//2,3时不展示meetingPlace
            actions.setFieldState('*(meetingPlace1, meetingPlace)', state => {
              state.visible = false
            })
            if (data[2] === '3') {//不预约 input
              actions.setFieldState('mainVenueLocation1', state => {
                state.visible = false
              })
              actions.setFieldState('mainVenueLocation', state => {
                state.visible = true
              })
            } else {
              actions.setFieldState('mainVenueLocation', state => {
                state.visible = false
              })
              actions.setFieldState('mainVenueLocation1', state => {
                state.visible = true
                state.props.enum = newMeetList
              })
              setTimeout(() => {
                actions.setFieldState('mainVenueLocation', state => {
                  state.visible = false
                })
              }, 1000)
            }
          } else {//1时展示mainVenueLocation
            actions.setFieldState('*(mainVenueLocation1, mainVenueLocation)', state => {
              state.visible = false
            })
            if (data[2] === '3') {//不预约 input
              actions.setFieldState('meetingPlace1', state => {
                state.visible = false
              })
              actions.setFieldState('meetingPlace', state => {
                state.visible = true
              })
            } else {
              actions.setFieldState('meetingPlace', state => {
                state.visible = false
              })
              actions.setFieldState('meetingPlace1', state => {
                state.visible = true
                state.props.enum = newMeetList
              })
            }
          }
        } else {
          actions.setFieldState('meetingPlace', state => {
            if (data[1] == '2' || data[1] == '3') {
              state.visible = false
            } else {
              state.visible = true
            }
          })
        }
      })
    })

    onFieldValueChange$('.*(isProduceMeetingFee, meetingForm)').subscribe(data => {
      const isProduceMeetingFee = actions.getFieldValue('isProduceMeetingFee')
      const meetingForm = actions.getFieldValue('meetingForm')
      Promise.all([meetingForm, isProduceMeetingFee]).then(values => {
        actions.setFieldState('meetingDays', state => {
          if (values[0] == '1' || values[0] == '3') {
            if (values[1] == '1') {
              state.visible = true
            } else {
              state.visible = false
            }
          } else {
            state.visible = false
          }
        })
      })
    })

    onFieldValueChange$('.*(meetingAategory, isProduceMeetingFee)').subscribe(data => {
      const meetingAategory = actions.getFieldValue('meetingAategory')
      const isProduceMeetingFee = actions.getFieldValue('isProduceMeetingFee')
      Promise.all([meetingAategory, isProduceMeetingFee]).then(values => {
        actions.setFieldState('meetingPersonNumber', state => {
          if (values[0] == '1' || values[0] == '3') {
            if (values[1] == '1') {
              state.visible = true
            } else {
              state.visible = false
            }
          } else {
            state.visible = false
          }
        })
      })
    })

  }
  const EvaluationBtn = () => {
    return (
      <div style={{ cursor: 'pointer', color: '#4f84d2' }} onClick={() => {
        setShowEvaluationModal(true)
      }}>基层减负一致性评估表</div>
    )
  }
  const components = {
    TextArea: Input.TextArea,
    Input,
    NumberPicker,
    FormMegaLayout,
    Upload,
    Select,
    Radio,
    RadioGroup: Radio.Group,
    RangePicker: DatePicker.RangePicker,
    DatePicker,
    SelectDept,
    PersonalInfo,
    Dept,
    Editor,
    SelectMember,
    EditableInput,
    WpsEditor,
    SelectOrg,
    newDeptSelect,
    SignCode,
    ApplyInfo,
    ApplyHeader,
    SeletMeetingPlan,
    EvaluationBtn
  }
  const smsOk = async () => {
    const data = await actions.submit()
    const { values } = data
    const defaultUserInfo = []
    if (values.applyList && values.applyList.length > 0) {
      values.applyList.forEach(item => {
        defaultUserInfo.push(item.defaultUser)
      })
    }

    const { bookRoom, meetingAategory, meetingName, startTime, endTime, meetingPlace, meetingPlace1, mainVenueLocation, mainVenueLocation1, meetingAddr, videoBranchVenueLocation } = values || {}
    let { meetingForm } = values || {}
    let content = ''
    let addr = meetingForm ? '' : meetingAddr

    try {
      let meetList = []
      const startT = moment(startTime).valueOf()
      const endT = moment(endTime).valueOf()
      if (bookRoom === '1' && startT && endT) {
        // 获取同步预约会议室列表
        await service.getRoom({
          reportId: procFormDataKey,
          startTime: startT,
          endTime: endT,
        }).then(res => {
          meetList = res.data
        })
      } else if (bookRoom === '2') {
        // 获取已预约会议列表
        await service.getMeeting({
          reportId: procFormDataKey,
        }).then(res => {
          meetList = res.data
        })
      }
      let newMeetList = [...meetList]
      newMeetList = newMeetList.map(item => {
        return {
          label: item.roomName,
          value: bookRoom !== '1' ? item.meetingInviteId : item.roomId,
        }
      })

      if (!['1', '2'].includes(meetingAategory)) {
        if (!meetingForm) {
          meetingForm = '1'
        }
        if (['1', '2'].includes(bookRoom)) {
          if (meetingForm === '1') {
            // meetingPlace1
            addr = newMeetList.find(item => item.value === meetingPlace1).label || ''
          } else {
            // mainVenueLocation1
            addr = newMeetList.find(item => item.value === mainVenueLocation1).label || ''
          }
        } else if (meetingForm === '1') {
          // meetingPlace
          addr = meetingPlace || ''
        } else {
          // mainVenueLocation
          addr = mainVenueLocation || ''
        }

        if (meetingForm === '1') {
          content = `您有一条会议提醒，请您准时参会，谢谢配合！\n会议信息：\n会议名称：${meetingName}\n会议时间：${startTime} 至 ${endTime}\n会议地点：${addr}`
        } else {
          content = `您有一条会议提醒，请您准时参会，谢谢配合！\n会议信息：\n会议名称：${meetingName}\n会议时间：${startTime} 至 ${endTime}\n主会场地点：${addr}\n视频分会场地点：${videoBranchVenueLocation || ''}`
        }
      }
      setSmsContent(content)
    } catch (err) {
      console.log('处理会议数据报错', err)
    }

    setDefaultUser(defaultUserInfo)
    setMeetingAategory(values.meetingAategory)
    setMeetingForm(values.meetingForm)
    setVisible(true)
  }
  return (
    <div>
      <Spin spinning={loading}>
        <h1 className='form-title'>{meetTitle}</h1>
        <Sms
          reportId={procFormDataKey}
          visible={visible}
          setVisible={setVisible}
          setSmsContent={setSmsContent}
          meetingAategory={meetingAategory}
          meetingForm={meetingForm}
          defaultUser={defaultUser}
          smsContent={smsContent}
        />
        <SchemaMarkupForm
          schema={schema({ debug, nodeName, limit: access.limit, taskStatus, enableWrite, operator, needFilePermissionBtn })}
          components={components}
          actions={actions}
          effects={() => {
            uesEffects()
          }}
          expressionScope={{ ...expressionScope }}
          previewPlaceholder='-'
          editable={editable}
        >
          <div>
            {
              !isEmpty(initValue) && (
                <Space>
                  <Buttons
                    userTaskId={userTaskId}
                    onMount={onMount}
                    onSubmitOpen={async (res) => {
                      const action = res ? res.action : null
                      console.log('onSubmitOpen', action)
                      if (!await formCheck(action)) return Promise.reject()
                      const data = await actions.submit()
                      const { values } = data
                      if (isNewData) {
                        await commonEvent(values)
                        values.errorTip = values.errorTip ? values.errorTip : ''
                        values.qrCodeUrl = values.qrCodeUrl ? values.qrCodeUrl : ''
                        if (!values.apply) {
                          values.apply = [{ "name": "姓名", "type": "name", "key": "1", "age": "", "index": 0, "isFixed": true, "checked": true }, { "name": "手机号", "type": "phone", "key": "2", "age": "", "index": 1, "isFixed": true, "checked": true }, { "name": "所在单位", "type": "unit", "key": "3", "age": "", "index": 2, "isFixed": true, "checked": true }, { "name": "职务", "type": "position", "key": "4", "age": "", "index": 3, "isFixed": true, "checked": true }, { "name": "民族", "type": "nation", "key": "5", "age": "", "index": 4, "isFixed": true, "checked": false }, { "name": "是否用车", "type": "isPickUp", "key": "6", "age": "", "index": 5, "isFixed": true, "checked": false }, { "name": "车牌号", "type": "licenseNum", "key": "7", "age": "", "index": 6, "isFixed": true, "checked": false }, { "name": "备注", "type": "remark", "key": "8", "age": "", "index": 7, "isFixed": true, "checked": true }]
                        }
                        const meetingId = values.meetingPlace || values.meetingPlace1 || values.mainVenueLocation1 || values.mainVenueLocation
                        const findOne = meetingList.filter(item => item.value === meetingId) || []
                        const meetingAddrInfo = meetingList && meetingList.length > 0 ? findOne[0] && findOne[0].label : ''
                        const meetingAddr = values.bookRoom === '3' ? meetingId : meetingAddrInfo
                        values.meetingAddr = meetingAddr
                        values.isSend = values.isSend || false
                        values.applyList = unionBy(values.applyList, cacheApplyList, 'defaultUser.id') || []
                      }
                      if (!debug) {
                        values.apply = initValue.apply
                      }
                      const result = patchData(values, initValue)
                      return result
                    }}
                    onSubmit={submitForm}
                    extraButtons={buttonGroupList.concat(ableEditBtn)}
                    customButtons={[
                      {
                        key: 'gksz',
                        onClick: () => {
                          setShowControlModal(true)
                        },
                        async: true,
                      },
                    ]}
                  />
                </Space>
              )
            }
          </div>
        </SchemaMarkupForm>

        <Tabs defaultActiveKey="1">
          <TabPane tab="审批流程" key="1">
            <Steps
              userTaskId={userTaskId}
            />
          </TabPane>
        </Tabs>
        <MainText key={domKey} title={meetTitle} content={settingParagraph ? paragraphContent : content} formType='meetNotice' actions={actions} />
        {showPermissionModal &&
          <PermissionModal
            onCancel={() => setPermissionModal(false)}
            operator={operator}
            onOk={onOKPromission}
            actions={actions}
            backFillDataSouce={backFillDataSouce}
            fileList={wpsFile}
            procFormDataKey={procFormDataKey}
            permissionObj={permissionObj}
            setPermissionObj={setPermissionObj}
          />
        }
        <ControlModal
          visible={showControlModal}
          setShowControlModal={setShowControlModal}
          actions={actions}
          deptInfo={deptInfo}
          userTaskId={userTaskId}
          procFormDataKey={procFormDataKey}
        />
        <EvaluationModal
          visible={showEvaluationModal}
          setShowEvaluationModal={setShowEvaluationModal}
          setIsAgree={setIsAgree}
          actions={actions}
        />
      </Spin>
    </div>
  )
}