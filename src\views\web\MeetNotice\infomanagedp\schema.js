import React, { useEffect, useMemo } from 'react'
import { Tooltip } from 'antd'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { UPLOAD_URL } from 'ROOT/constants'
import locale from 'antd/lib/date-picker/locale/zh_CN'
import moment from 'moment'
import { formConfig } from '../common/PCDIconfig'
import Cookies from 'js-cookie'
import EvaluationModal from 'ROOT/components/EvaluationModal'

const text = (
  <div>
    <p>同步预约：与会议室预约功能联动，表单提交后，系统将自动预约会议室</p>
    <p>
      已预约：如已在会议室预约功能中预约会议室，可通过本功能关联会议室，支持选择从起草日期之后，7天内的会议数据
    </p>
    <p>无需预约：会议不在本单位内进行，需手动输入</p>
  </div>
)

export default ({
  debug,
  needFilePermissionBtn = false,
  nodeName = '',
  limit = 'NODE',
  operator,
}) => ({
  type: 'object',
  properties: {
    layout: {
      'x-component': 'mega-layout',
      type: 'object',
      'x-component-props': {
        autoRow: true,
        grid: true,
        columns: 6,
        // labelWidth: 130,
        labelAlign: '{{labelAlign}}',
        full: true,
        // labelCol: 24
      },
      properties: {
        formTitle: {
          key: 'formTitle',
          name: 'formTitle',
          display: false,
          default: '会议通知',
          'x-mega-props': {
            span: 6,
          },
          'x-component': 'Input',
        },
        user: {
          type: 'string',
          title: '起草人',
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'EditableInput',
          // editable: false,
        },
        _dept: {
          type: 'string',
          title: '起草部门',
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'Dept',
          'x-component-props': {
            labelInValue: true,
          },
          // editable: false
        },
        phone: {
          type: 'string',
          title: '联系电话',
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'EditableInput',
          'x-rules': [{ format: 'phone', message: '手机格式不正确' }],
          // editable: false,
        },
        time: {
          type: 'string',
          title: '起草时间',
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'EditableInput',
          default: moment().format('YYYY年MM月DD日'),
          // editable: false
        },

        signer: {
          type: 'string',
          title: '签发人',
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'EditableInput',
          // default: '',
          // editable: false
        },
        issuanceTime: {
          type: 'string',
          title: '签发时间',
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'EditableInput',
          // editable: false
        },

        serialNumber: {
          type: 'string',
          title: '编号',
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'EditableInput',
          // editable: false
        },
        _taskLevel: {
          type: 'string',
          title: '缓急程度',
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'Select',
          default: '3',
          enum: [
            { label: '一般', value: '3' },
            { label: '急件', value: '2' },
            { label: '特急', value: '1' },
          ],
          'x-component-props': {
            style: {
              // width: 100
            },
          },
        },
        meetingAategory: {
          type: 'string',
          title: '会议类别',
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'Select',
          // default: '1',
          required: true,
          enum: [
            { label: '公司决策会议', value: '1' },
            { label: '公司领导专题办公会议', value: '2' },
            { label: '其他', value: '3' },
          ],
          'x-linkages': [
            {
              type: 'value:visible',
              target:
                '*(meetingForm, isNeedOtherDptAttend, isProduceMeetingFee, meetingPlan,isNeedPersonToUpDept,meetingNameAndPlanNotUniformDescription,isScheduledMeeting,evaluationForm)',
              condition: '{{$self.value == 3}}',
            },
          ],
        },

        isScheduledMeeting: {
          type: 'string',
          title: '是否为计划内会议',
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'RadioGroup',
          required: true,
          enum: [
            { label: '是', value: '1' },
            { label: '否', value: '2' },
          ],
          'x-linkages': [
            {
              type: 'value:visible',
              target: '*(Evaluation,evaluationForm)',
              condition: '{{$self.value == 2}}',
            },
          ],
        },
        meetingPlan: {
          type: 'string',
          title: '会议计划',
          'x-mega-props': {
            span: 4,
          },
          'x-component': 'SeletMeetingPlan',
          'x-component-props': {
            labelInValue: true,
          },
        },
        // 计划外会议为基层减负评估表
        evaluationForm: {
          type: 'string',
          title: '计划外会议为基层减负评估表',
          required: true,
          'x-mega-props': {
            span: 6,
          },
          'x-component': 'EvaluationForm',
        },
        isNeedPersonToUpDept: {
          type: 'string',
          default: '2',
          required: true,
          title: '是否需要员工到上级单位参会（不含同城）',
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'RadioGroup',
          enum: [
            { label: '是', value: '1' },
            { label: '否', value: '2' },
          ],
        },

        meetingNameAndPlanNotUniformDescription: {
          type: 'string',
          title: '会议名称与计划名称不一致情况说明',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '例:会议内容不变，会议名称调整。',
            style: { marginBottom: 10 },
          },
          'x-mega-props': {
            span: 2,
          },
          // editable: false
        },
        meetingForm: {
          type: 'string',
          title: '会议形式',
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'RadioGroup',
          default: '1',
          enum: [
            { label: '线下会议', value: '1' },
            { label: '线上会议（视频、电话会议）', value: '2' },
            { label: '线上+线下会议', value: '3' },
          ],
          'x-linkages': [
            // {
            //   type: "value:visible",
            //   target: '.meetingPlace',
            //   condition: '{{$self.value == 1}}'
            // },
            // {
            //   type: "value:visible",
            //   target: '*(isProduceMeetingFee)',
            //   condition: '{{$self.value == 3 || $self.value == 1}}'
            // },
            {
              type: 'value:visible',
              target:
                '*(videoBranchVenueLocation,videoBranchVenuePerson,isNeedPutCards,isNeedProjectionEquipment,isNeedElectronicScreenShow,branchIsNeedProjectionEquipment)',
              condition: '{{$self.value == 2 || $self.value == 3}}',
            },
          ],
        },
        isNeedOtherDptAttend: {
          type: 'string',
          default: '2',
          required: true,
          title: '是否需要其他部门领导参会',
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'RadioGroup',
          enum: [
            { label: '是', value: '1' },
            { label: '否', value: '2' },
          ],
        },
        isProduceMeetingFee: {
          type: 'string',
          title: '是否产生会议费用',
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'RadioGroup',
          enum: [
            { label: '是', value: '1' },
            { label: '否', value: '2' },
          ],
          required: true,
          default: '2',
          'x-linkages': [
            {
              type: 'value:visible',
              target: '*(meetingLevel,meetingType,repayDepartment,isInDptFeeBudget,budgetAmount)',
              condition: '{{$self.value == 1}}',
            },
          ],
        },
        meetingLevel: {
          type: 'string',
          title: '会议级别',
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'Select',
          required: true,
          'x-component-props': {
            placeholder: '请选择',
            style: {
              // width: 150
            },
          },
          enum: [
            { label: '区一类会议', value: 'area1' },
            { label: '区二类会议', value: 'area2' },
            { label: '区三类会议', value: 'area3' },
            { label: '市一类会议', value: 'city1' },
            { label: '市二类会议', value: 'city2' },
            { label: '市三类会议', value: 'city3' },
            { label: '市三类会议', value: 'other1' },
          ],
          'x-linkages': [
            {
              type: 'value:schema',
              target: '.meetingType',
              schema: {
                enum: '{{getDiffMeetType($self.value)}}',
              },
            },
            {
              type: 'value:visible',
              target: '.staffNumber',
              condition: '{{["area1","city1"].includes($self.value)}}',
            },
            {
              type: 'value:schema',
              target: '.isNeedOtherDptAttend',
              condition: '{{$self.value == "area1"}}',
              schema: {
                enum: [
                  { label: '是', value: '1' },
                  { label: '否', value: '2', disabled: true },
                ],
              },
              otherwise: {
                enum: [
                  { label: '是', value: '1' },
                  { label: '否', value: '2' },
                ],
              },
            },
          ],
        },
        meetingType: {
          type: 'string',
          title: '会议类型',
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'Select',
          required: true,
          'x-component-props': {
            placeholder: '请选择',
            style: {
              // width: 160
            },
          },
        },
        repayDepartment: {
          type: 'string',
          title: '报账部门',
          'x-mega-props': {
            span: 2,
          },
          // "x-component": "SelectOrg",
          'x-component': 'newDeptSelect',
          required: true,
          'x-component-props': {
            maxCount: 1,
            placeholder: '请选择',
          },
        },
        isInDptFeeBudget: {
          type: 'string',
          // title: (<div style={{ lineHeight: '16px' }} dangerouslySetInnerHTML={{ __html: '是否在部门<br/>年度会议费预算内' }}></div>),
          title: '是否在部门年度会议费预算内',
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'RadioGroup',
          // required: true,
          enum: [
            { label: '是', value: '1' },
            { label: '否', value: '2' },
          ],
        },
        budgetAmount: {
          title: '预算金额',
          'x-component': 'NumberPicker',
          description: '',
          required: true,
          'x-mega-props': {
            addonAfter: '（万元）',
            span: 2,
          },
          maxLength: 3,
          'x-component-props': {
            min: 0,
            max: null,
          },
        },

        meetingName: {
          type: 'string',
          title: '会议名称',
          'x-component': 'Input',
          'x-component-props': {
            style: { marginBottom: 10 },
          },
          required: true,
          description: "{{getColor('注：公司领导专题办公会议次数在通知签发后自动生成','#5C626B')}}",
          maxLength: 200,
          'x-mega-props': {
            span: 6,
          },
          style: {
            width: 200,
          },
          // editable: false
        },
        startTime: {
          type: 'string',
          title: '开始时间',
          required: true,
          'x-component': 'DatePicker',
          'x-mega-props': {
            span: 2,
            // labelAlign: 'top',
          },
          'x-component-props': {
            showTime: true,
            style: {
              width: 100,
            },
            locale,
            format: 'YYYY-MM-DD HH:mm',
            // disabledDate: debug ? null : '{{disabledDate}}',
            // disabledTime:
            // defaultValue: [moment(), moment().add(1,'hours')]
          },
        },
        endTime: {
          type: 'string',
          title: '结束时间',
          required: true,
          'x-component': 'DatePicker',
          'x-mega-props': {
            span: 2,
            // labelAlign: 'top',
          },
          'x-component-props': {
            showTime: true,
            style: {
              width: 100,
            },
            locale,
            format: 'YYYY-MM-DD HH:mm',
            // disabledDate: debug ? null : '{{disabledDate}}',
            // disabledTime:
            // defaultValue: [moment(), moment().add(1,'hours')]
          },
        },
        // rangePicker: {
        //   type: 'string',
        //   title: '开始以及结束时间',
        //   required: true,
        //   'x-component': 'RangePicker',
        //   'x-mega-props': {
        //     span: 4,
        //     // labelAlign: 'top',
        //   },
        //   "x-component-props": {
        //     showTime: true,
        //     style: {
        //       width: 100,
        //     },
        //     locale,
        //     format: 'YYYY-MM-DD HH:mm',
        //     disabledDate: debug ? null : '{{disabledDate}}',
        //     // disabledTime:
        //     // defaultValue: [moment(), moment().add(1,'hours')]

        //   },
        // },
        bookRoom: {
          type: 'string',
          title: '预约会议室',
          'x-mega-props': {
            span: 2,
            addonAfter: (
              <Tooltip title={text} placement="topRight">
                <QuestionCircleOutlined style={{ cursor: 'pointer' }} />
              </Tooltip>
            ),
          },
          'x-component': 'Select',
          default: '3',
          required: true,
          enum: [
            // { label: '同步预约', value: '1' },
            // { label: '已预约', value: '2' },
            { label: '无需预约', value: '3' },
          ],
          // 'x-linkages': []
        },
        meetingPlace1: {
          type: 'string',
          title: '会议地点',
          'x-mega-props': {
            span: 6,
          },
          'x-component': 'Select',
          // default: '1',
          required: true,
          'x-component-props': {
            placeholder: '请选择',
            style: {
              // width: 160
            },
          },
        },
        meetingPlace: {
          type: 'string',
          title: '会议地点',
          'x-component': 'Input',
          required: true,
          maxLength: 50,
          description: "{{getColor('注：优先公司自有场地或者协议酒店。','#5C626B')}}",
          'x-component-props': {
            placeholder: '请输入',
            style: { marginBottom: 10 },
            maxLength: 50,
          },
          'x-mega-props': {
            span: 6,
          },
        },

        mainVenueLocation1: {
          type: 'string',
          title: '主会场地点',
          'x-mega-props': {
            span: 6,
          },
          'x-component': 'Select',
          // default: '1',
          required: true,
          'x-component-props': {
            placeholder: '请选择',
            style: {
              // width: 160
            },
          },
        },
        mainVenueLocation: {
          typ: 'string',
          title: '主会场地点',
          'x-component': 'Input',
          required: true,
          maxLength: 50,
          description: "{{getColor('注：优先公司自有场地或者协议酒店。','#5C626B')}}",
          'x-component-props': {
            style: {
              width: 160,
              marginButton: 10,
            },
            maxLength: 50,
          },
          'x-mega-props': {
            span: 6,
          },
        },
        meetingHost: {
          type: 'string',
          title: '会议主持人',
          'x-component': 'Input',
          maxLength: 20,
          'x-mega-props': {
            span: 6,
          },
        },
        meetingPerson: {
          type: 'string',
          title: '现场会议参会人员',
          // required: true,
          'x-component': 'TextArea',
          'x-mega-props': {
            span: 6,
          },
          'x-component-props': {
            // listAllAction: '/baas-admin/web/listAll',
            // myListAction: '/baas-admin/web/org/myList?flag=1',
            // searchAction: '/web-search/web/search?size=100',// web-search/web/searchAll?groupCode=&keyword=&option=3&size=100
            style: {
              width: 400,
            },
          },
        },
        meetingPersonNumber: {
          type: 'string',
          title: '现场会议参加人数',
          'x-component': 'NumberPicker',
          required: true,
          'x-mega-props': {
            addonAfter: '人',
            span: 2,
          },
          'x-component-props': {
            max: 1000000,
            min: 0,
            formatter: value => {
              return value.replace(/[^0-9]/g, '')
            },
          },
        },
        staffNumber: {
          type: 'string',
          title: '工作人员数量',
          'x-component': 'NumberPicker',
          'x-mega-props': {
            addonAfter: '人',
            span: 2,
          },
          'x-component-props': {
            max: 1000000,
            min: 0,
          },
        },
        meetingDays: {
          type: 'string',
          title: '会议天数',
          'x-component': 'Select',
          required: true,
          enum: [
            { label: '0.5', value: '0.5' },
            { label: '1', value: '1' },
            { label: '1.5', value: '1.5' },
            { label: '2', value: '2' },
            { label: '2.5', value: '2.5' },
            { label: '3', value: '3' },
            { label: '3以上', value: 'more' },
          ],
          'x-mega-props': {
            addonAfter: '天',
            span: 2,
          },
        },
        videoBranchVenueLocation: {
          typ: 'string',
          title: '视频分会场地点',
          // required: true,
          maxLength: 50,
          'x-component': 'Input',
          'x-mega-props': {
            span: 6,
          },
        },

        videoBranchVenuePerson: {
          type: 'string',
          title: '视频分会场参会人员',
          'x-component': 'TextArea',
          // required: true,
          maxLength: 500,
          'x-mega-props': {
            span: 6,
          },
          'x-component-props': {
            style: {
              width: 600,
            },
          },
        },

        isNeedPutCards: {
          type: 'string',
          title: '是否需要摆放桌牌',
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'RadioGroup',
          default: '2',
          enum: [
            { label: '是', value: '1' },
            { label: '否', value: '2' },
          ],
          'x-linkages': [
            {
              type: 'value:visible',
              target: '.putRequire',
              condition: '{{$self.value == 1}}',
            },
            {
              type: 'value:schema',
              target: '.isNeedPutCards',
              condition: '{{$self.value == 2}}',
              schema: {
                'x-mega-props': {
                  span: 6,
                },
              },
              otherwise: {
                'x-mega-props': {
                  span: 2,
                },
              },
            },
          ],
        },
        putRequire: {
          type: 'string',
          title: '摆放要求',
          'x-component': 'Input',
          'x-mega-props': {
            span: 4,
            // wrapperWidth: 200
          },
        },
        isNeedProjectionEquipment: {
          type: 'string',
          title: '是否需要投影设备',
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'RadioGroup',
          enum: [
            { label: '是', value: '1' },
            { label: '否', value: '2' },
          ],
        },
        branchIsNeedProjectionEquipment: {
          type: 'string',
          // title: (<div style={{ lineHeight: '16px' }} dangerouslySetInnerHTML={{ __html: '分会场是否<br/>需要投影设备' }}></div>),
          title: '分会场是否需要投影设备',
          'x-component': 'RadioGroup',
          default: '2',
          enum: [
            { label: '是', value: '1' },
            { label: '否', value: '2' },
          ],
          'x-mega-props': {
            span: 4,
            // wrapperWidth: 200
          },
        },
        isNeedElectronicScreenShow: {
          type: 'string',
          title: '是否需要电子屏显',
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'RadioGroup',
          default: '2',
          enum: [
            { label: '是', value: '1' },
            { label: '否', value: '2' },
          ],
          'x-linkages': [
            {
              type: 'value:visible',
              target: '.screenShowContent',
              condition: '{{$self.value == 1}}',
            },
          ],
        },
        screenShowContent: {
          type: 'string',
          title: '屏显内容',
          'x-component': 'Input',
          maxLength: 200,
          'x-mega-props': {
            span: 4,
            // wrapperWidth: 200
          },
        },
        remark: {
          type: 'string',
          title: '备注',
          'x-component': 'TextArea',
          'x-mega-props': {
            span: 6,
          },
          'x-component-props': {
            style: {
              width: 600,
            },
          },
        },
        isSend: {
          type: 'string',
          title: '是否需要报名回执',
          'x-component': 'Select',
          default: false,
          enum: [
            { label: '是', value: true },
            { label: '否', value: false },
          ],
          'x-mega-props': {
            span: 2,
          },
          'x-linkages': [
            {
              type: 'value:visible',
              target: '*(code, apply, applyList)',
              condition: '{{$self.value === true }}',
            },
          ],
        },
        code: {
          type: 'string',
          title: '签到二维码',
          'x-component': 'SignCode',
          'x-mega-props': {
            span: 6,
          },
          'x-component-props': {
            // style: {
            //   width: 600,
            // },
          },
        },
        apply: {
          type: 'string',
          title: '报名字段',
          'x-component': 'ApplyHeader',
          'x-mega-props': {
            span: 6,
          },
          'x-component-props': {
            // style: {
            //   width: 600,
            // },
          },
        },
        applyList: {
          type: 'string',
          title: '报名人员',
          'x-component': 'ApplyInfo',
          'x-mega-props': {
            span: 6,
          },
          'x-component-props': {
            // style: {
            //   width: 600,
            // },
          },
        },
        upload: {
          type: 'string',
          title: '附件',
          'x-component': 'Upload',
          // description: "{{getColor('注：可点击下载查看上传附件','#5C626B')}}",
          'x-component-props': {
            action: UPLOAD_URL,
            listType: 'text',
            needFilePermissionBtn,
            nodeName,
            limit,
            operator,
            up: true,
          },
          'x-mega-props': {
            span: 6,
          },
        },
        fileList: {
          key: 'fileList',
          name: 'fileList',
          title: '会议内容',
          'x-component': 'WpsEditor',
          'x-mega-props': {
            span: 6,
          },
          'x-component-props': {
            orgId: '',
            reportId: '',
            type: '',
            config: formConfig,
            formType: 'meetNotice',
            actions: '{{getActions}}',
            meetTitle: '',
            currentSchema: '',
          },
        },
        Evaluation: {
          type: 'void',
          'x-component': 'EvaluationBtn',
        },
        flag: {
          key: 'flag',
          name: 'flag',
          display: false,
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'Input',
        },
        drafterInfo: {
          key: 'drafterInfo',
          name: 'drafterInfo',
          display: false,
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'Input',
        },
        deptInfo: {
          key: 'deptInfo',
          name: 'deptInfo',
          display: false,
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'Input',
        },
      },
    },
  },
})
