import React, { useEffect, useState, useMemo, useRef } from 'react'
import { Modal, Input, message } from 'antd'
import Cookies from 'js-cookie'
import moment from 'moment'
import service from 'ROOT/service'

const { TextArea } = Input

export default props => {
  const {
    visible,
    setVisible,
    setSmsContent,
    reportId,
    meetingAategory,
    meetingForm = '1',
    defaultUser,
    smsContent,
  } = props
  const [memberVisible, setMemberVisible] = useState(false)
  const [result, setResult] = useState([])
  const [list, setList] = useState([])
  const [names, setNames] = useState('')
  const [content, setContent] = useState('')
  const ifrRef = useRef(null)

  const {
    orgId = Cookies.get('orgId'),
    orgName,
    id = Cookies.get('userId'),
    name = Cookies.get('username'),
  } = JSON.parse(
    Cookies.get('uiapp') ||
      Cookies.get('WEBG_STORAGE') ||
      Cookies.get('EntAdminG_STORE') ||
      localStorage.WEBG_STORAGE ||
      localStorage.EntAdminG_STORE ||
      '{}',
  )
  useEffect(() => {
    setResult(defaultUser)
    let namesInfo = ''
    if (Array.isArray(defaultUser)) {
      namesInfo = defaultUser.map(x => x.name).join('，')
    }
    setNames(namesInfo)
  }, [defaultUser])

  const handleCancel = () => {
    setSmsContent('')
    setVisible(false)
  }

  const handleOk = () => {
    if (!content) {
      return message.error('请输入推送内容')
    }
    const meetingNoticeUserDTOList = result.map(item => {
      return {
        orgId: item.orgId,
        deptId: item.deptId,
        uid: item.id,
        mobile: item.mobile,
        userName: item.name,
      }
    })
    if (meetingNoticeUserDTOList.length === 0) {
      message.error('推送人员不能为空')
      return false
    }
    const params = {
      meetingNoticeUserDTOList,
      meetingAategory,
      meetingForm,
      reportId,
      messageContent: content,
    }
    setVisible(false)
    setSmsContent('')
    service.sendMessage(params).then(res => {
      // if (res.success) {
      //   setVisible(false)
      // }
    })
  }
  const defaultChooseSelf = false
  window.addEventListener('message', e => {
    if (!memberVisible) return false // 不是当前组件不执行
    const { data } = e
    const { type } = data

    // 点击选人组件中的【取消】按钮
    if (type === 'BAAS_TREE_CANCEL') {
      setMemberVisible(false)
    }

    // 点击选人组件中的【确定】按钮
    if (type === 'BAAS_TREE_CONFIRM') {
      setResult(data.data.users)
      let namesInfo = ''
      if (Array.isArray(data.data.users)) {
        namesInfo = data.data.users.map(x => x.name).join('，')
      }
      setNames(namesInfo)
      setMemberVisible(false)
    }
  })

  const getDefaultList = () => {
    let defaultList = []
    if (result && result.length > 0) {
      defaultList = result
    } else if (defaultChooseSelf) {
      defaultList = [
        {
          id,
          name,
        },
      ]
    }
    return defaultList
  }
  const notice = () => {
    // setVisible(false)
    setMemberVisible(true)
  }

  const onChange = e => {
    const { value } = e.currentTarget || e.target || {}
    setContent(value)
  }

  const handleIfrLoad = () => {
    const params = {
      visible: true,
      needSearchBar: false,
      orgId,
      orgName,
      defaultUserList: getDefaultList(),
      range: 999,
      type: 'multiUser',
      // chooseFromDeptId: '',
      // needLookUp: true,
      // isCustom: true,
      isChooseFromOtherOrgs: true, // 区公司支持从所有单位选
      // isAssingRootOrg: true,
    }
    ifrRef.current.contentWindow.postMessage(
      {
        type: 'USER_SELECTOR_TREE_CONFIG',
        data: params,
      },
      '*',
    )
  }

  useEffect(() => {
    if (reportId === '' || visible === false) {
      return false
    }
    service
      .listSendMessageRecord({
        reportId,
      })
      .then(res => {
        setList(res.data)
      })
  }, [reportId, visible])

  useEffect(() => {
    setContent(smsContent)
  }, [smsContent])

  return (
    <div>
      <Modal
        title="会务短信"
        visible={visible}
        onOk={handleOk}
        onCancel={handleCancel}
        width="440px"
        okText="确认"
        cancelText="取消"
        bodyStyle={{ padding: 0 }}
        className="sms-modal"
      >
        <div className="messages-info">
          <div className="item-info">
            <div className="label-info">通知人员</div>
            <Input placeholder="请选择" type="text" value={names} onClick={() => notice()} />
          </div>
          <div className="item-info">
            <div className="label-info">推送内容</div>
            <TextArea
              // defaultValue={content}
              value={content}
              autoSize={{ minRows: 6, maxRows: 6 }}
              maxLength={1000}
              onChange={onChange}
            />
          </div>
          <div className="item-info">
            <div className="label-info">推送记录</div>
            <div className="list-info">
              <ul>
                {list && list.length > 0 ? (
                  list.map((item, index) => {
                    return (
                      <div key={index}>
                        <li>
                          <div className="circle-info" />
                          <div className="time-info">
                            {moment(item.sendTime).format('YYYY-MM-DD HH:mm:ss')}
                          </div>
                          {item.successList && item.successList.length > 0 && (
                            <div className="record-info">
                              推送成功： {item.successList.map(i => i.userName || '').join('、')}
                            </div>
                          )}
                          {item.failureList && item.failureList.length > 0 && (
                            <div className="record-info">
                              推送失败： {item.failureList.map(i => i.userName || '').join('、')}
                            </div>
                          )}
                        </li>
                        {item.messageContent && (
                          <li>
                            <div className="record-info">推送内容：</div>
                            <div
                              className="record-info"
                              style={{
                                whiteSpaceCollapse: 'preserve-breaks',
                                wordBreak: 'break-all',
                              }}
                            >
                              {item.messageContent}
                            </div>
                          </li>
                        )}
                      </div>
                    )
                  })
                ) : (
                  <div className="empty-info">暂无推送记录</div>
                )}
              </ul>
            </div>
          </div>
        </div>
      </Modal>
      {memberVisible ? (
        <Modal
          visible={memberVisible}
          width={600}
          footer={null}
          closable={false}
          bodyStyle={{
            padding: 0,
            margin: 0,
            overflow: 'hidden',
            height: 590,
          }}
        >
          <iframe
            src={`${ab.api}/user-selector`}
            frameBorder="0"
            style={{ border: 0 }}
            width="100%"
            height="100%"
            ref={ifrRef}
            onLoad={handleIfrLoad}
          />
        </Modal>
      ) : null}
    </div>
  )
}
