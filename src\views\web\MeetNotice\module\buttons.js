import {
  Tabs,
  Table,
  Modal,
} from 'antd'

const { confirm } = Modal

export const closeBtn = {
  name: '关闭',
  type: 'default',
  onClick: () => confirm({
    content: '是否确定退出？',
    onOk: () => {
      // history.go(-1)
      window.close()
    },
  }),
}

export const setWpsPermissionBtn = (callback) => commonBtn({
  name: '设置正文段落权限',
  type: 'primary',
  callback,
})

export const saveBtn = (callback) => commonBtn({
  name: '保存',
  type: 'primary',
  callback,
})

export const saveAndExit = (callback) => commonBtn({
  name: '保存退出',
  type: 'primary',
  callback,
})

export const deleteBtn = (callback) => commonBtn({
  name: '注销文档',
  type: 'default',
  callback,
})

export const feedBackBtn = (callback) => commonBtn({
  name: '提交反馈',
  type: 'primary',
  callback,
})

export const dispatchBtn = (callback) => commonBtn({
  name: '一键自动编发',
  type: 'warning',
  callback,
})

const commonBtn = ({ name, type, callback}) => ({
  name,
  type,
  onClick: () => {
    if (typeof callback === 'function') callback()
  },
})