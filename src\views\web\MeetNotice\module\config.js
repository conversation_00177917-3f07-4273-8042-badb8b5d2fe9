export const meetingType = [
  {
    taskBizType: 14,
    type: '17',
    name: '会议通知',
    englishName: 'MEETING_ANNOUNCEMENT',
    signerNodeName: '部门总经理审核,领导审核',
    permissionNodeName: '起草人设置查看权限'
  },
  {
    taskBizType: 15,
    type: '18',
    name: '外单位会议通知',
    englishName: 'OUTSIDE_UNIT_MEETING_NOTIVE',
    signerNodeName: '部门总经理审核,领导审核'
  },
  {
    type: '19',
    name: '党委会议',
    englishName: 'PARTY_COMMITTEE_MEETING_NOTIVE',
    signerNodeName: '党委书记',
  },
  {
    type: '20',
    name: '直属机关党委会议',
    englishName: 'PARTY_COMMITTEE_MEETING_AAFFILIATED_ORGANS',
    signerNodeName: '党委书记',
  },
  {
    type: '21',
    name: '纪委会议',
    englishName: 'DISCIPLINARY_COMMITTEE_MEERING_NOTIVE',
    signerNodeName: '纪委书记',

  },
  {
    type: '22',
    name: '直属机关纪委会议',
    englishName: 'DISCIPLINARY_COMMITTEE_MEERING_NOTIVE_AFFILIATED',
    signerNodeName: '纪委书记',
  },
];

export const signerNodeName =  '部门总经理审核'

//党委会议通知
export const getMeetingTitle = (list, orgId, cityOrgName = '', loginOrgName = '',typeName) => {
  console.log('list', list);
  if (list && orgId) {
    console.log(Object.keys(list), '----', list, orgId);
    let a = Object.keys(list).filter(data => {
      return list[data].includes(Number(orgId))
    })
    if (a.join() === 'areaOrgIds') {
      return `中国移动通信集团广西有限公司${typeName}通知`
    }

    if (a.join() === 'cityOrgIds') {
      return `中国移动通信集团广西有限公司${loginOrgName}${typeName}通知`
    }

    if (a.join() == 'otherOrgIds') {
      return `中国移动通信集团广西有限公司${cityOrgName}${loginOrgName}${typeName}通知`
    }
  }
}