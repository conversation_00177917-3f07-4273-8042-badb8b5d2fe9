import React, { useState, useEffect } from 'react'
import { observer } from 'mobx-react-lite'
import { message, Input, Tag, Modal, Radio, Checkbox, Button, Steps } from 'antd'
import { makeObservable, observable, computed, action, runInAction } from 'mobx'
import axios from 'axios'
import { render, unmountComponentAtNode } from 'react-dom'

/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */

function __awaiter (thisArg, _arguments, P, generator) {
  function adopt (value) { return value instanceof P ? value : new P((resolve) => { resolve(value) }) }
  return new (P || (P = Promise))((resolve, reject) => {
    function fulfilled (value) { try { step(generator.next(value)) } catch (e) { reject(e) } }
    function rejected (value) { try { step(generator.throw(value)) } catch (e) { reject(e) } }
    function step (result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected) }
    step((generator = generator.apply(thisArg, _arguments || [])).next())
  })
}

const requeset = axios.create()
requeset.interceptors.response.use(res => {
  const { data } = res
  if (data.code === 401) {
    message.info('登录状态失效，请重新登录')
    // return Promise.reject(data.code)
  }
  return res
}, error => {
  message.error(error.message)
  throw error
})
const http = {
  get (url, config = {}) {
    return __awaiter(this, void 0, void 0, function * () {
      const { data } = yield requeset.get(url, config)
      if (data.success === true) {
        if (config && config.note) {
          message.success(config.note)
        }
        return data.data
      } 
        message.error(data.msg)
        return Promise.reject(data.code)
      
    })
  },
  post (url, body, config) {
    return __awaiter(this, void 0, void 0, function * () {
      const { data } = yield requeset.post(url, body, config)
      if (data.success === true) {
        if (config && config.note) {
          message.success(config.note)
        }
        return data.data
      } 
        message.error(data.msg)
        return Promise.reject(data.code)
      
    })
  },
}

const service = {
  getProcessConfig (procKey) {
    return __awaiter(this, void 0, void 0, function * () {
      const data = yield http.get('/baas-wf-portal/actinst/act/conf', {
        params: { procKey, actKey: 'start-none-event' },
      })
      return JSON.parse(data.viewConf)
    })
  },
  getInstanceConfig (userTaskId) {
    return __awaiter(this, void 0, void 0, function * () {
      const data = yield http.get('/baas-wf-portal/task/app/open', {
        params: { userTaskId },
      })
      const { childShape, procLogResponse } = data
      return { config: JSON.parse(childShape.viewConf), logs: procLogResponse.actinsts }
    })
  },
  getStartNextNodes ({ procKey, values }) {
    return __awaiter(this, void 0, void 0, function * () {
      return http.get(`/baas-wf-portal/actinst/startnext`, {
        params: { procKey, varJson: JSON.stringify(values) },
      })
    })
  },
  getApproveNextNodes ({ taskId, values }) {
    return __awaiter(this, void 0, void 0, function * () {
      const data = yield http.get(`/baas-wf-portal/actinst/next`, {
        params: { taskId, varJson: JSON.stringify(values) },
      })
      return data.map(it => ({
        actKey: it.id,
        actName: it.actinstName,
        assigneeInfo: it.assigneeInfo,
      }))
    })
  },
  getRejectNodes (taskId) {
    return __awaiter(this, void 0, void 0, function * () {
      const data = yield http.get('/baas-wf-portal/actinst/hiList', { params: { taskId } })
      return data.map(it => ({ actKey: it.id, actName: it.actinstName }))
    })
  },
  getUserByPosts (postIds) {
    return __awaiter(this, void 0, void 0, function * () {
      return []
    })
  },
  getProcessLogs (userTaskId) {
    return __awaiter(this, void 0, void 0, function * () {
      const data = yield http.get('/baas-wf-portal/task/app/open', {
        params: { userTaskId },
      })
      return data.procLogResponse.actinsts
    })
  },
  start ({ appId, procKey, actKey, assignees, formResult, comment }) {
    return __awaiter(this, void 0, void 0, function * () {
      http.post('/baas-wf-portal/exe/start', {
        appId,
        procKey,
        actKey,
        procFormDataKey: formResult.id,
        varJson: JSON.stringify(formResult.values),
        assigneeInfo: {
          assignees,
        },
        comment,
      }, { note: '流程已启动' })
    })
  },
  manualApprove ({ taskId, actKey, assignees, values, comment }) {
    return __awaiter(this, void 0, void 0, function * () {
      return http.post('baas-wf-portal/exe/user/task/manual/complete', {
        taskId,
        actinstId: actKey,
        assigneeInfo: {
          assignees,
        },
        varJson: JSON.stringify(values),
        comment,
      }, { note: '提交成功' })
    })
  },
  autoApprove ({ taskId, comment }) {
    return __awaiter(this, void 0, void 0, function * () {
      return http.post('baas-wf-portal/exe/user/task/auto/complete', { taskId, comment }, { note: '提交成功' })
    })
  },
  revoke (data) {
    return http.post('/baas-wf-portal/exe/user/task/revoke', data, { note: '流程已撤销' })
  },
  stop (data) {
    return http.post('/baas-wf-portal/exe/task/refuse', data, { note: '流程已终止' })
  },
  addSign ({ taskId, flags, assignee, comment }) {
    return http.post('/baas-wf-portal/exe/user/task/add-sign', {
      taskId,
      flags,
      assignee,
      comment,
    }, { note: '加签成功' })
  },
  forword ({ taskId, assignee, comment }) {
    return http.post('/baas-wf-portal/exe/task/deliver', { taskId, assignee, comment }, { note: '任务已转交' })
  },
  reject ({ taskId, backActinstId, comment }) {
    return http.post('/baas-wf-portal/exe/user/task/back', { taskId, backActinstId, comment }, { note: '已退回' })
  },
}

class Process {
  constructor () {
    this.status = 'start'
    this.formActions = []
    this.nextNodeMode = 'AUTO'
    this.nextUserMode = 'AUTO'
    this.nextUserNumber = 'SINGLE'
    this.logs = []
    this.usersTobeSelect = []
    this.nodesTobeSelect = []
    this.commentOptions = []
    this.submitModal = false
    this.addSignModal = false
    this.forwordModal = false
    this.rejectModal = false
    makeObservable(this, {
      status: observable,
      formActions: observable,
      usersTobeSelect: observable,
      nodesTobeSelect: observable,
      commentOptions: observable,
      userOptions: computed,
      nodeOptions: computed,
      buttons: computed,
      // 提交
      submitModal: observable,
      onSubmit: action.bound,
      onSubmitOk: action.bound,
      onNodeSelect: action.bound,
      hideSubmitModal: action.bound,
      // 加签
      addSignModal: observable,
      onAddSign: action.bound,
      hideAddSignModal: action.bound,
      onAddSignOk: action.bound,
      // 转交
      forwordModal: observable,
      hideForwordModal: action.bound,
      onForword: action.bound,
      onForwordOk: action.bound,
      // 退回
      rejectModal: observable,
      onReject: action.bound,
      hideRejectModal: action.bound,
      onRejectOk: action.bound,
    })
  }

  init ({ appId, procKey, taskId, userTaskStatus, setFormAccess, onSubmit }) {
    return __awaiter(this, void 0, void 0, function * () {
      this.appId = appId
      this.procKey = procKey
      this.taskId = taskId
      this.formSubmit = onSubmit
      this.status = procKey ? 'start' : getStatus(userTaskStatus)
      let config = {}
      if (procKey) {
        config = yield service.getProcessConfig(procKey)
      } else {
        const data = yield service.getInstanceConfig(taskId)
        config = data.config
        runInAction(() => (this.logs = data.logs))
      }
      runInAction(() => {
        this.formActions = config.formActions
        this.nextNodeMode = config.nextNodeMode
        this.nextUserMode = config.nextUserMode
        this.nextUserNumber = config.nextUserNumber
      })
      if (setFormAccess) {
        const timmer = setTimeout(() => {
          const formFields = ['start', 'approve'].includes(this.status)
            ? config.formFields
            : undefined
          setFormAccess(formFields)
          clearTimeout(timmer)
        }, 1000)
      }
    })
  }

  get userOptions () {
    return this.usersTobeSelect.map(it => ({ label: it.name, value: it.id }))
  }

  get nodeOptions () {
    return this.nodesTobeSelect.map(it => ({ label: it.actName, value: it.actKey }))
  }

  get acitonsMaps () {
    return this.formActions.reduce((acc, it) => ({...acc, [it.type]: it}), {})
  }

  get buttons () {
    switch (this.status) {
      case 'start':
        return this.formActions.filter(it => ['SUBMIT', 'SAVE'].includes(it.type))
      case 'started':
        return this.formActions.filter(it => ['URGING', 'REVOKE'].includes(it.type))
      case 'approve':
        return this.formActions
      default:
        return []
    }
  }

  onSubmit () {
    let _a; let _b; let _c
    return __awaiter(this, void 0, void 0, function * () {
      this.formResult = yield this.formSubmit()
      if (this.nextNodeMode === 'SELECT') {
        const nodes = this.status === 'start'
          ? yield service.getStartNextNodes({
            procKey: this.procKey,
            values: this.formResult.values,
          })
          : yield service.getApproveNextNodes({
            taskId: this.taskId,
            values: this.formResult.values,
          })
        runInAction(() => (this.nodesTobeSelect = nodes))
      }
      this.submitModal = true
      if (!this.commentOptions.length) {
        const options = (_c = (_b = (_a = this.acitonsMaps.SUBMIT) === null || _a === void 0 ? void 0 : _a.config) === null || _b === void 0 ? void 0 : _b.opinion) === null || _c === void 0 ? void 0 : _c.options
        if (options) { this.commentOptions = options }
      }
    })
  }

  onSubmitOk ({ actKey, assignees, comment }) {
    return __awaiter(this, void 0, void 0, function * () {
      const users = this.usersTobeSelect.filter(it => [].concat(assignees).includes(it.id))
      if (this.status === 'start') {
        yield service.start({
          appId: this.appId,
          procKey: this.procKey,
          actKey,
          assignees: users,
          formResult: this.formResult,
          comment,
        })
      } else if (this.nextNodeMode === 'SELECT') {
          yield service.manualApprove({
            taskId: this.taskId,
            actKey,
            assignees: users,
            values: this.formResult.values,
            comment,
          })
        } else {
          yield service.autoApprove({ taskId: this.taskId, comment })
        }
      this.submitModal = false
    })
  }

  hideSubmitModal () {
    this.submitModal = false
  }

  onNodeSelect (nodeKey) {
    return __awaiter(this, void 0, void 0, function * () {
      if (this.nextUserMode === 'SELECT') {
        const userRange = this.nodesTobeSelect.find(it => it.actKey === nodeKey).assigneeInfo
        const users = yield this.getUsersByRange(userRange)
        runInAction(() => (this.usersTobeSelect = users))
      }
    })
  }

  onAddSign () {
    let _a; let _b
    return __awaiter(this, void 0, void 0, function * () {
      const userRange = (_b = (_a = this.acitonsMaps.ADD) === null || _a === void 0 ? void 0 : _a.config) === null || _b === void 0 ? void 0 : _b.userRange
      if (userRange) {
        const users = yield this.getUsersByRange(userRange)
        runInAction(() => (this.usersTobeSelect = users))
      }
      this.addSignModal = true
    })
  }

  hideAddSignModal () {
    this.addSignModal = false
  }

  onAddSignOk ({ flags, userId, comment }) {
    return __awaiter(this, void 0, void 0, function * () {
      const assignee = this.usersTobeSelect.find(it => it.id === userId)
      yield service.addSign({ taskId: this.taskId, flags, assignee, comment })
      this.addSignModal = false
    })
  }

  onForword () {
    let _a; let _b
    return __awaiter(this, void 0, void 0, function * () {
      const userRange = (_b = (_a = this.acitonsMaps.FORWORD) === null || _a === void 0 ? void 0 : _a.config) === null || _b === void 0 ? void 0 : _b.userRange
      if (userRange) {
        const users = yield this.getUsersByRange(userRange)
        runInAction(() => (this.usersTobeSelect = users))
      }
      this.forwordModal = true
    })
  }

  hideForwordModal () {
    this.forwordModal = false
  }

  onForwordOk ({ assigneeId, comment }) {
    return __awaiter(this, void 0, void 0, function * () {
      const assignee = this.usersTobeSelect.find(it => it.id === assigneeId)
      yield service.forword({ taskId: this.taskId, assignee, comment })
      this.forwordModal = false
    })
  }

  onReject () {
    return __awaiter(this, void 0, void 0, function * () {
      const nodes = yield service.getRejectNodes(this.taskId)
      runInAction(() => (this.nodesTobeSelect = nodes))
      this.rejectModal = true
    })
  }

  hideRejectModal () {
    this.rejectModal = false
  }

  onRejectOk ({ actKey, comment }) {
    return __awaiter(this, void 0, void 0, function * () {
      yield service.reject({ taskId: this.taskId, backActinstId: actKey, comment })
      this.rejectModal = false
    })
  }

  getUsersByRange (userRange) {
    return __awaiter(this, void 0, void 0, function * () {
      const { assignees, assigneeStations } = userRange
      const postUsers = yield service.getUserByPosts(assigneeStations)
      return assignees.concat(postUsers)
    })
  }
}
function getStatus (userTaskStatus) {
  const num = Number(userTaskStatus)
  let status = 'readOnly'
  if (num & 2048) { status = 'started' }
  if (num & 2 || num & 4 || num & 8) { status = 'approve' }
  return status
}
const process = new Process()

function Comment ({ options = [], value = '', onChange }) {
  return (React.createElement(React.Fragment, null,
    React.createElement(Input.TextArea, { maxLength: 50, placeholder: '\u8BF7\u8F93\u5165\u610F\u89C1', value, onChange: e => onChange(e.target.value) }),
    React.createElement('div', { style: { paddingTop: 8, cursor: 'pointer' } }, options.map(it => (React.createElement(Tag, { key: it, style: { marginBottom: 8 }, onClick: () => onChange(value + it) }, it))))))
}

const FormItem = ({ label, children }) => {
  return (React.createElement('div', { style: { marginBottom: 16 } },
    React.createElement('div', { style: { color: 'rgba(0,0,0,.65)', fontSize: 14 } }, label),
    children))
}

function SubmitModal ({ visible, userOptions, nodeOptions, commentOptions, nextUserNumber, onNodeSelect, onCancel, onOk }) {
  const [actKey, setActKey] = useState()
  const [assignees, setAssignees] = useState([])
  const [comment, setComment] = useState()
  function handleOk () {
    if (nodeOptions.length && !actKey) { return message.error('请选择节点') }
    if (userOptions.length && !assignees.length) { return message.error('请选择人员') }
    onOk({ actKey, assignees, comment })
  }
  return (React.createElement(Modal, { title: '\u63D0\u4EA4', visible, onCancel, onOk: handleOk },
    Boolean(nodeOptions.length) && (React.createElement(FormItem, { label: '\u9009\u62E9\u8282\u70B9' },
      React.createElement(Radio.Group, { options: nodeOptions,
        value: actKey,
        onChange: e => {
          const {value} = e.target
          setActKey(value)
          onNodeSelect(value)
        } }))),
    Boolean(userOptions.length) && (React.createElement(FormItem, { label: '\u5904\u7406\u4EBA' }, nextUserNumber === 'MULTIPLE' ? (React.createElement(Checkbox.Group, { options: userOptions, value: assignees, onChange: setAssignees })) : (React.createElement(Radio.Group, { options: userOptions, value: assignees, onChange: e => setAssignees(e.target.value) })))),
    React.createElement(FormItem, { label: '\u610F\u89C1' },
      React.createElement(Comment, { options: commentOptions, value: comment, onChange: setComment }))))
}

function CommentModal ({ title, options, onOk }) {
  const root = document.getElementById('process-comment-root')
  const Body = () => {
    const [visible, setVisible] = useState(true)
    const [comment, setComment] = useState()
    function close () {
      setVisible(false)
      const timer = setTimeout(() => {
        unmountComponentAtNode(root)
        clearTimeout(timer)
      }, 500)
    }
    return (React.createElement(Modal, { title, visible, cancelText: '\u53D6\u6D88', okText: '\u786E\u5B9A', onCancel: close, onOk: () => onOk(comment).then(close) },
      React.createElement(Comment, { options, value: comment, onChange: setComment })))
  }
  render(React.createElement(Body, null), root)
}

function AddSignModal ({ visible, userOptions, commentOptions, onCancel, onOk }) {
  const [flags, setFlags] = useState(0)
  const [userId, setUserId] = useState()
  const [comment, setComment] = useState()
  return (React.createElement(Modal, { title: '\u52A0\u7B7E',
    visible,
    onCancel,
    onOk: () => {
      if (!userId) { return message.error('请选择加签人') }
      onOk({ flags, userId, comment })
    } },
  React.createElement(FormItem, { label: '\u52A0\u7B7E\u6A21\u5F0F' },
    React.createElement(Radio.Group, { options: [
      { label: '前加签', value: 0 },
      { label: '后加签', value: 1 },
    ],
    value: flags,
    onChange: e => setFlags(e.target.value) })),
  React.createElement(FormItem, { label: '\u9009\u62E9\u52A0\u7B7E\u4EBA' },
    React.createElement(Radio.Group, { options: userOptions, value: userId, onChange: e => setUserId(e.target.value) })),
  React.createElement(FormItem, { label: '\u610F\u89C1' },
    React.createElement(Comment, { options: commentOptions, value: comment, onChange: setComment }))))
}

function ForwordModal ({ visible, userOptions, commentOptions, onCancel, onOk }) {
  const [assigneeId, setAssigneeId] = useState()
  const [comment, setComment] = useState()
  return (React.createElement(Modal, { title: '\u8F6C\u4EA4',
    visible,
    onCancel,
    onOk: () => {
      if (!assigneeId) { return message.error('请选择转交人') }
      onOk({ assigneeId, comment })
    } },
  React.createElement(FormItem, { label: '\u9009\u62E9\u8F6C\u4EA4\u4EBA' },
    React.createElement(Radio.Group, { options: userOptions, value: assigneeId, onChange: e => setAssigneeId(e.target.value) })),
  React.createElement(FormItem, { label: '\u610F\u89C1' },
    React.createElement(Comment, { options: commentOptions, value: comment, onChange: setComment }))))
}

function RejectModal ({ visible, nodeOptions, commentOptions, onCancel, onOk }) {
  const [actKey, setActKey] = useState()
  const [comment, setComment] = useState()
  return (React.createElement(Modal, { title: '\u9000\u56DE',
    visible,
    onCancel,
    onOk: () => {
      if (!actKey) { return message.error('请选择节点') }
      onOk({ actKey, comment })
    } },
  React.createElement(FormItem, { label: '\u9009\u62E9\u9000\u56DE\u8282\u70B9' },
    React.createElement(Radio.Group, { options: nodeOptions, value: actKey, onChange: e => setActKey(e.target.value) })),
  React.createElement(FormItem, { label: '\u610F\u89C1' },
    React.createElement(Comment, { options: commentOptions, value: comment, onChange: setComment }))))
}

const ProcessAccess = ({ appId, userTaskId, procKey, userTaskFlags, onSubmit, setFormAccess, extra = [] }) => {
  const { nextUserNumber, nodeOptions, userOptions, commentOptions, buttons, submitModal, hideSubmitModal, onNodeSelect, onSubmitOk, addSignModal, hideAddSignModal, onAddSignOk, forwordModal, hideForwordModal, onForwordOk, rejectModal, hideRejectModal, onRejectOk } = process
  useEffect(() => {
    process.init({
      appId,
      procKey,
      taskId: userTaskId,
      userTaskStatus: userTaskFlags,
      setFormAccess,
      onSubmit,
    })
  }, [])
  function revoke () {
    CommentModal({
      title: '确定要撤销吗？',
      options: commentOptions,
      onOk (comment) {
        return service.revoke({ taskId: userTaskId, comment })
      },
    })
  }
  function stop () {
    CommentModal({
      title: '确定要终止此流程吗？',
      options: commentOptions,
      onOk (comment) {
        return service.stop({ taskId: userTaskId, comment })
      },
    })
  }
  const handlerMaps = {
    SUBMIT: process.onSubmit,
    ADD: process.onAddSign,
    FORWORD: process.onForword,
    REJECT: process.onReject,
    STOP: stop,
    REVOKE: revoke,
  }
  const filterButtons = buttons
    .filter(it => it.visible)
    .map(it => ({
      name: it.name,
      type: it.type === 'SUBMIT' ? 'primary' : 'default',
      onClick: handlerMaps[it.type],
    }))
  return (React.createElement(React.Fragment, null,
    extra.concat(filterButtons).map(it => (React.createElement(Button, { style: { margin: '0 4px' }, key: it.name, type: it.type, onClick: it.onClick }, it.name))),
    React.createElement(SubmitModal, { visible: submitModal, nextUserNumber, onNodeSelect, nodeOptions, userOptions, commentOptions, onCancel: hideSubmitModal, onOk: onSubmitOk }),
    React.createElement(AddSignModal, { visible: addSignModal, userOptions, commentOptions, onCancel: hideAddSignModal, onOk: onAddSignOk }),
    React.createElement(ForwordModal, { visible: forwordModal, commentOptions, userOptions, onCancel: hideForwordModal, onOk: onForwordOk }),
    React.createElement(RejectModal, { visible: rejectModal, commentOptions, nodeOptions, onCancel: hideRejectModal, onOk: onRejectOk }),
    React.createElement('div', { id: 'process-comment-root' })))
}
const buttons = observer(ProcessAccess)

const { Step } = Steps
const colors = {
  process: '#3888E8',
  success: '#27C777',
  waiting: '#FAC022',
}
const statusMap = {
  0: 'process',
  1: 'finish',
  2: 'wait',
}
const processStatusMap = {
  0: React.createElement('span', { style: { color: colors.process } }, '\u8FD0\u884C\u4E2D'),
  1: React.createElement('span', { style: { color: colors.success } }, '\u5DF2\u5B8C\u6210'),
  2: React.createElement('span', { style: { color: colors.waiting } }, '\u5F85\u5904\u7406'),
}
function ProcessLog ({ userTaskId }) {
  const [logs, setLogs] = useState([])
  useEffect(() => {
    service.getProcessLogs(userTaskId).then(setLogs)
  }, [])
  return (React.createElement(Steps, { direction: 'vertical' }, logs.map(it => (React.createElement(Step, { key: it.startTime, title: it.actinstName, subTitle: processStatusMap[it.status], status: statusMap[it.status], description: it.userTasks.map(u => (React.createElement(User, { key: u.id, data: u }))) })))))
}
const userTextMaps = {
  0: React.createElement('span', { style: { color: colors.waiting } }, '\u5F85\u5904\u7406'),
  1: React.createElement('span', { style: { color: colors.success } }, '\u5DF2\u5B8C\u6210'),
}
function User ({ data }) {
  return (React.createElement('div', { style: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 8,
    marginBottom: 8,
    background: '#f5f5f5',
  } },
  React.createElement('div', null,
    React.createElement('span', { style: { marginRight: 8, color: 'rgba(0,0,0,.85)', borderRadius: 4 } }, data.assigneeName),
    React.createElement('span', null, userTextMaps[data.status])),
  React.createElement('div', { style: { color: 'rgba(0,0,0,.45)' } }, dateString(data.gmtEnd))))
}
function dateString (stamp) {
  if (!stamp) { return '' }
  const date = new Date(stamp).toLocaleDateString()
  const time = new Date(stamp).toLocaleTimeString()
  return `${date} ${time}`
}

export { buttons as Buttons, ProcessLog as Steps }
