/* eslint-disable jsx-a11y/iframe-has-title */
import React, { useEffect, useState, useMemo } from 'react'
import { Modal, Table, Space, Spin } from 'antd'
import { getQueryString, onMouseDown } from 'ROOT/utils'
import service from 'ROOT/service'
import Cookies from 'js-cookie'
// import { get } from 'lodash'

export default ({
  onCancel,
  onOk,
  backFillDataSouce = [],
  fileList,
  procFormDataKey,
  operator = {},
  permissionObj = {},
  setPermissionObj = () => { }
}) => {
  const [visible, setVisible] = useState(false)
  const [actionIndex, setActionIndex] = useState('')
  const [dataSource, setDataSource] = useState()
  const [deptDefaultList, setDeptDefaultList] = useState([])
  const [loading, setLoading] = useState(true)
  const [totalAmount, setTotalAmount] = useState('')
  const [_fileId, setFileId] = useState(permissionObj.fileId)
  const [styleParams, setStyleParams] = useState({
    styleTop: 100,
    styleLeft: 0,
  })
  const [deptStyleParams, setDeptStyleParams] = useState({
    styleTop: 100,
    styleLeft: 0,
  })
  // 获取拖动值
  const getStyleParams = (styleTop, styleLeft) => {
    setStyleParams({ styleTop, styleLeft })
  }
  
  const getDeptStyleParams = (styleTop, styleLeft) => {
    setDeptStyleParams({ styleTop, styleLeft })
  }

  const style = { left: styleParams.styleLeft, top: styleParams.styleTop }
  const deptStyle = { left: deptStyleParams.styleLeft, top: deptStyleParams.styleTop }

  let {
    orgId = Cookies.get('orgId'),
    orgName,
  } = JSON.parse(
    Cookies.get('uiapp') ||
    Cookies.get('WEBG_STORAGE') ||
    Cookies.get('EntAdminG_STORE') ||
    localStorage.WEBG_STORAGE ||
    localStorage.EntAdminG_STORE ||
    '{}'
  )

  const getTableList = async () => {
    const res = await service.getWpsParagraph({
      name: fileList.name,
      url: fileList.url,
      pageIndex: 1,
      pageSize: 1000,
      htmlCode: fileList.html,
      fileId: _fileId,
    })
    if (res && res.success) {
      const { data = [] } = res || {}
      const { list = [], total, fileId } = data || []
      let arr = []
      const permissionObjList = permissionObj.list
      if (permissionObjList && permissionObjList.length > 0) {
        arr = list.map((item, index) => {
          const arr2 = permissionObjList.filter(i => i.id === item.id)
          console.log('item', item);
          return {
            ...item,
            key: index,
            number: index,
            content: item.text,
            id: item.id,
            visibleRange: arr2[0] ? arr2[0].visibleRange : []
          }
        })
      } else {
        arr = list.map((item, index) => ({
          ...item,
          key: index,
          number: index,
          content: item.text,
          id: item.id,
          visibleRange: [],
        }))
      }
      setTotalAmount(total)
      setFileId(fileId)
      permissionObj.fileId = fileId
      setPermissionObj({ ...permissionObj })
      setDataSource([...arr])
      setLoading(false)
    } else {
      setLoading(false)
    }
  }
  useEffect(() => {
    if (backFillDataSouce && backFillDataSouce.length > 0) {
      setDataSource(backFillDataSouce)
      setTotalAmount(backFillDataSouce.length)
      setLoading(false)
    } else {
      getTableList()
    }
  }, [])

  const contentFn = (x, y) => {
    if (y.type == 2) {
      return <img width={150} src='http://moa-dev.uban360.net:21007/sfs/srvfile?digest=fid25f1116cc13ec1c75740b9fdff56b4a1'></img>
    } else {
      return x
    }
  }

  const columns = [
    {
      title: '序号',
      dataIndex: 'number',
      key: 'number',
      render: text => <div>{text}</div>,
      width: 80
    },
    {
      title: '段落内容',
      dataIndex: 'content',
      key: 'content',
      render: (cal, text) => <div>{contentFn(cal, text)}</div>,
      width: 300,
      align: 'center'
    },
    {
      title: '可见范围',
      dataIndex: 'visibleRange',
      key: 'visibleRange',
      render: text => <div>{text.map(x => (x.selectName || x.name)).join('，')}</div>,
      align: 'center'
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      width: 100,
      render: (text, record) => (
        <Space size="middle">
          <a onClick={() => setPermission(text, record)}>设置权限</a>
        </Space>
      ),
    },
  ];

  const setPermission = (text, record) => {
    setVisible(true)
    dataSource.map(item => {
      if (text.key === item.key) {
        setDeptDefaultList(item.visibleRange)
      }
    })
    setActionIndex(text.key)
  }

  const getDefaultList = () => {
    let defaultList = []
    if (deptDefaultList && deptDefaultList.length > 0) {
      deptDefaultList.forEach(item => {
        item.name = item.selectName || item.name
      })
      defaultList = deptDefaultList
    }
    return defaultList
  }

  // http://npm.jituancaiyun.com/package/@xm/user-selector
  const params = {
    visible: true,
    orgId: operator.orgId,
    orgName: operator.orgName,
    defaultDeptList: getDefaultList(),
    range: 1000,
    type: 'multiOrgAndDept',
    title: '选择部门',
    isChooseFromOtherOrgs: operator.orgId === '10032' || operator.orgId === '10016', // 区公司支持从所有单位选
    needSearchBar: false,
    needHeader: false,
    isDiffOrg: true,
    isAssingRootOrg: true,
    assingDeptLevel: 1,
    onlyChooseDept: true,
    deptIsBefore: true,
    deptNameNeedOrg: true,
    expandTreeLoading: true,
  }

  const modalOnOk = () => {
    onOk(dataSource)
    service.saveParagraphDetail({
      formId: procFormDataKey,
      id: permissionObj.id,
      fileId: _fileId,
      data: {
        list: dataSource,
      }
    })
  }

  useEffect(() => {
    function messageEvent(e) {
      const { data = {} } = e
      const { type, data: _data = {} } = data
      const { depts = [], users = [], orgs = [] } = _data
      if (depts.length > 0) {
        depts.forEach(item => {
          item.name = item.selectName || item.name
        })
      }
      // 点击选人组件中的【取消】按钮
      if (type === 'BAAS_TREE_CANCEL') {
        setVisible(false)
      }
      // 点击选人组件中的【确定】按钮
      if (type === 'BAAS_TREE_CONFIRM') {
        // 保存到对应的dataSouce
        const _dataSource = dataSource.map(item => {
          if (item.key === actionIndex) {
            return {
              ...item,
              visibleRange: depts.concat(orgs),
            }
          } else {
            return item
          }
        })
        setDataSource([..._dataSource])
        setVisible(false)
      }
    }
    if (visible) {
      window.addEventListener('message', messageEvent)
    }
    return () => {
      window.removeEventListener('message', messageEvent)
    }
  }, [visible])

  return (
    <div>
      <Modal
        style={style}
        title={
          <div
            style={{ cursor: 'move', width: '100%' }}
            onMouseDown={e => onMouseDown(e, styleParams, getStyleParams)}
          >
            注: 如您未设置可见范围，则系统默认为所有人都可见
          </div>
        }
        visible
        onCancel={onCancel}
        onOk={() => modalOnOk()}
        width={1200}
      >
        <Spin spinning={loading}>
          <Table
            columns={columns}
            dataSource={dataSource}
            pagination={{
              showQuickJumper: true,
              pageSize: 10,
              total: totalAmount,
              showTotal: () => `总共${totalAmount}条数据`
            }}
          />
        </Spin>
      </Modal>
      {visible
        // eslint-disable-next-line multiline-ternary
        ? <Modal
        style={{ ...deptStyle, height: 592, overflow: 'hidden'}}
        title={
          <div
            style={{ cursor: 'move', width: '100%' }}
            onMouseDown={e => onMouseDown(e, deptStyleParams, getDeptStyleParams)}
          >
            选择部门
          </div>
        }
          visible={visible}
          maskClosable={false}
          width={600}
          footer={null}
          onCancel={ () => setVisible(false) }
          bodyStyle={{
            padding: 0,
            margin: 0,
            overflow: 'hidden',
            // height: 590
            height: 538
          }}
        >
          <iframe
            src={`${ab.api}/user-selector?query=${encodeURIComponent(
              JSON.stringify(params),
            )}`}
            id="permissionIframe"
            frameBorder="0"
            style={{ border: 0 }}
            width="100%"
            height="100%"
          />
        </Modal> : null
      }
    </div>
  )
}