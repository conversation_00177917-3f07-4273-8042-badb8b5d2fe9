import React, { useState, useEffect, useMemo } from 'react'
import { Modal, Divider, Descriptions } from 'antd'
import {
  SchemaMarkupForm,
  FormButtonGroup,
  Submit,
  Reset,
  createAsyncFormActions,
  createFormActions,
  FormEffectHooks,
} from '@formily/antd'
import foreignunitSchema from '../common/foreignunitSchema'
import {
  Input,
  NumberPicker,
  FormMegaLayout,
  Select,
  Radio,
  FormItemGrid,
  DatePicker,
  FormBlock,
} from '@formily/antd-components'
import Editor from 'ROOT/components/Formily/Editor'
import CustomRangePicker from 'ROOT/components/Formily/CustomRangePicker'
import onSiteSchema from '../common/onSiteSchema'
import onVidioSchema from '../common/onVidioSchema'
import siteVidioSchema from '../common/siteVidioSchema'
import onSiteSchema1 from '../common/onSiteSchema1'
import siteVidioSchema1 from '../common/siteVidioSchema1'
import previewSchame from '../common/previewSchame'
import { cloneDeep } from 'lodash'
import styles from './index.scss'
import service from 'ROOT/service'
import { get } from 'lodash'
import { getQueryString } from 'ROOT/utils'

export default ({ onCancel, schame, actions, type }) => {
  const [defaultValue, setDefaultValue] = useState()
  const [signer, setSigner] = useState()
  const [schema, setSchema] = useState()
  const [meetingList, setMeetingList] = useState([])
  const [meetingId, setMeetingId] = useState(null)
  const { procFormDataKey } = useMemo(() => getQueryString(`?${location.hash.split('?')[1]}`), [])

  CustomRangePicker.isFieldComponent = true
  Editor.isFieldComponent = true
  actions.getFormState().then(data => {
    const {
      values: { meetingPerson, participants },
      values,
    } = data
    // const newValue = {...values,meetingPerson: value}
    try {
      if (values && values.startTime && values.endTime) {
        values.rangePicker = [values.startTime, values.endTime]
      }
    } catch (err) {
      console.log(err)
    }
    const meetingIdInfo =
      values.meetingPlace ||
      values.meetingPlace1 ||
      values.mainVenueLocation1 ||
      values.mainVenueLocation
    setMeetingId(meetingIdInfo)
    setDefaultValue(values)
    const { signer } = data.values || {}
    setSigner(signer)
  })

  function changeMeetingPlaceSpan(obj, fields = [], span = 3) {
    const schema = cloneDeep(obj)
    // eslint-disable-next-line no-restricted-syntax
    for (const field of fields) {
      schema.properties.layout.properties[field]['x-mega-props'] =
        schema.properties.layout.properties[field]['x-mega-props'] || {}
      schema.properties.layout.properties[field]['x-mega-props'] = {
        ...schema.properties.layout.properties[field]['x-mega-props'],
        span,
      }
    }

    return schema
  }

  useEffect(() => {
    if (type) {
      const schema = changeMeetingPlaceSpan(previewSchame, ['meetingName', 'meetingPlace'])
      setSchema(schema)
    } else {
      const meetingAategory = actions.getFieldValue('meetingAategory')
      const meetingForm = actions.getFieldValue('meetingForm')
      const bookRoom = actions.getFieldValue('bookRoom')
      const startTime = actions.getFieldValue('startTime')
      const endTime = actions.getFieldValue('endTime')
      if (location.href.indexOf('foreignunit') > -1) {
        const schema = changeMeetingPlaceSpan(foreignunitSchema, ['meetingName', 'meetingPlace'])
        setSchema(schema)
      } else {
        Promise.all([meetingAategory, meetingForm, bookRoom, startTime, endTime]).then(
          async res => {
            let meetList = []
            const startT = new Date(res[3]).getTime()
            const endT = new Date(res[4]).getTime()
            if (res[2] === '1' && startT && endT) {
              // 获取同步预约会议室列表
              await service
                .getRoom({
                  reportId: procFormDataKey,
                  startTime: startT,
                  endTime: endT,
                })
                .then(res => {
                  meetList = res.data
                })
            } else if (res[2] === '2') {
              // 获取已预约会议列表
              await service
                .getMeeting({
                  reportId: procFormDataKey,
                })
                .then(res => {
                  meetList = res.data
                })
            }
            let newMeetList = [...meetList]
            newMeetList = newMeetList.map(item => {
              return {
                label: item.roomName,
                value: res[2] !== '1' ? item.meetingInviteId : item.roomId,
              }
            })
            setMeetingList(newMeetList)
            if (res[0] != 3) {
              const meetingPlace = res[2] && res[2] !== '3' ? 'meetingAddr' : 'meetingPlace'
              const dataSchema = res[2] && res[2] !== '3' ? onSiteSchema1 : onSiteSchema
              const schema = changeMeetingPlaceSpan(dataSchema, ['meetingName', meetingPlace])
              setSchema(schema)
            } else if (res[1] == 1) {
              const meetingPlace = res[2] && res[2] !== '3' ? 'meetingAddr' : 'meetingPlace'
              const dataSchema = res[2] && res[2] !== '3' ? onSiteSchema1 : onSiteSchema
              const schema = changeMeetingPlaceSpan(dataSchema, ['meetingName', meetingPlace])
              setSchema(schema)
            } else if (res[1] == 2) {
              const mainVenueLocation =
                res[2] && res[2] !== '3' ? 'meetingAddr' : 'mainVenueLocation'
              const dataSchema = res[2] && res[2] !== '3' ? siteVidioSchema1 : siteVidioSchema
              const schema = changeMeetingPlaceSpan(dataSchema, [
                'meetingName',
                mainVenueLocation,
                'videoBranchVenueLocation',
              ])
              setSchema(schema)
            } else if (res[1] == 3) {
              const mainVenueLocation =
                res[2] && res[2] !== '3' ? 'meetingAddr' : 'mainVenueLocation'
              const dataSchema = res[2] && res[2] !== '3' ? siteVidioSchema1 : siteVidioSchema
              const schema = changeMeetingPlaceSpan(dataSchema, [
                'meetingName',
                mainVenueLocation,
                'videoBranchVenueLocation',
              ])
              setSchema(schema)
            }
          },
        )
      }
    }
  }, [])

  const components = {
    TextArea: Input.TextArea,
    Input,
    NumberPicker,
    FormMegaLayout,
    Select,
    Radio,
    RadioGroup: Radio.Group,
    RangePicker: DatePicker.RangePicker,
    Editor,
    CustomRangePicker,
  }

  const getValue = e => {
    if (!e) return {}
    const { meetingPerson, participants } = e
    const value = meetingPerson
      ? meetingPerson
      : participants
      ? typeof participants == 'string'
        ? participants
        : participants.map(item => item.name).join('、')
      : ''
    // value.meetingList = meetingList
    const meetingAddr =
      meetingList && meetingList.length > 0
        ? get(
            meetingList.filter(item => item.value === meetingId),
            '[0].label',
            '',
          )
        : ''
    e.meetingAddr = meetingAddr
    return {
      ...e,
      meetingPerson: value,
    }
  }

  const rewriteSchemaForReadOnlyRender = schema => {
    if (schema) {
      // 重写日期范围控件在只读模式下的 UI
      const rangePicker = schema.properties.layout.properties.rangePicker

      rangePicker['x-component'] = 'CustomRangePicker'
    }

    return schema
  }

  return (
    <div>
      {/* <h1 style={{ textAlign: 'center' }}>{meetTitle || '-'}</h1> */}
      <div
        style={{
          textAlign: 'end',
          marginBottom: -20 + 'px',
          marginRight: 10 + 'px',
          fontFamily: '仿宋_GB2312, 宋体',
          fontSize: 21,
        }}
      >
        签发人: <span>{signer || '-'}</span>
      </div>
      <Divider />
      <SchemaMarkupForm
        schema={rewriteSchemaForReadOnlyRender(schema)}
        className={styles['preview-modal']}
        components={components}
        editable={false}
        previewPlaceholder="-"
        value={getValue(defaultValue)}
      />
      {/* <div style={{ display: 'flex', justifyContent: 'space-between', padding: ' 0 30px' }}>
        <div>起草人: {user || '-'}</div>
        <div>联系电话: {phone || '-'}</div>
      </div> */}
    </div>
  )
}
