import React, { useEffect, useState } from 'react'
import { Modal, Table, Button } from 'antd'
import { opTypes } from '../../oa/constants'
import { css } from 'emotion'

export default (props) => {
    const { data = [], showUploadMessage=false, closeImportMessageModal } = props

    const columns = [{
        title: '导入结果',
        dataIndex: 'importFlag',
        key: 'importFlag',
        render: (val) => {
            return val ? <span>成功</span> : <span>失败</span>
        }
    }, {
        title: '原因说明',
        dataIndex: 'importMsg',
        key: 'importMsg',
    }, {
        title: '操作',
        dataIndex: 'operateType',
        key: 'operateType',
    }, {
        title: '主账号',
        dataIndex: 'groupMasterAccount',
        key: 'groupMasterAccount'
    }, {
        title: '从账号姓名',
        dataIndex: 'userChineseName',
        key: 'userChineseName'
    }, {
        title: '从账号名',
        dataIndex: 'userSlaveAccount',
        key: 'userSlaveAccount'
    }, {
        title: '手机号码',
        dataIndex: 'userMobile',
        key: 'userMobile'
    },]
    return <Modal
        visible={showUploadMessage}
        title="导入结果"
        width={800}
        footer={false}
        closable={false}
    >
        <div>
            <div>导入结果：共校验{data.length}条数据，{data.filter(item=>item.importFlag).length}条校验通过，{data.filter(item=>!item.importFlag).length}条校验不通过</div>
            <div className={css`
                margin-top: 15px;
            `}>
                <Table
                    columns={columns}
                    dataSource={data}
                />
            </div>
            <div className={css`
            text-align: center;
            margin-top: 15px;
            `}>
                <Button onClick={()=>closeImportMessageModal(data.filter(item=>item.importFlag))} type='primary'>确定</Button>
            </div>
        </div>
    </Modal>
}