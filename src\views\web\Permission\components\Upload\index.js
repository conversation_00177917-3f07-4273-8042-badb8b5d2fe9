import React, { useState, useRef, useCallback } from 'react'
import {
  message
} from 'antd'
import { css } from 'emotion'
import Service from 'ROOT/service'
import Loading from 'ROOT/components/Loading'
import UploadFileList from '@xm/upload-file-list'
import {
  maxFilesNumber
} from 'ROOT/constants'
import { cloneDeep } from "lodash";

export default ({
  disabled,
  fileList,
  uploadFile,
  setUploadFiles
}) => {
  const InputImport = useRef()
  const [loading, setLoading] = useState(false)

  const selectFiles = () => {
    InputImport.current.click()
  }

  const fileChange = (e) => {
    let files = Array.from(e.target.files)
    let arr1 = [], arr2=[];
    let ableUpload = []
    if (maxFilesNumber > 0 && fileList.length > maxFilesNumber) {
      const str = `最多只能上传${maxFilesNumber}个文件`
      message.warning(str)
      return
    }
    files.map(item => {
      const { name, size } = item || {}
      if (size <= 0) {
        arr1.push(name)
        return
      }
      if (size > 20 * 1024 * 1024) {
        arr2.push(name)
        return
      }
      ableUpload.push(item)
    })
    if (arr1.length > 0) {
      message.warning(`${arr2.join(',')}0kb或0kb以下大小的文件，请重新选择文件上传`)
    } else if (arr2.length > 0) {
      message.warning(`${arr2.join(',')}文件大小需20M以下`)
    }
    if (files.length > 0) {
      ableUpload.forEach(i => uploadFiles(i))
    }
    InputImport.current.value = null
  }

  const uploadFiles = (files) => {
    let formData = new FormData()
    formData.append('file', files)
    setLoading(true)
    Service.uploadFile(formData).then(res => {
      if (res.code === 200) {
        const fileData = {
          name: files.name,
          size: files.size,
          url: res.fileUrl,
          suffix: files.name.substring(files.name.lastIndexOf('.') + 1),
          uid: files.lastModified
        }
        setUploadFiles(prev => {
          prev.push(fileData)
          return cloneDeep(prev)
        })
        setLoading(false)
      }
    })
  }

  const setUploadedFiles = (array) => {
    setUploadFiles(array)
  }

  const uploadOptions = {
    enableSort: !disabled,
    enableDownload: true,
    enableDelete: !disabled,
    enableRename: !disabled,
    enablePreview: disabled
  }

  const combineFileList = (list) => {
    return !Array.isArray(list)
    ? []
    : list.map((file, index) => ({
      name: `${(file.name || file.fileName)}`,
      size: file.size || file.fileSize,
      url: file.url || file.fileUrl,
      suffix: (file.name || file.fileName).substring((file.name || file.fileName).lastIndexOf('.') + 1),
      uid: file.uid
    }))
  }

  const onDelete = (fileList) => {
    setUploadedFiles(combineFileList(fileList))
  }

  const onSortEnd = (fileList) => {
    setUploadedFiles(combineFileList(fileList))
  }

  const onRename = (fileList) => {
    setUploadedFiles(combineFileList(fileList))
  }

  const combineFileName = useCallback(() => {
    return combineFileList(uploadFile)
  }, [uploadFile])

  return (
    <div style={{ width: '100%' }}>
      {!disabled
        ? <div style={{ width: '100%', marginBottom: 10 }}> 
            <span
              onClick={selectFiles}
              className="upload-file-btn"
            >上传文件</span>
            <input
              type="file"
              ref={InputImport}
              onChange={fileChange}
              className={css`
                position: absolute;
                top: -9999px;
                left: -9999px;
              `}
              multiple="multiple"
            />
          {loading && <Loading isFullScreen={true} />}
        </div>
        : null
      }
      <UploadFileList options={uploadOptions} dataSource={combineFileName()} onDelete={onDelete} onSortEnd={onSortEnd} onRename={onRename} />
    </div>
  )
}