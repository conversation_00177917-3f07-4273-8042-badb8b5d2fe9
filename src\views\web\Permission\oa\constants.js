import React from 'react'
export const detailInfo =
  '（说明：请根据模板导入或者填写用户信息，否则在4A系统中校验不通过，无法创建、修改账号信息。）'

export const downLoadUrlUpdate = '/extra-forms/template/OA账号更新_模板v2.xls'

export const downLoadUrlDel = '/extra-forms/template/OA账号删除_模板v2.xls'

export const downLoadUrlAdd = '/extra-forms/template/OA账号新增_模板v2.xls'

export const sexuality = [
  {
    value: 'm',
    label: '男',
  },
  {
    value: 'f',
    label: '女',
  },
]

export const workDuty = [
  {
    value: '1',
    label: '正职',
  },
  {
    value: '2',
    label: '副职',
  },
  {
    value: '3',
    label: '员工',
  },
]

export const userTypes = [
  {
    value: '0',
    label: '普通员工',
  },
  {
    value: '1',
    label: '服务厅员工',
  },
]

export const columns = [
  { title: '操作', dataIndex: 'operateType', key: 'operateType' },
  { title: '集团主账号', dataIndex: 'groupMasterAccount', key: 'groupMasterAccount' },
  { title: '集团副账号', dataIndex: 'groupSlaveAccount', key: 'groupSlaveAccount' },
  {
    title: '用户姓名',
    dataIndex: 'surname',
    key: 'surname',
    render: (j, k) => (j || '') + (k.name || ''),
  },
  { title: '用户从账号', dataIndex: 'userSlaveAccount', key: 'userSlaveAccount' },
  { title: '用户编号', dataIndex: 'userCode', key: 'userCode' },
  { title: '手机号码', dataIndex: 'userMobile', key: 'userMobile' },
  {
    title: '所属组织',
    dataIndex: 'newOaOrg',
    key: 'newOaOrg',
    render: (val) => {
      if (Array.prototype.isPrototypeOf(val)) {
        const names = val.map(item => item.name)
        return <div>{names.join('、')}</div>
      } else {
        return val
      }
    }
  },
]

export const userRelationSystem = {
  title: '邮箱/关联系统',
  dataIndex: 'system',
  key: 'system',
}

export const mockDataSource = [
  // {
  //   operateType: '1',
  //   groupMasterAccount: '2',
  //   groupSlaveAccount: '3',
  //   surname: '4',
  //   name: '5',
  //   userSlaveAccount: '从账号',
  //   userCode: '6',
  //   userMobile: '7',
  //   newOaOrg: 'dadadaa',
  //   userEmail: '9',
  // },
]

export const opTypes = [
  {
    value: '新增',
    label: '新增',
  },
  {
    value: '修改',
    label: '修改',
  },
  {
    value: '删除',
    label: '删除',
  },
]

export const titleRules = [
  {
    required: true,
    message: '请输入标题!',
  },
]

export const maxFilesNumber = 10

export const formChineseName = '统一信息平台新增用户和权限更改审批表'

export const formEnglishName = '统一信息平台新增用户和权限更改审批表'

export const fieldConfig = {
  attachments: '附件',
  content: '内容',
  tableDataSource: '表格数据',
  time: '申请时间',
  title: '标题',
  user: '用户',
  dept: '部门',
}

export const opTypeMap = {
  新增: '0',
  修改: '1',
  删除: '2',
}

export const ADD = '0'
export const MODIFY = '1'
export const DELETE = '2'

export default {
  detailInfo,
  columns,
  mockDataSource,
  opTypes,
  sexuality,
  workDuty,
  userTypes,
  titleRules,
  maxFilesNumber,
  userRelationSystem,
  downLoadUrlUpdate,
  downLoadUrlDel,
  downLoadUrlAdd,
  formChineseName,
  formEnglishName,
  fieldConfig,
  opTypeMap,
  ADD,
  MODIFY,
  DELETE,
}
