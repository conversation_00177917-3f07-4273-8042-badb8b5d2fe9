/* eslint-disable jsx-a11y/label-has-associated-control */
import React, { useEffect, useState } from 'react'
import UploadMessage from '../components/UploadMessage'
import getUserOrgType from 'ROOT/hooks/getUserOrgType'
import {
  Form,
  Row,
  Col,
  Select,
  Input,
  Table,
  Button,
  // Upload,
  Modal,
  DatePicker,
  Spin,
  message,
} from 'antd'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import XmTree from '@xm/Tree'
import { Buttons, Steps } from 'ROOT/components/Process'
import {
  detailInfo,
  opTypes,
  sexuality,
  workDuty,
  userTypes,
  titleRules,
  downLoadUrlUpdate,
  downLoadUrlDel,
  downLoadUrlAdd,
  formChineseName,
  opTypeMap,
  ADD,
  MODIFY,
  DELETE,
} from './constants'
import style from './index.scss'
import {
  useUpload,
  useImport,
  useAddModal,
  useOaOrg,
  useTable,
  useCodeNumber,
  useLoading,
  useSubmit,
  useFetchUserRelationSys,
  useFetchData,
  useCurrentTime,
  useAccessControll,
  useParams,
  useInfo,
  useChildFormChange,
  useInitialForm,
} from './useBusiness'
import Upload from '../components/Upload'
import { localColor } from 'ROOT/utils/resetTheme'
import { css } from 'emotion'
import useMyInfo from 'ROOT/hooks/useMyInfo'
import SelectDept from './selectDept'
import { openDownloadDialog } from 'ROOT/utils'
import request from 'ROOT/service'
import { patchData } from '../../../../utils'
import axios from 'axios'

const { Item: FormItem } = Form
const { Option } = Select
const { TextArea } = Input

const noop = ({ target }) => target.blur()

const OaPermission = props => {
  const { procKey, appId, procFormDataKey, userTaskId, debug, procFormKey, backurl } = useParams(props)
  const { procFormDataKeyRef } = useInitialForm(procFormDataKey)
  const [mainForm] = Form.useForm()
  const [addModalForm] = Form.useForm()
  const currentTime = useCurrentTime()
  const { loading, setLoading } = useLoading()
  const { deptList } = useInfo(userTaskId, mainForm)
  const { uploadProps, disableUpload, uploadFilesRef, setUploadFiles, uploadFile } = useUpload(setLoading)
  const { codeNumber, setCodeNumber } = useCodeNumber()
  const { onProcessSdkMount, permissionState, isStart } = useAccessControll(debug)
  const { tableData, setTableData, rowSelectionRef, handleDeleteRowData } = useTable(
    permissionState.tableDataSource.disable,
  )
  console.log(permissionState)
  const [initValue, setInitValue] = useState({})
  const handleMount = useFetchData(
    procFormDataKey,
    mainForm,
    setLoading,
    setUploadFiles,
    setTableData,
    setCodeNumber,
    setInitValue,
  )
  const { handleFileChange, showUploadMessage, uploadList, closeImportMessageModal } = useImport(setLoading, setTableData)
  const { showAddModal, closeAddModal, handldeShowAddModal, handleModalOk } = useAddModal(
    addModalForm,
    setTableData,
  )
  const { treeVis, handleTreeVis, closeTree, handleTreeConfirm, depts, handleFormValueChange, isOrgId } =
    useOaOrg(addModalForm)
  const { columns, dataSource } = tableData
  const { handleSubmit, actionsButton } = useSubmit(
    mainForm,
    procFormDataKey,
    uploadFilesRef,
    dataSource,
    setLoading,
    deptList,
    procFormDataKeyRef,
    appId,
    codeNumber,
    initValue,
  )
  const { handleFetchUserRelationSys } = useFetchUserRelationSys(
    setLoading,
    tableData,
    setTableData,
  )
  const myInfo = useMyInfo({ isSetStorage: true })
  const { loginOrgId } = myInfo
  const { opType, handleChildFormChange } = useChildFormChange()
  const opTypeValue = opTypeMap[opType]
  const [ableEditBtn, setAbleEditBtn] = useState([])

  useEffect(() => {
    if (debug) {
      ableEditForm()
    }

    // 判断是否是权限设置表单，并且是起草或者从我的草稿中进入是弹出提示
    if (procFormKey === 'oaquanxian' && !procFormDataKey) {
      Modal.confirm({
        icon: <ExclamationCircleOutlined style={{ color: '#f9bb2f' }} />,
        title: '提醒',
        content: '根据账号管理要求，用户在岗位变动时应及时发起账号及权限变更申请，请按流程对4A账号及其他应用系统的账号发起组织调整、权限变更或注销等申请。如一个月内不处理，相关账号将被锁定。',
        okText: '知道了',
        cancelButtonProps: { style: { display: 'none' } },
      })
    }
  }, [])

  const ableEditForm = async () => {
    const res = await request.getEditableForm()
    if (res.success && res.data) {
      // resetFormState()
      setAbleEditBtn([{
        name: '更新数据',
        async: true,
        onClick: async () => {
          await handleSubmit()
        }
      }])
    }
  }

  const getParam = () => {
    console.log(isOrgId, 'depts')
    const params = {
      visible: { treeVis },
      type: "singleDept",
      deptList: depts,
      ableChooseRoot: true,
      ableChooseOrg: true,
      ableChooseUser: false,
      API_GET_ROOT_ORG: `/baas-admin/web/org/myList?flag=${isOrgId ? 4 : 1}`,
      API_GET_LIST_ALL: `/baas-admin/web/listAll?flag=${isOrgId ? 3 : 2}`,
      API_SEARCH_LIST: "/web-search/web/search?size=100",
      treeCancelCallback: closeTree,
      treeCallback: handleTreeConfirm
    }
    return params
  }
  
  return (
    <Spin spinning={loading}>
      <div className={style['oa-permission-container']}>
        <div className={style['oa-permission-title']}>{formChineseName}</div>
        {codeNumber && (
          <div className={style['oa-permission-text-code']}>
            <span>编号：</span>
            {codeNumber}
          </div>
        )}
        <div className={style['oa-permission-form']}>
          <Form onValuesChange={handleFormValueChange} layout="vertical" form={mainForm}>
            <Row gutter={24}>
              {permissionState.dept.visible && (
                <Col span={8}>
                  {/* 前人留下问题，审核页展示deptName，起草页展示dept */}
                  {
                    procFormDataKey ? <FormItem
                      label="申请部门"
                      name="deptName"
                      rules={[
                        {
                          required: true,
                          message: '请选择部门!',
                        },
                      ]}
                    >
                      <Input disabled />
                    </FormItem> : <FormItem
                      label="申请部门"
                      name="dept"
                      rules={[
                        {
                          required: true,
                          message: '请选择部门!',
                        },
                      ]}
                    >
                      <Select placeholder="请选择部门">
                        {deptList.map(j => (
                          <Option disabled={permissionState.dept.disable} key={j.id}>
                            {j.deptPath}
                          </Option>
                        ))}
                      </Select>
                    </FormItem>
                  }
                </Col>
              )}
              <Col span={8}>
                {permissionState.user.visible && (
                  <FormItem label="申请人" name="user">
                    <Input disabled />
                  </FormItem>
                )}
              </Col>
              <Col span={8}>
                {permissionState.user.visible && (
                  <FormItem initialValue={currentTime} label="申请时间" name="time">
                    <Input disabled />
                  </FormItem>
                )}
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={24}>
                {permissionState.title.visible && (
                  <FormItem label="标题" name="title"
                    rules={titleRules}
                  >
                    <Input
                      placeholder="请输入"
                      disabled={permissionState.title.disable}
                      maxLength={100}
                      autoComplete="off"
                    />
                  </FormItem>
                )}
              </Col>
            </Row>
            <Row gutter={24}>
              {permissionState.content.visible && (
                <Col span={24}>
                  <FormItem
                    label="申请内容"
                    name="content"
                    rules={[
                      {
                        required: true,
                        message: '申请内容为必填项!',
                      },
                    ]}
                  >
                    <TextArea
                      disabled={permissionState.content.disable}
                      placeholder="请输入"
                      rows={5}
                      showCount
                      maxLength={1000}
                    />
                  </FormItem>
                </Col>
              )}
            </Row>
          </Form>
        </div>
        <div className={style['oa-permission-op-list-container']}>
          <div className={style['oa-permission-op-list-title']}>
            <span>OA账号操作列表</span>
            <span>{detailInfo}</span>
          </div>
          {permissionState.tableDataSource.visible && dataSource.length !== 0 && (
            <div className={style['oa-permission-op-list-table']}>
              <Table
                disabled={permissionState.tableDataSource.disable}
                pagination={false}
                rowSelection={rowSelectionRef.current}
                dataSource={dataSource}
                columns={columns}
              />
            </div>
          )}
          <div style={{ display: 'flex' }}>
            {permissionState.tableDataSource.visible && dataSource.length !== 0 && (
               <Button
                type="primary"
                style={{ margin: '14px 6px 0px 0px' }}
                onClick={() => {
                    axios.post('/security-manage-platform/web/business/export/currentUserChangeRecord', {
                        records: dataSource || [],
                      }, // 参数
                      {responseType: 'blob'},
                    ).then((res) => {
                      if ('download' in document.createElement('a')) {
                        const url = window.URL.createObjectURL(res.data) // 创建 url 并指向 blob
                        const a = document.createElement('a')
                        a.href = url
                        a.download = '导出数据.xlsx'
                        a.click()
                        window.URL.revokeObjectURL(url) // 释放该 url
                      } else { // IE10+下载
                        navigator.msSaveBlob(res.data, '导出数据.xlsx')
                      }
                    })
                }}
               >
                导出
               </Button>
            )}
            {
              !permissionState.tableDataSource.disable && <div className={style['oa-permission-action-area']}>
                <div
                  className={`${style['import-file']} ${permissionState.tableDataSource.disable ? style['import-file-disable'] : ''
                    }`}
                >
                  <label className={css`
                  background-color:${localColor.brand} !important;
                  border:${localColor.brand} !important;
                `} htmlFor="importFile">导入</label>
                  <input
                    disabled={permissionState.tableDataSource.disable}
                    onChange={handleFileChange}
                    style={{ display: 'none' }}
                    type="file"
                    id="importFile"
                  />
                </div>
                <Button
                  disabled={permissionState.tableDataSource.disable}
                  type="primary"
                  onClick={handldeShowAddModal}
                >
                  添加
                </Button>
                <Button
                  disabled={permissionState.tableDataSource.disable}
                  type="primary"
                  onClick={handleDeleteRowData}
                >
                  删除
                </Button>
                <Button
                  disabled={permissionState.tableDataSource.disable}
                  type="primary"
                  onClick={handleFetchUserRelationSys}
                >
                  获取用户关联系统
                </Button>
              </div>
            }
          </div>
          <div className={style['template-download']}>
            <span>导入模板下载：</span>
            <Button type="link" onClick={() => openDownloadDialog(downLoadUrlAdd, 'OA账号新增_模板v2')}>
              OA账号新增_模板v2
            </Button>
            <Button type="link" onClick={() => openDownloadDialog(downLoadUrlUpdate, 'OA账号更新_模板v2')}>
              OA账号更新_模板v2
            </Button>
            <Button type="link" onClick={() => openDownloadDialog(downLoadUrlDel, 'OA账号删除_模板v2')}>
              OA账号删除_模板v2
            </Button>
          </div>
        </div>
        {permissionState.attachments.visible && (
          <div className={style['upload-file']}>
            <div className={style['upload-file-title']}>附件</div>
            <div className={style['upload-file-container']}>
              <Upload
                disabled={permissionState.attachments.disable}
                setUploadFiles={setUploadFiles}
                uploadFile={uploadFile}
                {...uploadProps}
              />
            </div>
            <div className={style['upload-file-tips']}>
              支持doc、pdf、xls、ppt、zip等类型文件，20M以内，最多可添加100个附件
            </div>
          </div>
        )}
        <div className={style['step-process']}>
          {/*  userTaskId: 11e34cc035774179981221aa09db6653 */}
          {userTaskId && <Steps userTaskId={userTaskId} />}
        </div>
        <Modal
          title={
            <div
              style={{
                fontFamily: 'SimHei',
              }}
            >
              添加用户信息
            </div>
          }
          onOk={handleModalOk}
          onCancel={closeAddModal}
          visible={showAddModal}
          className={style['oa-permission-modal']}
        >
          <div className={style['add-modal-container']}>
            <Form layout="vertical" form={addModalForm} onValuesChange={handleChildFormChange}>
              <Row gutter={24}>
                <Col span={8}>
                  <FormItem
                    label="操作类型"
                    name="operateType"
                    rules={[
                      {
                        required: true,
                        message: '操作类型为空，请填写',
                      },
                    ]}
                  >
                    <Select placeholder="请选择" allowClear>
                      {opTypes.map(j => (
                        <Option key={j.value}>{j.label}</Option>
                      ))}
                    </Select>
                  </FormItem>
                </Col>
                <Col span={8}>
                  <FormItem
                    initialValue=""
                    label="集团主账号"
                    name="groupMasterAccount"
                    rules={[
                      {
                        required: true,
                        message: '集团主账号为空，请填写',
                      },
                    ]}
                  >
                    <Input placeholder="请输入" maxLength={50} autoComplete="off" />
                  </FormItem>
                </Col>
                <Col span={8}>
                  <FormItem
                    initialValue=""
                    label="集团副账号"
                    name="groupSlaveAccount"
                    rules={[
                      {
                        required: true,
                        message: '集团副账号为空，请填写',
                      },
                    ]}
                  >
                    <Input placeholder="请输入" maxLength={50} autoComplete="off" />
                  </FormItem>
                </Col>
              </Row>
              <Row gutter={24}>
                <Col span={8}>
                  <FormItem initialValue="" label="用户4A主账号" name="userMasterAccount" rules={opTypeValue === ADD ? [{
                    required: true,
                    message: '用户4A主账号为空，请填写',
                  }] : []}>
                    <Input placeholder="请输入" maxLength={50} autoComplete="off" />
                  </FormItem>
                </Col>
                <Col span={8}>
                  <FormItem
                    initialValue=""
                    label="用户中文名"
                    name="userChineseName"
                    rules={
                      opTypeValue === ADD || opTypeValue === MODIFY
                        ? [
                          {
                            required: true,
                            message: '用户中文名为空，请填写',
                          },
                        ]
                        : []
                    }
                  >
                    <Input placeholder="请输入" maxLength={50} autoComplete="off" />
                  </FormItem>
                </Col>
                <Col span={8}>
                  <FormItem
                    initialValue=""
                    label="从账号(OA账号)"
                    name="userSlaveAccount"
                    rules={opTypeValue === ADD || opTypeValue === MODIFY ? [
                      {
                        required: true,
                        message: '从账号为空，请填写',
                      },
                    ] : []}
                  >
                    <Input placeholder="请输入" maxLength={50} autoComplete="off" />
                  </FormItem>
                </Col>
              </Row>
              <Row gutter={24}>
                <Col span={opTypeValue === MODIFY ? 6 : 8}>
                  <FormItem
                    initialValue=""
                    label="用户手机号"
                    name="userMobile"
                    rules={
                      opTypeValue === ADD || opTypeValue === MODIFY
                        ? [
                          {
                            required: true,
                            message: '用户手机号为空，请填写',
                          },
                          () => ({
                            validator(_, value) {
                              const phone = `${value}`
                              if (phone.length !== 0) {
                                if (phone.length !== 11) {
                                  return Promise.reject(new Error('手机号码必须是11位!'))
                                }
                                if (phone.replace(/\d/g, '').length !== 0) {
                                  return Promise.reject(new Error('手机号码必须是数字!'))
                                }
                              }
                              return Promise.resolve()
                            },
                          }),
                        ]
                        : [
                          () => ({
                            validator(_, value) {
                              const phone = `${value}`
                              if (phone.length !== 0) {
                                if (phone.length !== 11) {
                                  return Promise.reject(new Error('手机号码必须是11位!'))
                                }
                                if (phone.replace(/\d/g, '').length !== 0) {
                                  return Promise.reject(new Error('手机号码必须是数字!'))
                                }
                              }
                              return Promise.resolve()
                            },
                          }),
                        ]
                    }
                  >
                    <Input placeholder="请输入" autoComplete="off" />
                  </FormItem>
                </Col>
                {opTypeValue === MODIFY && (
                  <Col span={6}>
                    <FormItem
                      label="OA组织（调整后）"
                      name="newOaOrg"
                      rules={[
                        {
                          required: true,
                          message: 'OA组织（调整后）为空，请填写',
                        }
                      ]}
                    >
                      <SelectDept
                        onFocus={noop}
                        className={style['oa-permission-dept-disabled']}
                        placeholder="请选择"
                        click={(e) => handleTreeVis({ key: 'newOaOrg', isTrue: true })}
                      />
                    </FormItem>
                  </Col>
                )}
                {opTypeValue === MODIFY && (
                  <Col span={6}>
                    <FormItem
                      label="OA组织（调整前）"
                      name="oldOaOrg"
                      rules={[
                        {
                          required: true,
                          message: 'OA组织（调整前）为空，请填写',
                        },
                      ]}
                    >
                      <SelectDept
                        onFocus={noop}
                        className={style['oa-permission-dept-disabled']}
                        placeholder="请选择"
                        click={() => handleTreeVis({ key: 'oldOaOrg', isTrue: false })}
                      />
                    </FormItem>
                  </Col>
                )}
                {(opTypeValue === DELETE || opTypeValue === ADD || opTypeValue === undefined) && (
                  <Col span={8}>
                    <FormItem label="OA组织" name="newOaOrg" rules={opTypeValue !== DELETE ? [
                      {
                        required: true,
                        message: 'OA组织为空，请填写',
                      },
                    ] : []}>
                      <SelectDept
                        onFocus={noop}
                        className={style['oa-permission-dept-disabled']}
                        placeholder="请选择"
                        click={(e) => handleTreeVis({ key: 'newOaOrg', isTrue: false })}
                      />
                    </FormItem>
                  </Col>
                )}
                <Col span={opTypeValue === MODIFY ? 6 : 8}>
                  <FormItem
                    initialValue=""
                    label="用户邮箱"
                    name="userEmail"
                    rules={
                      opTypeValue === MODIFY
                        ? [
                          {
                            required: true,
                            message: '用户邮箱为空，请填写',
                          },
                          {
                            type: 'email',
                            message: '请输入正确的邮箱格式!',
                          },
                        ]
                        : [
                          {
                            type: 'email',
                            message: '请输入正确的邮箱格式!',
                          },
                        ]
                    }
                  >
                    <Input placeholder="请输入" maxLength={50} autoComplete="off" />
                  </FormItem>
                </Col>
              </Row>
              <div className={style['mark-line']} />
              <Row gutter={24}>
                <Col span={4}>
                  <FormItem initialValue="" label="姓" name="surname" rules={opTypeValue === MODIFY ? [
                    {
                      required: true,
                      message: '姓为空，请填写',
                    },
                  ] : []}>
                    <Input placeholder="请输入" maxLength={50} autoComplete="off" />
                  </FormItem>
                </Col>
                <Col span={4}>
                  <FormItem initialValue="" label="名" name="name" rules={opTypeValue === MODIFY ? [
                    {
                      required: true,
                      message: '名为空，请填写',
                    },
                  ] : []}>
                    <Input placeholder="请输入" maxLength={50} autoComplete="off" />
                  </FormItem>
                </Col>
                <Col span={8}>
                  <FormItem label="显示姓名" name="viewName" rules={opTypeValue === MODIFY ? [{
                    required: true,
                    message: '显示姓名为空，请填写',
                  }] : []}>
                    <Input placeholder="请输入" maxLength={50} autoComplete="off" />
                  </FormItem>
                </Col>
                <Col span={8}>
                  <FormItem label="性别" name="sex" rules={opTypeValue === ADD || opTypeValue === MODIFY ? [{
                    required: true,
                    message: '性别为空，请填写',
                  }] : []}>
                    <Select placeholder="请选择">
                      {sexuality.map(j => (
                        <Option key={j.value}>{j.label}</Option>
                      ))}
                    </Select>
                  </FormItem>
                </Col>
              </Row>
              <Row gutter={24}>
                <Col span={8}>
                  <FormItem
                    initialValue=""
                    label="员工编号"
                    name="userCode"
                    rules={[
                      {
                        required: true,
                        message: '员工编号为空，请填写',
                      },
                    ]}
                  >
                    <Input placeholder="请输入" maxLength={50} autoComplete="off" />
                  </FormItem>
                </Col>
                <Col span={8}>
                  <FormItem label="员工卡号" name="userCardCode">
                    <Input placeholder="请输入" maxLength={50} autoComplete="off" />
                  </FormItem>
                </Col>
                <Col span={8}>
                  <FormItem label="工作职责" name="jobDesc" rules={opTypeValue === ADD ? [{
                    required: true,
                    message: '工作职责为空，请填写',
                  }] : []}>
                    <Select placeholder="请选择">
                      {workDuty.map(j => (
                        <Option key={j.value}>{j.label}</Option>
                      ))}
                    </Select>
                  </FormItem>
                </Col>
              </Row>
              <Row gutter={24}>
                <Col span={8}>
                  <FormItem label="用户类型" name="userType" rules={opTypeValue === ADD ? [{
                    required: true,
                    message: '用户类型为空，请填写',
                  }] : []}>
                    <Select placeholder="请选择">
                      {userTypes.map(j => (
                        <Option key={j.value}>{j.label}</Option>
                      ))}
                    </Select>
                  </FormItem>
                </Col>
                <Col span={8}>
                  <FormItem initialValue="" label="部门排位" name="deptRank">
                    <Input placeholder="请输入" maxLength={50} autoComplete="off" />
                  </FormItem>
                </Col>
                <Col span={8}>
                  <FormItem label="职务" name="jobTitle" rules={opTypeValue === ADD ? [{
                    required: true,
                    message: '职务为空，请填写',
                  }] : []}>
                    <Input placeholder="请输入" maxLength={50} autoComplete="off" />
                  </FormItem>
                </Col>
              </Row>
              <Row gutter={24}>
                <Col span={8}>
                  <FormItem
                    label="家庭电话"
                    name="homePhone"
                    initialValue=""
                    rules={[
                      () => ({
                        validator(_, value) {
                          const phone = `${value}`
                          if (phone.length !== 0) {
                            if (phone.replace(/\d/g, '').length !== 0) {
                              return Promise.reject(new Error('家庭电话必须是数字!'))
                            }
                          }
                          return Promise.resolve()
                        },
                      }),
                    ]}
                  >
                    <Input placeholder="请输入" maxLength={50} autoComplete="off" />
                  </FormItem>
                </Col>
                <Col span={8}>
                  <FormItem
                    initialValue=""
                    label="公司电话"
                    name="companyPhone"
                    rules={[
                      () => ({
                        validator(_, value) {
                          const phone = `${value}`
                          if (phone.replace(/\d/g, '').length !== 0) {
                            return Promise.reject(new Error('公司电话必须是数字!'))
                          }
                          return Promise.resolve()
                        },
                      }),
                    ]}
                  >
                    <Input placeholder="请输入" maxLength={50} autoComplete="off" />
                  </FormItem>
                </Col>
                <Col span={8}>
                  <FormItem label="身份证号码" name="idCard">
                    <Input placeholder="请输入" maxLength={18} autoComplete="off" />
                  </FormItem>
                </Col>
              </Row>
              <Row gutter={24}>
                <Col span={8}>
                  <FormItem label="职位" name="position" rules={opTypeValue === MODIFY ? [{
                    required: true,
                    message: '职位为空，请填写',
                  }] : []}>
                    <Input placeholder="请输入" maxLength={50} autoComplete="off" />
                  </FormItem>
                </Col>
                <Col span={8}>
                  <FormItem initialValue="" label="参加工作时间" name="workTime">
                    <DatePicker allowClear />
                  </FormItem>
                </Col>
                <Col span={8}>
                  <FormItem label="加入移动时间" name="joinChinaMobileTime">
                    <DatePicker allowClear />
                  </FormItem>
                </Col>
              </Row>
            </Form>
          </div>
        </Modal>
        {
          treeVis && <XmTree
            {...getParam()}
          />
        }
        {
          showUploadMessage && <UploadMessage closeImportMessageModal={closeImportMessageModal} showUploadMessage={showUploadMessage} data={uploadList} />
        }
      </div>
      <Buttons
        procKey={procKey}
        appId={appId}
        userTaskId={userTaskId}
        onMount={onProcessSdkMount(handleMount)}
        onSubmitOpen={async () => {
          try {
            let values = await mainForm.validateFields()
            values = patchData(values, initValue)
            const { dept } = values
            if (uploadFilesRef.current.length > 100) {
              message.error('附件最多只能上传100个，您已超出上传上限')
              throw new Error('附件最多只能上传100个，您已超出上传上限')
            }
            const deptName = procFormDataKey ? values.deptName : (deptList.find(j => j.id === dept) || {}).deptPath || ''
            const data = {
              ...values,
              tableDataSource: dataSource,
              attachments: uploadFilesRef.current,
              deptName,
              code: codeNumber,
            }
            console.log(data)
            return data
          } catch (e) {
            // message.error('请检查表单!')
            throw e
          }
        }}
        onSubmit={handleSubmit}
        extraButtons={(isStart || !userTaskId ? actionsButton : []).concat(ableEditBtn)}
      />
    </Spin>
  )
}

OaPermission.displayName = 'OaPermission'

export default OaPermission
