.oa-permission-container {
    .oa-permission-title {
        font-size: 30px;
        text-align: center;
        color: #2f81f0;
        padding-bottom: 60px;
    }
    .oa-permission-text-code {
        text-align: right;
        padding: 20px 0;
        color: #71a0e8;
    }
    .oa-permission-op-list-container {
        .oa-permission-op-list-title {
            display: flex;
            align-items: center;
            & > span:nth-child(2) {
                color: #f94848;
            }
        }
        .oa-permission-action-area {
            display: flex;
            align-items: center;
            margin-top: 14px;
            .import-file {
                label {
                    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
                    background-color: #1a90ff;
                    display: inline-block;
                    padding: 4px 15.9px;
                    letter-spacing: 2px;
                    color: white;
                    cursor: pointer;
                    border: .5px solid #40a9ff;
                }
                label:hover {
                    background-color: #40a9ff;
                }
            }

            .import-file-disable {
                label {
                    background-color: #f5f5f5 !important;
                    cursor: not-allowed;
                    color: rgba(0,0,0,.25);
                    border: 1px solid #d9d9d9;
                }
                label:hover {
                    background-color: #f5f5f5 !important;
                }
            }

            button {
                margin-left: 6px;
            }
        }
        .template-download {
            margin-top: 14px;
        }
    }
    .upload-file {
        margin-top: 12px;
        margin-bottom: 12px;
        .upload-file-title {
            padding-bottom: 12px;
        }
        .upload-file-container {
            // width: 20%;
        }
        .upload-file-tips {
            margin-top: 8px;
            font-size: 13px;
            color: #8d8d8d;
        }
    }
}

.oa-permission-modal {
    width: 780px !important;
    .mark-line {
        height: 1px;
        background-color: #e3e3e3;
        margin-bottom: 10px;
        margin-top: 16px;
    }
    div[class="ant-modal-body"] {
        padding-top: 10px;
    }
    div[class="ant-col ant-form-item-label"] {
        padding-bottom: 4px;
    }
    div[class="ant-picker"] {
        width: 100%;
    }
    div[class="ant-picker ant-picker-focused"] {
        width: 100%;
    }
    div[class="ant-picker ant-picker-status-success"] {
        width: 100%;
    }
    div[class="ant-picker ant-picker-status-success ant-picker-focused"] {
        width: 100%;
    }
}
