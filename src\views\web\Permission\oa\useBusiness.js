/* eslint-disable no-lonely-if */
import { useRef, useState, useEffect, useMemo, useLayoutEffect } from 'react'
import { message, Modal } from 'antd'
import moment from 'moment'
import Cookies from 'js-cookie'
import _, { conforms } from 'lodash'
import request from 'ROOT/service/request'
import service from 'ROOT/service'
import { getQueryString } from 'ROOT/utils'
import { UPLOAD_URL } from 'ROOT/constants'
import {
  columns,
  mockDataSource,
  maxFilesNumber,
  userRelationSystem,
  formChineseName,
  formEnglishName,
  fieldConfig,
} from './constants'
import { patchData, emergencyLevel } from '../../../../utils'

const initialFiles = []
const uploadUrl = '/security-manage-platform/web/oa/auth/upload'
const userSysUrl = '/security-manage-platform/web/oa/auth/getSystem'

export const useInitialForm = procFormDataKey => {
  const procFormDataKeyRef = useRef(procFormDataKey)
  useEffect(() => {
    if (!procFormDataKey) {
      service
        .saveForm({
          type: 33,
          classify: {
            name: '统一信息平台新增用户和权限更改审批表',
            englishName: '统一信息平台新增用户和权限更改审批表',
          },
          config: fieldConfig,
          data: {
            _taskLevel: '3',
            isFeedback: '2',
            draftDate: moment().format('YYYY年MM月DD日'),
          },
        })
        .then(res => {
          const { data } = res
          procFormDataKeyRef.current = data
        })
    }
  }, [procFormDataKey])
  return {
    procFormDataKeyRef,
  }
}

const makeSource = dataSource => {
  return (dataSource || []).map((j, index) => ({
    ...j,
    key: index,
  }))
}

export const useLoading = () => {
  const [loading, setLoading] = useState(false)
  return {
    loading,
    setLoading,
  }
}

// 附件
export const useUpload = () => {
  const [uploadFile, setUploadFiles] = useState(initialFiles)
  const [disableUpload, setDisable] = useState(false)
  const uploadFilesRef = useRef(uploadFile)
  uploadFilesRef.current = uploadFile

  const uploadProps = {
    name: 'file',
    action: UPLOAD_URL,
    onChange: info => {
      const { fileList } = info
      const makedFileList = (fileList || []).map(j => ({
        ...j,
        fileUrl: (j.response || {}).fileUrl || '',
      }))
      setUploadFiles(makedFileList)
      if (makedFileList.filter(j => j.status === 'done').length === maxFilesNumber) {
        setDisable(true)
      }
    },
    onRemove: file => {
      if (file.fileUrl) {
        setDisable(false)
        setUploadFiles(j => j.filter(j => j.fileUrl !== file.fileUrl))
      }
    },
    fileList: uploadFile,
    showUploadList: {
      showRemoveIcon: true,
      showDownloadIcon: true,
    },
    onDownload: file => {
      if ((file.response || {}).fileUrl) {
        const a = document.createElement('a')
        a.href = (file.response || {}).fileUrl
        document.body.appendChild(a)
        a.download = (file.response || {}).name
        a.click()
        document.body.removeChild(a)
      }
    },
  }

  return {
    uploadProps,
    uploadFilesRef,
    disableUpload,
    setUploadFiles,
    uploadFile,
  }
}

// 导入
export const useImport = (setLoading, setTableData) => {
  const [showUploadMessage, setShowUploadMessage] = useState(false)
  const [uploadList, setUploadList] = useState([])

  const closeImportMessageModal = evt => {
    setUploadList([])
    setShowUploadMessage(false)
    setTableData(prev => {
      return {
        ...prev,
        dataSource: makeSource([...(prev.dataSource || []), ...(evt || [])]),
      }
    })
  }

  const handleFileChange = evt => {
    evt.persist()
    const { target } = evt
    const file = target.files[0]
    const formData = new FormData()
    formData.append('file', file)
    setLoading(true)
    request
      .post(uploadUrl, formData, {
        'Content-Type': 'multipart/form-data',
      })
      .then(res => {
        const { data } = res
        setShowUploadMessage(true)
        setUploadList(data)
      })
      .catch(() => {
        message.error('导入失败!')
      })
      .finally(() => {
        setLoading(false)
        target.value = null
      })
  }
  return {
    handleFileChange,
    showUploadMessage,
    uploadList,
    closeImportMessageModal
  }
}

const rowSelection = {
  type: 'checkbox',
  getCheckboxProps: record => ({
    name: record.action,
  }),
}

// 表格
export const useTable = disable => {
  const [tableData, setTableData] = useState({ columns, dataSource: makeSource(mockDataSource) })

  const rowSelectionRef = useRef(rowSelection)
  const selectedRowsRef = useRef([])
  rowSelectionRef.current.onChange = (_, selectedRows) => {
    selectedRowsRef.current = selectedRows
  }

  rowSelectionRef.current.getCheckboxProps = record => ({
    name: record.action,
    disabled: disable,
  })

  const handleDeleteRowData = () => {
    if (selectedRowsRef.current.length === 0) return message.warn('请先选择!')
    Modal.confirm({
      title: '您确定要删除勾选数据吗，确定则删除勾选数据，否则退出弹窗',
      onOk: () => {
        setTableData(prev => {
          const { dataSource } = prev
          return {
            ...prev,
            dataSource: dataSource.filter(j => selectedRowsRef.current.every(k => j.key !== k.key)),
          }
        })
      },
    })
  }

  return {
    tableData,
    setTableData,
    rowSelectionRef,
    selectedRowsRef,
    handleDeleteRowData,
  }
}

// 添加
export const useAddModal = (modalForm, setTableData) => {
  const [showAddModal, setShowAddModal] = useState(false)
  const handldeShowAddModal = () => {
    setShowAddModal(true)
  }
  const closeAddModal = () => {
    setShowAddModal(false)
  }
  const handleModalOk = () => {
    modalForm
      .validateFields()
      .then(res => {
        let { joinChinaMobileTime, workTime } = res
        if (joinChinaMobileTime) {
          joinChinaMobileTime = joinChinaMobileTime.format('YYYY-MM-DD')
        }
        if (workTime) {
          workTime = workTime.format('YYYY-MM-DD')
        }
        setTableData(prev => {
          const { dataSource } = prev
          return {
            ...prev,
            dataSource: [
              ...dataSource,
              { ...res, joinChinaMobileTime, workTime, key: dataSource.length },
            ],
          }
        })
        setShowAddModal(false)
      })
      .catch(() => {
        message.error('请检查表单!')
      })
  }
  return {
    showAddModal,
    closeAddModal,
    handldeShowAddModal,
    handleModalOk,
  }
}

const adapterValue = (val, type) =>
  Array.isArray(val)
    ? val.map(item => ({
      id: +(item.departmentId || item.id),
      name: item.name,
      type: type,
      code: item.code,
    }))
    : val

// 选择部门
export const useOaOrg = modalForm => {
  const [treeVis, setTreeVis] = useState(false)
  const [depts, setDepts] = useState([])
  const [isOrgId, setIsOrgId] = useState(false)
  const keyRef = useRef('')

  const handleTreeVis = props => {
    const { key, isTrue } = props
    keyRef.current = key
    setIsOrgId(isTrue)
    setTreeVis(true)
    // setDepts(modalForm.getFieldValue(key) ? modalForm.getFieldValue(key) : [])
  }
  const closeTree = () => {
    keyRef.current = ''
    setTreeVis(false)
  }

  const handleTreeConfirm = treeVal => {
    console.log(treeVal, 'treeVal')
    const { deptList, orgList } = treeVal
    const newDepts = !deptList.length ? adapterValue(orgList || [], 'ORG') : adapterValue(deptList || [], "DEPT")
    console.log(newDepts, 'newDepts')
    setDepts(newDepts)
    modalForm.setFieldsValue({
      [keyRef.current]: !deptList.length ? (orgList || []).map(j => j.code).join('') : (deptList || []).map(j => j.code).join(''),
    })
    setTreeVis(false)
    keyRef.current = ''
  }

  const handleFormValueChange = valueObj => {
    if (valueObj.oaOrg !== undefined) {
      const value = valueObj.oaOrg || []
      setDepts(depts.filter(j => value.indexOf(j.name) >= 0))
    }
  }

  return {
    depts,
    treeVis,
    isOrgId,
    handleTreeVis,
    closeTree,
    handleTreeConfirm,
    handleFormValueChange,
  }
}

export const useLoginOrgType = (list, orgId) => {
  let loginOrgType = ''
  if (list && orgId) {
    let a = Object.keys(list).filter(data => {
      return list[data].includes(Number(orgId))
    })
    switch (a.join()) {
      case 'areaOrgIds': loginOrgType = 'area';
        break;
      case 'cityOrgIds': loginOrgType = 'city';
        break;
      case 'otherOrgIds': loginOrgType = 'other';
    }
  }
  return loginOrgType
}

export const useCodeNumber = () => {
  const [codeNumber, setCodeNumber] = useState('')
  return {
    codeNumber,
    setCodeNumber,
  }
}

// 获取用户关联系统
export const useFetchUserRelationSys = (setLoading, tableData, setTableData) => {
  const handleFetchUserRelationSys = () => {
    setLoading(true)
    const { dataSource } = tableData
    request
      .post(
        userSysUrl,
        (dataSource || []).map(j => j.userSlaveAccount),
      )
      .then(res => {
        const { data } = res
        if (data && !_.isEmpty(data))
          setTableData(prev => {
            const { columns, dataSource } = prev
            const makedDataSource = makeSource(
              (dataSource || []).map(j => {
                const { userSlaveAccount } = j
                return {
                  ...j,
                  system: data[userSlaveAccount] || '',
                }
              }),
            )
            if (columns.indexOf(userRelationSystem) >= 0) {
              return {
                ...prev,
                dataSource: makedDataSource,
              }
            }
            return {
              columns: [...columns, userRelationSystem],
              dataSource: makedDataSource,
            }
          })
        else message('暂无关联系统')
      })
      .catch(() => {
        message.error('获取关联系统出错!')
      })
      .finally(() => {
        setLoading(false)
      })
  }

  return {
    handleFetchUserRelationSys,
  }
}

// 表单字段权限
const acccesssControll = {
  attachments: {
    disable: true,
    visible: true,
  },
  content: {
    disable: true,
    visible: true,
  },
  tableDataSource: {
    disable: true,
    visible: true,
  },
  time: {
    disable: true,
    visible: true,
  },
  title: {
    disable: true,
    visible: true,
  },
  user: {
    disable: true,
    visible: true,
  },
  dept: {
    disable: true,
    visible: true,
  },
}

// 权限控制
export const useAccessControll = (debug) => {
  const [permissionState, setPermission] = useState(acccesssControll)
  console.log(debug, 'debug')
  const [isStart, setIsStart] = useState(false)
  const onProcessSdkMount =
    handleMount =>
      ({ access, editMode, draft, process }) => {
        const { nodeName, taskStatus } = process || {}
        if (nodeName === '开始节点' && taskStatus === 'running') {
          setIsStart(true)
        }
        handleMount(draft)
        if (debug) {

          Object.keys(acccesssControll).forEach(key => {
            setPermission(prev => {
              if (key === 'dept' || key === 'user') {
              } else {
                prev[key].disable = false
              }
              return { ...prev }
            })
          })
        } else {
          if (access && Object.keys(access).length > 0 && editMode && editMode.includes('web')) {
            console.log(access, 'access')
            Object.keys(access).forEach(key => {
              setPermission(prev => {
                if (prev[key]) {
                  switch (access[key]) {
                    case 'NONE':
                      prev[key].visible = false
                      break
                    case 'READ':
                      prev[key].disable = true
                      break
                    case 'WRITE':
                      prev[key].visible = true
                      prev[key].disable = false
                      break
                    default:
                      prev[key].disable = true
                  }
                }
                return { ...prev }
              })
            })
          }
        }
      }
  return {
    onProcessSdkMount,
    permissionState,
    isStart,
  }
}

const actionsButton = [
  {
    name: '保存退出',
    async: false,
    onClick: () => { },
  },
  {
    name: '保存',
    async: false,
    onClick: () => { },
  },
]

export const useSubmit = (
  mainForm,
  procFormDataKey,
  uploadFilesRef,
  dataSource,
  setLoading,
  deptList,
  procFormDataKeyRef,
  appId,
  codeNumber,
  initValue,
) => {
  // 发起流程删除草稿
  const finishProcess = async cb => {
    const info = localStorage.getItem('userInfo')
      ? JSON.parse(localStorage.getItem('userInfo'))
      : {}
    const res = await service.deleteDraft({
      appId,
      appTasks: [
        {
          appTaskId: `${procFormDataKeyRef.current}`,
          handlerIds: [info.loginUid],
        },
      ],
    })
    if (res.success) {
      setTimeout(() => {
        if (cb) {
          cb()
        } else {
          history.back()
        }
      }, 1000)
    }
  }
  useEffect(() => {
    window.addEventListener('process', e => {
      const { type } = e.detail || {}
      if (type === 'started') {
        if (appId) {
          finishProcess()
        } else {
          history.back()
        }
      } else {
        setTimeout(() => {
          try {
            window.close()
          } finally {
            history.back()
          }
        }, 1000)
      }
    })
  }, [])
  const handleSubmit = async () => {
    // setLoading(true)
    try {
      let values = await mainForm.validateFields()
      values = patchData(values, initValue)
      const { dept } = values
      const deptName = procFormDataKey ? values.deptName : (deptList.find(j => j.id === dept) || {}).deptPath || ''
      const data = {
        ...values,
        tableDataSource: dataSource,
        attachments: uploadFilesRef.current,
        reportId: Number(procFormDataKey),
        deptName,
        code: codeNumber,
      }

      let res
      // 流程已启动
      if (procFormDataKey) {
        res = await service.upDateForm({
          config: fieldConfig,
          data,
          reportId: procFormDataKey,
        })
      } else {
        // 起草阶段
        res = await service.saveForm({
          type: 33,
          config: fieldConfig,
          classify: {
            name: formChineseName,
            englishName: formEnglishName,
          },
          data,
        })
      }
      if (res.success) {
        setLoading(false)
        message.success('操作成功')
        if (procFormDataKey) return Number(procFormDataKey)
        return Number(res.data)
      }
    } catch (e) {
      setLoading(false)
      if (Array.isArray(e.errorFields)) {
        message.error('请检查表单!')
      } else {
        message.error('保存表单出错!')
      }
      return new Error(e.message || '保存表单出错!')
    }
  }

  const handleSaveDraft = flag => {
    return async () => {
      try {
        let values = await mainForm.validateFields()
        values = patchData(values, initValue)
        setLoading(true)
        const { dept } = values
        const deptName = (deptList.find(j => j.id === dept) || {}).deptPath || ''
        const res = await service.upDateForm({
          config: fieldConfig,
          data: {
            ...values,
            tableDataSource: dataSource,
            attachments: uploadFilesRef.current,
            deptName,
            code: codeNumber,
          },
          reportId: procFormDataKeyRef.current,
        })
        // 退回的不需要保存草稿箱
        if (!appId) {
          if (res.success) {
            message.success('操作成功')
          }
          if (flag) {
            setTimeout(() => {
              history.back()
            }, 500)
          }
          return
        }
        if (res.success) {
          const info = localStorage.getItem('userInfo')
            ? JSON.parse(localStorage.getItem('userInfo'))
            : {}
          const draftRes = await service.saveDraft({
            appId,
            appTasks: [
              {
                appTaskId: `${procFormDataKeyRef.current}`,
                businessType: 1, // 审批类
                handleEntry: [
                  {
                    handleType: 0, // 草稿
                    handlerId: info.loginUid,
                  },
                ],
                emergencyLevel: emergencyLevel['3'],
                processType: '统一信息平台新增用户和权限更改审批表',
                sponsorId: info.loginUid,
                jumpToDetail: 1,
                title: '统一信息平台新增用户和权限更改审批表',
                detailUrl: decodeURIComponent(
                  procFormDataKey
                    ? `${location.href
                      .replace('/extra-forms/', '/extra-forms-h5/')
                      .replace(/backurl=.*?&|backurl=.*?$/, '')}`
                    : `${location.href
                      .replace('/extra-forms/', '/extra-forms-h5/')
                      .replace(/backurl=.*?&|backurl=.*?$/, '')}&procFormDataKey=${procFormDataKeyRef.current
                    }`,
                ),
                webDetailUrl: decodeURIComponent(
                  procFormDataKey
                    ? `${location.href.replace(/backurl=.*?&|backurl=.*?$/, '')}`
                    : `${location.href.replace(/backurl=.*?&|backurl=.*?$/, '')}&procFormDataKey=${procFormDataKeyRef.current
                    }`,
                ),
              },
            ],
          })
          if (draftRes.success) {
            message.success('操作成功')
            // 保存退出
            if (flag) {
              setTimeout(() => {
                history.back() // show msg
              }, 500)
            }
          }
        }
      } catch (e) {
        if (Array.isArray(e.errorFields)) {
          message.error('请检查表单!')
        } else {
          message.error('保存临时数据出错!')
        }
      } finally {
        setLoading(false)
      }
    }
  }

  actionsButton[0].onClick = handleSaveDraft(true)

  actionsButton[1].onClick = handleSaveDraft(false)

  return {
    handleSubmit,
    actionsButton,
  }
}

export const useFetchData = (
  procFormDataKey,
  mainForm,
  setLoading,
  setUploadFiles,
  setTableData,
  setCodeNumber,
  setInitValue,
) => {
  return function handleMount(draft) {
    if (procFormDataKey) {
      // 获取审批数据
      setLoading(true)
      service
        .getFormData({
          reportId: procFormDataKey,
        })
        .then(res => {
          if (res && res.data) {
            const { data } = res.data
            const makedData = { ...data, ...(draft || {}) }
            setUploadFiles(makedData.attachments || [])
            setCodeNumber(data.code || '')
            setInitValue(data)
            setTableData(prev => ({
              ...prev,
              dataSource: makedData.tableDataSource || [],
            }))
            delete makedData.attachments
            delete makedData.tableDataSource
            mainForm.setFieldsValue(makedData)
          }
        })
        .catch(() => {
          message.error('初始化表单数据出错!')
        })
        .finally(() => {
          setLoading(false)
        })
    } else {
      console.log(draft, 'draft')
    }
  }
}

export const useCurrentTime = () => moment().format('YYYY年MM月DD日')

export const useParams = props => {
  return useMemo(() => getQueryString(props.location.search), [props])
}

const iniitalDeptList = []

export const useInfo = (userTaskId, mainForm) => {
  const [stateDeptList, setStateDeptList] = useState(iniitalDeptList)
  useLayoutEffect(() => {
    const myself = Cookies.getJSON('myself') || {}
    console.log(myself, 'myself')
    let { deptList } = myself
    deptList = deptList || []
    const { loginName, orgList = [] } = myself

    if (!userTaskId)
      mainForm.setFieldsValue({
        user: loginName,
      })
    // 部门如果为空则取orgList
    deptList =
      deptList.length !== 0
        ? deptList.map(j => ({
          ...j,
          id: `${j.id}`,
        }))
        : orgList.map(j => ({
          ...j,
          id: `${j.id}`,
          deptPath: j.name,
        }))
    mainForm.setFieldsValue({
      dept: (deptList[0] || {}).id,
    })
    setStateDeptList(deptList)
  }, [])

  return {
    deptList: stateDeptList,
  }
}

export const useChildFormChange = () => {
  const [opType, setOpType] = useState('')
  const handleChildFormChange = val => {
    if (val.operateType) {
      setOpType(val.operateType)
    }
  }

  return {
    opType,
    handleChildFormChange,
  }
}

export default {
  useUpload,
  useImport,
  useAddModal,
  useOaOrg,
  useCodeNumber,
  useSubmit,
  useFetchUserRelationSys,
  useFetchData,
  useAccessControll,
  useCurrentTime,
  useParams,
  useInfo,
  useChildFormChange,
  useInitialForm,
}