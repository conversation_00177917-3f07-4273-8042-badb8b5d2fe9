import React, { useCallback, useEffect, useState } from 'react'
import moment from 'moment'
import {
  SchemaMarkupForm,
  FormEffectHooks,
  createFormActions,
  createAsyncFormActions,
} from '@formily/antd'
import {
  FormMegaLayout,
  Input,
  DatePicker,
  Select,
  Radio,
} from '@formily/antd-components'
import service from 'ROOT/service'
import SelectOrg from 'ROOT/components/Formily/Select'
import SelectCityOrg from 'ROOT/components/Formily/SelectCityOrg'
import WpsEditor from 'ROOT/components/Formily/WpsRepoort'
import Dept from 'ROOT/components/Formily/Dept'
import PersonalInfo from 'ROOT/components/Formily/PersonalInfo'
import Editable from 'ROOT/components/Formily/Editable'
import MainText from 'ROOT/components/MainText'
import { getHtmlCode } from 'ROOT/utils'
import Upload from '../component/Upload'
import Schema from './schema'

const { onFieldValueChange$, onFieldInputChange$ } = FormEffectHooks
export const actions = createAsyncFormActions()

export default props => {
  const { signer, initValue, editable, extraBody } = props
  const [title, setTitle] = useState('')
  const [content, setContent] = useState('')

  useEffect(() => {
    document.title = '报表收集审批单'
    if (document.getElementById('breadnav')) {
      document.getElementById('breadnav').innerHTML = '报表收集审批单'
    }
  }, [])

  const effects = useCallback(() => {
    const { setFieldState } = createFormActions()
    onFieldInputChange$('cycleType').subscribe(({ value }) => {
      setFieldState('*(cycleDay)', state => {
        state.value = (value === 1) ? '' : 1
      })
    })
    onFieldValueChange$('cycleType').subscribe(({ value }) => {
      
      setFieldState('*(feedbackOverTime)', state => {
        state.rules = (value !== 1) ? [{
          validator: async (val) => {
            const res = await actions.getFormState()
            if (!val) return '反馈结束时间必填'
            if (!res.values.feedbackStartTime || !val) return
            return moment(res.values.feedbackStartTime).diff(moment(val)) <= 0 ? '' : '反馈结束时间不能小于反馈开始时间'
          },
        }] : []
      })
      setFieldState('*(feedbackStartTime)', state => {
        state.rules = (value !== 1) ? [{
          validator: async (val) => {
            const res = await actions.getFormState()
            if (!val) return '反馈开始时间必填'
            if (!res.values.feedbackOverTime || !val) return
            return moment(res.values.feedbackOverTime).diff(moment(val)) >= 0 ? '' : '反馈结束时间不能小于反馈开始时间'
          },
        }] : []
      })
    })
    
    onFieldValueChange$('title').subscribe(({ value }) => {
      setTitle(value)
    })
    onFieldValueChange$('fileList').subscribe(({ value }) => {
      if (value && value.length > 0) {
        if (value[0].needConvert) {
          // 'sfs/srvfile?digest=fidaa24490aa709410799fbde955e053725&filename=新建文档.html'
          getHtmlCode(value[0].url, (html) => {
            setContent(html)
          })
        } else {
          setContent(value[0].html)
        }
      }
    })
  }, [])

  useEffect(() => {
    if (Object.keys(initValue).length > 0) {
      actions.setFormState(state => {
        state.values = signer ? { ...initValue, signer } : initValue
      })
    }
  }, [initValue])

  useEffect(() => {
    // 判断是否有签发人
    if (signer) {
      actions.setFieldState('*(issuer)', state => {
        state.value = signer || '-'
      })
    }
  }, [initValue.signer, signer])

  WpsEditor.isFieldComponent = true
  SelectOrg.isFieldComponent = true
  SelectCityOrg.isFieldComponent = true
  PersonalInfo.isFieldComponent = true
  Dept.isFieldComponent = true
  Upload.isFieldComponent = true
  Editable.isFieldComponent = true

  return (
    <div>
      <p className="form-title">报表收集审批单</p>
      <SchemaMarkupForm
        schema={Schema(initValue)}
        actions={actions}
        components={{
          DatePicker,
          Input,
          FormMegaLayout,
          Select,
          TextArea: Input.TextArea,
          Upload,
          WpsEditor,
          SelectOrg,
          SelectCityOrg,
          Radio,
          RadioGroup: Radio.Group,
          PersonalInfo,
          Dept,
          Editable,
        }}
        editable={editable}
        previewPlaceholder="无"
        effects={effects}
        expressionScope={{
          labelAlign: editable ? 'top' : 'left',
        }}
      />
      {extraBody}
      <MainText title={title} content={content} />
    </div>
  )
}
