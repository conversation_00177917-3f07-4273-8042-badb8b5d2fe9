import React, { useEffect, useMemo, useRef, useState } from 'react'
import ReactDOM from 'react-dom'
import moment from 'moment'
import {
  Button,
  Space,
  message,
  Modal,
} from 'antd'
import { getQueryString, closeTheWindow, emergencyLevel } from 'ROOT/utils'
import service from 'ROOT/service'
import { getWpsUrl } from 'ROOT/utils/wps'
import { Buttons } from 'ROOT/components/Process'
import useMyInfo from 'ROOT/hooks/useMyInfo'
import { isEmpty } from 'lodash'
import Cookies from 'js-cookie'
import Form, { actions } from './form'
import { formConfig, letterType } from '../module/config'
import Dispatch from '../dialog/dispatch'

const { confirm } = Modal
const typeIndex = 0

export default props => {
  const { procKey, appId, procFormDataKey } = useMemo(() => getQueryString(props.location.search), [props.location.search])
  const [initValue, setInitValue] = useState({})
  const [formData, setFormData] = useState({})
  const [showDispatchBtn, setShowDispatchBtn] = useState(false)
  const formId = useRef(+procFormDataKey)
  const myInfo = useMyInfo({ isSetStorage: true })

  const resetUserInfo = async () => {
    const { loginName, loginMobile, linkPath, loginOrgName } = myInfo
    actions.setFieldState('createUserName', state => {
      state.value = loginName
    })
    actions.setFieldState('mobile', state => {
      state.value = loginMobile
    })
    actions.setFieldState('draftDepartment', state => {
      state.props.enum = linkPath && linkPath.length > 0 ? linkPath.map(item => ({
        value: item.deptId,
        label: item.linkPath,
      })) : []
      if (initValue.draftDepartment && initValue.draftDepartment.value) {
        state.value = initValue.draftDepartment
      } else {
        state.value = linkPath && linkPath.length > 0 ? {
          value: linkPath[0].deptId,
          label: linkPath[0].linkPath,
        } : { value: '', label: '' }
      }
    })
    if (!procFormDataKey) {
      const template = await service.getReportSerialTemplate({ key: loginOrgName })
      actions.setFieldState('*(fileSerial)', state => {
        state.value = template
      })
    }
  }

  useEffect(() => {
    if (Object.keys(myInfo).length > 0) {
      resetUserInfo()
    }
  }, [myInfo])

  useEffect(async() => {
    if (procFormDataKey) {
      const wpsContent = await getWpsUrl(procFormDataKey)
      service.getReportDetail({
        reportCollectId: procFormDataKey,
      }).then(res => {
        if (res.success && res.data) {
          res.data.fileList = JSON.parse(res.data.fileList || '[]')
          if (wpsContent) {
            res.data.fileList[0] = wpsContent
          }
          res.data.mainDelivery = JSON.parse(res.data.mainDeliveryElement || '[]')
          if (res.data.cycleType !== 1){
            res.data.cycleDay = parseInt(res.data.cycleDay, 10)
          }
          setFormData(res.data)
          resetContentProps(procFormDataKey)
        }
      })
    }
  }, [])

  useEffect(() => {
    if (procFormDataKey) {
      const { loginName, loginMobile, linkPath } = myInfo
      setInitValue({
        createUserName: loginName,
        mobile: loginMobile,
        draftDepartment: linkPath && linkPath.length > 0 ? {
          value: linkPath[0].deptId,
          label: linkPath[0].linkPath,
        } : { value: '', label: '' },
        ...formData,
      })
    }
  }, [formData, myInfo])
 

  const resetContentProps = (procFormDataKey) => {
    actions.setFieldState('fileList', (state) => {  
      state.props['x-component-props'] = {
        ...state.props['x-component-props'],
        reportId: procFormDataKey,
        type: letterType[typeIndex].type,
        config: formConfig,
      }
    })
  }

  useEffect(() => {
    service.getUserLabel().then(res => {
      if (res.success) {
        console.log(res.data,letterType[typeIndex].zw0, ((res.data.includes(letterType[typeIndex].zw0) || res.data.includes(letterType[typeIndex].zw1) || res.data.includes(letterType[typeIndex].zw2))))
        setShowDispatchBtn(!!((res.data.includes(letterType[typeIndex].zw0) || res.data.includes(letterType[typeIndex].zw1) || res.data.includes(letterType[typeIndex].zw2))))
      }
    })
  }, [])

  const commonCommit = async () => {
    const data = await actions.submit()
    console.log(data.values)
    const params = {
      type: 0, // 省：0,市县：1
      ...data.values,
      fileList: JSON.stringify((data.values && data.values.fileList) || []),
      mainDeliveryElement: JSON.stringify((data.values && data.values.mainDelivery) || []),
      mainDelivery: data.values && data.values.mainDelivery.map(item => ({
        deptId: item.code,
        deptName: item.showPath,
      })),
      orgId: Cookies.get('orgId'),
    }
    if (formId.current) {
      params.id = formId.current
    }
    const res = await service.reportSave(params)
    if (res.success) {
      formId.current = res.data
    }
    return res
  }

  const submitForm = async () => {
    const res = await commonCommit()
    // const data = await actions.submit()
    console.log(res)
    if (res.success) {
      // return { id: +formId.current, values: data.values }
      return res.data
    }
    throw new Error('保存出错')
  }

  useEffect(() => {
    window.addEventListener('process', () => finishSubmit())
  }, [])

  const finishSubmit = async () => {
    deleteDraft(() => {
      closeTheWindow(props.location.search)
    })
  }


  const saveDraft = async (callback) => {
    const data = await actions.submit()
    const result = data.values
    const info = localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo')) : {}
    const res = await service.saveDraft({
      appId,
      appTasks: [{
        appTaskId: formId.current.toString(),
        businessType: 2,
        emergencyLevel: emergencyLevel[+result._taskLevel],
        handleEntry: [{
          handleType: 0, // 草稿
          handlerId: info.loginUid,
        }],
        processType: letterType[typeIndex].processType,
        sponsorId: info.loginUid,
        jumpToDetail: 1,
        title: result.title,
        detailUrl: decodeURIComponent(procFormDataKey ? `${location.href.replace('/extra-forms/', '/extra-forms-h5/').replace(/backurl=.*?&|backurl=.*?$/, '')}` : `${location.href.replace('/extra-forms/', '/extra-forms-h5/').replace(/backurl=.*?&|backurl=.*?$/, '')}&procFormDataKey=${formId.current}`),
        webDetailUrl: decodeURIComponent(procFormDataKey ? `${location.href.replace(/backurl=.*?&|backurl=.*?$/, '')}` : `${location.href.replace(/backurl=.*?&|backurl=.*?$/, '')}&procFormDataKey=${formId.current}`),
      }],
    })
    if (res.success) {
      message.success('操作成功', () => {
        if (typeof callback === 'function') callback(res)
      })
    }
  }

  const saveForm = async (callback, isDraft) => {
    const res = await commonCommit()
    if (res.success) {
      if (isDraft) {
        saveDraft(callback)
      } else {
        message.success('操作成功', () => {
          if (typeof callback === 'function') callback(res)
        })
      }
    }
  }

  const deleteDraft = async (callback) => {
    const info = localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo')) : {}
    const res = await service.deleteDraft({
      appId,
      appTasks: [{
        appTaskId: formId.current.toString(),
        handlerIds: [info.loginUid],
      }],
    })
    if (res.success) {
      if (callback) {
        if (typeof callback === 'function') callback()
      }
    }
  }

  const deleteForm = () => {
    confirm({
      content: '你选择了注销当前文档，系统将删除该文档，是否继续？',
      onOk: async () => {
        const res = await service.deleteReport({
          reportCollectId: procFormDataKey,
        })
        if (res.success) {
          message.success('操作成功', () => {
            deleteDraft(() => {
              closeTheWindow(props.location.search)
            })
          })
        } else {
          message.error(data.msg)
        }
      },
    })
  }

  const buttonGroup = () => {
    const array = [{
      name: '保存退出',
      async: true,
      onClick: async () => {
        await saveForm(() => {
          closeTheWindow(props.location.search)
        }, true)
      },
    }, {
      name: '保存',
      async: true,
      onClick: async () => {
        await saveForm('', true)
      },
    }]
    if (procFormDataKey) {
      array.push({
        name: '注销文档',
        async: false,
        onClick: () => {
          deleteForm()
        },
      })
    }
    if (showDispatchBtn) {
      array.push({
        name: '一键分发',
        async: false,
        onClick: async () => {
          const res = await actions.getFormState()
          if (!res.values.mainDelivery || res.values.mainDelivery.length <= 0) {
            message.error('请选择主送人员')
            return
          }
          // 注释掉开发
          await commonCommit()
          showDispatchModal()
        },
      })
    }
    return array
  }

  const showDispatchModal = async () => {
    const overlay = document.createElement('div')
    document.body.appendChild(overlay)
    const close = () => {
      ReactDOM.unmountComponentAtNode(overlay)
      overlay.parentNode.removeChild(overlay)
    }
    const res = await actions.getFormState()
    ReactDOM.render(
      <Dispatch
        width={700}
        visible
        formData={res.values}
        procKey={procKey}
        appId={appId}
        procFormDataKey={formId.current}
        onOkCallback={finishSubmit}
        onCancel={() => {
          close()
        }}
      />,
      overlay,
    )
  }

  const onMount = ({ access, editMode }) => {
    if (access && Object.keys(access).length > 0 && editMode && editMode.includes('web')) {
      Object.keys(access).forEach(key => {
        actions.setFieldState(key, state => {
          switch (access[key]) {
            case 'NONE':
              state.display = false
              break
            case 'READ':
              state.editable = false
              break
            case 'WRITE':
              state.editable = true
              state.display = true
              break
            default: break
          }
        })
      })
    }
  }

  return (
    <div>
      <Form
        editable
        initValue={initValue}
      />
      <div>
        {
          (!isEmpty(formData) || !procFormDataKey) && (
            <Space>
              <Buttons
                procKey={procKey}
                appId={appId}
                onMount={onMount}
                onSubmitOpen={async () => {
                  const data = await actions.submit()
                  return {
                    ...data.values,
                    _dept: data.values.draftDepartment,
                  }
                }}
                onSubmit={submitForm}
                extraButtons={buttonGroup()}
              />
            </Space>
          )
        }
      </div>
    </div>
  )
}
