import React, { useEffect, useState, useMemo } from 'react'
import ReactDOM from 'react-dom'
import {
  Tabs,
  Space,
  Button,
  message,
} from 'antd'
import { closeTheWindow, getQueryString } from 'ROOT/utils'
import service from 'ROOT/service'
import { Buttons, Steps } from 'ROOT/components/Process'
import useMyInfo from 'ROOT/hooks/useMyInfo'
import useSigner from 'ROOT/hooks/useReportSigner'
import { patchData } from 'ROOT/utils/index'
import { getWpsUrl } from 'ROOT/utils/wps'
import Form, { actions } from './form'
import { formConfig, signerNodeName1, letterType, citynodelist, signerNodeName } from '../module/config'
import Dispatch from '../dialog/dispatch'
import ReportSave from '../dialog/ReportSave'
import { isEmpty, parseInt } from 'lodash'
import Feadback from '../component/feadback'
import moment from 'moment'

const { TabPane } = Tabs
const typeIndex = 0

export default props => {
  const { userTaskId, procFormDataKey, debug } = useMemo(() => getQueryString(props.location.search), [props.location.search])
  const [initValue, setInitValue] = useState({})
  const [editable, setEditable] = useState(false)
  // const [showSaveBtn, setShowSaveBtn] = useState(false)
  const [extraButton, setExtraButton] = useState([])
  const [dateList, setDateList] = useState([])
  const myInfo = useMyInfo({ isSetStorage: true })
  const { signer, originData } = useSigner({
    userTaskId,
  })
  const [nodeName, setNodeName] = useState('')
  const [nodeStatus, setNodeStatus] = useState('')
  const [ableEditBtn, setAbleEditBtn] = useState([])
  const [userTask, setUserTask] = useState({})
  const [draftValue, setDraftValue] = useState({})

  useEffect(() => {
    if (debug) {
      ableEditForm()
    }
  }, [])

  const ableEditForm = async () => {
    const res = await service.getEditableForm()
    if (res.success && res.data) {
      resetFormState()
      setAbleEditBtn([{
        name: '更新数据',
        async: true,
        onClick: async () => {
          await saveForm(() => {
            closeTheWindow(props.location.search)
          }, true)
        }
      }])
    }
  }

  useEffect(async () => {
    if (procFormDataKey) {
      const wpsContent = await getWpsUrl(procFormDataKey)
      service.getReportDetail({
        reportCollectId: procFormDataKey,
      }).then(res => {
        if (res.success && res.data) {
          const { data } = res
          data.fileList = JSON.parse(data.fileList || '[]')
          const { orgId } = data
          if (wpsContent) {
            data.fileList[0] = wpsContent
          }
          data.mainDelivery = JSON.parse(data.mainDeliveryElement || '[]')
          if (data.cycleType !== 1) {
            data.cycleDay = parseInt(data.cycleDay, 10)
          }
          setInitValue(res.data)
          resetContentProps()
          service.getCurrentOrgType({
            orgId,
          }).then(res=> {
            const type = res // （1:区公司/2:市公司/3:县公司/4:其他）
            if (type === 3) {
              service.getOneById({
                orgId,
              }).then(res=> {
                actions.setFieldState('*(mainDelivery)', state => {
                  state.props['x-component-props'].parentOrgId = res.data.parentOrgId
                })
              })
            }
          })

        }
      })
      service.queryCollectDate({
        reportCollectId: procFormDataKey,
      }).then(res => {
        if (res.success && res.data) {
          setDateList(res.data)
        }
      })
    }
    // 从流程获取系统时间
    if (userTaskId) {
      const res = await service.getUserTask({ taskUserKey: userTaskId })
      setUserTask(res.data)
    }
  }, [])

  const filterCollectTimey = (time, type) => {
    if (type === 1) {
      return initValue.cycleDay
    }
    const weekTime = time + 259200000 //待办时间(精确到时分秒)+3天判断
    let resultValue = 0
    let result = ''
    dateList.forEach((item, index) => {
      const itemDate = (moment(item).valueOf() + 36000000)//下个周期（10点）则是下个周期时间
      if (index === 0){
        resultValue = itemDate - weekTime
        result = item
      }

      console.log(itemDate, weekTime, itemDate - weekTime, itemDate > weekTime, (itemDate - weekTime) < resultValue)
      if (itemDate > weekTime) {
        if ((itemDate - weekTime) < resultValue) {
          resultValue = itemDate - weekTime
          result = item
        }
      }
    })
    console.log(result)
    return result
  }

  const resetContentProps = () => {
    actions.setFieldState('fileList', (state) => {
      state.props['x-component-props'] = {
        ...state.props['x-component-props'],
        reportId: procFormDataKey,
        type: letterType[typeIndex].type,
        config: formConfig,
      }
    })
  }

  const commonCommit = async () => {
    const data = await actions.submit()
    const params = {
      type: 1, // 省：0,市县：1
      ...data.values,
      fileList: JSON.stringify((data.values && data.values.fileList) || []),
      mainDeliveryElement: JSON.stringify(data.values && data.values.mainDelivery || []),
      mainDelivery: data.values && data.values.mainDelivery.map(item => ({
        deptId: item.code,
        deptName: item.showPath,
      })),
    }
    const res = await service.reportSave(params)
    return res
  }

  const submitForm = async () => {
    const res = await commonCommit()
    // const data = await actions.submit()
    if (res.success) {
      // return { id: +procFormDataKey, values: data.values }
      return Promise.resolve()
    }
    throw new Error('保存出错')
  }

  useEffect(() => {
    window.addEventListener('process', e => {
      switch (e.detail.type) {
        case 'saved': break;
        case 'approved': // 审批后
        case 'added': // 加签后
        case 'stoped': // 终止后
        case 'rejected': // 退回后
        case 'forworded': // 转交后
        case 'cced': // 抄送后
        default: finishSubmit()
      }
    })
    // window.addEventListener('process', () => finishSubmit())
  }, [])

  const finishSubmit = () => {
    setTimeout(() => {
      closeTheWindow(props.location.search)
    }, 2000)
  }

  const saveForm = async (callback) => {
    const res = await commonCommit()
    if (res.success) {
      message.success('操作成功', () => {
        if (typeof callback === 'function') callback(res)
      })
    }
  }

  const autoSubmit = () => {
    const btns = document.querySelector('.proc-buttons')
    if (btns.children && btns.children.length && btns.children[0].innerHTML === ' 提交') {
      console.log("autoSubmitreportcity===", btns.children[0])
      btns.children[0].click()
    }
  }

  const buttonGroup = () => {
    const array = []
    // if (showSaveBtn) {
    //   array.push(saveAndExit(() => {
    //     saveForm(() => {
    //       closeTheWindow(true)
    //     })
    //   }))
    // }
    if (originData.childShape.properties.name === signerNodeName  || originData.childShape.properties.name === signerNodeName1) {
      array.push({
        name: '一键分发',
        async: false,
        onClick: async () => {
          const res = await actions.getFormState()
          if (!res.values.mainDelivery || res.values.mainDelivery.length <= 0) {
            message.error('请选择主送人员')
            return
          }
          await commonCommit()
          // 注释掉开发用
          showDispatchModal()
        }
      })
    }
    return array
  }

  const showDispatchModal = async () => {
    const overlay = document.createElement('div')
    document.body.appendChild(overlay)
    const close = () => {
      ReactDOM.unmountComponentAtNode(overlay)
      overlay.parentNode.removeChild(overlay)
    }
    const res = await actions.getFormState()
    ReactDOM.render(
      <Dispatch
        width={700}
        visible
        formData={res.values}
        taskId={userTaskId}
        procFormDataKey={procFormDataKey}
        onOkCallback={finishSubmit}
        onCancel={() => {
          close()
        }}
        domHidden
      />,
      overlay,
    )
  }



  // type = 1 代表按钮事件 =2代表表格
  const showSaveModal = async (record, type, cb) => {
    const overlay = document.createElement('div')
    document.body.appendChild(overlay)
    const close = () => {
      ReactDOM.unmountComponentAtNode(overlay)
      overlay.parentNode.removeChild(overlay)
      if (cb) {
        cb()
      }
    }
    // const res = await actions.getFormState()
    ReactDOM.render(
      <ReportSave
        width={600}
        visible
        record={record}
        procFormDataKey={procFormDataKey}
        onOkCallback={type === 1 ? autoSubmit : close}
        onCancel={() => {
          close()
        }}
      />,
      overlay,
    )
  }

  const getSerialNumber = async () => {
    const res = await service.serialMake({serialTemplate: await actions.getFieldValue('fileSerial'), recordId: procFormDataKey })
    actions.setFieldState('*(fileSerial)', (state) => {
      state.value = res
    })
  }
  const hideSubmitBtn = () => {
    const buttons = document.querySelectorAll('.proc-btn')
    buttons.forEach(btn => {
      console.log(btn.textContent, btn.textContent && btn.textContent.trim() === '提交')
      if (btn.textContent && btn.textContent.trim() === '提交') {
        btn.style.display = 'none'
      }
    })
  }
  const onMount = ({ access, editMode, draft, process }) => {
    process.nodeName = process.nodeName.trim()
    if (process.nodeName === '主办人员填报' && access && access.isSubmitButton === 'NONE') {
      setTimeout(() => {
        hideSubmitBtn()
      })
    }
    console.log(access, editMode, draft, process)
    setNodeName(process.nodeName)
    setNodeStatus(process.taskStatus)
    if (process.nodeName === '开始节点' && process.taskStatus === 'running') {
      setExtraButton([{
        name: '保存退出',
        async: true,
        onClick: async () => {
          await saveForm(() => {
            closeTheWindow(props.location.search)
          }, true)
        }
      }, {
        name: '保存',
        async: true,
        onClick: async () => {
          await saveForm()
        }
      }])
    }
    if (access && Object.keys(access).length > 0 && editMode && editMode.includes('web')) {
      Object.keys(access).forEach(key => {
        if (key === 'isEditable' && access[key] === 'WRITE') {
          // setShowSaveBtn(true)
          resetFormState()
          return
        }
        actions.setFieldState(key, state => {
          switch (access[key]) {
            case 'NONE':
              state.display = false
              break
            case 'READ':
              state.editable = false
              break
            case 'WRITE':
              state.editable = true
              state.display = true
              break
            default: break
          }
        })
      })
    }
    if (draft) {
      draft.fileList = JSON.parse(draft.fileList || '[]')
      draft.mainDelivery = JSON.parse(draft.mainDeliveryElement || '[]')
      setDraftValue({ ...draft })
    }
  }

  const resetFormState = () => {
    setEditable(true)
    actions.setFieldState('*(draftDepartment)', state => {
      state.props['x-component'] = 'Editable'
    })
  }

  const getFormData = async () => {
    const data = await actions.submit()
    const params = {
      type: 1, // 省：0,市县：1
      ...data.values,
      fileList: JSON.stringify((data.values && data.values.fileList) || []),
      mainDeliveryElement: JSON.stringify(data.values && data.values.mainDelivery || []),
      mainDelivery: data.values && data.values.mainDelivery.map(item => ({
        deptId: item.code,
        deptName: item.showPath,
      })),
    }
    return params
  }

  useEffect(() => {
    if (citynodelist.includes(nodeName) && nodeStatus === 'running') {
      getSerialNumber()
      actions.setFieldState('*(issuer)', state => {
        if (!state.value) {
          state.value = signer || ''
        }
      })
    }
  }, [initValue, nodeName, nodeStatus])

  return (
    <div>
      <Form
        signer={signer}
        initValue={draftValue && Object.keys(draftValue).length > 0 ? draftValue : initValue}
        editable={editable}
        extraBody={
          <div className="mb10">
            <div>
              <div style={{margin:'15px 0 10px'}}>反馈内容：</div>
              <Feadback procFormDataKey={procFormDataKey} initValue={initValue} dateList={dateList} nodeName={nodeName} showSaveModal={showSaveModal} debug={editable ? debug : 0}/>
            </div>
            <div>
              <div style={{margin:'20px 0 10px'}}>审批流程：</div>
              <Steps userTaskId={userTaskId} />
            </div>
          </div>
          // <Tabs defaultActiveKey="1" className="mb10">
          //   <TabPane tab="审批流程" key="1">
          //     <Steps
          //       userTaskId={userTaskId}
          //     />
          //   </TabPane>
          //   <TabPane tab="反馈内容" key="2">
          //     <Feadback procFormDataKey={procFormDataKey} initValue={initValue} dateList={dateList} nodeName={nodeName} showSaveModal={showSaveModal} debug={editable ? debug : 0}/>
          //   </TabPane>
          // </Tabs>
        }
      />
      <div>
        {
          !isEmpty(initValue) && (
            <Space>
              {/* {originData && originData.procFlags === 0 && !originData.userTaskStatus && buttonGroup().map(item => <Button key={item.name} type={item.type} danger={item.name === '一键分发'} onClick={item.onClick}>{item.name}</Button>)} */}
              <Buttons
                userTaskId={userTaskId}
                onMount={onMount}
                onSave={getFormData}
                onSubmitOpen={getFormData}
                onSubmit={submitForm}
                extraButtons={originData && originData.procFlags === 0 && !originData.userTaskStatus ? [...buttonGroup(), ...extraButton, ...ableEditBtn] : [...extraButton, ...ableEditBtn]}
                customButtons={[
                  {
                    key: 'bbfk',
                    onClick: () => showSaveModal({ collectTime: filterCollectTimey(userTask && userTask.gmtCreate, initValue && initValue.cycleType), title:  initValue && initValue.title }, 1),
                    async: false,
                  },
                ]}
              // onFinish={finishSubmit}
              />
            </Space>
          )
        }
      </div>
    </div>
  )
}
