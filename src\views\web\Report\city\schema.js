import moment from 'moment'
import { UPLOAD_URL } from 'ROOT/constants'
import locale from 'antd/lib/date-picker/locale/zh_CN'
import Cookies from 'js-cookie'
import { isArray, isUndefined } from 'lodash'

export default (initValue) => ({
  type: 'object',
  properties: {
    layout: {
      'x-component': 'mega-layout',
      'x-component-props': {
        grid: true,
        autoRow: true,
        // labelWidth: 100,
        labelAlign: '{{labelAlign}}',
        className: 'grid-gaps',
        responsive: {
          lg: 3,
          m: 2,
          s: 1,
        },
      },
      properties: {
        title: {
          key: 'title',
          name: 'title',
          title: '标题',
          maxLength: 200,
          required: true,
          'x-component': 'Input',
          'x-mega-props': {
            span: 2,
          },
        },
        issuer: {
          key: 'issuer',
          name: 'issuer',
          title: '签发人',
          'x-component': 'Editable',
        },
        fileSerial: {
          key: 'fileSerial',
          name: 'fileSerial',
          title: '文件编码',
          'x-component': 'Editable',
        },
        createUserName: {
          key: 'createUserName',
          name: 'createUserName',
          title: '起草人',
          'x-component': 'Editable',
        },
        draftDepartment: {
          key: 'draftDepartment',
          name: 'draftDepartment',
          title: '起草部门',
          required: true,
          'x-component': 'Dept',
          'x-component-props': {
            labelInValue: true,
          },
          'x-rules': [
            {
              validator: (val) => {
                if (!val.value) {
                  return '请选择起草部门'
                }
              },
            },
          ],
        },
        mobile: {
          key: 'mobile',
          name: 'mobile',
          title: '电话',
          'x-component': 'Editable',
        },
        draftDate: {
          key: 'draftDate',
          name: 'draftDate',
          title: '起草时间',
          default: moment().format('YYYY年MM月DD日'),
          'x-component': 'Editable',
        },
        _taskLevel: {
          key: '_taskLevel',
          name: '_taskLevel',
          title: '缓急',
          default: 3,
          enum: [
            { label: '一般', value: 3 },
            { label: '急件', value: 2 },
            { label: '特急', value: 1 },
          ],
          required: true,
          'x-component': 'Select',
        },
        mainDelivery: {
          key: 'mainDelivery',
          name: 'mainDelivery',
          title: '主送',
          required: true,
          'x-component': isArray(initValue.mainDelivery) || isUndefined(initValue.mainDelivery) ? 'SelectCityOrg' : 'Input', // 'SelectDept',
          'x-component-props': {
            listAllAction: '/baas-admin/web/listAll',
            myListAction: '/baas-easylabel/business/workLineMyList',
            searchAction: '/web-search/web/search?size=100',
            type: '2',
            // flag: '3',
            workLineType: '1',
            parentOrgId: Cookies.get('orgId'),
          },
          'x-mega-props': {
            span: 3,
          },
        },
        cycleType: {
          key: 'cycleType',
          name: 'cycleType',
          title: '反馈周期',
          'x-component': 'Select',
          default: 1,
          enum: [
            { label: '按次', value: 1 },
            // { label: '按周', value: 2 },
            // { label: '按月', value: 3 },
          ],
          'x-linkages': [{
            type: 'value:schema',
            target: '*(cycleDay)',
            condition: '{{ $self.value === 1 }}',
            schema: {
              'x-component': 'DatePicker',
              'x-component-props': {
                locale,
              },
            },
            otherwise: {
              'x-component': 'Select',
            },
          }, {
            type: 'value:schema',
            target: '*(cycleDay)',
            condition: '{{ $self.value === 2 }}',
            schema: {
              enum: [
                { label: '周一', value: 1 },
                { label: '周二', value: 2 },
                { label: '周三', value: 3 },
                { label: '周四', value: 4 },
                { label: '周五', value: 5 },
              ],
            },
          }, {
            type: 'value:schema',
            target: '*(cycleDay)',
            condition: '{{ $self.value === 3}}',
            schema: {
              enum: new Array(28).fill(0).map((a, index) => {
                return {
                  label: index + 1,
                  value: index + 1,
                }
              }),
            },
          }, {
            type: 'value:visible',
            target: '*(feedbackStartTime, feedbackOverTime)',
            condition: '{{ $self.value === 2 || $self.value === 3}}',
          }],
        },
        cycleDay: {
          key: 'cycleDay',
          name: 'cycleDay',
          title: '反馈时间',
          required: true,
          'x-component': 'Select',
          'x-component-props': {
            style: {
              width: '100%',
            },
          },
        },
        feedbackStartTime: {
          key: 'feedbackStartTime',
          name: 'feedbackStartTime',
          title: '反馈开始时间 ',
          required: true,
          'x-component': 'DatePicker',
          'x-component-props': {
            format: 'YYYY-MM-DD',
            locale,
            style: {
              width: '100%',
            },
            disabledDate: (current) => {
              // Can not select days before today and today
              return current && current < moment().startOf('day')
            },
          },
        },
        feedbackOverTime: {
          key: 'feedbackOverTime',
          name: 'feedbackOverTime',
          title: '反馈结束时间',
          required: true,
          'x-component': 'DatePicker',
          'x-component-props': {
            format: 'YYYY-MM-DD',
            locale,
            style: {
              width: '100%',
            },
            disabledDate: (current) => {
              // Can not select days before today and today
              return current && current < moment().startOf('day')
            },
          },
        },
        remark: {
          key: 'remark',
          name: 'remark',
          title: '备注',
          maxLength: 1000,
          'x-component': 'Input',
          'x-mega-props': {
            span: 3,
          },
        },
        files: {
          key: 'files',
          name: 'files',
          title: '附件',
          'x-component': 'Upload',
          'x-component-props': {
            listType: 'text',
            action: UPLOAD_URL,
          },
          'x-mega-props': {
            span: 3,
          },
        },
        fileList: {
          key: 'fileList',
          name: 'fileList',
          title: '收集内容说明',
          'x-component': 'WpsEditor',
          'x-mega-props': {
            span: 3,
          },
          'x-component-props': {
            orgId: '',
            reportId: '',
            type: '',
            config: '',
          },
        },
      },
    },
  },
})
