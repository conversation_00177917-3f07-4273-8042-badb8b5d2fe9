import React, { useState, useRef, useCallback } from 'react'
import {
  message
} from 'antd'
import { css } from 'emotion'
import Service from 'ROOT/service'
import Loading from 'ROOT/components/Loading'
import UploadFileList from '@xm/upload-file-list'

export default itemProps => {
  const InputImport = useRef()
  const { value = [], mutators, props, schema, form, editable } = itemProps
  const { disable } = props['x-component-props'] || {}
  const [loading, setLoading] = useState(false)

  const selectFiles = () => {
    InputImport.current.click()
  }

  const fileChange = (e) => {
    // let files = e.currentTarget.files[0]
    let files = Array.from(e.target.files)
    // console.log(Array.from(files))
    let arr = []
    let ableUpload = []
    files.map(item => {
      const { name, size } = item || {}
      if (size > 200 * 1024 * 1024) {
        arr.push(name)
        // const str = `文件大小需${maxSize}M以下`
        // message.error(str)
        return
      }
      ableUpload.push(item)
    })
    if (arr.length > 0) {
      message.warning(`${arr.join(',')}文件大小需200M以下`)
    }
    if (files.length > 0) {
      ableUpload.forEach(i => uploadFiles(i))
    }
    InputImport.current.value = null
  }

  const uploadFiles = (files) => {
    let formData = new FormData()
    formData.append('file', files)
    setLoading(true)
    Service.uploadFile(formData).then(res => {
      if (res.code === 200) {
        mutators.push({
          name: files.name,
          size: files.size,
          url: res.fileUrl,
          suffix: files.name.substring(files.name.lastIndexOf('.') + 1),
          uid: files.lastModified
        })
        setLoading(false)
      }
    })
  }

  const uploadOptions = {
    enableSort: editable,
    enableDownload: true,
    enableDelete: editable,
    enableRename: editable,
    enablePreview: true
  }

  const combineFileList = (list) => {
    return !Array.isArray(list)
    ? []
    : list.map((file, index) => ({
      name: `${(file.name || file.fileName)}`,
      size: file.size || file.fileSize,
      url: file.url || file.fileUrl,
      suffix: (file.name || file.fileName).substring((file.name || file.fileName).lastIndexOf('.') + 1),
      uid: file.uid
    }))
  }

  const onDelete = (fileList) => {
    mutators.change(combineFileList(fileList))
  }

  const onSortEnd = (fileList) => {
    mutators.change(combineFileList(fileList))
  }

  const onRename = (fileList) => {
    mutators.change(combineFileList(fileList))
  }


  const combineFileName = useCallback(() => {
    return combineFileList(value)
  }, [value])

  return (
    <div style={{ width: '100%' }}>
      {editable && !disable
        ? <div style={{ width: '100%', marginBottom: 10 }}> 
            <span
              onClick={selectFiles}
              className="upload-file-btn"
            >上传文件</span>
            <input
              type="file"
              ref={InputImport}
              onChange={fileChange}
              className={css`
                position: absolute;
                top: -9999px;
                left: -9999px;
              `}
              multiple="multiple"
            />
          {loading && <Loading isFullScreen={true} />}
        </div>
        : null
      }
      <UploadFileList options={uploadOptions} dataSource={combineFileName()} onDelete={onDelete} onSortEnd={onSortEnd} onRename={onRename} />
    </div>
  )
}