import React, { useState, useEffect } from 'react'
import moment from 'moment'
import { Table, Select, Button, message } from 'antd'
import { PaperClipOutlined } from '@ant-design/icons'
import service from 'ROOT/service'
import { DOWN_FILE } from 'ROOT/service/api'
import { formBizId } from 'ROOT/constants'
import { downloadUrl } from 'ROOT/utils'
import { downloadFileByGet } from 'ROOT/utils/download'
import { css } from 'emotion'


const fbTableConfig = [
  {
    title: '序号',
    width: 84,
    dataIndex: 'index',
    key: 'index',
  },
  {
  title: '反馈人部门',
  width: 200,
  dataIndex: 'deptName',
  key: 'deptName',
  },
  {
  title: '反馈人',
  width: 100,
  dataIndex: 'userName',
  key: 'userName',
  },
  {
    title: '反馈时间',
    width: 150,
    dataIndex: 'cycleDay',
    key: 'cycleDay',
  },
  {
    title: '收集日期',
    width: 150,
    dataIndex: 'collectTime',
    key: 'collectTime',
  },
  {
    title: '反馈文件',
    dataIndex: 'extra',
    key: 'extra',
    render: (text, record) => {
      if (record && record.fileResource) {
        return (
          <Button key={record.fileResource} type="link" href={downloadUrl(record.fileResource, record.fileName)} onClick={e => {
            e.stopPropagation()
          }}>
            {record.fileName}
          </Button>
        )
      }
    },
}]

export default (props) => {
  const { procFormDataKey, initValue: { fileSerial, title } = {}, nodeName, debug= 0 } = props
  const [dataSource, setDataSource] = useState([])
  const [initDataSource, setInitDataSource] = useState([])
  const [isChangeDate, setIsChangeDate] = useState(false)
  const [date, setDate] = useState('')
  const [showLoadMore, setShowLoadMore] = useState(false)

  const getTableList = async (hideLoadMore = false) => {
    const res = await service.getReportQuertList({
      reportCollectId: procFormDataKey,
      collectTime: date,
      processNodeName: nodeName,
      debug,
    })
    if (res.success) {
      const dataList = res.data.map((item, index) => ({...item, index: ++index}))
      if (dataList && dataList.length > 5 && !hideLoadMore) {
        setInitDataSource(dataList.slice(0, 5))
        setShowLoadMore(true)
        setDataSource(dataList)
      } else {
        setShowLoadMore(false)
        setDataSource(dataList)
        setInitDataSource(dataList)
      }
    }
  }

  const onChageDate = (date) => {
    setDate(date)
    setIsChangeDate(true)
  }

  const onLoadMore = () => {
    setShowLoadMore(false)
  }

  useEffect(() => {
    if (date) {
      getTableList(isChangeDate)
    }
  }, [date, isChangeDate, nodeName])

  useEffect(() => {
    if (props.dateList && props.dateList.length) {
      let closestTime = moment(props.dateList[0])
      let timeDiff = Math.abs(closestTime - moment())
      for (let i = 1; i < props.dateList.length; i++) {
        const newTimeDiff = Math.abs(moment(props.dateList[i]) - moment())
        if (newTimeDiff < timeDiff) {
          timeDiff = newTimeDiff
          closestTime = moment(props.dateList[i])
        }
      }
      setDate(closestTime.format('YYYY-MM-DD')) 
    } else {
      setDate('')
    }
  }, [props.dateList])

  const downFile = () => {
    downloadFileByGet('/baas-wf-api/report-collect/record/export', {
      collectTime: date,
      reportCollectId: procFormDataKey,
      processNodeName: nodeName,
      debug,
    }, `${fileSerial} ${title}.zip`)
  }

  const downLoadAll = () => {
    downloadFileByGet('/baas-wf-api/report-collect/record/export', {
      reportCollectId: procFormDataKey,
      processNodeName: nodeName,
      debug,
    }, `${fileSerial} ${title}.zip`)
  }

  return (
    <div className={css`
      position: relative;
    `}>
      <div className={css`
        display: flex;
        margin-bottom: 16px;
      `}>
        <Button onClick={() => downLoadAll()} className={css`
          margin-right: 8px;
          border-radius: 4px;
        `} type='primary'>全部下载</Button>
        <Button onClick={() => downFile()} className={css`
          margin-right: 16px;
          border-radius: 4px;
        `}>下载本次反馈</Button>
        <span className={css`
          margin-right: 16px;
          border-radius: 4px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 14px;
          color: #5C626B;
          line-height: 32px;
        `}>
          请点击条目信息打开之后上传反馈材料
        </span>
      </div>
      <div className={css`
        position: absolute;
        right: 0px;
        top: 0px;
      `}>
        <span className={css`
          margin-right: 16px;
          border-radius: 4px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 14px;
          color: #33373C;
          line-height: 32px;
        `}>
          收集日期
        </span>
        <Select className={css`
          width: 150px;
          border-radius: 4px;
          `}
          value={date}
          onChange={e => onChageDate(e)}
        >
          {props.dateList && props.dateList.map(item => <Select.Option value={item} key={item}>{item}</Select.Option>)}
        </Select>
      </div>
      <Table
        columns={fbTableConfig}
        rowKey="index"
        dataSource={showLoadMore ? initDataSource : dataSource}
        pagination={false}
        scroll={{
          x: 1000,
          // y: 400,
        }}
        onRow={record => {
          return {
            onClick: event => {
              const param = {
                collectTime: record && record.collectTime,
                title,
                deptId: record && record.deptId,
                deptName: record && record.deptName,
                userName: record && record.userName,
              }
              if (record.fileName && record.fileResource) {
                param.files = [{
                  name: record.fileName,
                  url: record.fileResource,
                  size: record.fileSize,
                  uid: Math.random(),
                }]
               }
              props.showSaveModal(param, '2', () => getTableList(!showLoadMore))
            }, // 点击行
          }
        }}
      />
      {showLoadMore && <div className={css`
        color: #4f84d2;
        text-align: center;
        margin: 30px 0;
        cursor: pointer;
      `} onClick={onLoadMore}>
        加载更多...
      </div>}
    </div>
  )
}