import React, { useEffect, useState, useCallback } from 'react'
import { Mo<PERSON>, Button, Descriptions, message } from 'antd'
import moment from 'moment'
import service from 'ROOT/service'
import { UPLOAD_URL } from 'ROOT/constants'
import useMyInfo from 'ROOT/hooks/useMyInfo'
import { SchemaMarkupForm, createAsyncFormActions, FormEffectHooks } from '@formily/antd'
import { FormMegaLayout, Input, DatePicker } from '@formily/antd-components'
import Dept from 'ROOT/components/Formily/Dept'
import Editable from 'ROOT/components/Formily/Editable'
import Upload from '../component/Upload'

const { onFieldValueChange$, onFieldInputChange$ } = FormEffectHooks

export const actions = createAsyncFormActions()

const schema = {
  type: 'object',
  properties: {
    layout: {
      'x-component': 'mega-layout',
      'x-component-props': {
        grid: true,
        autoRow: true,
        // labelWidth: 100,
        labelAlign: '{{labelAlign}}',
        className: 'grid-gaps',
        responsive: {
          lg: 2,
          m: 2,
          s: 2,
        },
      },
      properties: {
        title: {
          key: 'title',
          name: 'title',
          title: '标题',
          maxLength: 200,
          required: true,
          'x-component': 'Editable',
          'x-mega-props': {
            span: 2,
          },
        },
        draftDepartment: {
          key: 'draftDepartment',
          name: 'draftDepartment',
          title: '部门名称',
          required: true,
          'x-component': 'Dept',
          'x-component-props': {
            labelInValue: true,
          },
          'x-rules': [
            {
              validator: val => {
                if (!val.value) {
                  return '请选择起草部门'
                }
              },
            },
          ],
        },
        collectTime: {
          key: 'collectTime',
          name: 'collectTime',
          title: '收集日期',
          'x-component': 'Editable',
        },
        userName: {
          key: 'userName',
          name: 'userName',
          title: '反馈人员',
          'x-component': 'Editable',
        },
        cycleDay: {
          key: 'cycleDay',
          name: 'cycleDay',
          title: '反馈时间',
          'x-component': 'Editable',
        },
        files: {
          key: 'files',
          name: 'files',
          title: '反馈材料',
          'x-component': 'Upload',
          'x-component-props': {
            listType: 'text',
            action: UPLOAD_URL,
          },
          'x-mega-props': {
            span: 2,
          },
        },
      },
    },
  },
}

export default ({ width, visible, onOkCallback, onCancel, formData, procFormDataKey, record }) => {
  const [loading, setLoading] = useState(false)

  const myInfo = useMyInfo({ isSetStorage: true })

  const onOk = async () => {
    const data = await actions.submit()
    const { draftDepartment, cycleDay, collectTime, files } = data.values
    if (!files || (files && files.length < 1)) {
      return message.warn('请反馈材料')
    }
    const res = await service.recordSave({
      deptId: draftDepartment.value,
      cycleDay,
      collectTime,
      fileName: files[0].name,
      fileResource: files[0].url,
      fileSize: files[0].size,
      reportCollectId: procFormDataKey,
      // fileName: files[0].name
    })
    if (res.success) {
      message.success('操作成功')
      if (typeof onOkCallback === 'function') {
        onOkCallback()
        // onOkCallback(() => {
        //   closeTheWindow()
        // })
      }
      setLoading(false)
      onCancel()
    }
  }

  useEffect(() => {
    if (Object.keys(myInfo).length > 0) {
      const { loginName, loginMobile, loginOrgName } = myInfo
      const userName = record && record.userName ? record.userName : ''
      actions.setFieldState('userName', state => {
        state.value = userName && userName !== '' ? userName : myInfo.loginName
      })
      actions.setFieldState('cycleDay', state => {
        state.value = moment().format('YYYY-MM-DD HH:mm:ss')
      })
      service.queryOrgLinkPath().then(res => {
        const {linkPath} = res.data.data
        actions.setFieldState('draftDepartment', state => {
          if (record.deptName) {
            state.props['x-component'] = 'Editable'
            state.value = {
              label: record.deptName,
              value: record.deptId,
            }
          } else {
            state.props.enum =
              linkPath && linkPath.length > 0
                ? linkPath.map(item => ({
                    value: item.deptId || '0', // 单位直属 部门ID 为 0
                    // value: item.rootDeptId,
                    label: item.linkPath,
                  }))
                : []
            state.value =
              linkPath && linkPath.length > 0
                ? {
                    value: linkPath[0].deptId || '0', // 单位直属 部门ID 为 0
                    // value: linkPath[0].rootDeptId,
                    label: linkPath[0].linkPath,
                  }
                : { value: '', label: '' }
          }
        })
      })
    }
  }, [myInfo, record.deptName])

  useEffect(() => {
    if (Object.keys(record).length > 0) {
      actions.setFormState(state => {
        state.values = record
      })
    }
  }, [record])

  const effects = useCallback(() => {
    // onFieldValueChange$('draftDepartment').subscribe(({ value }) => {
    //   if (value && value.label) {
    //     actions.setFieldState('fileList', (state) => {
    //       state.props['x-component-props'].orgId = value.value
    //     })
    //   }
    // })
    onFieldValueChange$('files').subscribe(({ value }) => {
      console.log(value)
      if (value && value.length >= 1) {
        actions.setFieldState('files', state => {
          state.props['x-component-props'].disable = true
        })
      } else {
        actions.setFieldState('files', state => {
          state.props['x-component-props'].disable = false
        })
      }
    })
  }, [])

  Dept.isFieldComponent = true
  Upload.isFieldComponent = true
  Editable.isFieldComponent = true

  return (
    <Modal
      width={width}
      title="报表收集反馈"
      visible={visible}
      onCancel={onCancel}
      bodyStyle={{
        maxHeight: '600px',
        overflow: 'auto',
      }}
      footer={[
        <Button key="back" onClick={onCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" loading={loading} onClick={onOk}>
          提交
        </Button>,
      ]}
    >
      <div>
        <SchemaMarkupForm
          schema={schema}
          actions={actions}
          components={{
            DatePicker,
            Input,
            FormMegaLayout,
            Upload,
            Dept,
            Editable,
          }}
          previewPlaceholder="无"
          effects={effects}
          expressionScope={{
            labelAlign: 'top',
          }}
        />
      </div>
    </Modal>
  )
}