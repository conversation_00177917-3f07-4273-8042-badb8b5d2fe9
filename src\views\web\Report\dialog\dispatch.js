import React, { useEffect, useState, useMemo } from 'react'
import { Modal, Button, Descriptions, message } from 'antd'
import moment from 'moment'
import service from 'ROOT/service'
import { closeTheWindow } from 'ROOT/utils'
import useMyInfo from 'ROOT/hooks/useMyInfo'
import { nodelist, copyNodeList } from '../module/config'

export default ({
  width,
  visible,
  onOkCallback,
  onCancel,
  formData,
  taskId,
  procKey,
  procFormDataKey,
  appId,
  domHidden
}) => {
  const [serialNumber, setSerialNumber] = useState('')
  const [tableInfo, setTableInfo] = useState({})
  const [content, setContent] = useState({})
  const [mainSend, setMainSend] = useState([])
  const [loading, setLoading] = useState(false)
  const [userTaskLevel, setUserTaskLevel] = useState()
  const [initValue, setInitValue] = useState({})

  const myInfo = useMyInfo({ isSetStorage: true })


  useEffect(() => {
    getFormData()
    getDetailInfo()
  }, [])

  const getFormData = () => {
    service.getReportDetail({
      reportCollectId: procFormDataKey,
    }).then(res => {
      if (res.success && res.data) {
        setSerialNumber(res.data.fileSerial)
        setUserTaskLevel(res.data._taskLevel)
        setInitValue(res.data.data)
      }
    })
  }

  const getDetailInfo = async () => {
    const array = [{
      type: 1,
      orgDepts: formData.mainDelivery.map(item => ({
        ...item,
        deptId: item.deptId && item.deptId.toString().includes('_allDept') ? null : item.deptId,
      })),
    }]
    setLoading(true)
    const res = await service.batchGetCustomerFormPrincipal({
      data: array,
    })
    setLoading(false)
    if (res.success) {
      setTableInfo(res.data)
      setContent(combineTableInfo(res.data))
      console.log(combineTableInfo(res.data))
    }
  }

  useEffect(() => {
    const result = []
    Object.keys(content).forEach((key) => {
      if (content[key]) {
        result.push(...content[key])
      }
    })
    setMainSend(result)
  }, [content])

  const combineTableInfo = (data) => {
    const obj = {}
    Object.keys(data).forEach(key => {
      obj[key] = []
      data[key].forEach(item => {
        obj[key].push(`${item.loginOrgName}${item.deptName === '未归属' ? '' : `\\${item.deptName}`}(${item.loginName})`)
      })
    })
    return obj
  }

  const getMemberInfo = (nodeName) => {
    console.log(nodeName, 'nodeName')
    switch (nodeName) {
      case nodelist[0]:
        return tableInfo.areaOrgs.map(item => ({
          id: item.loginUid,
          name: item.loginName,
          orgId: item.loginOrgId,
          deptId: item.deptId,
        }))
      case nodelist[1]:
        return (tableInfo.cityOrgs.concat(tableInfo.countyOrgs)).map(item => ({
          id: item.loginUid,
          name: item.loginName,
          orgId: item.loginOrgId,
          deptId: item.deptId,
        }))
      case nodelist[2]:
        return tableInfo.proOrgs.map(item => ({
          id: item.loginUid,
          name: item.loginName,
          orgId: item.loginOrgId,
          deptId: item.deptId,
        }))
      case nodelist[3]:
        return tableInfo.terminalProOrgs.map(item => ({
          id: item.loginUid,
          name: item.loginName,
          orgId: item.loginOrgId,
          deptId: item.deptId,
        }))
      case '落实部门收办':
        return tableInfo.areaOrgs.map(item => ({
          id: item.loginUid,
          name: item.loginName,
          orgId: item.loginOrgId,
          deptId: item.deptId,
        }))
      default: break
    }
  }

  const combineAssignee = (node) => {
    const array = []
    node.forEach(item => {
      const member = getMemberInfo(item.actinstName || item.actName)
      if (member && member.length > 0) {
        array.push(procKey ? {
          actKey: item.actKey,
          assigneeInfo: {
            assignees: member
          },
        } : {
          actinstId: item.id,
          assigneeInfo: {
            assignees: member
          },
        })
      }
    })
    return array
  }

  const onOk = async () => {
    setLoading(true)
    const operator = {
      id: myInfo.loginUid,
      name: myInfo.loginName,
      orgId: myInfo.loginOrgId,
      orgName: myInfo.loginOrgName,
    }
    const result = formData
    result._dept = result.draftDepartment
    const node = await service.getStartNode(procKey ? {
      procKey,
      operator,
      varJson: JSON.stringify(result)
    } : {
      taskId,
      operator,
      varJson: JSON.stringify(result)
    })
    const allNode = [...node.data]
    // if (procKey) {
    //   const copyNode = await service.getStartNextCopyNode({
    //     procKey,
    //     varJson: JSON.stringify(formData)
    //   })
    //   if (copyNode.data && copyNode.data.length > 0) {
    //     allNode.push(...copyNode.data)
    //   }
    // }
    console.log(allNode, combineAssignee(allNode), '+++')
    const res = procKey ? await service.startTask({
      manualStartInfos: combineAssignee(allNode),
      appId,
      procKey,
      procFormDataKey,
      starterDeptId: formData.draftDepartment.value,
      varJson: JSON.stringify(result),
      comment: JSON.stringify({ text: '同意' })
    }) : await service.completeTask({
      manualCompleteInfos: combineAssignee(allNode),
      taskId,
      userTaskLevel,
      varJson: JSON.stringify(result),
      comment: JSON.stringify({ text: '同意' })
    })
    if (res.success) {
      setLoading(false)
      onCancel()
      message.success('操作成功', () => {
        if (typeof onOkCallback === 'function') {
          onOkCallback()
          // onOkCallback(() => {
          //   closeTheWindow()
          // })
        }
      })
    }
  }
  return (
    <Modal
      width={width}
      title="一键自动编发"
      visible={visible}
      onCancel={onCancel}
      bodyStyle={{
        maxHeight: '550px',
        overflow: 'auto',
      }}
      footer={[
        <Button key="back" onClick={onCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" loading={loading} onClick={onOk}>
          确定
        </Button>,
      ]}
    >
      <div>
        <Descriptions column={1} bordered>
          <Descriptions.Item labelStyle={{ width: '165px' }} contentStyle={{ marginBottom: '20px' }} label="公文编号">{serialNumber}</Descriptions.Item>
          <Descriptions.Item labelStyle={{ width: '165px' }} contentStyle={{ marginBottom: '20px' }} label="主送">
            <div dangerouslySetInnerHTML={{ __html: mainSend.join('<br />') }} />
          </Descriptions.Item>
        </Descriptions>
        <div className="modal-sub-title">
          <span className="modal-title-line" />
          <p className="modal-title-text">流程分布信息</p>
        </div>
        <Descriptions column={1} bordered>
          { domHidden ? null :
            <Descriptions.Item labelStyle={{ width: '165px' }} contentStyle={{ marginBottom: '20px' }} label="区公司部门收办">
              <div dangerouslySetInnerHTML={{ __html: content.areaOrgs && content.areaOrgs.join('<br />') }} />
            </Descriptions.Item>
          }
          <Descriptions.Item labelStyle={{ width: '165px' }} contentStyle={{ marginBottom: '20px' }} label="市公司部门收办">
            <div dangerouslySetInnerHTML={{ __html: content.cityOrgs && content.cityOrgs.concat(content.countyOrgs && content.countyOrgs).join('<br />') }} />
          </Descriptions.Item>
          { domHidden ? null :
            <Descriptions.Item labelStyle={{ width: '165px' }} contentStyle={{ marginBottom: '20px' }} label="在线公司部门收办">
              <div dangerouslySetInnerHTML={{ __html: content.proOrgs && content.proOrgs.join('<br />') }} />
            </Descriptions.Item>
          }
          { domHidden ? null :
            <Descriptions.Item labelStyle={{ width: '165px' }} contentStyle={{ marginBottom: '20px' }} label="终端公司部门收办">
              <div dangerouslySetInnerHTML={{ __html: content.terminalProOrgs && content.terminalProOrgs.join('<br />') }} />
            </Descriptions.Item>
          }
        </Descriptions>
      </div>
    </Modal>
  )
}