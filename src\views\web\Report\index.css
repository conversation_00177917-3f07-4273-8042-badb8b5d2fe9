.button-group {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 60px;
  padding-top: 14px;
  text-align: center;
  background-color: white;
  box-shadow: 0 0 5px rgba(0, 0, 0, .2);
  z-index: 103;
}

.preview-title {
  font-size: 18px;
  font-weight: bold;
  text-align: center;
}

/* .ant-descriptions-row>td, .ant-descriptions-row>th {
  padding: 0 !important;
} */

.form-number {
  text-align: right;
  font-size: 14px;
}

.button-group .proc-buttons button {
  margin-left: 0 !important;
}

.form-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 200;
}

.form-modal-mask {
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
}

.form-modal-body {
  position: absolute;
  top: 50%;
  left: 50%;
  display: flex;
  flex-direction: column;
  width: 70%;
  height: 80%;
  padding: 48px 40px 25px;
  margin: -32% 0 0 -35%;
  background-color: white;
}

.form-modal-closeBtn {
  position: absolute;
  top: 0;
  right: 0;
  width: 48px;
  height: 48px;
  text-align: center;
  line-height: 48px;
  cursor: pointer;
}

.form-modal-title {
  width: 100%;
  flex: 0;
  margin-bottom: 36px;
  text-align: center;
  line-height: 32px;
  font-size: 24px;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.85);
}

.form-modal-content {
  width: 100%;
  flex: 1;
  /* height: 667px; */
  padding: 40px 0 0;
  overflow: auto;
  border-top: 1px solid #E9ECF0;
}

/* .ant-descriptions-item-label {
  color: rgba(0,0,0,.45);
} */

.modal-sub-title {
  margin: 40px 0 24px;
  padding-bottom: 8px;
  border-bottom: 1px solid #F0F0F0;
  overflow: hidden;
  zoom: 1;
}

.modal-title-line {
  float: left;
  display: block;
  width: 2px;
  height: 14px;
  margin-top: 3px;
  margin-right: 8px;
  background: #488FF9;
}

.modal-title-text {
  margin-bottom: 0;
  line-height: 20px;
  font-size: 14px;
  color: #262A30;
}