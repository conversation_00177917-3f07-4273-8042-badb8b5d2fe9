import React, { useState ,useEffect} from 'react'
import { Tabs } from 'antd'
import { cx } from 'emotion'
import PdfPreview from 'ROOT/components/PdfPreview'
import { getQueryString } from 'ROOT/utils'
import service from 'ROOT/service'
import { getWpsUrl } from 'ROOT/utils/wps'
import { tab } from './style.js'
import RuleDetail from '../ruleDetail'

const iconAdd = require('ROOT/assets/images/bodyfile.png')

const { TabPane } = Tabs
const RuleOfficial = (props) => {
	const { userTaskId, procKey, appId ,procFormDataKey} = getQueryString(props.location.search)
	const [initValue,setInitValue] = useState()
	const [ key, setKey ] = useState('1')
	const [ fileList, setFileList ] = useState([])
	const [ fileUrl, setFileUrl ] = useState('')
	const type = '23'
	const config = {title:'规章制度2'}
	const [ currentIndex, setCurrentIndex ] = useState(0)
	const callback = (key) => {
		console.log(key)
		setKey(key)
	}
	const watchFileChange = (list) => {
		console.log(list)
		if (list.length !== 0) {
			setFileList(list)
			setFileUrl(list[0].url)
		}
	}
	const handleChangePreviewFile = (item, index) => {
		setCurrentIndex(index)
		setFileUrl(item.url)
	}
	useEffect(async () => {
		if (procFormDataKey) {
			const wpsContent = await getWpsUrl(procFormDataKey)
			service.getFormData({
			  reportId: procFormDataKey, // 获取详情，也就是初始值
			}).then((res) => {
			  if (res) {
					if (wpsContent) {
            res.data.data.fileList[0] = wpsContent
          }
				  	const {data,status} =res.data
					  const {loginUid,drafter} =data
					const formateData = {
						...data,
						drafter:{
							label:drafter,
							value:loginUid,
						},
					}
					console.log(formateData,'dd')
					setInitValue(data)
					window.parent.postMessage({$detailData:formateData,status,userTaskId,procFormDataKey})
				// actions.setFormState(state => {
				//   state.values = res.data.data
				// })
			  }
			})
		  }
	}, [])
	return (
		<div>
			<Tabs activeKey={key} onChange={callback} className={cx(tab)}>
				{fileList.length === 1 && (
					<TabPane tab="正文" key="2">
						<PdfPreview url={fileUrl} />
					</TabPane>
				)}
				{fileList.length > 1 && (
					<TabPane tab="正文" key="2">
						<div className="body-file">
							<div className="fileListBox">
								{fileList.map((item, index) => {
									return (
										<div
											className={cx({ everyList: true, active: currentIndex === index })}
											key={item.url}
											onClick={() => handleChangePreviewFile(item, index)}
										>
											<img src={iconAdd} alt="" className="img" />
											<div className="name">{item.name}</div>
										</div>
									)
								})}
							</div>
							<PdfPreview url={fileUrl} />
						</div>
					</TabPane>
				)}
				<TabPane tab="基本信息" key="1">
					<RuleDetail props={props} onFilechange={watchFileChange}  initValue={initValue} type={type} config={config} />
				</TabPane>
			</Tabs>
		</div>
	)
}

export default RuleOfficial
