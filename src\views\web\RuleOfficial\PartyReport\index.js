import React, { useState } from 'react'
import { Tabs } from 'antd'
import { cx } from 'emotion'
import PdfPreview from 'ROOT/components/PdfPreview'
import { tab } from './style.js'
import RuleContent from '../ruleContent'

const iconAdd = require('ROOT/assets/images/bodyfile.png')

const { TabPane } = Tabs
const RuleOfficial = (props) => {
	const [ key, setKey ] = useState('1')
	const [ fileList, setFileList ] = useState([])
	const [ fileUrl, setFileUrl ] = useState('')
	const [ currentIndex, setCurrentIndex ] = useState(0)
	const type = '23'
	const config = {title:'规章制度2'}
	const callback = (key) => {
		console.log(key)
		setKey(key)
	}
	const watchFileChange = (list) => {
		console.log(list)
		if (list.length !== 0) {
			setFileList(list)
			setFileUrl(list[0].pdfUrl)
		}
	}
	const handleChangePreviewFile = (item, index) => {
		setCurrentIndex(index)
		const { url,pdfUrl } =item  || {}
		setFileUrl(pdfUrl || url)
	}
	return (
		<div>
			<Tabs activeKey={key} onChange={callback} className={cx(tab)}>
				{fileList.length === 1 && (
					<TabPane tab="正文" key="2">
						<PdfPreview url={fileUrl} />
					</TabPane>
				)}
				{fileList.length > 1 && (
					<TabPane tab="正文" key="2">
						<div className="body-file">
							<div className="fileListBox">
								{fileList.map((item, index) => {
									return (
										<div
											className={cx({ everyList: true, active: currentIndex === index })}
											key={item.url}
											onClick={() => handleChangePreviewFile(item, index)}
										>
											<img src={iconAdd} alt="" className="img" />
											<div className="name">{item.name}</div>
										</div>
									)
								})}
							</div>
							<PdfPreview url={fileUrl} />
						</div>
					</TabPane>
				)}
				<TabPane tab="基本信息" key="1">
					<RuleContent props={props} onFilechange={watchFileChange} type={type} config={config} />
				</TabPane>
			</Tabs>
		</div>
	)
}

export default RuleOfficial
