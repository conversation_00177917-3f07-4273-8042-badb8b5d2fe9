import {css} from 'emotion'

export const tab = css`
.ant-tabs-nav-wrap{
    justify-content:center !important;
    margin:30px  !important;
}
.ant-tabs-nav-list{
    display:flex !important;
    .ant-tabs-tab{
        width: 82px !important;
        height: 32px !important;
        border: 1px solid #5C626B !important;
        /* border-radius: 4px !important; */
        line-height:32px !important;
        justify-content:center;
        text-align:center !important;
    }
    .ant-tabs-tab:first-of-type{
        border-radius: 4px 0 0 4px !important;
    }
    .ant-tabs-tab:last-of-type{
        border-radius: 0px 4px 4px 0px !important;
    }
    .ant-tabs-tab:hover{
        color:inherit;
    }
    .ant-tabs-tab-active{
        width: 82px !important;
        height: 32px !important;
        background: #5C626B !important;
    }
    .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn{
        color: #fff;
    }
    .ant-tabs-tab+.ant-tabs-tab{
        margin :0;
    }
    .ant-tabs-ink-bar{
        background:none;
    }
}
.fileListBox{
    position: absolute;
    left: 8px;
    top: -6px;
    padding: 12px;
    z-index: 1;
    background: #fff;
    box-shadow: 0 0 10px 0 rgb(0 0 0 / 10%);
    .everyList{
        box-sizing: border-box;
        width: 160px;
        height: 48px;
        padding: 8px 12px;
        background: #fff;
        border: 1px solid #cbcfd6;
        line-height: 0;
        vertical-align: top;
        border-radius: 4px;
        text-align: left;
        cursor: pointer;
        display: flex;
        align-items: center;
        margin-top: 10px;
        .img{
            width: 32px;
            height: 32px;
        }
        .name{
            font-size: 12px;
            line-height: normal;
            color: #262a30;
            margin-left: 10px;
        }
    }
    .active{
            border-color: #488ff9;
    }
}
.body-file{
    position: relative;
}
`