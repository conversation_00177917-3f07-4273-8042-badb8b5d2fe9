import React ,{useEffect,useState}from 'react'
import {But<PERSON>,Select } from 'antd'
import { css } from  'emotion'

const  SelectProcessFile = (itemProps) => {
	const { value, mutators, props, editable, schema } = itemProps
    const xComponentProps = schema['x-component-props'] || {}
    const { reportId,myInfo ,dept} = xComponentProps
    const [loginName,setLoginName] = useState('')
    const [deptList,setDeptList] = useState('')
    const hostName = window.location.hostname
    const databasePath ='oa/qu/fwgl.nsf'
    const [showValue,setShowValue] = useState(value)
    const [showData,setShowData] = useState(false)
    const options= [
        { label: '是', value: 1 },
        { label: '否', value: 2 },
    ]
    const [visible,setVisible] = useState(false)
    const selectFile = () =>{
       window.open(`http://*************:10025/workflow-manager/regu/queryModel.do?currPage=1&docId=${reportId}&hostName=${hostName}
       &databasePath=${databasePath}&Draftsman=${loginName}&draftsDept=${deptList}
       `,'','height=800, width=800, top=100,left=100, toolbar=no, menubar=no, scrollbars=no, resizable=no,location=no, status=no','_self')
    }
    const handleValueChange = (val) =>{
        const { value ,label} = val  
        if(value === 1){
            setVisible(true)
            setShowValue({value,label})
            mutators.change({value,label})
        }else{
            setVisible(false)
            setShowValue({value,label})
            mutators.change({value,label})
        }
    }
    useEffect(()=>{
        if(value&&Object.keys(value).length!==0){
            setShowValue(value)
            setShowData(false)
        }else{
            setShowData(true)
        }
    },[value])
    useEffect(()=>{
        if(Object.keys(myInfo).length!==0){
            const {  loginName ,linkPath} = myInfo
            setLoginName(encodeURIComponent(loginName))
            setDeptList(encodeURIComponent(linkPath[0].linkPath))
        }
    },[myInfo,dept])
   
    // eslint-disable-next-line no-nested-ternary
    return editable?(
        <div className={css`display:flex`}>
            <Select labelInValue options={options}  onChange={handleValueChange}  style={{width:'100px'}} value={showValue} disabled={!editable}/>
           {visible &&  <Button className={css`margin-left:20px`} onClick={selectFile}>选择流程附件</Button>}
        </div>
    ):(showData?`暂无数据`:<Select labelInValue options={options}  onChange={handleValueChange}  style={{width:'100px'}} value={showValue} disabled={!editable}/>)
}

export default SelectProcessFile