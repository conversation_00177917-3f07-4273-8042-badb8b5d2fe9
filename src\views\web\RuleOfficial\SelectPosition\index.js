import React ,{ useEffect,useState }from 'react'
import { Input } from 'antd'
import { PreviewText } from '@formily/react-shared-components'

const  SelectPosition = (itemProps)=> {
    const { value={}, mutators, props, editable, schema } = itemProps
    const [inputValue,setInputValue] = useState(value)
    useEffect(()=>{
        const handleData=(event) =>{
            const { data } =event
            // eslint-disable-next-line no-prototype-builtins
            if(data.hasOwnProperty('position')){
                setInputValue(data.position)
                mutators.change(data)
            }else{
                setInputValue('')
            }
        }
        window.addEventListener('message',handleData)
        return () =>{
            window.removeEventListener('message', handleData)
        }
    },[])
    useEffect(()=>{
        if(value){
            setInputValue(value.position)
        }
    },[value])
    return  editable?(
        <div style={{width:'300px'}}>
            <Input 
            value={inputValue}
            width={300}
            onClick={()=>window.open("rule-work.html",'','height=350, width=420, top=100,left=100, toolbar=no, menubar=no, scrollbars=no, resizable=no,location=no, status=no','_self')}/>
        </div>
    ):(<PreviewText value={value && value.position} />)
}
export default SelectPosition