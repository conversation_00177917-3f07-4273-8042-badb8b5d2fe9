import React ,{ useState, useEffect }from 'react'
import { Button } from 'antd'
import {css,cx} from 'emotion'
const SelectRule = (itemProps)=> {
    const { value, mutators, props, editable, schema } = itemProps
    const [ruleData,setRuleData] = useState([]) 
    const xComponentProps = schema['x-component-props'] || {}
    const {name} = xComponentProps
    const  selectRule = () =>{
        let businessType
        if(name === 'draft'){
            businessType ='revise'
        }else if(name === 'revise'){
            businessType ='abolish'
        }
        const reUrl = encodeURIComponent(`${window.location.protocol}//${window.location.hostname}:${window.location.port}/extra-forms/dataProxy.html?1=1`)
        window.open(`http://unify1.gx.cmcc:10025/regulation/regulationAction.do?method=requireAbolishRegulation&startIndex=0&reportType=htmlReport&reUrl=${reUrl}&businessType=${businessType}`,'','height=800, width=800, top=100,left=100, toolbar=no, menubar=no, scrollbars=no, resizable=no,location=no, status=no','_self')
    }

    useEffect(() => {
        const getProxyData = (e) => {
            const { data: { type, data }} = e || {}

            if (type === 'GET_REGULATION_DATA') {
                const returnStr=JSON.parse(unescape(data))
                const {items} = returnStr
                const ruleArr = items.map(item=>{
                    return {
                        name:`${item.title}${item.code}` ,
                    }
                })
                setRuleData(ruleData.concat(ruleArr))
                mutators.change(ruleData.concat(ruleArr))
            }
        }

        window.addEventListener('message', getProxyData)
        
        return () => {
            window.removeEventListener('message', getProxyData)
        }
    }, [])
    useEffect(()=>{
        if(value){
            setRuleData(value)
        }
    },[value])

    return editable?(
        <div>
        <div>
            <Button onClick={selectRule}>选择</Button>
        </div>
        {
            ruleData&& ruleData.map((item,index)=>{
                return <div key={index} className={css`line-height:30px`}>{item.name}</div>
            })
        }
        </div>
    ):(
       <div>
            {
                ruleData.length!==0?
            ruleData&& ruleData.map((item,index)=>{
                return <div key={index}  className={css`line-height:30px`}>{item.name}</div>
            }):(<div>暂无数据</div>)
        }
       </div>
    )
}


export default SelectRule