import React,{ useState ,useEffect} from 'react'
import { Upload,Button ,Modal,Dropdown,Menu,Input} from 'antd'
import { cx,css } from 'emotion'
import { UPLOAD_URL } from 'ROOT/constants'
import {getFileSize,suffix,downloadFile} from 'ROOT/utils'
import {upLoadWrap,modal} from './style.js'

const iconAdd = require('ROOT/assets/images/icon-add.png')
const bodyfileImg = require('ROOT/assets/images/bodyfile.png')
const more = require('ROOT/assets/images/more.png')

const iconImg = () =>{
    return <img src={iconAdd} alt="" style={{width:'14px',height:'14px',marginTop:'-3px',marginRight:'8px'}}/>
}

 const UploadEnclousre = (itemProps)=> {
    const { value, mutators, props, editable, schema } = itemProps
    const xComponentProps = schema['x-component-props'] || {}
    const { typeName }  = xComponentProps
    const [fileList,setFileList] =useState([])
    const [showReName, setShowReName] = useState(false) // 进行重命名
    const [name, setName] = useState('')
    const [uid, setUid] = useState('')
    const itemRender = (originNode, file, currFileList) => {
        const {size, name,uid} =file
        const realSize =getFileSize(size)
        return (<div className='item_Render' style={{marginTop:'16px'}}>
            <div className={css`display:flex; align-items:center;`}>
         <img src={bodyfileImg} alt="" className={css`width:25px;height:34px`} />
            <div className={css`display:flex;flex-direction: column;margin-left:14px;`}>
                <div className={css`font-family: PingFangSC-Regular;font-size: 14px;color: #333333;`}>{name}</div>
                <div className={css`font-family: PingFangSC-Regular;font-size: 12px;color: #999999;`}>{realSize}</div>
            </div>
            </div>
            <div className={css`display:flex`}>
            <Dropdown overlay={menu(file)} placement="bottomLeft" className="drop-down">
            <img
              src={more}
              alt=""
              className={css`
                width: 20px;
                height: auto;
              `}
            />
          </Dropdown>
            <div className={css`cursor:pointer;margin-left: 14px;`} onClick={()=>{deleteFile(uid)}}>X</div>
            </div>
        </div>)
    }
    const menu = file => {
    const { name, uid, url } = file
    const realName = name.substring(0, name.lastIndexOf('.'))
    return (
      <Menu className="menu">
        <Menu.Item
          onClick={() => {
            setShowReName(true)
            setName(realName)
            setUid(uid)
          }}
          key="1"
        >
          <div>重命名</div>
        </Menu.Item>
        <Menu.Item
          key="2"
          onClick={() => {
            downloadFile(url, name)
          }}
        >
          <div>下载</div>
        </Menu.Item>
      </Menu>
    )
  }
    const deleteFile = (uid) =>{
        Modal.confirm({
            title: '确定要删除该文件吗?',
            okText: '确定',
            cancelText: '取消',
            onOk() {
                const newFileList = fileList.filter((item)=>item.uid !== uid)
                setFileList(newFileList)
                mutators.change(newFileList)
            },
            onCancel() {
              console.log('Cancel')
            },
          })
      
    }
    const onChange= (info) => {
        let fileList = [...info.fileList]
        fileList = fileList.map(file => {
      if (file.response) {
        file.url = file.response.fileUrl
      }
      return file
    })
    setFileList(fileList)
    mutators.change(fileList)
    }
    const handleRename = e => {
    setName(e.target.value)
  }
  // 确认name
  const handleChangeName = () => {
    setShowReName(false)
    // eslint-disable-next-line array-callback-return
    const newFileList = fileList.map(item => {
      if (item.uid === uid) {
        const sufix = suffix(item.name)

        return {
          ...item,
          name: `${name}${sufix}`,
        }
      }
      return item
    })
    setFileList(newFileList)
    mutators.change(newFileList)
  }
  // 取消重命名
  const handleModalNameCancel = () => {
    setShowReName(false)
  }
    useEffect(() => {
		setFileList(value)
	}, [value])
	
    return (
        editable?( <div className={cx(upLoadWrap)}>
        <Upload
          itemRender={itemRender}
          action={UPLOAD_URL}
          onChange={onChange}
          fileList={fileList}
      >
          <div className={css`display:flex`}>
          <Button icon={iconImg()} >{typeName === 'information'?'上传文件':'添加附件'}</Button>
          <div className='upload-content' onClick={(e)=>e.stopPropagation()}>格式不限，最多9份</div>
          </div>
      </Upload>
      <Modal
        title="重命名"
        visible={showReName}
        className={cx(modal)}
        onCancel={handleModalNameCancel}
        onOk={handleChangeName}
      >
        <Input value={name} onChange={handleRename} />
      </Modal>
  </div>):(
    <div className={cx(upLoadWrap)}>
          {
            value?
          value && value.map((item,index)=>{
              return (
                <div className={cx({item_Render:true,mt16:index!==0})} key={index}>
            <div className={css`display:flex; align-items:center;`}>
         <img src={bodyfileImg} alt="" className={css`width:25px;height:34px`} />
            <div className={css`display:flex;flex-direction: column;margin-left:14px;`}>
                <div className={css`font-family: PingFangSC-Regular;font-size: 14px;color: #333333;`}>{item.name}</div>
                <div className={css`font-family: PingFangSC-Regular;font-size: 12px;color: #999999;`}>{getFileSize(item.size)}</div>
            </div>
            </div>
            <div className={css`display:flex`}>
            <Dropdown overlay={menu(item)} placement="bottomLeft" className="drop-down">
            <img
              src={more}
              alt=""
              className={css`
                width: 20px;
                height: auto;
              `}
            />
          </Dropdown>
          <Modal
        title="重命名"
        visible={showReName}
        className={cx(modal)}
        onCancel={handleModalNameCancel}
        onOk={handleChangeName}
      >
        <Input value={name} onChange={handleRename} />
      </Modal>
            {/* <div className={css`cursor:pointer;margin-left: 14px;`} onClick={()=>{deleteFile(uid)}}>X</div> */}
            </div>
        </div>
              )
          }):(
					<div>暂无数据</div>
				)
      }
    </div>
  )
       
    )
}


export default UploadEnclousre