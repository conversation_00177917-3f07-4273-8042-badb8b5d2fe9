import {css } from 'emotion'

export const upLoadWrap = css`
    .item_Render{
        display:flex;
        width: 560px;
        height: 52px;
        background: #F7F8F9;
        border: 1px solid #E9ECF0;
        border-radius: 2px;
        display:flex;
        justify-content:space-between;
        padding:0 16px;
        align-items:center;
    }
    .upload-content{
        height: 20px;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #959BA3;
        margin-left:16px;
        line-height:32px;
    }
    .mt16{
        margin-top: 16px;
    }
`

export const modal =css`
.template{
    margin: 10px auto;
    height: 54px;
    line-height: 54px;
    font-size: 14px;
    border-radius: 4px;
    background: #f7f8f9;
    color: #333;
    text-align: center;
    border: 1px solid #d9d9d9;
    overflow: hidden;
    text-overflow: ellipsis;
    -ms-text-overflow: ellipsis;
    white-space: nowrap;
    word-break: keep-all;
    &:hover{
        border: 1px solid red;
        cursor:pointer;
    }
}
`