import React, { useState, useEffect } from 'react'
import { Upload, Button, Modal, Menu, Dropdown, message, Input, Progress } from 'antd'
import { UPLOAD_URL } from 'ROOT/constants'
import service from 'ROOT/service'
import { cx, css } from 'emotion'
import { suffix, downloadFile } from 'ROOT/utils'
import { outWpsEdit, outTaohong } from 'ROOT/utils/wps'
import PdfPreviewModal from 'ROOT/components/PdfPreview/PdfPreviewModal'
import { itemRenderStyle, modal } from './style.js'

const iconAdd = require('ROOT/assets/images/icon-add.png')

const iconImg = () => {
  return (
    <img
      src={iconAdd}
      alt=""
      style={{ width: '14px', height: '14px', marginTop: '-3px', marginRight: '8px' }}
    />
  )
}
const more = require('ROOT/assets/images/more.png')
const bodyfileImg = require('ROOT/assets/images/bodyfile.png')

const UpLoadFile = itemProps => {
  const { value, mutators, props, editable, schema, form } = itemProps
  const xComponentProps = schema['x-component-props'] || {}
  const { orgId, reportId, type, config } = xComponentProps
  const [showTemplate, setShowTemplate] = useState(false) // 选择模板弹窗、
  const [showTaohong, setShowTaohong] = useState(false) // 套红模板弹窗
  const [showReName, setShowReName] = useState(false) // 进行重命名
  const [previewShow, setPreviewShow] = useState(false) // 预览
  const [templateData, setTemplateData] = useState([])
  const [taoHongData, setTaoHongData] = useState([])
  const [finish, setFinish] = useState(false)
  const [fileUrl, setFileUrl] = useState('')
  const [name, setName] = useState('')
  const [uid, setUid] = useState('')
  const [fileList, setFileList] = useState([])
  const [taohongIndex, setTaohongIndex] = useState(Number)
  const itemRender = (originNode, file, currFileList) => {
    const { name, percent, uploadFinish, response } = file
    const index = currFileList.findIndex(item => item.name === file.name)
    let per
    if (percent === 100) {
      per = ((100 / 110) * 100).toFixed(1)
    }
    if ((percent === 100 && finish) || uploadFinish) {
      per = 100
    }
    return (
      <div
        className="item_Render"
        onClick={() => handlePreview(file)}
        style={{ marginTop: '16px' }}
      >
        <div className="item-left">
          <img
            src={bodyfileImg}
            alt=""
            className={css`
              width: 25px;
              height: 34px;
            `}
          />
          <div
            style={{ width: 300 }}
            className={css`
              display: flex;
              flex-direction: column;
              margin-left: 16px;
            `}
          >
            <div className="itemRender_name">{name}</div>
            <Progress percent={per} style={{ width: 300 }} />
          </div>
        </div>

        <div
          className={css`
            display: flex;
          `}
          onClick={e => e.stopPropagation()}
        >
          <div
            className={css`
              margin-right: 10px;
              cursor: pointer;
            `}
            onClick={e => {
              handleEdit(e, file, index)
            }}
          >
            编辑
          </div>
          <div
            className={css`
              margin-right: 10px;
              cursor: pointer;
            `}
            onClick={e => {
              handleTaohong(e, file, index)
            }}
          >
            套红
          </div>
          <Dropdown overlay={menu(file)} placement="bottomLeft" className="drop-down">
            <img
              src={more}
              alt=""
              className={css`
                width: 20px;
                height: auto;
              `}
            />
          </Dropdown>
        </div>
      </div>
    )
  }
  const menu = file => {
    const { name, uid, url } = file
    const realName = name.substring(0, name.lastIndexOf('.'))
    return (
      <Menu className="menu">
        <Menu.Item
          onClick={() => {
            setShowReName(true)
            setName(realName)
            setUid(uid)
          }}
          key="1"
        >
          <div>重命名</div>
        </Menu.Item>
        <Menu.Item key="2" onClick={() => handlePreview(file)}>
          <div>预览</div>
        </Menu.Item>
        <Menu.Item
          key="3"
          onClick={() => {
            downloadFile(url, name)
          }}
        >
          <div>下载</div>
        </Menu.Item>
        {editable && (
          <Menu.Item key="4">
            <div onClick={() => deleteFile(uid)}>删除</div>
          </Menu.Item>
        )}
      </Menu>
    )
  }
  const handleTaohong = (e, file, index) => {
    e.stopPropagation()
    setShowTaohong(true)
    setTaohongIndex(index)
    service.getDocumentBodyFileList({ orgId, type: 2 }).then(res => {
      const { bodyFiles } = res.data
      const templateData = bodyFiles.map(item => {
        const data = JSON.parse(item.fileInfo)
        return {
          ...item,
          name: data.title,
          url: data.fileUrl,
          uid: data.fileUrl,
          type: 'application/docx',
        }
      })
      setTaoHongData(templateData)
    })
  }
  const selectTaoHong = item => {
    outTaohong({
      ruleType: type,
      config,
      tplUrl: item.url,
      index: taohongIndex,
      reportId,
      fileList,
      isNew: false,
      formData: form.getFormState().values,
      callback: (reportId, fileList) => {
        service
          .getFormData({
            reportId,
          })
          .then(res => {
            const {
              data: { fileList },
            } = res.data
            setFileList(fileList)
            mutators.change(fileList)
          })
      },
      orgId,
    })
    setShowTaohong(false)
  }
  const handleEdit = (e, file, index) => {
    e.stopPropagation()
    outWpsEdit({
      ruleType: type,
      config,
      fileList,
      index,
      reportId,
      isNew: false,
      formData: form.getFormState().values,
      callback: (reportId, fileList) => {
        service
          .getFormData({
            reportId,
          })
          .then(res => {
            const {
              data: { fileList },
            } = res.data
            setFileList(fileList)
            mutators.change(fileList)
          })
      },
      orgId,
    })
  }
  const handlePreview = file => {
    const { url, pdfUrl } = file || {}
    setPreviewShow(true)
    setFileUrl(pdfUrl || url)
  }
  const deleteFile = uid => {
    Modal.confirm({
      title: '确定要删除该文件吗?',
      okText: '确定',
      cancelText: '取消',
      onOk() {
        const newFileList = fileList.filter(item => item.uid !== uid)
        setFileList(newFileList)
        mutators.change(newFileList)
      },
      onCancel() {
        console.log('Cancel')
      },
    })
  }

  const handlePdfFile = file => {
    return new Promise(resolve => {
      service.getPdfUrl({ fileName: file.name, orgId, srcUrl: file.response.fileUrl }).then(res => {
        const { descUrl } = res.data
        setFinish(true)
        resolve(descUrl)
      })
    })
  }

  const onChange = async info => {
    const fileList = [...info.fileList]
    const { file } = info
    let pdfUrl
    if (file.response) {
      const sufix = suffix(file.name)
      if (sufix !== '.pdf') {
        pdfUrl = await handlePdfFile(file)
      }
      file.url = file.response.fileUrl
      file.pdfUrl = pdfUrl
    }
    setFileList(fileList)
    mutators.change(fileList)
  }
  const selectTemplate = e => {
    e.stopPropagation()
    service.getDocumentBodyFileList({ orgId, type: 1 }).then(res => {
      const { bodyFiles } = res.data
      const templateData = bodyFiles.map(item => {
        const data = JSON.parse(item.fileInfo)
        return {
          ...item,
          name: data.title,
          url: data.fileUrl,
          uid: data.fileUrl,
          uploadFinish: true,
          type: 'application/docx',
        }
      })
      setTemplateData(templateData)
    })

    setShowTemplate(true)
  }
  const handleCancel = () => {
    setShowTemplate(false)
  }
  const handleTaohongCancel = () => {
    setShowTaohong(false)
  }
  // 上传前判断文件类型
  const beforeUpload = file => {
    const { name } = file
    if (!/\.(|word|docx|doc)$/.test(name)) {
      message.warning('请上传合法文件')
      return Upload.LIST_IGNORE
    }
  }
  // 重命名
  const handleRename = e => {
    setName(e.target.value)
  }
  // 确认name
  const handleChangeName = () => {
    setShowReName(false)
    // eslint-disable-next-line array-callback-return
    const newFileList = fileList.map(item => {
      if (item.uid === uid) {
        const sufix = suffix(item.name)

        return {
          ...item,
          name: `${name}${sufix}`,
        }
      }
      return item
    })
    setFileList(newFileList)
    mutators.change(newFileList)
  }
  // 取消重命名
  const handleModalNameCancel = () => {
    setShowReName(false)
  }
  // 确认模板
  const confirmTemplate = item => {
    const { name, url } = item
    service.getPdfUrl({ fileName: name, orgId, srcUrl: url }).then(res => {
      const { descUrl } = res.data
      const newItem = {
        ...item,
        url: descUrl,
      }
      const arr = [newItem]
      setFileList(arr)
      mutators.change(arr)
    })
    setShowTemplate(false)
  }
  // 关闭预览弹窗
  const handlePdfPreviewClose = () => {
    setPreviewShow(false)
  }
  // 唤起wps在线编辑
  const handleClickWpsEdit = e => {
    e.stopPropagation()
    outWpsEdit({
      ruleType: type,
      config,
      fileList,
      index: -1,
      reportId,
      isNew: false,
      formData: form.getFormState().values,
      callback: (reportId, fileList) => {
        service
          .getFormData({
            reportId,
          })
          .then(res => {
            const {
              data: { fileList },
            } = res.data
            setFileList(fileList)
            mutators.change(fileList)
          })
      },
      orgId,
    })
  }
  useEffect(() => {
    setFileList(value)
  }, [value])

  return editable ? (
    <div className={cx(itemRenderStyle)}>
      <div
        className={css`
          display: flex;
        `}
      >
        <Upload
          itemRender={itemRender}
          onChange={onChange}
          action={UPLOAD_URL}
          className="up-load"
          beforeUpload={beforeUpload}
          fileList={fileList}
          maxCount={1}
        >
          <div
            className={css`
              display: flex;
            `}
          >
            <Button onClick={handleClickWpsEdit}>在线编辑</Button>
            <Button
              onClick={selectTemplate}
              className={css`
                margin-left: 16px;
              `}
            >
              选择模板
            </Button>
          </div>
          <div className="upload-content" onClick={e => e.stopPropagation()}>
            提示:
            <div className='up_load_list'>1. 上传仅限word文件，最多1份</div>
            <div>2. 文件正文一般不超过2500字，签报正文一般不超过1500字；</div>
            <div>3.请严格控制附件的数量和篇幅。</div>
          </div>
        </Upload>
      </div>
      <Modal
        title="选择模板"
        visible={showTemplate}
        footer={null}
        className={cx(modal)}
        onCancel={handleCancel}
      >
        {templateData.map(item => {
          return (
            <div className="template" key={item.name} onClick={() => confirmTemplate(item)}>
              {item.name}
            </div>
          )
        })}
      </Modal>
      <Modal
        title="选择正文"
        visible={showTaohong}
        footer={null}
        className={cx(modal)}
        onCancel={handleTaohongCancel}
      >
        {taoHongData.map(item => {
          return (
            <div className="template" key={item.name} onClick={() => selectTaoHong(item)}>
              {item.name}
            </div>
          )
        })}
      </Modal>
      <Modal
        title="重命名"
        visible={showReName}
        className={cx(modal)}
        onCancel={handleModalNameCancel}
        onOk={handleChangeName}
      >
        <Input value={name} onChange={handleRename} />
      </Modal>
      {previewShow && (
        <PdfPreviewModal
          url={fileUrl}
          marks={[]}
          onClose={handlePdfPreviewClose}
          // extraBtns={this.getPreviewExtraBtns()}
        />
      )}
    </div>
  ) : (
    <div className={cx(itemRenderStyle)}>
      {value ? (
        value &&
        value.map((item, index) => {
          return (
            <div
              className={cx({ item_Render: true, mt16: index !== 0 })}
              onClick={() => handlePreview(item)}
              key={index}
            >
              <div className="item-left">
                <img
                  src={bodyfileImg}
                  alt=""
                  className={css`
                    width: 25px;
                    height: 34px;
                  `}
                />
                <div className="itemRender_name" style={{ marginLeft: '14px' }}>
                  {item.name}
                </div>
              </div>
              <div
                className={css`
                  display: flex;
                `}
                onClick={e => e.stopPropagation()}
              >
                {/* <div
                  className={css`
                    margin-right: 10px;
                    cursor: pointer;
                  `}
                  onClick={e => {
                    handleEdit(e, item,index)
                  }}
                >
                  编辑
                </div> */}
                <Dropdown overlay={menu(item)} placement="bottomLeft" className="drop-down">
                  <img
                    src={more}
                    alt=""
                    className={css`
                      width: 20px;
                      height: auto;
                    `}
                  />
                </Dropdown>
              </div>
            </div>
          )
        })
      ) : (
        <div>暂无数据</div>
      )}
      <Modal
        title="重命名"
        visible={showReName}
        className={cx(modal)}
        onCancel={handleModalNameCancel}
        onOk={handleChangeName}
      >
        <Input value={name} onChange={handleRename} />
      </Modal>
      {previewShow && (
        <PdfPreviewModal
          url={fileUrl}
          marks={[]}
          onClose={handlePdfPreviewClose}
          // extraBtns={this.getPreviewExtraBtns()}
        />
      )}
    </div>
  )
}

export default UpLoadFile
