import { css } from 'emotion'

export const itemRenderStyle = css`
    .item_Render{
        width: 560px;
        height: 52px;
        background: #F7F8F9;
        border: 1px solid #E9ECF0;
        border-radius: 2px;
        display:flex;
        justify-content:space-between;
        padding:0 16px;
        align-items:center;
      
        .item-left{
            display:flex;
            align-items:center;
            .itemRender_name{
                font-family: PingFangSC-Regular;
                font-size: 14px;
                color: #262A30;
                letter-spacing: 0;
                margin-top: 4px;
            }
        }
        .menu{
            .ant-dropdown-menu {
                margin:-8px !important;
            }
        }
    }
    .upload-content{
        height: 20px;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #FA5F5F;
        line-height:32px;
        margin-top: 16px;
        .up_load_list{
            font-family: PingFangSC-Regular;
            font-size: 13px;
            color: #FA5F5F;
            letter-spacing: 0;
        }
    }
    .mt16{
            margin-top: 16px;
        }
`

export const modal =css`
.template{
    margin: 10px auto;
    height: 54px;
    line-height: 54px;
    font-size: 14px;
    border-radius: 4px;
    background: #f7f8f9;
    color: #333;
    text-align: center;
    border: 1px solid #d9d9d9;
    overflow: hidden;
    text-overflow: ellipsis;
    -ms-text-overflow: ellipsis;
    white-space: nowrap;
    word-break: keep-all;
    &:hover{
        border: 1px solid red;
        cursor:pointer;
    }
}
`