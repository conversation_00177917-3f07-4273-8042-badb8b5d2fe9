import React ,{useState,useEffect}from 'react'
import {css,cx} from 'emotion'
import { Select,Input, message ,AutoComplete,Modal,Table} from 'antd'
import moment from 'moment' 
import {getSerialNumStrFromStr} from 'ROOT/utils'
import service from 'ROOT/service'

import { fileStyle } from './style.js'

const { Option } = Select
const  dateArr=[]
for(let i=2016;i<2070;i++){
    dateArr.push(i)
}

const FileNumber = (itemProps) =>{
    const { value={}, mutators, props, editable, schema } = itemProps
    const xComponentProps = schema['x-component-props'] || {}
    const {deptId,orgId,flowId} = xComponentProps
    console.log(deptId,orgId,flowId,'dddd')
    const [optionData,setOptionsData] = useState([])
    const [histories,setHistories] = useState([])
    const [isShow,setIsShow] = useState(false)
    const [isModalVisible,setIsModalVisible] = useState(false)
    const [showData,setShowData] = useState(false)
    const [fileNumberValue,setFileNumberValue] = useState({
      title:'',
      year:new Date().getFullYear(),
      number:'',
    })
    const [id,setId] = useState(Number)
    const handleChangeTitle = (value) =>{
      let showVisible =false
      optionData.forEach(item=>{
        if(item.value === value){
          showVisible = true
        }
      })
      setIsShow(showVisible)
      setFileNumberValue({
        ...fileNumberValue,
        title:value,
      })
        mutators.change({
          ...fileNumberValue,
            title:value,
        })
    }
    const handleChangeYear = (val) =>{
      setFileNumberValue({
        ...fileNumberValue,
        year:val,
      })
        mutators.change({
          ...fileNumberValue,
            year:val,
        })
    }
    const handleChangeNumber = (e) =>{
        const reg = /^\d{0,4}$/
        if(!reg.test(e.target.value)){
            message.error('输入不合法，请输入不超过4位的整数')
          }else{
            setFileNumberValue({
              ...fileNumberValue,
              number:e.target.value,
            })
            mutators.change({
                ...fileNumberValue,
                number:e.target.value,
            })
          }
       
      
    }
    const handleAutoNumber = () =>{
      service.getAutoNumber({id,orgId,year:fileNumberValue.year}).then(res=>{
        console.log(res,'aa')
        const { refNo } =res.data
        setFileNumberValue({
          ...fileNumberValue,
          number:refNo,
        })
        mutators.change({
          ...fileNumberValue,
          number:refNo,
        })
      })
    }
    const handleScanHistory = () =>{
      setIsModalVisible(true)
      service.getHistory({
        id,orgId,year:fileNumberValue.year,
      }).then(res=>{
        const {infos} =res.data
        setHistories(infos)
      })
    }
    const handleOk = () =>{
      setIsModalVisible(false)
    }
    const handleCancel = () => {
      setIsModalVisible(false)
    }
    const handleSelect = (value) =>{
      console.log(value,'val')
      let id
      optionData.forEach(item=>{
        if(item.value===value){
          id=item.id
        }
      })
      setId(id)
    }
    const getColumns = () => {
        return [
          {
            title: '时间',
            dataIndex: 'endTime',
            width: 200,
            render: (data) => {
              return !data ? '-' : moment(data).format('YYYY/MM/DD HH:mm')
            },
          },
          {
            title: '文号',
            width: 560,
            render: (data) => {
              return (
                <div>
                  <div>{getSerialNumStrFromStr(data.refNo)}</div>
                  <div>{data.title}</div>
                </div>
              )
            },
          },
        ]
    }
    useEffect(()=>{
      // eslint-disable-next-line no-unused-expressions
      flowId && deptId && orgId&& service.getNumberData({deptId,orgId,flowId}).then(res=>{
            const {refNos} = res.data || {}
            const dataArr = refNos.map(item=>{
              return {
                ...item,
                value:item.name,
              }
            })
            setOptionsData(dataArr)
        })
    },[deptId,orgId,flowId])
    useEffect(()=>{
      const error = Object.keys(value)
      if(error && error.length > 0){
        setShowData(true)
        setFileNumberValue(value)
      }
    },[value])
    return (
      editable? (
        <div>
        <div className={cx(fileStyle)}>
            <div>
          <AutoComplete
              style={{ width: 260 }}
              options={optionData}
              placeholder="发文机关代字"
              onSelect={handleSelect}
              onChange={handleChangeTitle}
              value={fileNumberValue.title}
          />
            </div>
            <span className={css`color: #cbcfd6;font-size: 16px;`}>&nbsp;〔 </span><Select placeholder='请选择' className='select' onChange={handleChangeYear} value={fileNumberValue.year}>
            {dateArr.map(date => (
          <Option key={date}>{date}</Option>
        ))}
            </Select> <span className={css`color: #cbcfd6;font-size: 16px;`}>&nbsp;〕</span>
            <Input type='number' placeholder='序号' width={160} className='sort'onChange={handleChangeNumber} 
              max={9999} min={0}
            value={fileNumberValue.number}/>
        </div>
      {
        isShow &&   <div className={css`display:flex;margin-top:14px;`}>
            <div onClick={handleAutoNumber} className={css`cursor: pointer;font-size: 14px;color: #2e58d6;`}>自动编号</div>
            <div onClick={handleScanHistory} className={css`cursor: pointer;font-size: 14px;color: #2e58d6;margin-left:14px`}>查看历史</div>
        </div>
      }
      {isModalVisible &&  <Modal title="历史公文" 
      width={800}
      visible={isModalVisible} onOk={handleOk} onCancel={handleCancel}>
       <Table   columns={getColumns()}
            dataSource={histories}
              pagination={false}
            />
      </Modal>}
        </div>
      ):<div className={css`display:flex`}>
        <span>{fileNumberValue.title||'暂无数据'}</span>
        {
          showData && <div>
          <span> 〔 </span> <span>{fileNumberValue.year}</span> <span> 〕</span>
        <span>{fileNumberValue.number}</span>号
          </div>
        }
      </div>
       
    )
}

export default FileNumber