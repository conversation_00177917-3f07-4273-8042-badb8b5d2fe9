import React, { useState, useEffect, useMemo } from 'react'
import { cx, css } from 'emotion'
import {
  SchemaForm,
  Schema<PERSON>arkupField as Field,
  createAsyncFormActions,
  createFormActions,
  FormEffectHooks,
} from '@formily/antd'
import { setup, Select } from '@formily/antd-components'
import { Buttons, Steps } from 'ROOT/components/Process'
import { Button } from 'antd'
import SelectDept from 'ROOT/components/Formily/SelectDept'
import SelectMember from 'ROOT/components/Formily/SelectMember'
import SelectDepartment from 'ROOT/components/Formily/SelectDepartment'
import DeptSelect from 'ROOT/components/Formily/commonDeptSelect'
import { getQueryString, closeTheWindow } from 'ROOT/utils'
import moment from 'moment'
import useMyInfo from 'ROOT/hooks/useMyInfo'
import service from 'ROOT/service'
import Dept from 'ROOT/components/Formily/Dept'
import Cookies from 'js-cookie'
import fileNumber from '../fileNumber'
import UploadEnclousre from '../UploadEnclousre'
import SelectRule from '../SelectRule'
import SelectPosition from '../SelectPosition'
import SelectProcessFile from '../SelectPorcessFile'
import { urgencyOptions, secretOptions, typeRule, ruleLevel } from './constant/index'
import UpFileLoad from '../Upload'
import { ruleContent } from './style.js'
import effects from './effect'
import { saveBtn } from '../module/button'

const { onFieldValueChange$ } = FormEffectHooks

const RuleContent = prop => {
  const { props, onFilechange, type, config } = prop
  SelectDept.isFieldComponent = true
  SelectMember.isFieldComponent = true
  UpFileLoad.isFieldComponent = true
  fileNumber.isFieldComponent = true
  UploadEnclousre.isFieldComponent = true
  SelectRule.isFieldComponent = true
  Dept.isFieldComponent = true
  SelectPosition.isFieldComponent = true
  SelectProcessFile.isFieldComponent = true
  SelectDepartment.isFieldComponent = true
  DeptSelect.isFieldComponent = true
  const [initValue, setInitValue] = useState()
  const [flowId, setFlowId] = useState('')
  const [orgId, setOrgId] = useState('')
  const [deptId, setDeptId] = useState('')
  const myInfo = useMyInfo({ isSetStorage: true })
  const [editable, setEditable] = useState(true)
  const actions = useMemo(() => createAsyncFormActions(), [])
  const { procKey, appId } = getQueryString(props.location.search)
  const [reportId, setReportId] = useState('')
  useEffect(() => {
    service
      .saveForm({
        type,
        classify: {
          name: '规章制度发文',
          englishName: 'rule_officical',
        },
        config,
        data: {},
      })
      .then(res => {
        const { data } = res
        actions.setFieldState('fileList', state => {
          state.props['x-component-props'].reportId = data
        })
        setReportId(data)
      })
  }, [])
  const commonCommit = async () => {
    const data = await actions.submit()
    const { loginUid } = myInfo
    const { drafterTime } = data.values || {}
    const formateData = {
      ...data.values,
      drafter_time: moment().endOf(drafterTime).valueOf(),
      loginUid,
      flowId,
      orgId,
      deptId,
    }
    console.log(formateData, 'aa')
    const res = await service.upDateForm({
      type,
      classify: {
        name: '规章制度发文',
        englishName: 'rule_officical',
      },
      config,
      data: formateData,
      reportId,
    })
    return res
  }
  const submitForm = async () => {
    const res = await commonCommit()
    const data = await actions.submit()
    if (res && res.success) {
      // return { id: +formId.current || res.data, values: values }
      return +reportId
    }
    throw new Error('保存出错')
  }

  const saveForm = async callback => {
    const res = commonCommit()
    if (res.success) {
      callback(res)
    }
  }

  const buttonGroup = () => {
    const array = [
      saveBtn(() => {
        saveForm(() => {
          // window.location.reload()
        })
      }),
    ]
    return array
  }
  const resetUserInfo = () => {
    const { loginName, loginOrgId, linkPath, loginMobile } = myInfo
    setOrgId(loginOrgId)
    actions.setFieldState('drafter', state => {
      state.value = `${loginName} (${loginMobile})`
    })
    actions.setFieldState('drafter_time', state => {
      state.value = moment().format('YYYY年MM月DD日')
    })

    actions.setFieldState('fileList', state => {
      state.props['x-component-props'].orgId = loginOrgId
    })

    actions.setFieldState('fileNumber', state => {
      state.props['x-component-props'].orgId = loginOrgId
      if (linkPath && linkPath.length > 0) {
        state.props['x-component-props'].deptId = linkPath[0].deptId
      }
    })
    actions.setFieldState('_dept', state => {
      state.props.enum =
        linkPath && linkPath.length > 0
          ? linkPath.map(item => ({
              value: item.deptId,
              label: item.linkPath,
            }))
          : []
      if (linkPath && linkPath.length > 0) {
        state.value = {
          value: linkPath[0].deptId,
          label: linkPath[0].linkPath,
        }
        setDeptId(linkPath[0].deptId)
      }
    })
  }
  const useManyEffects = () => {
    onFieldValueChange$('fileList').subscribe(({ value = [] }) => {
      onFilechange(value)
    })
    onFieldValueChange$('rule-type').subscribe(({ value = [] }) => {
      if (value === 1) {
        actions.setFieldState('absort-rule', state => {
          state.props['x-component-props'].name = 'draft'
        })
      } else if (value === 2) {
        actions.setFieldState('absort-rule', state => {
          state.props['x-component-props'].name = 'revise'
        })
      }
    })
    onFieldValueChange$('_dept').subscribe(({ value = [] }) => {
      // console.log(value,'value')
      // actions.setFieldState('fileNumber',state=>{
      // 	state.props['x-component-props'].deptId = value.value
      // })
      // actions.setFieldState('process-file',state=>{
      // 	state.props['x-component-props'].dept = value
      //   })
    })
  }

  const finishSubmit = () => {
    closeTheWindow()
  }
  const onMount = ({ access, editMode }) => {
    if (access && Object.keys(access).length > 0 && editMode && editMode.includes('web')) {
      Object.keys(access).forEach(key => {
        actions.setFieldState(key, state => {
          switch (access[key]) {
            case 'NONE':
              state.display = false
              break
            case 'READ':
              state.editable = false
              break
            case 'WRITE':
              state.editable = true
              state.display = true
              break
            default:
              break
          }
        })
      })
    } else {
      // actions.setFormState((state) => {
      //   state.editable = false
      // })
    }
  }
  useEffect(() => {
    if (Object.keys(myInfo).length > 0) {
      resetUserInfo()
    }
  }, [myInfo])
  useEffect(() => {
    const { loginOrgId } = myInfo
    if (loginOrgId) {
      service.getFlowID({ orgId: loginOrgId, type: 50, name: config.title }).then(res => {
        const { flows } = res.data || {}
        actions.setFieldState('fileNumber', state => {
          if (flows && flows.length > 0) {
            state.props['x-component-props'].flowId = flows[0].flowId
            setFlowId(flows[0].flowId)
          }
        })
      })
    }
  }, [myInfo])
  setup()

  return (
    <div className={cx(ruleContent)}>
      <SchemaForm
        components={{
          UpFileLoad,
          fileNumber,
          UploadEnclousre,
          SelectDept,
          SelectMember,
          Select,
          SelectRule,
          Dept,
          SelectPosition,
          SelectProcessFile,
          SelectDepartment,
          DeptSelect,
        }}
        effects={() => {
          useManyEffects()
        }}
        actions={actions}
        labelCol={8}
        previewPlaceholder="暂无数据"
        labelAlign="right"
      >
        <Field
          name="_dept"
          title="拟稿部门"
          x-component="Dept"
          editable={false}
          x-component-props={{ labelInValue: true, isWidth: true }}
          x-props={{
            style: {
              width: 560,
            },
          }}
          x-rules={[
            {
              validator: val => {
                if (!val.value) {
                  return '请选择拟稿部门'
                }
              },
            },
          ]}
        />
        <Field
          name="drafter"
          title="拟稿人"
          type="string"
          editable={false}
          x-props={{
            style: {
              width: 200,
            },
          }}
        />
        <Field
          name="drafter_time"
          title="拟稿时间"
          type="string"
          editable={false}
          x-props={{
            style: {
              width: 200,
            },
          }}
        />
        <Field
          name="title"
          title="标题"
          type="string"
          required
          x-props={{
            style: {
              width: 560,
            },
          }}
        />

        <Field
          name="fileNumber"
          title="文件编号"
          x-component="fileNumber"
          editable={false}
          x-component-props={{
            orgId: '',
            deptId: '',
            flowId: '',
          }}
        />

        <Field
          name="_taskLevel"
          title="缓急"
          type="string"
          enum={urgencyOptions}
          x-props={{
            style: {
              width: 200,
            },
          }}
        />
        <Field
          name="secret-degree"
          title="密级"
          x-props={{
            style: {
              width: 200,
            },
          }}
          type="string"
          enum={secretOptions}
        />

        {/* <Field
				name="secret-date"
				title="保密期限"
				type="date"
				x-props={{
					style: {
						width: 200,
					},
				}}
				x-component-props={{
					placeholder: '请选择日期',
				}}
			/> */}
        <Field
          name="z-give"
          title="主送"
          x-component="DeptSelect"
          x-component-props={{
            placeholder: '请选择',
            maxCount: 1000,
            defaultChooseSelf: false,
          }}
        />

        <Field
          name="c-give"
          title="抄送"
          x-component="DeptSelect"
          x-component-props={{
            placeholder: '请选择',
            maxCount: 1000,
            defaultChooseSelf: false,
          }}
        />

        <Field
          name="counter-sign"
          title="会签部门"
          x-component="DeptSelect"
          x-component-props={{
            placeholder: '请选择',
            maxCount: 1000,
            defaultChooseSelf: false,
          }}
        />

        <Field
          name="fileList"
          title="正文"
          x-component="UpFileLoad"
          x-component-props={{
            orgId: '',
            reportId: null,
            type,
            config,
          }}
        />

        <Field name="enclosure" title="附件" x-component="UploadEnclousre" />
        <Field
          name="reference_information"
          title="参考信息"
          x-component="UploadEnclousre"
          x-component-props={{ typeName: 'information' }}
        />

        <Field
          name="remarks"
          title="备注"
          type="textarea"
          x-props={{
            className: 'text_area',
          }}
        />

        <Field
          name="rule-name"
          title="规章制度名称"
          type="string"
          x-props={{
            style: {
              width: 560,
            },
          }}
        />

        <Field
          name="rule-type"
          title="制度审批类型"
          type="radio"
          enum={typeRule}
          x-linkages={[
            {
              type: 'value:visible',
              target: '*(absort-rule)',
              condition: `{{$self.value === 3||$self.value===2}}`,
            },
          ]}
        />

        <Field name="rule-level" title="规章制度级别" type="radio" enum={ruleLevel} />
        <Field
          name="rule-important"
          title="是否三重一大事项"
          type="radio"
          enum={[
            { label: '是', value: 1 },
            { label: '否', value: 2 },
          ]}
          x-linkages={[
            {
              type: 'value:visible',
              target: '*(meeting-organ)',
              condition: `{{$self.value === 1}}`,
            },
          ]}
        />
        <Field
          name="meeting-organ"
          title="上会机构"
          type="radio"
          enum={[
            { label: '董事会', value: 1 },
            { label: '党委会', value: 2 },
            { label: '总经理办公会', value: 3 },
            { label: '其他', value: 4 },
          ]}
        />
        <Field
          name="process-file"
          title="流程附件"
          x-component="SelectProcessFile"
          x-component-props={{
            reportId,
            myInfo,
            dept: {},
          }}
        />
        <Field name="application-position" title="适用岗位" x-component="SelectPosition" />
        <Field
          name="absort-rule"
          title="需要废止的原制度"
          x-component="SelectRule"
          x-component-props={{
            name: '',
          }}
        />
      </SchemaForm>

      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          position: 'fixed',
          left: 0,
          bottom: 0,
          height: '60px',
          width: '100%',
          zIndex: '9999',
        }}
        className="button"
      >
        {editable && (
          <Buttons
            procKey={procKey}
            onFinish={finishSubmit}
            appId={appId}
            onMount={onMount}
            onSubmit={submitForm}
            onSubmitOpen={async () => {
              const data = await actions.submit()
              return data.values
            }}
            extra={buttonGroup()}
          />
        )}

        {/* <Button onClick={() => submitForm()}>提交</Button> */}
      </div>
    </div>
  )
}

export default RuleContent
