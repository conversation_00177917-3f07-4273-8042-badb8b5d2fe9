import React, { useState, useEffect, useMemo, createRef } from 'react'
import { cx, css } from 'emotion'
import {
  SchemaForm,
  SchemaMarkupField as Field,
  createAsyncFormActions,
  createFormActions,
  FormEffectHooks,
} from '@formily/antd'
import { setup, Select } from '@formily/antd-components'
import { Buttons, Steps } from 'ROOT/components/Process'
import { Button, Tabs, message } from 'antd'
import Cookies from 'js-cookie'
import moment from 'moment'
import SelectDept from 'ROOT/components/Formily/SelectDept'
import NewDistribute from 'ROOT/components/NewDistribute/pureIndex.jsx'
import SecretDistribute from 'ROOT/components/SecretDistribute/pureIndex'
import EasyDistribute from 'ROOT/components/EasyDistribute/pureIndex.jsx'
import Dept from 'ROOT/components/Formily/Dept'
import SelectMember from 'ROOT/components/Formily/SelectMember'
import { getQueryString, FIELD_TYPE, closeTheWindow } from 'ROOT/utils'
import useMyInfo from 'ROOT/hooks/useMyInfo'
import service from 'ROOT/service'
import SelectDepartment from 'ROOT/components/Formily/SelectDepartment'
import _, { isEmpty } from 'lodash'
import { patchData } from 'ROOT/utils/index'
import fileNumber from '../fileNumber'
import UploadEnclousre from '../UploadEnclousre'
import SelectRule from '../SelectRule'
import SelectPosition from '../SelectPosition'
import SelectProcessFile from '../SelectPorcessFile'
import { urgencyOptions, secretOptions, typeRule, ruleLevel } from './constant/index'
import UpFileLoad from '../Upload'
import { ruleContent } from './style.js'
import effects from './effect'
import { saveBtn } from '../module/button'


const { onFieldValueChange$ } = FormEffectHooks
const { TabPane } = Tabs

const RuleContent = prop => {
  const { props, onFilechange, initValue, type, config, showButton } = prop
  const [formateData, setFormateData] = useState({})
  SelectDept.isFieldComponent = true
  SelectMember.isFieldComponent = true
  UpFileLoad.isFieldComponent = true
  fileNumber.isFieldComponent = true
  UploadEnclousre.isFieldComponent = true
  SelectRule.isFieldComponent = true
  Dept.isFieldComponent = true
  SelectPosition.isFieldComponent = true
  SelectProcessFile.isFieldComponent = true
  SelectDepartment.isFieldComponent = true
  const myInfo = useMyInfo({ isSetStorage: true })
  const [editable, setEditable] = useState(true)
  const [flowId, setFlowId] = useState('')
  const [orgId, setOrgId] = useState('')
  const [levelCodeNodes, setLevelCodeNodes] = useState([])
  const [userLabelCodes, setUserLabelCodes] = useState([])
  const [secertLevel, setSeretLevel] = useState(Number)
  const [isDistributeShow, setIsDistributeShow] = useState(false) // 分发
  const [isEasyDistributeShow, setIsEasyDistributeShow] = useState(false) // 一键分发
  const [isSendSecretFileShow, setIsSendSecretFileShow] = useState(false) // 密级分发
  const [sendOrgId, setSendOrgid] = useState('') // 分发
  const [sendDeptId, setSendDeptId] = useState('') // 分发deptid
  const [sendUid, setSendUid] = useState('') // 分发uid
  const [easyDistributeLeaderList, setEasyDistributeLeaderList] = useState([]) // 分发列表
  const [showDistribute, setShowDistribute] = useState(false)
  const [expressions, setExpressions] = useState([])
  const [deptId, setDeptId] = useState('')
  const [loginUid, setLoginUid] = useState('')
  const actions = useMemo(() => createAsyncFormActions(), [])
  const [draftValue, setDraftValue] = useState({})
  const { userTaskId, procFormDataKey } = getQueryString(props.location.search)

  const commonCommit = async () => {
    const data = await actions.submit()
    console.log(data, 'data')
    const result = patchData(data.values, draftValue && Object.keys(draftValue).length > 0 ? draftValue : formateData)
    const res = await service.upDateForm({
      type,
      classify: {
        name: '规章制度发文',
        englishName: 'rule_officical',
      },
      config,
      data: result,
      reportId: procFormDataKey,
    })
    return res
  }
  const submitForm = async () => {
    const res = await commonCommit()
    // const data = await actions.submit()
    if (res.success) {
      // return { id: procFormDataKey || res.data, values: values }
      return Promise.resolve()
    }
    throw new Error('保存出错')
  }

  const saveForm = async callback => {
    const res = commonCommit()
    if (res.success) {
      callback(res)
    }
  }

  const buttonGroup = () => {
    const array = [
      saveBtn(() => {
        saveForm(() => {
          // window.location.reload()
        })
      }),
    ]
    return array
  }
  // 分发逻辑
  const handleHideDistribute = () => {
    setIsDistributeShow(false)
  }
  const handleShowDistribute = () => {
    const secretClass = _.get(initValue, 'secret-degree')
    if (secretClass > 3) {
      setIsSendSecretFileShow(true)
    } else {
      setIsDistributeShow(true)
    }

  }
  const handleHideSendSecretFile = () => {
    setIsSendSecretFileShow(false)
  }
  const handleOkDistribute = async (sendData) => {
    await submitUserToSendDocument(sendData)
    setIsDistributeShow(false)
    setIsSendSecretFileShow(false)
  }
  const submitUserToSendDocument = (sendData) => {
    const sendFields = [
      {
        fieldId: 0,
        fieldName: 'title',
        aliasName: '标题',
        isFill: true,
        fillSteps: [],
        property:
          '{"disabledt":true,"type":"input","max":200,"isSetFlow":true,"disabled":true,"options":[],"format":"","inputType":1,"refNoList":[],"flowValue":1,"timeStamp":""}',
        value: '',
        customName: '',
        extraValue: '',
      },
      {
        fieldId: 0,
        fieldName: 'refNo',
        aliasName: '发文文号',
        isFill: false,
        fillSteps: [],
        property:
          '{"disabledt":true,"type":"refno","isSetFlow":true,"flowValue":1,"options":[],"disabled":false,"format":"","inputType":0,"max":0,"refNoList":[],"timeStamp":1636552253824,"automatic":false}',
        value: '',
        customName: '',
        extraValue: '',
      },
      {
        fieldId: 0,
        fieldName: 'urgencyLevel',
        aliasName: '紧急程度',
        isFill: false,
        fillSteps: [],
        property:
          '{"disabledt":true,"type":"dropDown","max":0,"isSetFlow":true,"disabled":false,"options":[{"text":"特急","value":1},{"text":"紧急","value":2},{"text":"常规","value":3}],"format":"","inputType":0,"refNoList":[],"flowValue":1,"timeStamp":1636528817633}',
        value: '',
        customName: '',
        extraValue: '',
      },
    ]
    sendFields.forEach((item) => {
      if (item.fieldName === FIELD_TYPE.TITLE) {
        item.value = _.get(initValue, 'title', '')
      }
      if (item.fieldName === FIELD_TYPE.REF_NO) {
        const ref = []
        ref[0] = _.get(initValue, 'fileNumber.title', '')
        ref[1] = _.get(initValue, 'fileNumber.year', '')
        ref[2] = _.get(initValue, 'fileNumber.number', '')
      }
      if (item.fieldName === FIELD_TYPE.URGENCY_LEVEL) {
        item.value = _.get(initValue, '_taskLevel', '')
      }
    })
    const checkFileParams = {
      orgId: sendOrgId,
      type: 50,
      bizId: `procFormDataKey=${procFormDataKey}&userTaskId=${userTaskId}`,
      flowId,
    }
    service.getDocumentIdByBusiness(checkFileParams).then((res) => {
      if (res.id <= 0) {
        const fileItem = {
          orgId: sendOrgId,
          info: {
            type: 50,
            createName: _.get(initValue, 'drafter', '').split('(')[0].trim(),
            createUid: _.get(initValue, 'loginUid', ''),
            fields: sendFields,
            createTime: _.get(initValue, 'drafter_time', 0),
            endTime: 0,
            deptId: _.get(initValue, '_dept.value', 0),
            bizId: `procFormDataKey=${procFormDataKey}&userTaskId=${userTaskId}`,
            flowId,
            deptName: _.get(initValue, '_dept.label', 0),
          },
        }

        service.createDocumentByBusiness(fileItem).then((res2) => {
          const sendparam1 = {
            orgId: sendOrgId,
            id: res2.id,
            sendInfo: {
              hostUsers: sendData.hostUsers,
              coUsers: sendData.coUsers,
              knowUsers: sendData.knowUsers,
              opinion: sendData.opinion,
            },
            isSendSms: sendData.isSendSms,
          }
          service.sendDocument(sendparam1).then(() => {
            // setIsEasyDistributeShow(false)
          })
        })
      }
      if (res.id > 0) {
        const sendparam2 = {
          orgId: sendOrgId,
          id: res.id,
          sendInfo: {
            hostUsers: sendData.hostUsers,
            coUsers: sendData.coUsers,
            knowUsers: sendData.knowUsers,
            opinion: sendData.opinion,
          },
          isSendSms: sendData.isSendSms,
        }
        service.sendDocument(sendparam2).then(() => {
          // setIsEasyDistributeShow(false)
        })
      }
    })
  }
  // 一键分发逻辑
  const handleOkEasyDistribute = (hostUsers) => {
    const sendFields = [
      {
        fieldId: 0,
        fieldName: 'title',
        aliasName: '标题',
        isFill: true,
        fillSteps: [],
        property:
          '{"disabledt":true,"type":"input","max":200,"isSetFlow":true,"disabled":true,"options":[],"format":"","inputType":1,"refNoList":[],"flowValue":1,"timeStamp":""}',
        value: '',
        customName: '',
        extraValue: '',
      },
      {
        fieldId: 0,
        fieldName: 'refNo',
        aliasName: '发文文号',
        isFill: false,
        fillSteps: [],
        property:
          '{"disabledt":true,"type":"refno","isSetFlow":true,"flowValue":1,"options":[],"disabled":false,"format":"","inputType":0,"max":0,"refNoList":[],"timeStamp":1636552253824,"automatic":false}',
        value: '',
        customName: '',
        extraValue: '',
      },
      {
        fieldId: 0,
        fieldName: 'urgencyLevel',
        aliasName: '紧急程度',
        isFill: false,
        fillSteps: [],
        property:
          '{"disabledt":true,"type":"dropDown","max":0,"isSetFlow":true,"disabled":false,"options":[{"text":"特急","value":1},{"text":"紧急","value":2},{"text":"常规","value":3}],"format":"","inputType":0,"refNoList":[],"flowValue":1,"timeStamp":1636528817633}',
        value: '',
        customName: '',
        extraValue: '',
      },
    ]
    sendFields.forEach((item) => {
      if (item.fieldName === FIELD_TYPE.TITLE) {
        item.value = _.get(initValue, 'title', '')
      }
      if (item.fieldName === FIELD_TYPE.REF_NO) {
        const ref = []
        ref[0] = _.get(initValue, 'fileNumber.title', '')
        ref[1] = _.get(initValue, 'fileNumber.year', '')
        ref[2] = _.get(initValue, 'fileNumber.number', '')
      }
      if (item.fieldName === FIELD_TYPE.URGENCY_LEVEL) {
        item.value = _.get(initValue, '_taskLevel', '')
      }
    })
    const checkFileParams = {
      orgId: sendOrgId,
      type: 50,
      bizId: `procFormDataKey=${procFormDataKey}&userTaskId=${userTaskId}`,
      flowId,
    }
    service.getDocumentIdByBusiness(checkFileParams).then((res) => {
      if (res.id <= 0) {
        const fileItem = {
          orgId: sendOrgId,
          info: {
            type: 50,
            createName: _.get(initValue, 'drafter', '').split('(')[0].trim(),
            createUid: _.get(initValue, 'loginUid', ''),
            fields: sendFields,
            createTime: _.get(initValue, 'drafter_time', 0),
            endTime: 0,
            deptId: _.get(initValue, '_dept.value', 0),
            bizId: `procFormDataKey=${procFormDataKey}&userTaskId=${userTaskId}`,
            flowId,
            deptName: _.get(initValue, '_dept.label', 0),
          },
        }

        service.createDocumentByBusiness(fileItem).then((res2) => {
          const sendparam1 = {
            orgId: sendOrgId,
            id: res2.id,
            sendInfo: {
              hostUsers,
              coUsers: [],
              knowUsers: [],
              opinion: '',
            },
            isSendSms: '',
          }
          service.sendDocument(sendparam1).then(() => {
            setIsEasyDistributeShow(false)
          })
        })
      }
      if (res.id > 0) {
        const sendparam2 = {
          orgId: sendOrgId,
          id: res.id,
          sendInfo: {
            hostUsers,
            coUsers: [],
            knowUsers: [],
            opinion: '',
          },
          isSendSms: '',
        }
        service.sendDocument(sendparam2).then(() => {
          setIsEasyDistributeShow(false)
        })
      }
    })
  }
  const handleShowEasyDistribute = () => {
    const mainSendList = _.get(initValue, 'z-give')
    const copySendList = _.get(initValue, 'c-give')
    if (mainSendList && copySendList) {
      const allList = [...mainSendList, ...copySendList].filter((item) => {
        return !item.isCacheable
      })
      console.log(allList, 'allList')
      const deptId = allList.map((item) => Number(item.id || item.key))
      service
        .getDepartmentLeaderList({
          orgId: sendOrgId,
          deptIds: deptId,
        })
        .then((res) => {
          if (res.length === 0) {
            return message.error('一键分发失败，未找到主送/抄送部门负责人')
          }
          setIsEasyDistributeShow(true)
          setEasyDistributeLeaderList(res)
        })
    } else {
      message.error('一键分发失败，未找到主送/抄送部门负责人')
    }

  }
  const handleHideEasyDistribute = () => {
    setIsEasyDistributeShow(false)
  }
  const useManyEffects = () => {
    onFieldValueChange$('fileList').subscribe(({ value = [] }) => {
      onFilechange(value)
    })
    onFieldValueChange$('rule-type').subscribe(({ value = [] }) => {
      console.log(value, 'value')
    })
  }
  useEffect(() => {
    if (sendUid) {
      service
        .getIsDistribute({
          inCodes: ['jtgw_OEBREG'],
          uid: sendUid,
        })
        .then(res => {
          setShowDistribute(res.data)
        })
    }
  }, [sendUid])
  useEffect(() => {
    if (initValue) {
      const { drafterTime, flowId, orgId, deptId, loginUid } = initValue
      const secretClass = _.get(initValue, 'secret-degree', 4)
      setSeretLevel(secretClass)
      const formateData = {
        ...initValue,
        drafter_time: moment(drafterTime).format('YYYY年MM月DD日'),
      }
      setFormateData(formateData)
      setFlowId(flowId)
      setDeptId(deptId)
      setOrgId(orgId)
      setLoginUid(loginUid)
    }
  }, [initValue])
  useEffect(() => {
    const { loginOrgId, linkPath, loginUid } = myInfo
    setSendOrgid(loginOrgId)
    setSendDeptId(linkPath ? linkPath[0].deptId : '')
    setSendUid(loginUid)
    if (Object.keys(myInfo).length > 0) {
      service
        .getUserLevelCode({
          orgId: loginOrgId,
          userId: Number(loginUid),
          deptIds: [linkPath ? linkPath[0].deptId : ''],
        })
        .then(res => {
          const { nodes = [], userLabelCodes = [] } = res
          setLevelCodeNodes(nodes)
          setUserLabelCodes(userLabelCodes)
        })
    }
  }, [myInfo])
  useEffect(() => {
    // eslint-disable-next-line no-unused-expressions
    sendOrgId && service.getDocumentUsefulExpression({ orgId: sendOrgId }).then(res => {
      setExpressions(res.data || [])

    })
  }, [sendOrgId])
  const finishSubmit = () => {
    closeTheWindow()
    // window.location.reload()
  }
  const onMount = ({ access, editMode, draft }) => {
    if (access && Object.keys(access).length > 0 && editMode && editMode.includes('web')) {
      Object.keys(access).forEach(key => {
        if (key === 'isEditable' && access[key] === 'WRITE') {
          setShowSaveBtn(true)
          resetFormState()
          return
        }
        actions.setFieldState(key, state => {
          switch (access[key]) {
            case 'NONE':
              state.display = false
              break
            case 'READ':
              state.editable = false
              break
            case 'WRITE':
              state.editable = true
              state.display = true
              break
            default: break
          }
        })
      })
    } else {
      // actions.setFormState((state) => {
      //   state.editable = false
      // })
    }

    if (draft) {
      const formateData = {
        ...draft,
        drafter_time: moment(drafterTime).format('YYYY年MM月DD日'),
      }
      setDraftValue(formateData)
    }
  }
  setup()

  return (
    <div
      className={css`
        padding: 0 80px;
      `}
    >
      <SchemaForm
        className={cx(ruleContent)}
        initialValues={draftValue && Object.keys(draftValue).length > 0 ? draftValue : formateData}
        editable={false}
        components={{
          UpFileLoad,
          fileNumber,
          UploadEnclousre,
          SelectDept,
          SelectMember,
          Select,
          SelectRule,
          Dept,
          SelectPosition,
          SelectProcessFile,
          SelectDepartment,
        }}
        effects={() => {
          useManyEffects()
        }}
        actions={actions}
        labelCol={8}
        previewPlaceholder="暂无数据"
        labelAlign="right"
      >
        <Field
          name="_dept"
          title="拟稿部门"
          x-component="Dept"
          x-component-props={{ labelInValue: true }}
          x-props={{
            style: {
              width: 560,
            },
          }}
          x-rules={[
            {
              validator: val => {
                if (!val.value) {
                  return '请选择拟稿部门'
                }
              },
            },
          ]}
        />
        <Field
          name="drafter"
          title="拟稿人"
          type="string"
          editable={false}
          x-props={{
            style: {
              width: 200,
            },
          }}
        />
        <Field
          name="drafter_time"
          title="拟稿时间"
          type="string"
          editable={false}
          x-props={{
            style: {
              width: 200,
            },
          }}
        />
        <Field
          name="fileNumber"
          title="文件编号"
          x-component="fileNumber"
          x-component-props={{
            orgId,
            deptId,
            flowId,
          }}
        />

        <Field
          name="_taskLevel"
          title="缓急"
          type="string"
          enum={urgencyOptions}
          x-props={{
            style: {
              width: 200,
            },
          }}
        />
        <Field
          name="secret-degree"
          title="密级"
          x-props={{
            style: {
              width: 200,
            },
          }}
          type="string"
          enum={secretOptions}
        />
        <Field
          name="title"
          title="标题"
          type="string"
          required
          x-props={{
            style: {
              width: 560,
            },
          }}
        />
        {/* <Field
				name="secret-date"
				title="保密期限"
				type="date"
				x-props={{
					style: {
						width: 200
					}
				}}
				x-component-props={{
					placeholder: '请选择日期'
				}}
			/> */}

        <Field
          name="z-give"
          title="主送"
          x-component="SelectDepartment"
          x-component-props={{
            listAllAction: '/baas-admin/web/listAll',
            myListAction: '/baas-easylabel/business/workLineMyList',
            searchAction: '/web-search/web/search?size=100',
            type: '7',
            parentOrgId: Cookies.get('groupId'),
            isRit: true,
          }}
        />

        <Field
          name="c-give"
          title="抄送"
          x-component="SelectDepartment"
          x-component-props={{
            listAllAction: '/baas-admin/web/listAll',
            myListAction: '/baas-easylabel/business/workLineMyList',
            searchAction: '/web-search/web/search?size=100',
            type: '7',
            parentOrgId: Cookies.get('groupId'),
            isRit: true,
          }}
        />

        <Field
          name="counter-sign"
          title="会签部门"
          x-component="SelectDepartment"
          x-component-props={{
            listAllAction: '/baas-admin/web/listAll',
            myListAction: '/baas-easylabel/business/workLineMyList',
            searchAction: '/web-search/web/search?size=100',
            type: '7',
            parentOrgId: Cookies.get('groupId'),
            isRit: true,
          }}
        />

        <Field
          name="fileList"
          title="正文"
          x-component="UpFileLoad"
          x-component-props={{
            orgId: '',
            reportId: procFormDataKey,
            type,
            config,
          }}
        />

        <Field name="enclosure" title="附件" x-component="UploadEnclousre" />
        <Field
          name="reference_information"
          title="参考信息"
          x-component="UploadEnclousre"
          x-component-props={{ typeName: 'information' }}
        />
        <Field
          name="remarks"
          title="备注"
          type="textarea"
          x-props={{
            className: 'text_area',
          }}
        />

        <Field
          name="rule-name"
          title="规章制度名称"
          type="string"
          x-props={{
            style: {
              width: 560,
            },
          }}
        />

        <Field
          name="rule-type"
          title="制度审批类型"
          type="radio"
          enum={typeRule}
          x-linkages={[
            {
              type: 'value:visible',
              target: '*(absort-rule)',
              condition: `{{$self.value === 3||$self.value===2}}`,
            },
          ]}
        />

        <Field name="rule-level" title="规章制度级别" type="radio" enum={ruleLevel} />
        <Field
          name="rule-important"
          title="是否三重一大事项"
          type="radio"
          enum={[
            { label: '是', value: 1 },
            { label: '否', value: 2 },
          ]}
          x-linkages={[
            {
              type: 'value:visible',
              target: '*(meeting-organ)',
              condition: `{{$self.value === 1}}`,
            },
          ]}
        />
        <Field
          name="meeting-organ"
          title="上会机构"
          type="radio"
          enum={[
            { label: '董事会', value: 1 },
            { label: '党委会', value: 2 },
            { label: '总经理办公会', value: 3 },
            { label: '其他', value: 4 },
          ]}
        />
        <Field
          name="process-file"
          title="流程附件"
          x-component="SelectProcessFile"
          x-component-props={{
            reportId: procFormDataKey,
            myInfo,
            dept: {},
          }}
        />
        <Field name="absort-rule" title="需要废止的原制度" x-component="SelectRule" />
        <Field name="application-position" title="适用岗位" x-component="SelectPosition" />
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            position: 'fixed',
            bottom: 0,
            height: '60px',
            width: '100%',
            zIndex: '500',
            left: 0,
          }}
          className="button"
        >
          {(!isEmpty(initValue) || !procFormDataKey) && (
            <Buttons
              userTaskId={userTaskId}
              onFinish={finishSubmit}
              onSubmit={submitForm}
              onMount={onMount}
              onSubmitOpen={async () => {
                const data = await actions.submit()
                const result = patchData(data.values, draftValue && Object.keys(draftValue).length > 0 ? draftValue : formateData)
                return result
              }}
            />
          )}
          {
            (!isEmpty(initValue) || !procFormDataKey) && showButton && <div>
              <Button onClick={handleShowDistribute}>分发</Button>
              <Button onClick={handleShowEasyDistribute} className={css`margin-left:16px`}>一键分发</Button>
            </div>
          }
        </div>
      </SchemaForm>
      <Tabs defaultActiveKey="1">
        <TabPane tab="审批流程" key="1">
          <Steps userTaskId={userTaskId} />
        </TabPane>
      </Tabs>
      <NewDistribute
        visible={isDistributeShow}
        isDefSendSms={false}
        id={procFormDataKey}
        orgId={sendOrgId}
        deptList={[{ id: sendDeptId, value: sendDeptId }]}
        isBeSender={false}
        levelCodeNodes={levelCodeNodes}
        userLabelCodes={userLabelCodes}
        fillOpinions={[]}
        phrases={expressions}
        onClose={handleHideDistribute}
        onSuccess={handleOkDistribute}
      />
      <SecretDistribute
        visible={isSendSecretFileShow}
        isDefSendSms={false}
        id={procFormDataKey}
        isBeSender={false}
        secretClass={secertLevel}
        fillOpinions={[]}
        passUsers={expressions}
        levelCodeNodes={levelCodeNodes}
        userLabelCodes={userLabelCodes}
        onClose={handleHideSendSecretFile}
        onSuccess={handleOkDistribute}
      />
      <EasyDistribute
        visible={isEasyDistributeShow}
        id={procFormDataKey}
        easyDistributeLeaderList={easyDistributeLeaderList}
        onClose={handleHideEasyDistribute}
        onSuccess={handleOkEasyDistribute}
      />
    </div>
  )
}

export default RuleContent
