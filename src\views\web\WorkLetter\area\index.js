import React, { useEffect, useMemo, useRef, useState } from 'react'
import ReactDOM from 'react-dom'
import moment from 'moment'
import {
  Button,
  Space,
  message,
  Modal,
} from 'antd'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import { getQueryString, closeTheWindow, emergencyLevel } from 'ROOT/utils'
import service from 'ROOT/service'
import { getWpsUrl } from 'ROOT/utils/wps'
import { patchData } from 'ROOT/utils/index'
import { Buttons } from 'ROOT/components/Process'
import useMyInfo from 'ROOT/hooks/useMyInfo'
import { formBizId } from 'ROOT/constants'
import { useGetTips } from 'ROOT/hooks'
import { isEmpty } from 'lodash'
import Form, { actions } from './form'
import { formConfig, letterType } from '../module/config'
import { closeBtn, deleteBtn, saveAndExit, dispatchBtn } from '../module/buttons'
import Dispatch from '../dialog/dispatch'

const { confirm } = Modal
const typeIndex = 0

export default props => {
  const { procKey, appId, procFormDataKey } = useMemo(() => getQueryString(props.location.search), [props.location.search])
  const [initValue, setInitValue] = useState({})
  const [formData, setFormData] = useState({})
  const [showDispatchBtn, setShowDispatchBtn] = useState(false)
  const formId = useRef(+procFormDataKey)
  const myInfo = useMyInfo({ isSetStorage: true })
  const [currentDept, setCurrentDept] = useState({})

  useEffect(() => {
    if (!procFormDataKey) {
      service.saveForm({
        type: letterType[typeIndex].type,
        classify: {
          name: letterType[typeIndex].name,
          englishName: letterType[typeIndex].englishName,
        },
        config: formConfig,
        data: {
          _taskLevel: '3',
          isFeedback: '2',
          draftDate: moment().format('YYYY年MM月DD日'),
        },
      }).then(res => {
        const { data } = res
        resetContentProps(data)
        formId.current = data
      })
    }
  }, [])

  useEffect(() => {
    actions.setFieldState('*(serialNumber)', state => {
      state.visible = false
    })
  }, [])

  const { controlTipsInfo } = useGetTips({ procKey, currentDept })

  useEffect(() => {
    if (controlTipsInfo.type === 2) {
      Modal.confirm({
        icon: <ExclamationCircleOutlined style={{color: '#4F84D2'}}/>,
        title: '提示',
        content: (
          // eslint-disable-next-line react/no-danger
          <div id='__CONTROL_CONFIRM__' dangerouslySetInnerHTML={{__html: controlTipsInfo.promptContent}} />
        ),
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          console.log('aaaaaa')
        },
      })
    }
  }, [controlTipsInfo])

  const resetUserInfo = () => {
    const { loginName, loginMobile, linkPath } = myInfo
    actions.setFieldState('draftUser', state => {
      state.value = loginName
    })
    actions.setFieldState('draftPhone', state => {
      state.value = loginMobile
    })
    actions.setFieldState('_dept', state => {
      state.props.enum = linkPath && linkPath.length > 0 ? linkPath.map(item => ({
        value: item.deptId,
        label: item.linkPath,
      })) : []
      if (initValue._dept && initValue._dept.value) {
        state.value = initValue._dept
      } else {
        state.value = linkPath && linkPath.length > 0 ? {
          value: linkPath[0].deptId,
          label: linkPath[0].linkPath,
        } : { value: '', label: '' }
      }
    })
  }

  useEffect(() => {
    if (Object.keys(myInfo).length > 0) {
      resetUserInfo()
    }
  }, [myInfo])

  useEffect(async() => {
    if (procFormDataKey) {
      const wpsContent = await getWpsUrl(procFormDataKey)
      service.getFormData({
        reportId: procFormDataKey,
      }).then(res => {
        if (res.success && res.data) {
          if (wpsContent) {
            res.data.data.fileList[0] = wpsContent
          }
          setFormData(res.data.data)
          resetContentProps(procFormDataKey)
        }
      })
    }
  }, [])

  useEffect(() => {
    if (procFormDataKey) {
      const { loginName, loginMobile, linkPath } = myInfo
      setInitValue({
        draftUser: loginName,
        draftPhone: loginMobile,
        _dept: linkPath && linkPath.length > 0 ? {
          value: linkPath[0].deptId,
          label: linkPath[0].linkPath,
        } : { value: '', label: '' },
        ...formData,
      })
    }
  }, [formData, myInfo])

  const resetContentProps = (procFormDataKey) => {
    actions.setFieldState('fileList', (state) => {
      state.props['x-component-props'] = {
        ...state.props['x-component-props'],
        reportId: procFormDataKey,
        type: letterType[typeIndex].type,
        config: formConfig,
      }
    })
  }

  useEffect(() => {
    service.getUserLabel().then(res => {
      if (res.success) {
        setShowDispatchBtn(!!((res.data.includes(letterType[typeIndex].directorFirst) || res.data.includes(letterType[typeIndex].directorSecond))))
      }
    })
  }, [])

  const commonCommit = async () => {
    const data = await actions.submit()
    const result = patchData(data.values, formData)
    const res = service.upDateForm({
      config: formConfig,
      data: result,
      reportId: formId.current,
    })
    return res
  }

  const createFeedback = async ({ formData }) => {
    const res = await service.createFeedback({
      procFormDataKey: formId.current,
      formBizId,
      taskType: formData.taskType,
      taskBizType: letterType[typeIndex].taskBizType,
      day: formData.day,
      endTime: formData.endTime,
    })
    if (res.success) {
      closeTheWindow(props.location.search)
    }
  }

  const submitForm = async () => {
    const res = await commonCommit()
    // const data = await actions.submit()
    if (res.success) {
      // return { id: +formId.current, values: data.values }
      return +formId.current
    }
    throw new Error('保存出错')
  }

  useEffect(() => {
    window.addEventListener('process', () => finishSubmit())
  }, [])

  const finishSubmit = async () => {
    const data = await actions.submit()
    const result = patchData(data.values, formData)
    if (data.values.isFeedback === '1') {
      deleteDraft()
      createFeedback({
        formData: result,
      })
    } else {
      deleteDraft(() => {
        closeTheWindow(props.location.search)
      })
    }
  }

  const saveDraft = async (callback) => {
    const data = await actions.submit()
    const result = patchData(data.values, formData)
    const info = localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo')) : {}
    const res = await service.saveDraft({
      appId,
      appTasks: [{
        appTaskId: formId.current.toString(),
        businessType: 2,
        emergencyLevel: emergencyLevel[+result._taskLevel],
        handleEntry: [{
          handleType: 0, // 草稿
          handlerId: info.loginUid,
        }],
        processType: letterType[typeIndex].processType,
        sponsorId: info.loginUid,
        jumpToDetail: 1,
        title: result.title,
        detailUrl: decodeURIComponent(procFormDataKey ? `${location.href.replace('/extra-forms/', '/extra-forms-h5/').replace(/backurl=.*?&|backurl=.*?$/, '')}` : `${location.href.replace('/extra-forms/', '/extra-forms-h5/').replace(/backurl=.*?&|backurl=.*?$/, '')}&procFormDataKey=${formId.current}`),
        webDetailUrl: decodeURIComponent(procFormDataKey ? `${location.href.replace(/backurl=.*?&|backurl=.*?$/, '')}` : `${location.href.replace(/backurl=.*?&|backurl=.*?$/, '')}&procFormDataKey=${formId.current}`),
      }],
    })
    if (res.success) {
      message.success('操作成功', () => {
        if (typeof callback === 'function') callback(res)
      })
    }
  }

  const saveForm = async (callback, isDraft) => {
    const res = await commonCommit()
    if (res.success) {
      if (isDraft) {
        saveDraft(callback)
      } else {
        message.success('操作成功', () => {
          if (typeof callback === 'function') callback(res)
        })
      }
    }
  }

  const deleteDraft = async (callback) => {
    const info = localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo')) : {}
    const res = await service.deleteDraft({
      appId,
      appTasks: [{
        appTaskId: formId.current.toString(),
        handlerIds: [info.loginUid],
      }],
    })
    if (res.success) {
      if (callback) {
        if (typeof callback === 'function') callback()
      }
    }
  }

  const deleteForm = () => {
    confirm({
      content: '你选择了注销当前文档，系统将删除该文档，是否继续？',
      onOk: async () => {
        const res = await service.deleteForm({
          reportId: procFormDataKey,
        })
        if (res.success) {
          message.success('操作成功', () => {
            deleteDraft(() => {
              closeTheWindow(props.location.search)
            })
          })
        } else {
          message.error(data.msg)
        }
      },
    })
  }

  const buttonGroup = () => {
    const array = [{
      name: '保存退出',
      async: true,
      onClick: async () => {
        await saveForm(() => {
          closeTheWindow(props.location.search)
        }, true)
      }
    }, {
      name: '保存',
      async: true,
      onClick: async () => {
        await saveForm('', true)
      }
    }]
    if (procFormDataKey) {
      array.push({
        name: '注销文档',
        async: false,
        onClick: () => {
          deleteForm()
        }
      })
    }
    if (showDispatchBtn) {
      array.push({
        name: '一键分发',
        async: false,
        onClick: async () => {
          const res = await actions.getFormState()
          if (!res.values.mainDelivery || res.values.mainDelivery.length <= 0) {
            message.error('请选择主送人员')
            return
          }
          actions.setFieldState('*(isDispatch)', state => {
            state.value = showDispatchBtn
          })
          await commonCommit()
          showDispatchModal()
        }
      })
    }
    // const array = [saveAndExit(() => {
    //   saveForm(() => {
    //     closeTheWindow(props.location.search)
    //   }, true)
    // })]
    // if (procFormDataKey) {
    //   array.push(deleteBtn(() => {
    //     deleteForm()
    //   }))
    // }
    // if (showDispatchBtn) {
    //   array.push(dispatchBtn(async () => {
    //     const res = await actions.getFormState()
    //     if (!res.values.mainDelivery || res.values.mainDelivery.length <= 0) {
    //       message.error('请选择主送人员')
    //       return
    //     }
    //     actions.setFieldState('*(isDispatch)', state => {
    //       state.value = showDispatchBtn
    //     })
    //     await commonCommit()
    //     showDispatchModal()
    //   }))
    // }
    return array
  }

  const showDispatchModal = async () => {
    const overlay = document.createElement('div')
    document.body.appendChild(overlay)
    const close = () => {
      ReactDOM.unmountComponentAtNode(overlay)
      overlay.parentNode.removeChild(overlay)
    }
    const res = await actions.getFormState()
    ReactDOM.render(
      <Dispatch
        width={700}
        visible
        formData={res.values}
        procKey={procKey}
        appId={appId}
        procFormDataKey={formId.current}
        onOkCallback={finishSubmit}
        onCancel={() => {
          close()
        }}
      />,
      overlay,
    )
  }

  const onMount = ({ access, editMode }) => {
    console.log('access', access)
    if (access && Object.keys(access).length > 0 && editMode && editMode.includes('web')) {
      Object.keys(access).forEach(key => {
        actions.setFieldState(key, state => {
          switch (access[key]) {
            case 'NONE':
              state.display = false
              break
            case 'READ':
              state.editable = false
              break
            case 'WRITE':
              state.editable = true
              state.display = true
              break
            default: break
          }
        })
      })
    } else {
      // actions.setFormState((state) => {
      //   state.editable = false
      // })
    }
  }

  const getCurrentDept = (value) => {
    console.log(value, 'value');
    setCurrentDept(value)
  }

  return (
    <div>
      {controlTipsInfo.type === 1 && <div id='__CONTROL_CONFIRM_TEXT__' dangerouslySetInnerHTML={{__html: controlTipsInfo.promptContent}} />}
      <Form
        editable
        initValue={initValue}
        getCurrentDept={getCurrentDept}
      />
      <div>
        {
          (!isEmpty(formData) || !procFormDataKey) && (
            <Space>
              {/* {buttonGroup().map(item => <Button key={item.name} type={item.type} danger={item.name === '一键分发'} onClick={item.onClick}>{item.name}</Button>)} */}
              <Buttons
                procKey={procKey}
                appId={appId}
                onMount={onMount}
                onSubmitOpen={async () => {
                  const data = await actions.submit()
                  const result = patchData(data.values, formData)
                  return result
                }}
                onSubmit={submitForm}
                extraButtons={buttonGroup()}
              // onFinish={finishSubmit}
              />
            </Space>
          )
        }
      </div>
    </div>
  )
}
