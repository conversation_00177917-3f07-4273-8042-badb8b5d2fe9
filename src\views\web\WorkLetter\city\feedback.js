import React, { useEffect, useState, useMemo } from 'react'
import ReactDOM from 'react-dom'
import {
  Tabs,
  Button,
  Space,
} from 'antd'
import { closeTheWindow, getQueryString } from 'ROOT/utils'
import service from 'ROOT/service'
import useMyInfo from 'ROOT/hooks/useMyInfo'
import { getWpsUrl } from 'ROOT/utils/wps'
import Form, { actions } from './form'
import Feedback from '../dialog/feedback'
import { formConfig, letterType } from '../module/config'
import { feedBackBtn } from '../module/buttons'
import FeedbackTable from '../component/feedbackTable'

const { TabPane } = Tabs
const typeIndex = 2

export default props => {
  const { appTaskId, procFormDataKey } = useMemo(() => getQueryString(props.location.search), [props.location.search])
  const [isFeedback, setIsFeedback] = useState(false)
  const [initValue, setInitValue] = useState({})
  const [showBtn, setShowBtn] = useState(false)
  const myInfo = useMyInfo({ isSetStorage: true })

  useEffect(() => {
    actions.setFieldState('*(mappingValue)', state => {
      state.visible = false
    })
  }, [])

  useEffect(async () => {
    if (procFormDataKey) {
      const wpsContent = await getWpsUrl(procFormDataKey)
      service.getFormData({
        reportId: procFormDataKey,
      }).then(res => {
        if (res.success && res.data) {
          if (wpsContent) {
            res.data.data.fileList[0] = wpsContent
          }
          const { isFeedback } = res.data.data
          setIsFeedback(isFeedback === '1')
          setInitValue(res.data.data)
          resetContentProps()
        }
      })
    }
  }, [])

  const resetContentProps = () => {
    actions.setFieldState('fileList', (state) => {
      state.props['x-component-props'] = {
        ...state.props['x-component-props'],
        reportId: procFormDataKey,
        type: letterType[typeIndex].type,
        config: formConfig,
      }
    })
  }

  useEffect(() => {
    if (appTaskId) {
      service.isShowFeedback({
        appTaskId,
      }).then(res => {
        if (res.success) {
          setShowBtn(res.data)
        }
      })
    }
  }, [])

  const showFeedback = () => {
    const overlay = document.createElement('div')
    document.body.appendChild(overlay)
    const close = () => {
      ReactDOM.unmountComponentAtNode(overlay)
      overlay.parentNode.removeChild(overlay)
    }
    ReactDOM.render(
      <Feedback
        appTaskId={appTaskId}
        width={600}
        visible
        onCancel={() => close()}
        onOk={() => {
          closeTheWindow(props.location.search)
        }}
      />,
      overlay,
    )
  }

  const buttonGroup = () => {
    const array = [feedBackBtn(() => {
      showFeedback()
    })]
    return array
  }

  return (
    <div>
      <Form
        initValue={initValue}
        editable={false}
        extraBody={
          <Tabs defaultActiveKey="1" className="mb10">
            {isFeedback
              ? <TabPane tab="反馈内容" key="1">
                  <FeedbackTable
                    procFormDataKey={procFormDataKey}
                    initValue={initValue}
                  />
              </TabPane>
              : null
            }
          </Tabs>
        }
      />
      {showBtn
        ? <div className="button-group">
          <Space>
            {buttonGroup().map(item => <Button key={item.name} type={item.type} onClick={item.onClick}>{item.name}</Button>)}
          </Space>
        </div>
        : null
      }
    </div>
  )
}
