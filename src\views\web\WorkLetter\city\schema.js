import moment from 'moment'
import { UPLOAD_URL } from 'ROOT/constants'
import locale from 'antd/lib/date-picker/locale/zh_CN'
import Cookies from 'js-cookie'
import { isArray, isUndefined } from 'lodash'

export default initValue => ({
  type: 'object',
  properties: {
    layout: {
      'x-component': 'mega-layout',
      'x-component-props': {
        grid: true,
        autoRow: true,
        // labelWidth: 100,
        labelAlign: '{{labelAlign}}',
        className: 'grid-gaps',
        responsive: {
          lg: 3,
          m: 2,
          s: 1,
        },
      },
      properties: {
        formTitle: {
          key: 'formTitle',
          name: 'formTitle',
          title: '表单标题',
          display: false,
          default: '',
          'x-component': 'Input',
        },
        isDispatch: {
          key: 'isDispatch',
          name: 'isDispatch',
          title: '是否一键分发',
          display: false,
          default: false,
          'x-component': 'Input',
        },
        title: {
          key: 'title',
          name: 'title',
          title: '标题',
          maxLength: 200,
          required: true,
          'x-component': 'Input',
          'x-mega-props': {
            span: 2,
          },
        },
        signer: {
          key: 'signer',
          name: 'signer',
          title: '签发人',
          'x-component': 'Editable',
        },
        mappingValue: {
          key: 'mappingValue',
          name: 'mappingValue',
          title: '文件编码',
          enum: [],
          required: true,
          'x-component': 'SerialNumber',
        },
        serialNumber: {
          key: 'serialNumber',
          name: 'serialNumber',
          title: '文件编码',
          'x-component': 'Editable',
        },
        draftUser: {
          key: 'draftUser',
          name: 'draftUser',
          title: '起草人',
          'x-component': 'Editable',
        },
        _dept: {
          key: '_dept',
          name: '_dept',
          title: '起草部门',
          required: true,
          'x-component': 'Dept',
          'x-component-props': {
            labelInValue: true,
          },
          'x-rules': [
            {
              validator: val => {
                if (!val.value) {
                  return '请选择起草部门'
                }
              },
            },
          ],
        },
        draftPhone: {
          key: 'draftPhone',
          name: 'draftPhone',
          title: '联系电话',
          'x-component': 'Editable',
        },
        draftDate: {
          key: 'draftDate',
          name: 'draftDate',
          title: '起草时间',
          default: moment().format('YYYY年MM月DD日'),
          'x-component': 'Editable',
        },
        _taskLevel: {
          key: '_taskLevel',
          name: '_taskLevel',
          title: '缓急',
          default: '3',
          enum: [
            { label: '一般', value: '3' },
            { label: '急件', value: '2' },
            { label: '特急', value: '1' },
          ],
          required: true,
          'x-component': 'Select',
        },
        mainDelivery: {
          key: 'mainDelivery',
          name: 'mainDelivery',
          title: '主送',
          'x-component':
            isArray(initValue.mainDelivery) || isUndefined(initValue.mainDelivery)
              ? 'SelectCityOrg'
              : 'Input', // 'SelectDept',
          'x-component-props': {
            listAllAction: '/baas-admin/web/listAll',
            myListAction: '/baas-easylabel/business/workLineMyList',
            searchAction: '/web-search/web/search?size=100',
            type: '2',
            workLineType: '1',
            parentOrgId: Cookies.get('orgId'),
          },
          'x-mega-props': {
            span: 3,
          },
        },
        carbonCopy: {
          key: 'carbonCopy',
          name: 'carbonCopy',
          title: '抄送',
          'x-component':
            isArray(initValue.carbonCopy) || isUndefined(initValue.carbonCopy)
              ? 'SelectCityOrg'
              : 'Input', // 'SelectDept',
          'x-component-props': {
            listAllAction: '/baas-admin/web/listAll',
            myListAction: '/baas-easylabel/business/workLineMyList',
            searchAction: '/web-search/web/search?size=100',
            type: '2',
            workLineType: '1',
            parentOrgId: Cookies.get('orgId'),
          },
          'x-mega-props': {
            span: 3,
          },
        },
        sendBasis: {
          key: 'sendBasis',
          name: 'sendBasis',
          title: '发函依据',
          'x-component': 'Input',
          maxLength: 500,
          'x-component-props': {
            format: 'YYYY-MM-DD',
          },
          'x-mega-props': {
            span: 3,
          },
        },
        isFeedback: {
          key: 'isFeedback',
          name: 'isFeedback',
          title: '是否反馈',
          'x-component': 'RadioGroup',
          default: '2',
          // 'x-component-props': {
          //   disabled: true,
          // },
          enum: [
            { label: '是', value: '1' },
            { label: '否', value: '2' },
          ],
          'x-linkages': [
            {
              type: 'value:visible',
              target: '*(taskType,day,endTime)',
              condition: '{{ $self.value === "1" }}',
            },
            {
              type: 'value:state',
              target: '*(taskType,day,endTime)',
              condition: '{{ $self.value === "1" }}',
              state: {
                required: true,
              },
              otherwise: {
                required: false,
              },
            },
          ],
        },
        taskType: {
          key: 'taskType',
          name: 'taskType',
          title: '反馈周期',
          'x-component': 'Select',
          default: '1',
          enum: [
            { label: '按次', value: '1' },
            { label: '按周', value: '2' },
            { label: '按月', value: '3' },
            { label: '按季', value: '4' },
          ],
          'x-linkages': [
            {
              type: 'value:schema',
              target: '*(day)',
              condition: '{{ $self.value === "1" }}',
              schema: {
                'x-component': 'DatePicker',
                'x-component-props': {
                  locale,
                },
              },
              otherwise: {
                'x-component': 'Select',
              },
            },
            {
              type: 'value:schema',
              target: '*(day)',
              condition: '{{ $self.value === "2" }}',
              schema: {
                title: '反馈时间',
                enum: [
                  { label: '周一', value: '1' },
                  { label: '周二', value: '2' },
                  { label: '周三', value: '3' },
                  { label: '周四', value: '4' },
                  { label: '周五', value: '5' },
                ],
              },
            },
            {
              type: 'value:schema',
              target: '*(day)',
              condition: '{{ $self.value === "3" }}',
              schema: {
                title: '反馈时间(每月几日)',
                enum: new Array(31).fill(0).map((a, index) => {
                  return {
                    label: `${index + 1}`,
                    value: `${index + 1}`,
                  }
                }),
              },
            },
            {
              type: 'value:schema',
              target: '*(day)',
              condition: '{{ $self.value === "4" }}',
              schema: {
                title: '反馈时间(每季几日一季度月为: 3、6、9、12月)',
                enum: new Array(31).fill(0).map((a, index) => {
                  return {
                    label: `${index + 1}`,
                    value: `${index + 1}`,
                  }
                }),
              },
            },
          ],
        },
        day: {
          key: 'day',
          name: 'day',
          title: '反馈时间',
          'x-component': 'Select',
          'x-component-props': {
            style: {
              width: '100%',
            },
          },
        },
        endTime: {
          key: 'endTime',
          name: 'endTime',
          title: '反馈结束时间',
          required: true,
          'x-component': 'DatePicker',
          'x-component-props': {
            format: 'YYYY-MM-DD',
            locale,
            style: {
              width: '100%',
            },
            disabledDate: current => {
              // Can not select days before today and today
              return current && current < moment().startOf('day')
            },
          },
        },
        remark: {
          key: 'remark',
          name: 'remark',
          title: '备注',
          'x-component': 'Input',
          'x-mega-props': {
            span: 3,
          },
        },
        attach: {
          key: 'attach',
          name: 'attach',
          title: '附件',
          'x-component': 'Upload',
          'x-component-props': {
            listType: 'text',
            action: UPLOAD_URL,
          },
          'x-mega-props': {
            span: 3,
          },
        },
        fileList: {
          key: 'fileList',
          name: 'fileList',
          title: '函件内容',
          display: '{{editable}}',
          'x-component': 'WpsEditor',
          'x-mega-props': {
            span: 3,
          },
          'x-component-props': {
            orgId: '',
            reportId: '',
            type: '',
            config: '',
          },
        },
      },
    },
  },
})
