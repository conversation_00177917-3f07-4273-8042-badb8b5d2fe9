import React, { useCallback, useEffect, useState } from 'react'
import moment from 'moment'
import {
  SchemaMarkupForm,
  FormEffectHooks,
  createFormActions,
  createAsyncFormActions,
} from '@formily/antd'
import { FormMegaLayout, Input, DatePicker, Select, Radio } from '@formily/antd-components'
import service from 'ROOT/service'
import SelectOrg from 'ROOT/components/Formily/Select'
import SelectCityOrg from 'ROOT/components/Formily/SelectCityOrg'
import WpsEditor from 'ROOT/components/Formily/WpsEditor'
import Dept from 'ROOT/components/Formily/Dept'
import PersonalInfo from 'ROOT/components/Formily/PersonalInfo'
import Editable from 'ROOT/components/Formily/Editable'
import SerialNumber from 'ROOT/components/Formily/SerialNumber'
import MainText from 'ROOT/components/MainText'
import { getHtmlCode } from 'ROOT/utils'
import Upload from '../component/Upload'
import Schema from './schema'

const { onFieldValueChange$, onFieldInputChange$ } = FormEffectHooks
export const actions = createAsyncFormActions()

export default props => {
  const { signer, initValue, editable, extraBody, getCurrentDept } = props
  const [formTitle, setFormTitle] = useState('')
  const [title, setTitle] = useState('')
  const [content, setContent] = useState('')

  useEffect(() => {
    document.title = formTitle || '工作协调函'
    if (document.getElementById('breadnav')) {
      document.getElementById('breadnav').innerHTML = formTitle || '工作协调函'
    }
  }, [formTitle])

  const useManyEffects = useCallback(() => {
    const { setFieldState } = createFormActions()
    onFieldInputChange$('taskType').subscribe(({ value }) => {
      setFieldState('*(day)', state => {
        state.value = value === '1' ? '' : '1'
      })
    })
    onFieldValueChange$('taskType').subscribe(({ value }) => {
      // setFieldState('*(endTime)', state => {
      //   state.rules = (value === '1') ? [{
      //     validator: async (val) => {
      //       const res = await actions.getFormState()
      //       if (!res.values.day || !val) return
      //       return moment(res.values.day).diff(moment(val)) <= 0 ? '' : '反馈结束时间不能小于反馈时间'
      //     },
      //   }] : []
      // })
      setFieldState('*(day)', state => {
        // 选择【按次】的时候【反馈时间】自动填入系统当前时间，页面隐藏该字段，反馈时间以选择【按次】的时候为准
        state.display = value !== '1'
        if (value === '1' && !state.value) {
          state.value = moment().format('YYYY-MM-DD')
        }
        state.required = value === '1'
        state.rules =
          value === '1'
            ? [
                {
                  validator: async val => {
                    const res = await actions.getFormState()
                    if (!res.values.endTime || !val) return
                    return moment(res.values.endTime).diff(moment(val)) >= 0
                      ? ''
                      : '反馈结束时间不能小于反馈时间'
                  },
                },
              ]
            : []
      })
    })
    // onFieldInputChange$('_dept').subscribe(({ value }) => {
    //   setFieldState('*(mappingValue)', state => {
    //     state.value = ''
    //   })
    // })
    onFieldValueChange$('_dept').subscribe(({ value }) => {
      if (value && value.label) {
        getCurrentDept && getCurrentDept(value)
        getNumberPrefix(value.label) // todo '区公司\\网络部\\应急通信办公室\\阳光工程办公室'
        actions.setFieldState('fileList', state => {
          state.props['x-component-props'].orgId = value.value
        })
        const array = value.label.split('\\')
        setFormTitle(`${array[0] || ''}${array[1] || ''}工作协调函`)
        actions.setFieldState('*(formTitle)', state => {
          state.value = `${array[0] || ''}${array[1] || ''}工作协调函`
        })
      }
    })
    onFieldValueChange$('title').subscribe(({ value }) => {
      setTitle(value)
    })
    onFieldValueChange$('fileList').subscribe(({ value }) => {
      if (value && value.length > 0) {
        if (value[0].needConvert) {
          // 'sfs/srvfile?digest=fidaa24490aa709410799fbde955e053725&filename=新建文档.html'
          getHtmlCode(value[0].url, html => {
            setContent(html)
          })
        } else {
          setContent(value[0].html)
        }
      }
    })
  }, [])

  const getNumberPrefix = async dept => {
    const res = await service.getNumberPrefix({
      dept,
    })
    actions.setFieldState('*(mappingValue)', state => {
      state.value = res.data && res.data.length > 0 ? res.data[0].mappingValue : ''
      state.props.enum = res.data.map(item => ({
        value: item.mappingValue,
        label: item.mappingValue,
      }))
    })
  }

  useEffect(() => {
    if (Object.keys(initValue).length > 0) {
      actions.setFormState(state => {
        state.values = signer ? { ...initValue, signer } : initValue
      })
      // actions.setFieldState('*(content)', state => {
      //   state.value = initValue.fileList || []
      // })
      if (!editable) {
        setFormTitle(initValue.formTitle)
      }
      // if (!editable && !initValue.isAutoDispatch) {
      //   actions.setFieldState('isAutoDispatch', state => {
      //     state.value = '2'
      //   })
      // }
    }
  }, [initValue])

  // 字段显示之后要重新设置 orgId,不然会是空
  useEffect(() => {
    if (!editable) return

    actions.getFieldValue('_dept').then(value => {
      if (value && value.label) {
        actions.setFieldState('fileList', state => {
          state.props['x-component-props'].orgId = value.value
        })
      }
    })
  }, [editable])

  // useState(() => {
  //   if (serialNumber) {
  //     actions.setFieldState('serialNumber', state => {
  //       state.value = serialNumber
  //     })
  //   }
  // }, [serialNumber])

  useEffect(() => {
    // 判断是否有签发人
    if (signer) {
      actions.setFieldState('*(signer)', state => {
        state.value = signer || '-'
      })
    }
  }, [signer])

  WpsEditor.isFieldComponent = true
  SelectOrg.isFieldComponent = true
  SelectCityOrg.isFieldComponent = true
  PersonalInfo.isFieldComponent = true
  Dept.isFieldComponent = true
  Upload.isFieldComponent = true
  Editable.isFieldComponent = true
  SerialNumber.isFieldComponent = true

  return (
    <div>
      <p className="form-title">{formTitle || '工作协调函'}</p>
      {/* todo 提交之后才显示编号 */}
      {/* {initValue.serialNumber || serialNumber
        ? <p className="form-number">编号：{initValue.serialNumber || serialNumber}</p>
        : null
      } */}
      <SchemaMarkupForm
        schema={Schema(initValue)}
        actions={actions}
        components={{
          DatePicker,
          Input,
          FormMegaLayout,
          Select,
          TextArea: Input.TextArea,
          Upload,
          WpsEditor,
          SelectOrg,
          SelectCityOrg,
          Radio,
          RadioGroup: Radio.Group,
          PersonalInfo,
          Dept,
          Editable,
          SerialNumber,
        }}
        editable={editable}
        previewPlaceholder="无"
        effects={useManyEffects}
        expressionScope={{
          editable,
          labelAlign: editable ? 'top' : 'left',
        }}
      />
      {extraBody}
      <MainText title={title} content={content} />
    </div>
  )
}
