import React, { useEffect, useState, useMemo } from 'react'
import ReactDOM from 'react-dom'
import { Tabs, Space, Button, message } from 'antd'
import { closeTheWindow, getQueryString, oneKeyDownload } from 'ROOT/utils'
import service from 'ROOT/service'
import { Buttons, Steps } from 'ROOT/components/Process'
import useMyInfo from 'ROOT/hooks/useMyInfo'
import useSigner from 'ROOT/hooks/useSigner'
import { patchData } from 'ROOT/utils/index'
import { getWpsUrl } from 'ROOT/utils/wps'
import ControlModal from 'ROOT/components/ControlModal'
import { isEmpty } from 'lodash'
import { signerNodeName as signerNodeName2 } from 'ROOT/views/web/WorkLetter/module/config'
import moment from 'moment'
import Form, { actions } from './form'
import { formConfig, letterType, nodelist } from '../module/config'
import { closeBtn, saveAndExit, dispatchBtn } from '../module/buttons'
import FeedbackTable from '../component/feedbackTable'
import Dispatch from '../dialog/dispatch'
import ReportSave from '../dialog/ReportSave'

const { TabPane } = Tabs
const typeIndex = 1
const signerNodeName = '市公司领导审批'

export default props => {
  const { userTaskId, procFormDataKey, debug } = useMemo(
    () => getQueryString(props.location.search),
    [props.location.search],
  )
  const [isFeedback, setIsFeedback] = useState(false)
  const [initValue, setInitValue] = useState({})
  const [editable, setEditable] = useState(false)
  // const [showSaveBtn, setShowSaveBtn] = useState(false)
  const [draftValue, setDraftValue] = useState({})
  const [extraButton, setExtraButton] = useState([])
  const myInfo = useMyInfo({ isSetStorage: true })
  const { signer, originData } = useSigner({
    userTaskId,
    nodeName: signerNodeName,
  })
  const [nodeName, setNodeName] = useState('')
  const [serialNumber, setSerialNumber] = useState('')
  const [ableEditBtn, setAbleEditBtn] = useState([])
  const [showControlModal, setShowControlModal] = useState(false)
  const [deptInfo, setDeptInfo] = useState({})
  const [dateList, setDateList] = useState([])
  const [userTask, setUserTask] = useState({})
  const [enableYJFF, setEnableYJFF] = useState(false)
  useEffect(() => {
    if (debug) {
      ableEditForm()
    }
  }, [])

  const ableEditForm = async () => {
    const res = await service.getEditableForm()
    if (res.success && res.data) {
      resetFormState()
      setAbleEditBtn([
        {
          name: '更新数据',
          async: true,
          onClick: async () => {
            await saveForm(() => {
              closeTheWindow(props.location.search)
            }, true)
          },
        },
      ])
    }
  }

  useEffect(async () => {
    if (procFormDataKey) {
      const wpsContent = await getWpsUrl(procFormDataKey)
      service
        .getFormData({
          reportId: procFormDataKey,
        })
        .then(res => {
          if (res.success && res.data) {
            if (wpsContent) {
              res.data.data.fileList[0] = wpsContent
            }
            const { isFeedback, serialNumber } = res.data.data
            setIsFeedback(isFeedback === '1')
            setInitValue(res.data.data)
            resetContentProps()
            setSerialNumber(serialNumber)
          }
        })
      service
        .getFeedbackDay({
          reportId: procFormDataKey,
        })
        .then(res => {
          if (res.success && res.data) {
            setDateList(res.data)
          }
        })
    }
    // 从流程获取系统时间
    if (userTaskId) {
      const res = await service.getUserTask({ taskUserKey: userTaskId })
      setUserTask(res.data)
    }
  }, [])

  useEffect(() => {
    if (serialNumber || nodelist.includes(nodeName)) {
      isShowSerialNumber(true)
    } else {
      isShowSerialNumber(false)
    }
  }, [serialNumber, nodeName])

  const isShowSerialNumber = flag => {
    actions.setFieldState('*(serialNumber)', state => {
      state.visible = flag
      state.required = flag
    })
    actions.setFieldState('*(mappingValue)', state => {
      state.visible = !flag
    })
  }

  const resetContentProps = () => {
    actions.setFieldState('fileList', state => {
      state.props['x-component-props'] = {
        ...state.props['x-component-props'],
        reportId: procFormDataKey,
        type: letterType[typeIndex].type,
        config: formConfig,
      }
    })
  }

  const commonCommit = async () => {
    const data = await actions.submit()
    const result = patchData(data.values, initValue)
    const res = service.upDateForm({
      config: formConfig,
      data: result,
      reportId: procFormDataKey,
      userTaskId,
    })
    return res
  }

  const submitForm = async () => {
    const res = await commonCommit()
    // const data = await actions.submit()
    if (res.success) {
      // return { id: +procFormDataKey, values: data.values }
      return Promise.resolve()
    }
    throw new Error('保存出错')
  }

  useEffect(() => {
    window.addEventListener('process', e => {
      switch (e.detail.type) {
        case 'xthfkan':
          showSaveModal({ title: initValue && initValue.title }, 1)
          break
        case 'saved':
          break
        case 'approved': // 审批后
        case 'added': // 加签后
        case 'stoped': // 终止后
        case 'rejected': // 退回后
        case 'forworded': // 转交后
        case 'cced': // 抄送后
        default:
          finishSubmit()
      }
    })
    // window.addEventListener('process', () => finishSubmit())
  }, [])

  // 字段显示之后要重新设置 reportId, config, formConfig,不然会是空
  useEffect(() => {
    if (!editable) return

    resetContentProps()
  }, [editable])

  const finishSubmit = () => {
    setTimeout(() => {
      closeTheWindow(props.location.search)
    }, 2000)
  }

  const saveForm = async callback => {
    const res = await commonCommit()
    if (res.success) {
      message.success('操作成功', () => {
        if (typeof callback === 'function') callback(res)
      })
    }
  }

  const buttonGroup = () => {
    const array = []
    // if (showSaveBtn) {
    //   array.push(saveAndExit(() => {
    //     saveForm(() => {
    //       closeTheWindow(true)
    //     })
    //   }))
    // }
    if (
      originData.childShape.properties.name === signerNodeName ||
      originData.childShape.properties.name === signerNodeName2 ||
      enableYJFF
    ) {
      array.push({
        name: '一键分发',
        async: false,
        onClick: async () => {
          const res = await actions.getFormState()
          if (!res.values.mainDelivery || res.values.mainDelivery.length <= 0) {
            message.error('请选择主送人员')
            return
          }
          await commonCommit()
          showDispatchModal()
        },
      })
    }
    // if (originData.childShape.properties.name === signerNodeName) {
    //   array.push(dispatchBtn(async () => {
    //     const res = await actions.getFormState()
    //     if (!res.values.mainDelivery || res.values.mainDelivery.length <= 0) {
    //       message.error('请选择主送人员')
    //       return
    //     }
    //     await commonCommit()
    //     showDispatchModal()
    //   }))
    // }
    return array
  }

  const showDispatchModal = async () => {
    const overlay = document.createElement('div')
    document.body.appendChild(overlay)
    const close = () => {
      ReactDOM.unmountComponentAtNode(overlay)
      overlay.parentNode.removeChild(overlay)
    }
    const res = await actions.getFormState()
    ReactDOM.render(
      <Dispatch
        width={700}
        visible
        formData={res.values}
        taskId={userTaskId}
        procFormDataKey={procFormDataKey}
        onOkCallback={finishSubmit}
        onCancel={() => {
          close()
        }}
      />,
      overlay,
    )
  }

  const getSerialNumber = () => {
    service
      .getSerialNumber({
        reportId: procFormDataKey,
      })
      .then(res => {
        if (res.success && res.data) {
          actions.setFieldState('*(serialNumber)', state => {
            state.value = res.data
          })
        } else {
          // 生成编号失败的话手动把编号变成空，防止表单继续提交
          actions.setFieldState('*(serialNumber)', state => {
            state.value = ''
          })
          actions.setFieldState('*(mappingValue)', state => {
            state.value = ''
          })
        }
      })
  }

  const hideSubmitBtn = () => {
    const buttons = document.querySelectorAll('.proc-btn')
    buttons.forEach(btn => {
      if (btn.textContent && btn.textContent.trim() === '提交') {
        btn.style.display = 'none'
      }
    })
  }
  const onMount = ({ access, editMode, draft, process }) => {
    if (process.nodeName === '起草人分发') {
      setEnableYJFF(true)
    }
    // 流程【经办人反馈】节点，选择【隐藏】,isSubmitButton 为‘NONE’隐藏提交按钮
    if (process.nodeName === '经办人反馈' && access && access.isSubmitButton === 'NONE') {
      setTimeout(() => {
        hideSubmitBtn()
      })
    }
    setNodeName(process.nodeName)
    if (process.nodeName === '开始节点' && process.taskStatus === 'running') {
      setExtraButton([
        {
          name: '保存退出',
          async: true,
          onClick: async () => {
            await saveForm(() => {
              closeTheWindow(props.location.search)
            }, true)
          },
        },
        {
          name: '保存',
          async: true,
          onClick: async () => {
            await saveForm()
          },
        },
      ])
    }

    if (nodelist.includes(process.nodeName) && !initValue.serialNumber) {
      getSerialNumber()
    }
    if (access && Object.keys(access).length > 0 && editMode && editMode.includes('web')) {
      Object.keys(access).forEach(key => {
        if (key === 'isEditable' && access[key] === 'WRITE') {
          // setShowSaveBtn(true)
          resetFormState()
          return
        }
        actions.setFieldState(key, state => {
          switch (access[key]) {
            case 'NONE':
              state.display = false
              break
            case 'READ':
              state.editable = false
              break
            case 'WRITE':
              state.editable = true
              state.display = true
              break
            default:
              break
          }
        })
      })
    } else {
      // actions.setFormState((state) => {
      //   state.editable = false
      // })
    }
    if (draft) {
      setDraftValue({ ...draft })
    }
  }

  const resetFormState = () => {
    setEditable(true)
    actions.setFieldState('*(_dept)', state => {
      state.props['x-component'] = 'Editable'
    })
  }

  const getFormData = async () => {
    const data = await actions.submit()
    const result = patchData(data.values, initValue)
    return result
  }
  const filterCollectTimey = (time, type) => {
    if (type === 1) {
      return initValue.cycleDay
    }
    const weekTime = time + 259200000 // 待办时间(精确到时分秒)+3天判断
    let resultValue = 0
    let result = ''
    dateList.forEach((item, index) => {
      const itemDate = moment(item).valueOf() + 36000000 // 下个周期（10点）则是下个周期时间
      if (index === 0) {
        resultValue = itemDate - weekTime
        result = item
      }

      console.log(
        itemDate,
        weekTime,
        itemDate - weekTime,
        itemDate > weekTime,
        itemDate - weekTime < resultValue,
      )
      if (itemDate > weekTime) {
        if (itemDate - weekTime < resultValue) {
          resultValue = itemDate - weekTime
          result = item
        }
      }
    })
    console.log(result)
    return result
  }
  const autoSubmit = () => {
    const btns = document.querySelector('.proc-buttons')
    if (btns.children && btns.children.length && btns.children[0].innerHTML === ' 提交') {
      console.log('autoSubmitworkcitydept===', btns.children[0])
      btns.children[0].click()
    }
  }
  // type = 1 代表按钮事件 =2代表表格
  const showSaveModal = async (record, type, cb) => {
    const overlay = document.createElement('div')
    document.body.appendChild(overlay)
    const close = () => {
      ReactDOM.unmountComponentAtNode(overlay)
      overlay.parentNode.removeChild(overlay)
      if (cb) {
        cb()
      }
    }
    // const res = await actions.getFormState()
    ReactDOM.render(
      <ReportSave
        width={600}
        visible
        record={record}
        procFormDataKey={procFormDataKey}
        userTaskId={userTaskId}
        onOkCallback={type === 1 ? autoSubmit : close}
        onCancel={() => {
          close()
        }}
      />,
      overlay,
    )
  }
  return (
    <div>
      <Form
        signer={signer}
        initValue={draftValue && Object.keys(draftValue).length > 0 ? draftValue : initValue}
        editable={editable}
        extraBody={
          <div key={nodeName}>
            <div className="ant-col ant-form-item-label">
              <label className="" title="反馈内容">
                反馈内容
              </label>
            </div>
            {isFeedback ? (
              <FeedbackTable
                procFormDataKey={procFormDataKey}
                initValue={initValue}
                dateList={dateList}
                nodeName={nodeName}
                showSaveModal={showSaveModal}
                debug={editable ? debug : 0}
              />
            ) : null}
            <Tabs defaultActiveKey="1" className="mb10">
              <TabPane tab="审批流程" key="1">
                <Steps userTaskId={userTaskId} />
              </TabPane>
            </Tabs>
          </div>
        }
      />
      <div>
        {!isEmpty(initValue) && (
          <Space>
            {/* {originData && originData.procFlags === 0 && !originData.userTaskStatus && buttonGroup().map(item => <Button key={item.name} type={item.type} danger={item.name === '一键分发'} onClick={item.onClick}>{item.name}</Button>)} */}
            <Buttons
              userTaskId={userTaskId}
              onMount={onMount}
              onSave={getFormData}
              onSubmitOpen={getFormData}
              onSubmit={submitForm}
              extraButtons={
                originData && originData.procFlags === 0 && !originData.userTaskStatus
                  ? [...buttonGroup(), ...extraButton, ...ableEditBtn]
                  : [...extraButton, ...ableEditBtn]
              }
              // onFinish={finishSubmit}
              customButtons={[
                {
                  key: 'gksz',
                  onClick: () => {
                    setShowControlModal(true)
                  },
                  async: false,
                },
                {
                  key: 'yjxz',
                  onClick: () => oneKeyDownload(procFormDataKey),
                  async: false,
                },
              ]}
            />
          </Space>
        )}
      </div>
      <ControlModal
        visible={showControlModal}
        setShowControlModal={setShowControlModal}
        actions={actions}
        deptInfo={deptInfo}
        userTaskId={userTaskId}
        procFormDataKey={procFormDataKey}
      />
    </div>
  )
}
