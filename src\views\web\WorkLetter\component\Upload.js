import React, { useState, useRef, useCallback } from 'react'
import {
  message
} from 'antd'
import { css } from 'emotion'
import Service from 'ROOT/service'
import Loading from 'ROOT/components/Loading'
import UploadFileList from '@xm/upload-file-list'

export default itemProps => {
  const InputImport = useRef()
  const { value = [], mutators, props, schema, form, editable } = itemProps
  const { noSpecialName = false, nameVerify = true } = props['x-component-props'] || {}
  const [loading, setLoading] = useState(false)
  const selectFiles = () => {
    InputImport.current.click()
  }

  const fileChange = (e) => {
    // let files = e.currentTarget.files[0]
    let files = Array.from(e.target.files)
    // console.log(Array.from(files))
    let arr1 = [], arr2 = [], arr3=[];
    let ableUpload = []
    files.map(item => {
      const { name, size } = item || {}
      const shortName = name.substring(0, name.lastIndexOf('.'))
      if (nameVerify && !noSpecialName && !(/^\d+(\.|\．)/.test(shortName))) {
        arr1.push(name)
        // message.warning('上传文件不符合附件命名要求，附件名称以数字序号+“.”开头')
        // InputImport.current.value = null
        return
      }
      if (size <= 0) {
        arr2.push(name)
        // message.error('无法上传0kb或0kb以下大小的文件，请重新选择文件上传')
        // InputImport.current.value = null
        return
      }
      if (size > 200 * 1024 * 1024) {
        arr3.push(name)
        // const str = `文件大小需${maxSize}M以下`
        // message.error(str)
        return
      }
      ableUpload.push(item)
    })
    if (arr1.length > 0) {
      if (nameVerify) {
        message.warning(`${arr1.join(',')}文件不符合附件命名要求，附件名称以数字序号+“.”或“．”开头`)
      }
    } else if (arr2.length > 0) {
      message.warning(`${arr2.join(',')}0kb或0kb以下大小的文件，请重新选择文件上传`)
    } else if (arr3.length > 0) {
      message.warning(`${arr2.join(',')}文件大小需200M以下`)
    }
    if (files.length > 0) {
      ableUpload.forEach(i => uploadFiles(i))
    }
    InputImport.current.value = null
  }

  const uploadFiles = (files) => {
    let formData = new FormData()
    formData.append('file', files)
    setLoading(true)
    Service.uploadFile(formData).then(res => {
      if (res.code === 200) {
        mutators.push({
          name: files.name,
          size: files.size,
          url: res.fileUrl,
          suffix: files.name.substring(files.name.lastIndexOf('.') + 1),
          uid: files.lastModified
        })
        setLoading(false)
      }
    })
  }

  const uploadOptions = {
    enableSort: editable,
    enableDownload: true,
    enableDelete: editable,
    enableRename: editable,
    enablePreview: true
  }

  const combineFileList = (list) => {
    return !Array.isArray(list)
    ? []
    : list.map((file, index) => ({
      name: !noSpecialName ? `附件：${(file.name || file.fileName).replace(/^附件：/, '')}` : `${(file.name || file.fileName)}`,
      size: file.size || file.fileSize,
      url: file.url || file.fileUrl,
      suffix: (file.name || file.fileName).substring((file.name || file.fileName).lastIndexOf('.') + 1),
      uid: file.uid
    }))
  }

  const onDelete = (fileList) => {
    mutators.change(combineFileList(fileList))
  }

  const onSortEnd = (fileList) => {
    mutators.change(combineFileList(fileList))
  }

  const onRename = (fileList) => {
    mutators.change(combineFileList(fileList))
  }

  const beforeRename = (file) => {
    if (nameVerify && !noSpecialName && !(/^\d+(\.|\．)/.test(file.name.replace(/^附件：/, '')))) {
      message.warning('上传文件不符合附件命名要求，附件名称以数字序号+“.”或“．”开头')
			return false
    }
    return true
  }

  const combineFileName = useCallback(() => {
    return combineFileList(value)
  }, [value])
  return (
    <div style={{ width: '100%' }}>
      {editable && value.length < 10
        ? <div style={{ width: '100%', marginBottom: 10 }}>
            <span
              onClick={selectFiles}
              className="upload-file-btn"
            >上传文件</span>
            <input
              type="file"
              ref={InputImport}
              onChange={fileChange}
              className={css`
                position: absolute;
                top: -9999px;
                left: -9999px;
              `}
              multiple="multiple"
            />
          {loading && <Loading isFullScreen={true} />}
        </div>
        : null
      }
      <UploadFileList options={uploadOptions} dataSource={combineFileName()} onDelete={onDelete} onSortEnd={onSortEnd} onRename={onRename} beforeRename={beforeRename} />
    </div>
  )
}
