import React, { useState, useEffect } from 'react'
import moment from 'moment'
import { Table, Space, Button, message, Select } from 'antd'
import { PaperClipOutlined } from '@ant-design/icons'
import service from 'ROOT/service'
import Api from 'ROOT/service/api'
import { formBizId } from 'ROOT/constants'
import { downloadUrl } from 'ROOT/utils'
import { downloadFileByGet, downloadFileByPost } from 'ROOT/utils/download'
import { css } from 'emotion'

const pageSize = 5

const fbTableConfig = [
  {
    title: '序号',
    width: 84,
    dataIndex: 'index',
    key: 'index',
  },
  {
    title: '反馈单位部门',
    width: 200,
    dataIndex: 'deptName',
    key: 'deptName',
  },
  {
    title: '反馈人',
    width: 100,
    dataIndex: 'userName',
    key: 'userName',
  },
  {
    title: '反馈时间',
    width: 150,
    dataIndex: 'feedbackTime',
    key: 'feedbackTime',
  },
  {
    title: '反馈日期',
    width: 200,
    dataIndex: 'feedbackDay',
    key: 'feedbackDay',
  },
  // {
  //   title: '情况说明',
  //   dataIndex: 'content',
  //   key: 'content',
  // },
  {
    title: '反馈文件',
    dataIndex: 'feedbackAttach',
    key: 'feedbackAttach',
    render: text => {
      if (text && text.length > 0) {
        return (
          <Space wrap>
            {text.map(item => {
              return (
                <Button
                  key={item.fileUrl || item.url}
                  type="link"
                  href={downloadUrl(item.fileUrl || item.url, item.fileName || item.name)}
                  onClick={e => {
                    e.stopPropagation()
                  }}
                >
                  <PaperClipOutlined style={{ color: 'rgba(0,0,0,.45)' }} />
                  {item.fileName || item.name}
                </Button>
              )
            })}
          </Space>
        )
      }
    },
  },
]

export default props => {
  const { procFormDataKey, initValue: { serialNumber, title } = {}, nodeName, debug = 0 } = props
  console.log(props, 'props')

  const [dataSource, setDataSource] = useState([])
  const [date, setDate] = useState('')

  // 是否展示更多标志
  const [showMore, setShowMore] = useState(false)

  const getTableList = async () => {
    const res = await service.getWorkFeedbackList({
      reportId: procFormDataKey,
      feedbackDay: date,
      processNodeName: nodeName,
      debug,
    })
    const resData = res.data
    if (res.success) {
      setDataSource(resData.map((item, index) => ({ ...item, index: ++index })))
    }
  }
  useEffect(() => {
    if (date) {
      getTableList()
    }
  }, [date])

  useEffect(() => {
    if (props.dateList && props.dateList.length) {
      let closestTime = moment(props.dateList[0])
      let timeDiff = Math.abs(closestTime - moment())
      for (let i = 1; i < props.dateList.length; i++) {
        const newTimeDiff = Math.abs(moment(props.dateList[i]) - moment())
        if (newTimeDiff < timeDiff) {
          timeDiff = newTimeDiff
          closestTime = moment(props.dateList[i])
        }
      }
      setDate(closestTime.format('YYYY-MM-DD'))
    } else {
      setDate('')
    }
  }, [props.dateList])

  const downFile = () => {
    if (dataSource.length === 0) {
      message.error('暂无相关数据')
      return false
    }
    downloadFileByGet(
      '/security-manage-platform/web/report/exportFeedback',
      {
        feedbackDay: date,
        reportId: procFormDataKey,
        processNodeName: nodeName,
        debug,
      },
      `${serialNumber} ${title}.zip`,
    )
  }

  const downLoadAll = () => {
    if (dataSource.length === 0) {
      message.error('暂无相关数据')
      return false
    }
    downloadFileByGet(
      '/security-manage-platform/web/report/exportFeedback',
      {
        reportId: procFormDataKey,
        processNodeName: nodeName,
        debug,
      },
      `${serialNumber} ${title}.zip`,
    )
  }

  return (
    <div
      className={css`
        position: relative;
      `}
    >
      <div
        className={css`
          display: flex;
          margin-bottom: 16px;
        `}
      >
        <Button
          onClick={() => downLoadAll()}
          className={css`
            margin-right: 8px;
            border-radius: 4px;
          `}
          type="primary"
        >
          全部下载
        </Button>
        <Button
          onClick={() => downFile()}
          className={css`
            margin-right: 16px;
            border-radius: 4px;
          `}
        >
          下载本次反馈
        </Button>
        <span
          className={css`
            margin-right: 16px;
            border-radius: 4px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 14px;
            color: #5c626b;
            line-height: 32px;
          `}
        >
          请点击条目信息打开之后上传反馈材料
        </span>
      </div>
      <div
        className={css`
          position: absolute;
          right: 0px;
          top: 0px;
        `}
      >
        <span
          className={css`
            margin-right: 16px;
            border-radius: 4px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 14px;
            color: #33373c;
            line-height: 32px;
          `}
        >
          反馈日期
        </span>
        <Select
          className={css`
            width: 150px;
            border-radius: 4px;
          `}
          value={date}
          onChange={e => setDate(e)}
        >
          {props.dateList &&
            props.dateList.map(item => (
              <Select.Option value={item} key={item}>
                {item}
              </Select.Option>
            ))}
        </Select>
      </div>
      <Table
        columns={fbTableConfig}
        rowKey="index"
        dataSource={showMore ? dataSource : dataSource.slice(0, pageSize)}
        pagination={false}
        scroll={{
          x: 1000,
        }}
        onRow={record => {
          return {
            onClick: event => {
              const param = {
                collectTime: record && record.collectTime,
                title,
                deptId: record && record.deptId,
                deptName: record && record.deptName,
                id: record && record.id,
              }
              if (record.fileName && record.fileResource) {
                param.files = [
                  {
                    name: record.fileName,
                    url: record.fileResource,
                    size: record.fileSize,
                    uid: Math.random(),
                  },
                ]
              }
              props.showSaveModal(param, '2', getTableList)
            }, // 点击行
          }
        }}
      />
      {!showMore && (dataSource || []).length > pageSize && (
        <div
          className={css`
            text-align: center;
            margin: 20px 0;
          `}
        >
          <a
            className={css`
              cursor: pointer;
              color: rgb(79, 132, 210) !important;
            `}
            onClick={() => {
              setShowMore(true)
            }}
          >
            展示更多...
          </a>
        </div>
      )}
    </div>
  )
}
