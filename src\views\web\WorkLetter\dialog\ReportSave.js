import React, { useEffect, useState, useCallback } from 'react'
import { Mo<PERSON>, Button, Descriptions, message } from 'antd'
import moment from 'moment'
import service from 'ROOT/service'
import { UPLOAD_URL } from 'ROOT/constants'
import useMyInfo from 'ROOT/hooks/useMyInfo'
import { SchemaMarkupForm, createAsyncFormActions, FormEffectHooks } from '@formily/antd'
import { FormMegaLayout, Input, DatePicker } from '@formily/antd-components'
import Upload from '../component/Upload'
import Editable from 'ROOT/components/Formily/Editable'

const { onFieldValueChange$, onFieldInputChange$ } = FormEffectHooks

export const actions = createAsyncFormActions()

const schema = {
  type: 'object',
  properties: {
    layout: {
      'x-component': 'mega-layout',
      'x-component-props': {
        grid: true,
        autoRow: true,
        // labelWidth: 100,
        labelAlign: '{{labelAlign}}',
        className: 'grid-gaps',
        responsive: {
          lg: 2,
          m: 2,
          s: 2,
        },
      },
      properties: {
        title: {
          key: 'title',
          name: 'title',
          title: '标题',
          maxLength: 200,
          required: true,
          'x-component': 'Editable',
          'x-mega-props': {
            span: 2,
          },
        },
        deptName: {
          key: 'deptName',
          name: 'deptName',
          title: '部门名称',
          required: true,
          'x-component': 'Editable',
          'x-mega-props': {
            span: 2,
          },
        },
        feedbackDay: {
          key: 'feedbackDay',
          name: 'feedbackDay',
          title: '反馈日期',
          'x-component': 'Editable',
        },
        userName: {
          key: 'userName',
          name: 'userName',
          title: '反馈人',
          'x-component': 'Editable',
        },
        feedbackTime: {
          key: 'feedbackTime',
          name: 'feedbackTime',
          title: '反馈时间',
          'x-component': 'Editable',
        },
        feedbackAttach: {
          key: 'feedbackAttach',
          name: 'feedbackAttach',
          title: '反馈文件',
          required: true,
          'x-component': 'Upload',
          'x-component-props': {
            listType: 'text',
            action: UPLOAD_URL,
            nameVerify: false,
          },
          'x-mega-props': {
            span: 2,
          },
        },
      },
    },
  },
}

export default ({ width, visible, onOkCallback, onCancel, formData, procFormDataKey, record, userTaskId}) => {
  const [loading, setLoading] = useState(false)

  const myInfo = useMyInfo({ isSetStorage: true })
  const [recordInfo, setRecordInfo] = useState({})
  const [extra, setExtra] = useState({})
  useEffect(() => {
    service.getFeedbackDetail({
      reportId: procFormDataKey,
      id: record.id ? record.id : null,
      userTaskId,
    }).then(res => {
      if (res.success && res.data) {
        const dataInfo = res.data
        if (dataInfo)
        setRecordInfo(dataInfo)
      }
    })
  }, [])
  const onOk = async () => {
    const data = await actions.submit()
    const { feedbackDay, feedbackAttach, deptName } = data.values
    if (!feedbackAttach || (feedbackAttach && feedbackAttach.length < 1)) {
      return message.warn('请反馈材料')
    }
    const res = await service.saveFeedback({
      id: record.id ? record.id : null,
      feedbackAttach,
      reportId: procFormDataKey,
      feedbackDay,
      deptName,
    })
    if (res.success) {
      message.success('操作成功')
      if (typeof onOkCallback === 'function') {
        onOkCallback()
        // onOkCallback(() => {
        //   closeTheWindow()
        // })
      }
      setLoading(false)
      onCancel()
    }
  }

  useEffect(() => {
    if (Object.keys(myInfo).length > 0) {
      const { loginName, loginMobile, linkPath, loginOrgName } = myInfo
      let extraInfo = {}
      // if (!recordInfo.userName) {
        extraInfo.userName = loginName
        console.log("userName===", loginName)
        // actions.setFieldState('userName', state => {
        //   state.value = myInfo.loginName
        // })
      // }
      // if (!recordInfo.feedbackTime) {
        extraInfo.feedbackTime = moment().format('YYYY-MM-DD HH:mm:ss')
        // actions.setFieldState('feedbackTime', state => {
        //   state.value = moment().format('YYYY-MM-DD HH:mm:ss')
        // })
      // }
      // if (!recordInfo.deptName) {
        extraInfo.deptName = linkPath[0].linkPath
        // actions.setFieldState('deptName', state => {
        //     state.value = linkPath[0].linkPath
        // })
      // }
      setExtra(extraInfo)
    }
  }, [myInfo, record.deptName])

  useEffect(() => {
    const newInfo = Object.assign(record, recordInfo, extra)
    console.log('newInfo===', record, recordInfo, extra, newInfo)
    if (Object.keys(newInfo).length > 0) {
      actions.setFormState(state => {
        state.values = newInfo
      })
    }
  }, [recordInfo, extra])

  const effects = useCallback(() => {
    onFieldValueChange$('feedbackAttach').subscribe(({ value }) => {
      console.log(value)
      if (value && value.length >= 1) {
        actions.setFieldState('feedbackAttach', state => {
          state.props['x-component-props'].disable = true
        })
      } else {
        actions.setFieldState('feedbackAttach', state => {
          state.props['x-component-props'].disable = false
        })
      }
    })
  }, [])

  Upload.isFieldComponent = true
  Editable.isFieldComponent = true

  return (
    <Modal
      width={width}
      title="反馈"
      visible={visible}
      onCancel={onCancel}
      bodyStyle={{
        maxHeight: '600px',
        overflow: 'auto',
      }}
      footer={[
        <Button key="back" onClick={onCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" loading={loading} onClick={onOk}>
          提交
        </Button>,
      ]}
    >
      <div>
        <SchemaMarkupForm
          schema={schema}
          actions={actions}
          components={{
            DatePicker,
            Input,
            FormMegaLayout,
            Upload,
            Editable,
          }}
          previewPlaceholder="无"
          effects={effects}
          expressionScope={{
            labelAlign: 'top',
          }}
        />
      </div>
    </Modal>
  )
}
