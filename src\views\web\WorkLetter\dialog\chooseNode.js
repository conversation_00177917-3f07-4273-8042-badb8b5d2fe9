import React, { useState, useEffect } from 'react'
import { message, Modal, Radio, Space, Button } from 'antd'
import service from 'ROOT/service'

export default ({
  type,
  width,
  onOk,
  onCancel,
  onFinish,
  visible,
}) => {
  const [choosed, setChoosed] = useState()
  const [serviceData, setServiceData] = useState({})
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    getNodeList()
  }, [])

  const getNodeList = () => {
    service.getNodeList({

    }).then(res => {
      handleRequest(res)
    }).catch(res => {
      // message.error(res)
    })
  }

  const handleRequest = (res) => {
    if (res.success) {
      setServiceData(res.data)
    } else {
      message.error(res.msg || '网络错误')
    }
  }

  const onChange = (e) => {
    setChoosed(e.target.value)
  }

  const enterOk = () => {
    if (!choosed) {
      message.error('请选择提交路径')
      return
    }
    setLoading(true)
    onOk({
      chooseItem: choosed,
      callback: () => {
        setLoading(false)
      },
    })
  }

  const renderFooter = () => {
    const array = [
      <Button key="back" onClick={onCancel}>
        取消
      </Button>,
      <Button key="submit" loading={loading} type="primary" onClick={enterOk}>
        确定
      </Button>,
    ]
    if (type === 'next') {
      array.push(
        <Button key="submit" loading={loading} type="primary" onClick={onFinish}>
          结束流程
        </Button>,
      )
    }
    return array
  }

  return (
    <Modal
      width={width}
      title="路径选择"
      visible={visible}
      // onOk={enterOk}
      onCancel={onCancel}
      footer={renderFooter()}
    >
      <Radio.Group onChange={onChange} value={choosed}>
        <Space direction="vertical">
          {/* {serviceData.map(item => <Radio value={1}>Option A</Radio>)} */}
          <Radio value={1}>Option A</Radio>
          <Radio value={2}>Option B</Radio>
          <Radio value={3}>Option C</Radio>
        </Space>
      </Radio.Group>
    </Modal>
  )
}
