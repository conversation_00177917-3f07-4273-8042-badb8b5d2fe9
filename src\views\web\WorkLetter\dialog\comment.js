import React, { useState, useMemo } from 'react'
import { Mo<PERSON>, Button, message } from 'antd'
import {
  SchemaMarkupForm,
  createAsyncFormActions,
} from '@formily/antd'
import {
  FormMegaLayout,
  Input,
  DatePicker,
  Upload,
} from '@formily/antd-components'

const { TextArea } = Input

export default ({
  width,
  visible,
  onCancel,
  onOk,
  body,
}) => {
  const actions = useMemo(() => createAsyncFormActions(), [])
  const [content, setContent] = useState()

  const enterOk = () => {
    const res = actions.submit()
    // if (!content) {
    //   message.error('意见内容不能为空')
    //   return
    // }
  }

  return (
    <Modal
      width={width}
      title="填写处理意见"
      visible={visible}
      onCancel={onCancel}
      onOk={enterOk}
    >
      <SchemaMarkupForm
        schema={{
          type: 'object',
          properties: {
            layout: {
              'x-component': 'mega-layout',
              'x-component-props': {
                grid: true,
                autoRow: true,
                labelWidth: 80,
              },
              properties: {
                comment: {
                  key: 'comment',
                  name: 'comment',
                  title: '意见内容',
                  required: true,
                  'x-component': 'TextArea',
                  'x-mega-props': {
                    span: 3,
                  },
                },
              },
            },
          },
        }}
        actions={actions}
        components={{
          FormMegaLayout,
          TextArea: Input.TextArea,
        }}
        previewPlaceholder="无"
        expressionScope={{
          dept: '部门啊',
        }}
      />
      {/* <TextArea rows={4} value={content} onChange={(e) => setContent(e.target.value)} /> */}
    </Modal>
  )
}