import React, { useEffect, useState } from 'react'
import { Modal, Button, Descriptions, message } from 'antd'
import moment from 'moment'
import _ from 'lodash'
import service from 'ROOT/service'
import { closeTheWindow, xhrWrapper } from 'ROOT/utils'
import useMyInfo from 'ROOT/hooks/useMyInfo'
import { nodelist, copyNodeList } from '../module/config'
import { patchData } from '../../../../utils'

export default ({
  width,
  visible,
  onOkCallback,
  onCancel,
  formData,
  taskId,
  procKey,
  procFormDataKey,
  appId,
  domHidden,
}) => {
  const [serialNumber, setSerialNumber] = useState('')
  const [tableInfo, setTableInfo] = useState({})
  const [content, setContent] = useState({})
  const [loading, setLoading] = useState(false)
  const [userTaskLevel, setUserTaskLevel] = useState()
  const [initValue, setInitValue] = useState({})
  const [replaceUsers, setReplaceUsers] = useState([])

  const myInfo = useMyInfo({ isSetStorage: true })

  useEffect(() => {
    getFormData()
    getDetailInfo()
  }, [])

  const getFormData = () => {
    service
      .getFormData({
        reportId: procFormDataKey,
      })
      .then(res => {
        if (res.success && res.data) {
          setSerialNumber(
            res.data.data.serialNumber ||
              `${res.data.data.mappingValue}〔${moment().format('YYYY')}〕 号`,
          )
          setUserTaskLevel(res.data.data._taskLevel)
          setInitValue(res.data.data)
        }
      })
  }

  // 获取分发人员对应的代理人员信息
  const getReplaceUsers = async (users = []) => {
    const [err, data] = await xhrWrapper(service.queryDeptAuthGrantInfo(users))

    // 过滤掉没有代理人的数据
    const hasProxyUsers = data.filter(user => !!user.replace)

    if (!err) {
      setReplaceUsers(hasProxyUsers)
    }

    return hasProxyUsers
  }

  const getDetailInfo = async () => {
    const array = [
      {
        type: 1,
        orgDepts: formData.mainDelivery.map(item => ({
          ...item,
          deptId: item.deptId && item.deptId.toString().includes('_allDept') ? null : item.deptId,
        })),
      },
    ]
    if (formData.carbonCopy && formData.carbonCopy.length > 0) {
      array.push({
        type: 2,
        orgDepts: formData.carbonCopy.map(item => ({
          ...item,
          deptId: item.deptId && item.deptId.toString().includes('_allDept') ? null : item.deptId,
        })),
      })
    }
    setLoading(true)
    const res = await service.getDispatchInfo({
      data: array,
    })
    setLoading(false)
    if (res.success) {
      const users = _.uniqWith(
        Object.keys(res.data).reduce((result, key) => {
          const users = res.data[key] || []

          return result.concat(users)
        }, []),
        (a, b) => {
          return a.loginOrgId === b.loginOrgId && a.deptId === b.deptId && a.loginUid === b.loginUid
        },
      ).map(user => {
        return {
          id: user.loginUid,
          name: user.loginName,
          orgId: user.loginOrgId,
          deptId: user.deptId,
        }
      })

      const proxyUsers = await getReplaceUsers(users)

      setTableInfo(res.data)
      setContent(combineTableInfo(res.data, proxyUsers))
    }
  }

  const combineTableInfo = (data, proxyUsers) => {
    const obj = {}

    const getUserName = item => {
      const { loginUid, loginOrgId, deptId } = item
      const proxyUser = proxyUsers.find(
        user =>
          String(user.id) === String(loginUid) &&
          String(user.orgId) === String(loginOrgId) &&
          String(user.deptId) === String(deptId),
      )

      if (!proxyUser) return item.loginName

      return `<span style="color: #f00">${proxyUser.replace.name}</span> 代 ${item.loginName}`
    }

    Object.keys(data).forEach(key => {
      obj[key] = []
      data[key].forEach(item => {
        obj[key].push(
          `${item.loginOrgName}${
            item.deptName === '未归属' ? '' : `\\${item.deptName}`
          }(${getUserName(item)})`,
        )
      })
    })
    return obj
  }

  const getMemberInfo = nodeName => {
    switch (nodeName) {
      case nodelist[0]:
        return tableInfo.areaOrgs.map(item => ({
          id: item.loginUid,
          name: item.loginName,
          orgId: item.loginOrgId,
          deptId: item.deptId,
        }))
      case nodelist[1]:
        return tableInfo.cityOrgs.map(item => ({
          id: item.loginUid,
          name: item.loginName,
          orgId: item.loginOrgId,
          deptId: item.deptId,
        }))
      case nodelist[2]:
        return tableInfo.proOrgs.map(item => ({
          id: item.loginUid,
          name: item.loginName,
          orgId: item.loginOrgId,
          deptId: item.deptId,
        }))
      case copyNodeList[0]:
        return tableInfo.areaCopyOrgs.map(item => ({
          id: item.loginUid,
          name: item.loginName,
          orgId: item.loginOrgId,
          deptId: item.deptId,
        }))
      case copyNodeList[1]:
        return tableInfo.cityCopyOrgs.map(item => ({
          id: item.loginUid,
          name: item.loginName,
          orgId: item.loginOrgId,
          deptId: item.deptId,
        }))
      case copyNodeList[2]:
        return tableInfo.proCopyOrgs.map(item => ({
          id: item.loginUid,
          name: item.loginName,
          orgId: item.loginOrgId,
          deptId: item.deptId,
        }))
      default:
        break
    }
  }

  const combineAssignee = node => {
    const array = []
    node.forEach(item => {
      const member = getMemberInfo(item.actinstName || item.actName)
      if (member && member.length > 0) {
        let realMember = []
        if (nodelist.includes(item.actinstName || item.actName)) {
          realMember = member.filter(
            it => tableInfo.mainSend.findIndex(i => i.loginUid === it.id) > -1,
          )
        }
        if (copyNodeList.includes(item.actinstName || item.actName)) {
          realMember = member.filter(
            it => tableInfo.copySend.findIndex(i => i.loginUid === it.id) > -1,
          )
        }
        // const mainSendArray = member.filter(it => tableInfo.mainSend.findIndex(i => i.loginUid === it.id) > -1)
        // const copySendArray = member.filter(it => tableInfo.copySend.findIndex(i => i.loginUid === it.id) > -1)
        if (realMember && realMember.length > 0) {
          array.push(
            procKey
              ? {
                  actKey: item.actKey,
                  assigneeInfo: {
                    assignees: realMember,
                  },
                }
              : {
                  actinstId: item.id,
                  assigneeInfo: {
                    assignees: realMember,
                  },
                },
          )
        }
      }
    })
    return array
  }

  const addReplaceUsers = (infos = []) => {
    infos.forEach(info => {
      const users = info.assigneeInfo.assignees || []

      info.assigneeInfo.assignees = users.map(user => {
        const { id, orgId, deptId } = user
        const replaceUser = (replaceUsers || []).find(
          user =>
            String(user.id) === String(id) &&
            String(user.orgId) === String(orgId) &&
            String(user.deptId) === String(deptId),
        )

        if (replaceUser) {
          return {
            ...replaceUser.replace,
            replace: user,
          }
        }

        return user
      })
    })

    return infos
  }

  const onOk = async () => {
    setLoading(true)
    const operator = {
      id: myInfo.loginUid,
      name: myInfo.loginName,
      orgId: myInfo.loginOrgId,
      orgName: myInfo.loginOrgName,
    }
    const result = patchData(formData, initValue)
    const node = await service.getStartNode(
      procKey
        ? {
            procKey,
            operator,
            varJson: JSON.stringify(result),
          }
        : {
            taskId,
            operator,
            varJson: JSON.stringify(result),
          },
    )
    const allNode = [...(node.data || [])]

    // if (procKey) {
    //   const copyNode = await service.getStartNextCopyNode({
    //     procKey,
    //     varJson: JSON.stringify(formData)
    //   })
    //   if (copyNode.data && copyNode.data.length > 0) {
    //     allNode.push(...copyNode.data)
    //   }
    // }

    const res = procKey
      ? await service.startTask({
          manualStartInfos: addReplaceUsers(combineAssignee(allNode)),
          appId,
          procKey,
          procFormDataKey,
          starterDeptId: formData._dept.value,
          varJson: JSON.stringify(result),
          comment: JSON.stringify({ text: '已分发' }),
        })
      : await service.completeTask({
          manualCompleteInfos: addReplaceUsers(combineAssignee(allNode)),
          taskId,
          userTaskLevel,
          varJson: JSON.stringify(result),
          comment: JSON.stringify({ text: '已分发' }),
        })
    if (res.success) {
      setLoading(false)
      onCancel()
      message.success('操作成功', () => {
        if (typeof onOkCallback === 'function') {
          onOkCallback()
          // onOkCallback(() => {
          //   closeTheWindow()
          // })
        }
      })
    }
  }

  return (
    <Modal
      width={width}
      title="一键自动编发"
      visible={visible}
      onCancel={onCancel}
      bodyStyle={{
        maxHeight: '550px',
        overflow: 'auto',
      }}
      footer={[
        <Button key="back" onClick={onCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" loading={loading} onClick={onOk}>
          确定
        </Button>,
      ]}
    >
      <div>
        <Descriptions column={1} bordered>
          <Descriptions.Item
            labelStyle={{ width: '165px' }}
            contentStyle={{ marginBottom: '20px' }}
            label="公文编号"
          >
            {serialNumber}
          </Descriptions.Item>
          <Descriptions.Item
            labelStyle={{ width: '165px' }}
            contentStyle={{ marginBottom: '20px' }}
            label="主送"
          >
            <div
              dangerouslySetInnerHTML={{
                __html: content.mainSend && content.mainSend.join('<br />'),
              }}
            />
          </Descriptions.Item>
          <Descriptions.Item labelStyle={{ width: '165px' }} label="抄送">
            <div
              dangerouslySetInnerHTML={{
                __html: content.copySend && content.copySend.join('<br />'),
              }}
            />
          </Descriptions.Item>
        </Descriptions>
        <div className="modal-sub-title">
          <span className="modal-title-line" />
          <p className="modal-title-text">流程分布信息</p>
        </div>
        <Descriptions column={1} bordered>
          <Descriptions.Item
            labelStyle={{ width: '165px' }}
            contentStyle={{ marginBottom: '20px' }}
            label="市公司部门收办"
          >
            <div
              dangerouslySetInnerHTML={{
                __html:
                  content.cityOrgs &&
                  content.cityOrgs
                    .concat(content.countyOrgs && content.countyOrgs)
                    .filter(it => content.mainSend.findIndex(i => i === it) > -1)
                    .join('<br />'),
              }}
            />
          </Descriptions.Item>
          {domHidden ? null : (
            <Descriptions.Item
              labelStyle={{ width: '165px' }}
              contentStyle={{ marginBottom: '20px' }}
              label="专业公司收办"
            >
              <div
                dangerouslySetInnerHTML={{
                  __html:
                    content.proOrgs &&
                    content.proOrgs
                      .filter(it => content.mainSend.findIndex(i => i === it) > -1)
                      .join('<br />'),
                }}
              />
            </Descriptions.Item>
          )}
          {domHidden ? null : (
            <Descriptions.Item labelStyle={{ width: '165px' }} label="区公司部门收办">
              <div
                dangerouslySetInnerHTML={{
                  __html:
                    content.areaOrgs &&
                    content.areaOrgs
                      .filter(it => content.mainSend.findIndex(i => i === it) > -1)
                      .join('<br />'),
                }}
              />
            </Descriptions.Item>
          )}
          {content.otherOrgs && content.otherOrgs.length > 0 ? (
            <Descriptions.Item labelStyle={{ width: '165px' }} label="其他公司部门收办">
              <div
                dangerouslySetInnerHTML={{
                  __html:
                    content.otherOrgs &&
                    content.otherOrgs
                      .filter(it => content.mainSend.findIndex(i => i === it) > -1)
                      .join('<br />'),
                }}
              />
            </Descriptions.Item>
          ) : null}
          <Descriptions.Item
            labelStyle={{ width: '165px' }}
            contentStyle={{ marginBottom: '20px' }}
            label="市公司部门收阅"
          >
            <div
              dangerouslySetInnerHTML={{
                __html:
                  content.cityOrgs &&
                  content.cityOrgs
                    .filter(it => content.copySend.findIndex(i => i === it) > -1)
                    .join('<br />'),
              }}
            />
          </Descriptions.Item>
          {domHidden ? null : (
            <Descriptions.Item
              labelStyle={{ width: '165px' }}
              contentStyle={{ marginBottom: '20px' }}
              label="专业公司收阅"
            >
              <div
                dangerouslySetInnerHTML={{
                  __html:
                    content.proOrgs &&
                    content.proOrgs
                      .filter(it => content.copySend.findIndex(i => i === it) > -1)
                      .join('<br />'),
                }}
              />
            </Descriptions.Item>
          )}
          {domHidden ? null : (
            <Descriptions.Item labelStyle={{ width: '165px' }} label="区公司部门收阅">
              <div
                dangerouslySetInnerHTML={{
                  __html:
                    content.areaOrgs &&
                    content.areaOrgs
                      .filter(it => content.copySend.findIndex(i => i === it) > -1)
                      .join('<br />'),
                }}
              />
            </Descriptions.Item>
          )}
        </Descriptions>
      </div>
    </Modal>
  )
}
