import React, { useEffect, useMemo } from 'react'
import { Modal, message } from 'antd'
import {
  SchemaMarkupForm,
  createAsyncFormActions,
} from '@formily/antd'
import {
  FormMegaLayout,
  Input,
  DatePicker,
  // Upload,
} from '@formily/antd-components'
import useMyInfo from 'ROOT/hooks/useMyInfo'
import service from 'ROOT/service'
import Schema from '../module/feedbackSchema'
import Upload from '../component/Upload'

export default ({
  appTaskId,
  width,
  onOk,
  onCancel,
  visible,
}) => {
  const actions = useMemo(() => createAsyncFormActions(), [])
  const myInfo = useMyInfo()

  useEffect(() => {
    actions.setFieldState('fbUser', state => {
      state.value = myInfo.loginName
    })
  }, [myInfo])

  const enterOk = async () => {
    const data = await actions.submit()
    console.log(data)
    const res = await service.postFeedback({
      appTaskId,
      content: data.values.remark,
      attachments: data.values.attach && data.values.attach.length > 0 && data.values.attach.map(item => ({
        fileName: item.name,
        fileUrl: item.fileUrl || item.url,
      })),
    })
    if (res.success) {
      message.success('操作成功', () => {
        onOk()
      })
    } else {
      message.error(res.msg)
    }
  }

  Upload.isFieldComponent = true

  return (
    <Modal
      width={width}
      title="反馈"
      visible={visible}
      onOk={enterOk}
      onCancel={onCancel}
    >
      <SchemaMarkupForm
        schema={Schema}
        actions={actions}
        components={{
          DatePicker,
          Input,
          FormMegaLayout,
          TextArea: Input.TextArea,
          Upload,
        }}
        previewPlaceholder="无"
      />
    </Modal>
  )
}
