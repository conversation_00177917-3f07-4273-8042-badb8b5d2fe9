import React from 'react'
import { <PERSON><PERSON>, But<PERSON> } from 'antd'
import {
  CloseOutlined,
} from '@ant-design/icons'

export default ({
  onCancel,
  content,
}) => {
  return (
    <div className="form-modal">
      <div className="form-modal-mask" />
      <div className="form-modal-body">
        <div className="form-modal-closeBtn" onClick={onCancel}>
          <CloseOutlined style={{ fontSize: '14px' }} />
        </div>
        <div className="form-modal-title">{content.title}</div>
        <div className="form-modal-content">
          <div dangerouslySetInnerHTML={{ __html: content.content }} />
        </div>
      </div>
    </div>
  )
}