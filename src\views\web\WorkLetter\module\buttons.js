import {
  Tabs,
  Table,
  Modal,
} from 'antd'

const { confirm } = Modal

export const closeBtn = (flag) => ({
  name: '关闭',
  type: 'default',
  onClick: () => confirm({
    content: '是否确定退出？',
    onOk: () => {
      // if (flag) {
      //   history.go(-1)
      //   return
      // }
      window.close()
    },
  }),
})

export const previewBtn = (callback) => commonBtn({
  name: '查看函件内容',
  type: 'primary',
  callback,
})

export const saveBtn = (callback) => commonBtn({
  name: '保存',
  type: 'primary',
  callback,
})

export const saveAndExit = (callback) => commonBtn({
  name: '保存退出',
  type: 'primary',
  callback,
})

export const deleteBtn = (callback) => commonBtn({
  name: '注销文档',
  type: 'default',
  callback,
})

export const feedBackBtn = (callback) => commonBtn({
  name: '提交反馈',
  type: 'primary',
  callback,
})

export const dispatchBtn = (callback) => commonBtn({
  name: '一键分发',
  type: 'primary',
  callback,
})

export const submitBtn = (callback) => commonBtn({
  name: '提交下一处理',
  type: 'primary',
  callback,
})

export const dealBtn = (callback) => commonBtn({
  name: '填写处理意见',
  type: 'primary',
  callback,
})

const commonBtn = ({ name, type, callback}) => ({
  name,
  type,
  onClick: () => {
    if (typeof callback === 'function') callback()
  },
})