export const formConfig = {
  isDispatch: '是否一键分发',
  formTitle: '表单标题',
  mappingValue: '文件编号映射',
  serialNumber: '文件编号',
  _dept: '拟稿部门',
  draftUser: '拟稿人',
  draftPhone: '拟稿电话',
  title: '标题',
  signer: '签发人',
  _taskLevel: '缓急',
  draftDate: '拟稿日期',
  isAutoDispatch: '是否一键自动编发',
  mainDelivery: '主送',
  carbonCopy: '抄送',
  sendDate: '发函时间',
  sendBasis: '发函依据',
  isFeedback: '是否反馈',
  taskType: '反馈周期',
  day: '反馈时间',
  endTime: '反馈结束时间',
  remark: '备注',
  attach: '附件',
  fileList: '函件内容',
}

export const letterType = [{
  taskBizType: 14,
  type: '14',
  name: '区公司协调函',
  englishName: 'district_company_work_coordination_letter',
  directorFirst: 'zj_2_2_1', // 区公司正部长
  directorSecond: 'zj_2_2_2', // 区公司副部长
  processType: '区公司部门工作协调函',
}, {
  taskBizType: 15,
  type: '15',
  name: '市公司上传区公司协调函',
  englishName: 'city_district_company_work_coordination_letter',
  directorFirst: 'zj_1_2_1', // 市公司正领导
  directorSecond: 'zj_1_2_2', // 市公司副领导
  processType: '市公司上传部门工作协调函',
}, {
  taskBizType: 16,
  type: '16',
  name: '市公司协调函',
  englishName: 'city_company_work_coordination_letter',
  directorFirst: 'zj_2_3_1', // 市公司正部长
  directorSecond: 'zj_2_3_2', // 市公司副部长
  processType: '市公司工作协调函',
},{
  taskBizType: 30,
  type: '30',
  name: '专业公司协调函',
  englishName: 'professional_coordination_letter',
  directorFirst: 'zj_1_2_1', // 市公司正领导
  directorSecond: 'zj_1_2_2', // 市公司副领导
  processType: '专业公司工作协调函',
}, {
  taskBizType: 40,
  type: '40',
  name: '县公司协调函',
  englishName: 'county_company_work_coordination_letter',
  directorFirst: 'zj_2_3_1', // 市公司正部长
  directorSecond: 'zj_2_3_2', // 市公司副部长
  processType: '县公司工作协调函',
}]

export const signerNodeName = '部门经理审核' // '部门经理审核'

export const areaDirectorFirst = 'zj_2_2_1' // 区公司正部长

export const areaDirectorSecond = 'zj_2_2_2' // 区公司副部长

export const cityDirectorFirst = 'zj_2_3_1' // 市公司正部长

export const cityDirectorSecond = 'zj_2_3_2' // 市公司副部长

export const cityDeptDirectorFirst = 'zj_1_2_1' // 市公司正领导

export const cityDeptDirectorSecond = 'zj_1_2_2' // 市公司副领导

export const nodelist = ['区公司收办', '市公司收办', '专业公司收办', '区公司收阅', '市公司收阅', '专业公司收阅']

export const copyNodeList = ['区公司收阅', '市公司收阅', '专业公司收阅']