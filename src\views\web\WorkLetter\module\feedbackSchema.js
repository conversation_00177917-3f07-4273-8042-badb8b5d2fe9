import moment from 'moment'
import { UPLOAD_URL } from 'ROOT/constants'

export default {
  type: 'object',
  properties: {
    layout: {
      'x-component': 'mega-layout',
      'x-component-props': {
        grid: true,
        autoRow: true,
        columns: 2,
        labelWidth: 80,
      },
      properties: {
        fbUser: {
          key: 'fbUser',
          name: 'fbUser',
          title: '反馈人',
          editable: false,
          default: '',
          'x-component': 'Input',
        },
        fbDate: {
          key: 'fbDate',
          name: 'fbDate',
          title: '反馈日期',
          editable: false,
          default: moment().format('YYYY年MM月DD日'),
          'x-component': 'DatePicker',
        },
        remark: {
          key: 'remark',
          name: 'remark',
          title: '情况说明',
          required: true,
          'x-component': 'TextArea',
          'x-mega-props': {
            span: 2,
          },
        },
        attach: {
          key: 'attach',
          name: 'attach',
          title: '文件上传',
          'x-component': 'Upload',
          'x-component-props': {
            listType: 'text',
            action: UPLOAD_URL,
            noSpecialName: true
          },
          'x-mega-props': {
            span: 2,
          },
        },
      },
    },
  },
}
