:root {
  --adm-color-primary: #1677ff;
  --adm-color-success: #00b578;
  --adm-color-warning: #ff8f1f;
  --adm-color-danger: #ff3141;
  --adm-color-white: #ffffff;
  --adm-color-weak: #999999;
  --adm-color-light: #cccccc;
  --adm-border-color: #eeeeee;
  --adm-font-size-main: 13px;
  --adm-color-text: #333333;

  --adm-font-family: -apple-system, blinkmacsystemfont, 'Helvetica Neue', helvetica, segoe ui, arial,
    roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}

body {
  background: #f7f8f9;
}

.main-container {
  /* padding: 80px 106px 97px; */
  padding-top: 48px;
  /* background: #F7F8F9; */
}

.form-container {
  max-width: 1708px;
  margin: 0 auto;
  padding: 24px 59px 70px;
  background: white;
}

.form-container .ant-form-item {
  margin-bottom: 0;
}

.form-title {
  margin-bottom: 24px;
  line-height: 33px;
  font-size: 24px;
  text-align: center;
  font-weight: bold;
  color: #4f84d2;
}

.create-info {
  position: fixed;
  right: 5px;
  top: 12px;
  z-index: 999;
}

.create-info-text {
  color: #115ed2;
}

.grid-gaps
  > .ant-form-item-control-wrapper
  > .ant-form-item-control
  > .ant-form-item-children
  > .mega-layout-container-wrapper
  > .mega-layout-container-content.grid,
.ant-form-item-control-wrapper
  > .ant-form-item-control
  > .ant-form-item-children
  > .mega-layout-item-content
  > .mega-layout-container-content.grid,
.ant-form-item-control
  > .ant-form-item-control-input
  > .ant-form-item-control-input-content
  > .mega-layout-container-wrapper
  > .mega-layout-container-content.grid,
.ant-form-item-control
  > .ant-form-item-control-input
  > .ant-form-item-control-input-content
  > .mega-layout-item-content
  > .mega-layout-container-content.grid {
  grid-column-gap: 40px !important;
  grid-row-gap: 11px !important;
}

/* .main-container textarea.ant-input {
  resize: none;
} */

.form-flex-wrap {
  display: flex;
  width: 100%;
}

.form-flex-item {
  flex: 1;
}

.form-flex-label {
  width: 90px;
  text-align: right;
}

.ant-input[disabled] {
  background: #f4f5f7 !important;
  color: rgba(0, 0, 0, 0.65);
}

/* 调整行高 */
/* .grid-gaps .mega-layout-item-content {
  line-height: 22px !important;
}

.grid-gaps .ant-form-item-control-input {
  align-items: baseline !important;
}

.grid-gaps.ant-row .ant-form-item-label > label {
  align-items: baseline !important;
}

.grid-gaps .ant-space-align-center {
  align-items: baseline !important;
} */
/* end */

.wps-preview {
  padding-top: 15px;
  border-top: 1px dashed #d9d9d9;
}

.wps-preview-disable {
  padding: 0;
  border: none;
}

.wps-preview > div {
  max-width: 100%;
  width: auto !important;
  padding: 0;
  margin: 0 !important;
}

.h5-main-container {
  background-color: #f7f8f9;
}

.h5-main-container .adm-tabs-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: white !important;
  z-index: 1;
}

.h5-main-container .adm-tabs-tab-wrapper {
  background-color: white !important;
}

.h5-main-container .adm-tabs-tab {
  padding: 6px 0 8px;
  font-size: 14px;
}

.h5-main-container .adm-tabs-content {
  padding: 45px 0 0;
}

.h5-main-container .adm-empty-image {
  width: 50%;
}

.h5-none-wrap {
  height: 100vh;
  padding-top: 20px;
  text-align: center;
  font-size: 16px;
  background-color: white;
}

.h5-main-body {
  padding-bottom: 60px;
  background-color: white;
}

.h5-main-body .form-title {
  display: none;
}

.h5-main-body .mega-layout-container-content {
  grid-row-gap: 0 !important;
}

.h5-main-body .mega-layout-item {
  padding-left: 15px;
}

.h5-main-body .mega-layout-container-content .ant-row {
  padding: 10px 15px 10px 0;
  border-bottom: 1px solid #e9ecf0;
}

.h5-main-body .mega-layout-item:last-child .ant-row {
  border-bottom: none;
}

.h5-main-body .mega-layout-container-content .ant-form-item-label {
  padding: 0;
  margin-bottom: 5px;
  line-height: 24px !important;
  font-size: 16px;
  color: #959ba3;
}

.h5-main-body .mega-layout-container-content .ant-form-item-label label {
  height: 24px !important;
  color: #959ba3 !important;
}

.h5-main-body .ant-form-item-control-input {
  min-height: auto;
}

.h5-main-body .mega-layout-item-content {
  line-height: 24px !important;
  color: #262a30 !important;
}

.h5-main-body .ant-form-item {
  margin: 0;
}

.h5-button-group {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  min-height: 48px;
  padding: 8px 0;
  text-align: center;
  background-color: white;
  box-shadow: 0 -1px 8px 0 rgba(0, 0, 0, 0.1);
}

.h5-button-group .adm-button {
  height: 33px;
  padding: 4px 15px;
  font-size: 14px;
}

.h5-preview-container {
  display: flex;
  height: 100%;
  flex-direction: column;
}

.h5-preview-header {
  padding: 15px 15px 0;
  text-align: right;
}

.h5-preview-wrap {
  padding: 15px;
  flex: 1;
}

.h5-file-list {
  display: flex;
  margin-bottom: 5px;
}

.h5-file-name {
  flex: 1;
  line-height: 20px;
  padding-left: 10px;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.h5-file-list .adm-button-mini {
  height: 28px;
  padding: 5px 9px;
  margin-top: 3px;
}

.h5-dispatch-wrap {
  overflow-y: auto;
}

.adm-dialog-body {
  padding: 16px 0 !important;
}

.adm-dialog-body-title {
  line-height: 16px;
  padding-bottom: 16px;
  font-size: 16px;
  color: #262a30;
  border-bottom: 1px solid #ebedf0;
}

.adm-space-vertical > .adm-space-item {
  margin-bottom: 0 !important;
}

.h5-dialog-body {
  padding: 0 0 0 15px;
}

.h5-dialog-items {
  padding: 10px 15px 12px 0;
  border-bottom: 1px solid #e9ecf0;
}

.h5-dialog-items:last-child {
  border-bottom: none;
}

.h5-dialog-items p {
  padding: 0;
  margin: 0;
}

.h5-dialog-items .title {
  margin-bottom: 5px;
  font-size: 14px;
  line-height: 24px;
  color: #959ba3;
  word-break: break-all;
}

.h5-dialog-items .text {
  font-size: 16px;
  line-height: 24px;
  color: #262a30;
  word-break: break-all;
}

.h5-dialog-items .text span {
  margin-left: 3px;
  color: #e53e3e;
}

.adm-dialog-mask .adm-dialog-main {
  border-radius: 4px !important;
}

.h5-dialog-slide {
  height: 40px;
  line-height: 40px;
  padding: 0 15px;
  color: #488ff9;
  background-color: #f7f8f9;
}

.h5-upload-wrap {
  position: relative;
  text-align: right;
}

.h5-upload-btn {
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  text-align: right;
  line-height: 20px;
}

.h5-upload-btn span {
  font-size: 16px;
  vertical-align: top;
  color: #3993f3;
}

.h5-filelist {
  margin-top: 5px;
}

.h5-filelist span {
  font-size: 16px;
  vertical-align: top;
  color: #3993f3;
}

.h5-attach-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: url(../../assets/images/attach.png) center center;
  background-size: 20px;
}

.h5-upload-input {
  opacity: 0;
  width: 100px;
}

.h5-progress-wrap {
  margin-top: 15px;
}

.h5-page-wrap {
  text-align: center;
  margin-top: 10px;
  /* line-height: 32px; */
}

.h5-page-wrap .adm-button {
  padding: 4px 16px;
  color: #262a30;
  font-size: 14px;
}

.main-nav-wrap {
  height: 50px;
  padding: 9px 0 0 21px;
  line-height: 32px;
  font-size: 14px;
  font-weight: bold;
  color: #262a30;
  border-bottom: 1px solid #e9ecf0;
  background-color: white;
  z-index: 2;
  position: fixed;
  overflow: hidden;
  left: 0;
  right: 0;
}

.main-text-label {
  margin-bottom: 8px;
  color: #262a30;
}

.main-text-container {
  padding: 8% 8%;
  border: 1px solid #cbcfd6;
}

.main-text-action {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  position: fixed;
  right: 18px;
  bottom: 76px;
  width: 70px;
  height: 55px;
  border: 1px solid #e9ecf0;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  background-color: #fff;
  color: #4f84d2;
  cursor: pointer;
  z-index: 99;
}

.main-text-title {
  margin-bottom: 35px;
  text-align: center;
  line-height: 32px;
  font-family: 'PingFangSC-Regular';
  font-weight: 400;
  font-size: 26px;
}

.main-text-content {
  line-height: 44px;
  word-break: break-all;
}

.main-text-content p {
  margin-bottom: 0;
}
.main-text-content del {
  display: none;
}
.main-text-content ins {
  text-decoration: none;
}

.mb10 {
  margin-bottom: 10px;
}

.upload-file-btn {
  border: 1px solid #d9d9d9;
  padding: 0 15px;
  display: inline-block;
  cursor: pointer;
  border-radius: 2px;
  height: 32px;
  line-height: 30px;
}

.upload-file-btn-disabled {
  cursor: not-allowed;
  pointer-events: none;
  background-color: #f5f5f5;
  color: rgba(0, 0, 0, 0.25);
}

.form-container .ant-form-item-control-input-content {
  width: 100%;
}
.code-info {
  display: flex;
  align-items: end;
  position: relative;
}
.code-img-info {
  width: 100px;
  height: 100px;
  background: #ffffff;
  border: 1px solid #cbcfd6;
  border-radius: 4px;
  margin-right: 13px;
}
.code-img-info img {
  width: 100%;
  height: 100%;
}
.down-code {
  width: 102px;
  height: 32px;
  background: #ffffff;
  border: 1px solid #cbcfd6;
  border-radius: 4px;
  font-weight: 400;
  font-size: 14px;
  color: #5c626b;
  text-align: center;
}
.sign-record {
  width: 102px;
  height: 32px;
  background: #ffffff;
  border: 1px solid #cbcfd6;
  border-radius: 4px;
  margin-left: 8px;
}
.hover-info {
  width: 84px;
  height: 84px;
  background: rgba(0, 0, 0, 0.4);
  justify-items: center;
  align-items: center;
  position: absolute;
  display: flex;
  top: 8px;
  left: 8px;
  cursor: pointer;
  display: none;
}

.code-info:hover .hover-info {
  display: flex;
}
.flex-info img {
  width: 24px;
  height: 24px;
  display: inline-block;
}
.font-12 {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 12px;
  color: #ffffff;
  text-align: center;
  line-height: 16px;
}
.flex-info {
  display: flex;
  justify-content: center;
}
.width-100 {
  width: 100%;
}
.edit-header {
  width: 116px;
  height: 32px;
  background: #ffffff;
  border: 1px solid #cbcfd6;
  border-radius: 4px;
}
.down-btn {
  width: 88px;
  height: 32px;
  background: #ffffff;
  border: 1px solid #cbcfd6;
  border-radius: 4px;
  margin-right: 10px;
}
.apply-btn {
  width: 60px;
  height: 32px;
  background: #4f84d2;
  border-radius: 4px;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
}
.apply-btn:focus,
.apply-btn:hover {
  background: #4f84d2;
  color: #ffffff;
}
.flex-right {
  display: flex;
  justify-content: right;
  margin-bottom: 17px;
}
.apply-label {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 14px;
  color: #262a30;
  letter-spacing: 0;
  line-height: 22px;
  margin: 0px 0 8px 0;
}
.custom-modal .ant-modal-body {
  padding-bottom: 42px;
}
.delete-img {
  width: 14px;
  height: 14px;
}
.header-list-table tr {
  /* width: 432px;
  height: 52px; */
  background: #ffffff;
  border: 1px solid #cbcfd6;
  border-radius: 4px;
}
.header-list-table .ant-table-tbody td {
  text-align: right;
  padding: 16px 0;
}
.header-list-table .ant-table-tbody td:larst-child {
  padding-left: 5px;
}
.header-list-table .ant-table-tbody td:first-child {
  text-align: left;
}
.sort-list {
}
.sort-list .label-info {
  padding: 0 3px;
  border: 0.5px solid #cbcfd6;
  background: #f7f8f9;
  border-radius: 2px;
  color: #5c626b;
}
.sort-list .custom-info {
  border-radius: 2px;
  background-color: #eaeff7;
  border: 0.5px solid #115ed2;
  color: #2277ff;
  font-weight: 400;
  font-size: 12px;
  padding: 3px 6px;
}
.custom-btn {
  margin-bottom: 17px;
}
.modal-columns .ant-row.ant-form-item {
  display: block;
  margin-bottom: 0;
  min-height: 88px;
}
.apply-edit {
  width: 46px;
  height: 28px;
  border-radius: 3px;
  border-radius: 4px;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 13px;
  color: #2f7fff;
  text-align: center;
  line-height: 18px;
}
.ant-table-tbody > tr > td,
.ant-table-thead > tr > th {
  border-right: none !important;
}
.ant-show-help-appear,
.ant-show-help-enter,
.ant-show-help-leave {
  animation-duration: none !important;
  animation-play-state: none !important;
}
.array-table .ant-table-thead > tr > th,
.array-table .ant-table.ant-table-bordered > .ant-table-container,
.custom-modal .ant-table-thead > tr > th,
.custom-modal .ant-table.ant-table-bordered > .ant-table-container {
  padding: 8px;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 14px;
  color: #959ba3;
  border: none;
}
.ant-table-tbody > tr td {
  color: #262a30;
}
.ant-table-tbody > tr:last-child td {
  border: none;
}
/* .array-table .ant-table-tbody>tr>td{
  padding: 8px;
} */

.drawer-body .ant-drawer-body {
  padding-bottom: 53px;
}
.array-table {
  overflow-x: auto;
}
.array-table .ant-table-thead > tr > th {
  white-space: nowrap;
}
.sms-modal .messages-info {
  padding: 20px 25px;
}
.sms-modal .label-info {
  width: 56px;
  height: 22px;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 14px;
  color: #262a30;
  letter-spacing: 0;
  line-height: 22px;
  margin-bottom: 6px;
}
.sms-modal .list-info {
  background: #f9f9f9;
  border-radius: 4px;
}
.sms-modal .list-info ul {
  padding: 10px 3px 10px 15px;
  height: 215px;
  overflow-y: auto;
}
.sms-modal .list-info li {
  position: relative;
  list-style: none;
  padding-left: 25px;
  padding-bottom: 30px;
}

.sms-modal .list-info li:nth-last-child(2) {
  padding-bottom: 10px;
}

.sms-modal .list-info li:before {
  border-left: 1px dashed #979797;
  content: '';
  position: absolute;
  top: 4px;
  left: 3px;
  width: 0;
  height: 100%;
}
.sms-modal .item-info {
  margin-bottom: 16px;
}
.sms-modal .circle-info {
  width: 8px;
  height: 8px;
  background: #9a9a9a;
  border-radius: 50%;
  position: absolute;
  top: 4px;
  left: 0;
}
.sms-modal .time-info {
  font-family: PingFangSC-Medium;
  font-weight: 500;
  font-size: 14px;
  color: #262a30;
}
.sms-modal .record-info {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 14px;
  color: #5c626b;
}
.empty-info {
  padding-right: 12px;
  height: 192px;
  background: #f9f9f9;
  border-radius: 4px;
  text-align: center;
  line-height: 192px;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 14px;
  color: #959ba3;
}
.waterMark-modal .waterMark-info {
  padding: 20px 25px;
}
.waterMark-modal .waterMark-title {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 14px;
  color: #959ba3;
  margin-bottom: 16px;
}
.waterMark-modal .waterMark-title span {
  color: #262a30;
  margin-right: 8px;
}
.waterMark-modal .ant-radio-wrapper {
  display: block;
  margin-bottom: 16px;
}
.waterMark-modal .ant-input {
  width: 257px;
  margin-left: 8px;
}
.moa-upload-file-list .moa-file-item-file-name {
  word-break: break-all !important;
  line-height: 1.5 !important;
}
@media print {
  .no-print,
  .no-print * {
    display: none !important;
  }
}
.meetManage-search-table .ant-form-item-label>label, .meetManage-search-table .ant-table-thead>tr>th {
 color: #959BA3;
 font-weight: normal;
}
.meetManage-search-table .ant-table-tbody>tr>td, .meetManage-search-table .ant-table-thead>tr>th, .meetManage-search-table .ant-table tfoot>tr>td, .meetManage-search-table .ant-table tfoot>tr>th {
  padding: 8px;
}
.meetManage-search-table .ant-input-affix-wrapper-focused,.meetManage-search-table .ant-input-affix-wrapper:hover,.meetManage-search-table .ant-input-affix-wrapper  {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}
#root .meetManage-search-table .ant-input:focus {
  /* border: none !important; */
  outline: none !important;
  box-shadow: none !important;
}