import React from 'react'
import bowser from 'bowser'

import { Space, Button } from 'antd'
import { LeftOutlined } from '@ant-design/icons'
import { withRouter } from 'react-router-dom'
import { getQueryString, closeTheWindow } from 'ROOT/utils'

import './index.css'

const agent = bowser.parse(window.navigator.userAgent)

const Web = withRouter(props => {
  const isNeedBack = () => {
    const { backurl = '' } = getQueryString(props.location.search)
    return backurl ? (
      <div className="main-nav-wrap">
        <Space size="middle">
          <Button className='no-print' icon={<LeftOutlined />} onClick={() => closeTheWindow(props.location.search, props)}>
            返回
          </Button>
          <span id="breadnav"></span>
        </Space>
      </div>
    ) : null
  }
  return (
    <div>
      {isNeedBack()}
      <div className={agent.platform.type === 'desktop' ? 'main-container' : 'h5-main-container'}>
        <div className={agent.platform.type === 'desktop' ? 'form-container' : ''}>
          {props.children}
        </div>
        {/* {agent.platform.type === 'desktop'
				? <div className="form-container">{children}</div>
				: <div style={{ margin: '20px auto', textAlign: 'center', fontSize: '16px' }}>暂不支持手机端操作，请至WEB端操作</div>} */}
      </div>
    </div>
  )
})

export default Web
